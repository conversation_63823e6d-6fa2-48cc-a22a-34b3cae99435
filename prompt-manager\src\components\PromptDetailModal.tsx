'use client'

import { useState } from 'react'
import { toast } from 'react-hot-toast'
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>eader, 
  ModalT<PERSON><PERSON>, 
  ModalContent, 
  Modal<PERSON>ooter,
  ModalCloseButton 
} from '~/components/ui/Modal'
import { Button } from '~/components/ui/Button'
import { Badge } from '~/components/ui/Badge'
import { useModals } from '~/hooks/useStore'
import { copyToClipboard, formatRelativeTime } from '~/lib/utils'
import { api } from '~/trpc/react'

export function PromptDetailModal() {
  const { modals, currentViewingPrompt, closePromptDetail, openEditPrompt } = useModals()
  const [isExpanded, setIsExpanded] = useState(false)
  
  // 复制提示词的mutation
  const copyPromptMutation = api.prompt.copy.useMutation()
  
  if (!currentViewingPrompt) return null

  const handleCopy = async () => {
    const success = await copyToClipboard(currentViewingPrompt.content)
    if (success) {
      toast.success('提示词已复制到剪贴板')
      try {
        await copyPromptMutation.mutateAsync({ id: currentViewingPrompt.id })
      } catch (error) {
        console.error('更新使用次数失败:', error)
      }
    } else {
      toast.error('复制失败，请重试')
    }
  }

  const handleEdit = () => {
    closePromptDetail()
    openEditPrompt(currentViewingPrompt)
  }

  const contentLines = currentViewingPrompt.content.split('\n')
  const shouldShowExpand = contentLines.length > 10
  const displayContent = isExpanded || !shouldShowExpand 
    ? currentViewingPrompt.content 
    : contentLines.slice(0, 10).join('\n') + '\n...'

  return (
    <Modal
      open={modals.promptDetail}
      onClose={closePromptDetail}
      size="lg"
    >
      <ModalHeader>
        <ModalTitle className="pr-8">
          {currentViewingPrompt.title}
        </ModalTitle>
        <ModalCloseButton onClose={closePromptDetail} />
        
        {/* 分类和使用次数 */}
        <div className="flex items-center gap-3 mt-3">
          {currentViewingPrompt.category && (
            <div className="flex items-center gap-2">
              <div 
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: currentViewingPrompt.category.color }}
              />
              <span className="text-sm text-muted-foreground">
                {currentViewingPrompt.category.name}
              </span>
            </div>
          )}
          
          <Badge variant="secondary">
            使用 {currentViewingPrompt.usageCount} 次
          </Badge>
          
          {!currentViewingPrompt.isPublic && (
            <Badge variant="outline">
              私有
            </Badge>
          )}
        </div>
      </ModalHeader>

      <ModalContent>
        {/* 描述 */}
        {currentViewingPrompt.description && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-muted-foreground mb-2">描述</h4>
            <p className="text-sm">{currentViewingPrompt.description}</p>
          </div>
        )}

        {/* 标签 */}
        {currentViewingPrompt.tags.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-muted-foreground mb-2">标签</h4>
            <div className="flex flex-wrap gap-1">
              {currentViewingPrompt.tags.map((tag, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* 提示词内容 */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-muted-foreground">提示词内容</h4>
            {shouldShowExpand && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="text-xs"
              >
                {isExpanded ? '收起' : '展开全部'}
              </Button>
            )}
          </div>
          
          <div className="bg-muted/50 rounded-lg p-4 max-h-96 overflow-y-auto">
            <pre className="text-sm whitespace-pre-wrap font-mono">
              {displayContent}
            </pre>
          </div>
        </div>

        {/* 元信息 */}
        <div className="grid grid-cols-2 gap-4 text-xs text-muted-foreground">
          <div>
            <span className="font-medium">创建时间：</span>
            <span>{formatRelativeTime(currentViewingPrompt.createdAt)}</span>
          </div>
          <div>
            <span className="font-medium">更新时间：</span>
            <span>{formatRelativeTime(currentViewingPrompt.updatedAt)}</span>
          </div>
          {currentViewingPrompt.createdBy?.name && (
            <div className="col-span-2">
              <span className="font-medium">创建者：</span>
              <span>{currentViewingPrompt.createdBy.name}</span>
            </div>
          )}
        </div>
      </ModalContent>

      <ModalFooter>
        <div className="flex items-center gap-2 w-full sm:w-auto">
          <Button
            variant="outline"
            onClick={handleEdit}
            className="flex-1 sm:flex-none"
          >
            <svg
              className="w-4 h-4 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
              />
            </svg>
            编辑
          </Button>
          
          <Button
            onClick={handleCopy}
            loading={copyPromptMutation.isPending}
            className="flex-1 sm:flex-none"
          >
            <svg
              className="w-4 h-4 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
              />
            </svg>
            复制内容
          </Button>
        </div>
      </ModalFooter>
    </Modal>
  )
}
