{"version": 3, "file": "generator.es.js", "sources": ["../src/generator.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Root} Root\n * @typedef Options options\n *   Configuration.\n * @property {boolean} [showLineNumbers]\n *   Set `showLineNumbers` to `true` to always display line number\n * @property {boolean} [ignoreMissing]\n *   Set `ignoreMissing` to `true` to ignore unsupported languages and line highlighting when no language is specified\n * @property {string} [defaultLanguage]\n *   Uses the specified language as the default if none is specified. Takes precedence over `ignoreMissing`.\n *   Note: The language must be registered with refractor.\n */\n\nimport { visit } from 'unist-util-visit'\nimport { toString } from 'hast-util-to-string'\nimport { filter } from 'unist-util-filter'\nimport rangeParser from 'parse-numeric-range'\n\nconst getLanguage = (node) => {\n  const className = node.properties.className\n  //@ts-ignore\n  for (const classListItem of className) {\n    if (classListItem.slice(0, 9) === 'language-') {\n      return classListItem.slice(9).toLowerCase()\n    }\n  }\n  return null\n}\n\n/**\n * @param {import('refractor/lib/core').Refractor} refractor\n * @param {string} defaultLanguage\n * @return {void}\n */\nconst checkIfLanguageIsRegistered = (refractor, defaultLanguage) => {\n  if (defaultLanguage && !refractor.registered(defaultLanguage)) {\n    throw new Error(`The default language \"${defaultLanguage}\" is not registered with refractor.`)\n  }\n}\n\n/**\n * Create a closure that determines if we have to highlight the given index\n *\n * @param {string} meta\n * @return { (index:number) => boolean }\n */\nconst calculateLinesToHighlight = (meta) => {\n  const RE = /{([\\d,-]+)}/\n  // Remove space between {} e.g. {1, 3}\n  const parsedMeta = meta\n    .split(',')\n    .map((str) => str.trim())\n    .join()\n  if (RE.test(parsedMeta)) {\n    const strlineNumbers = RE.exec(parsedMeta)[1]\n    const lineNumbers = rangeParser(strlineNumbers)\n    return (index) => lineNumbers.includes(index + 1)\n  } else {\n    return () => false\n  }\n}\n\n/**\n * Check if we want to start the line numbering from a given number or 1\n * showLineNumbers=5, will start the numbering from 5\n * @param {string} meta\n * @returns {number}\n */\nconst calculateStartingLine = (meta) => {\n  const RE = /showLineNumbers=(?<lines>\\d+)/i\n  // pick the line number after = using a named capturing group\n  if (RE.test(meta)) {\n    const {\n      groups: { lines },\n    } = RE.exec(meta)\n    return Number(lines)\n  }\n  return 1\n}\n\n/**\n * Create container AST for node lines\n *\n * @param {number} number\n * @return {Element[]}\n */\nconst createLineNodes = (number) => {\n  const a = new Array(number)\n  for (let i = 0; i < number; i++) {\n    a[i] = {\n      type: 'element',\n      tagName: 'span',\n      properties: { className: [] },\n      children: [],\n    }\n  }\n  return a\n}\n\n/**\n * Split multiline text nodes into individual nodes with positioning\n * Add a node start and end line position information for each text node\n *\n * @return { (ast:Element['children']) => Element['children'] }\n *\n */\nconst addNodePositionClosure = () => {\n  let startLineNum = 1\n  /**\n   * @param {Element['children']} ast\n   * @return {Element['children']}\n   */\n  const addNodePosition = (ast) => {\n    return ast.reduce((result, node) => {\n      if (node.type === 'text') {\n        const value = /** @type {string} */ (node.value)\n        const numLines = (value.match(/\\n/g) || '').length\n        if (numLines === 0) {\n          node.position = {\n            // column: 1 is needed to avoid error with @next/mdx\n            // https://github.com/timlrx/rehype-prism-plus/issues/44\n            start: { line: startLineNum, column: 1 },\n            end: { line: startLineNum, column: 1 },\n          }\n          result.push(node)\n        } else {\n          const lines = value.split('\\n')\n          for (const [i, line] of lines.entries()) {\n            result.push({\n              type: 'text',\n              value: i === lines.length - 1 ? line : line + '\\n',\n              position: {\n                start: { line: startLineNum + i, column: 1 },\n                end: { line: startLineNum + i, column: 1 },\n              },\n            })\n          }\n        }\n        startLineNum = startLineNum + numLines\n\n        return result\n      }\n\n      if (Object.prototype.hasOwnProperty.call(node, 'children')) {\n        const initialLineNum = startLineNum\n        // @ts-ignore\n        node.children = addNodePosition(node.children, startLineNum)\n        result.push(node)\n        node.position = {\n          start: { line: initialLineNum, column: 1 },\n          end: { line: startLineNum, column: 1 },\n        }\n        return result\n      }\n\n      result.push(node)\n      return result\n    }, [])\n  }\n  return addNodePosition\n}\n\n/**\n * Rehype prism plugin generator that highlights code blocks with refractor (prismjs)\n *\n * Pass in your own refractor object with the required languages registered:\n * https://github.com/wooorm/refractor#refractorregistersyntax\n *\n * @param {import('refractor/lib/core').Refractor} refractor\n * @return {import('unified').Plugin<[Options?], Root>}\n */\nconst rehypePrismGenerator = (refractor) => {\n  return (options = {}) => {\n    checkIfLanguageIsRegistered(refractor, options.defaultLanguage)\n    return (tree) => {\n      visit(tree, 'element', visitor)\n    }\n\n    /**\n     * @param {Element} node\n     * @param {number} index\n     * @param {Element} parent\n     */\n    function visitor(node, index, parent) {\n      if (!parent || parent.tagName !== 'pre' || node.tagName !== 'code') {\n        return\n      }\n\n      // @ts-ignore meta is a custom code block property\n      let meta = /** @type {string} */ (node?.data?.meta || node?.properties?.metastring || '')\n      // Coerce className to array\n      if (node.properties.className) {\n        if (typeof node.properties.className === 'boolean') {\n          node.properties.className = []\n        } else if (!Array.isArray(node.properties.className)) {\n          node.properties.className = [node.properties.className]\n        }\n      } else {\n        node.properties.className = []\n      }\n\n      let lang = getLanguage(node)\n      // If no language is set on the code block, use defaultLanguage if specified\n      if (!lang && options.defaultLanguage) {\n        lang = options.defaultLanguage\n        node.properties.className.push(`language-${lang}`)\n      }\n      node.properties.className.push('code-highlight')\n\n      /** @type {Element} */\n      let refractorRoot\n\n      // Syntax highlight\n      if (lang) {\n        try {\n          let rootLang\n          if (lang?.includes('diff-')) {\n            rootLang = lang.split('-')[1]\n          } else {\n            rootLang = lang\n          }\n          // @ts-ignore\n          refractorRoot = refractor.highlight(toString(node), rootLang)\n          // @ts-ignore className is already an array\n          parent.properties.className = (parent.properties.className || []).concat(\n            'language-' + rootLang\n          )\n        } catch (err) {\n          if (options.ignoreMissing && /Unknown language/.test(err.message)) {\n            refractorRoot = node\n          } else {\n            throw err\n          }\n        }\n      } else {\n        refractorRoot = node\n      }\n\n      refractorRoot.children = addNodePositionClosure()(refractorRoot.children)\n\n      // Add position info to root\n      if (refractorRoot.children.length > 0) {\n        refractorRoot.position = {\n          start: { line: refractorRoot.children[0].position.start.line, column: 0 },\n          end: {\n            line: refractorRoot.children[refractorRoot.children.length - 1].position.end.line,\n            column: 0,\n          },\n        }\n      } else {\n        refractorRoot.position = {\n          start: { line: 0, column: 0 },\n          end: { line: 0, column: 0 },\n        }\n      }\n\n      const shouldHighlightLine = calculateLinesToHighlight(meta)\n      const startingLineNumber = calculateStartingLine(meta)\n      const codeLineArray = createLineNodes(refractorRoot.position.end.line)\n\n      const falseShowLineNumbersStr = [\n        'showlinenumbers=false',\n        'showlinenumbers=\"false\"',\n        'showlinenumbers={false}',\n      ]\n      for (const [i, line] of codeLineArray.entries()) {\n        // Default class name for each line\n        line.properties.className = ['code-line']\n\n        // Syntax highlight\n        const treeExtract = filter(\n          refractorRoot,\n          (node) => node.position.start.line <= i + 1 && node.position.end.line >= i + 1\n        )\n        line.children = treeExtract.children\n\n        // Line number\n        if (\n          (meta.toLowerCase().includes('showLineNumbers'.toLowerCase()) ||\n            options.showLineNumbers) &&\n          !falseShowLineNumbersStr.some((str) => meta.toLowerCase().includes(str))\n        ) {\n          line.properties.line = [(i + startingLineNumber).toString()]\n          line.properties.className.push('line-number')\n        }\n\n        // Line highlight\n        if (shouldHighlightLine(i)) {\n          line.properties.className.push('highlight-line')\n        }\n\n        // Diff classes\n        if (\n          (lang === 'diff' || lang?.includes('diff-')) &&\n          toString(line).substring(0, 1) === '-'\n        ) {\n          line.properties.className.push('deleted')\n        } else if (\n          (lang === 'diff' || lang?.includes('diff-')) &&\n          toString(line).substring(0, 1) === '+'\n        ) {\n          line.properties.className.push('inserted')\n        }\n      }\n\n      // Remove possible trailing line when splitting by \\n which results in empty array\n      if (\n        codeLineArray.length > 0 &&\n        toString(codeLineArray[codeLineArray.length - 1]).trim() === ''\n      ) {\n        codeLineArray.pop()\n      }\n\n      node.children = codeLineArray\n    }\n  }\n}\n\nexport default rehypePrismGenerator\n"], "names": ["rehypePrismGenerator", "refractor", "options", "defaultLanguage", "registered", "Error", "checkIfLanguageIsRegistered", "tree", "visit", "visitor", "node", "index", "parent", "_node$data", "_node$properties", "tagName", "meta", "data", "properties", "metastring", "className", "Array", "isArray", "refractorRoot", "startLineNum", "lang", "_step", "_iterator", "_createForOfIteratorHelperLoose", "done", "classListItem", "value", "slice", "toLowerCase", "getLanguage", "push", "_lang", "rootLang", "includes", "split", "highlight", "toString", "concat", "err", "ignoreMissing", "test", "message", "children", "addNodePosition", "ast", "reduce", "result", "type", "numLines", "match", "length", "position", "start", "line", "column", "end", "_step2", "lines", "_iterator2", "entries", "_step2$value", "i", "Object", "prototype", "hasOwnProperty", "call", "initialLineNum", "_step3", "shouldHighlightLine", "RE", "parsedMeta", "map", "str", "trim", "join", "strlineNumbers", "exec", "lineNumbers", "<PERSON><PERSON><PERSON><PERSON>", "calculateLinesToHighlight", "startingLineNumber", "_wrapRegExp", "_RE$exec", "Number", "groups", "calculateStartingLine", "codeLineArray", "number", "a", "createLineNodes", "falseShowLineNumbersStr", "_loop", "_lang2", "_lang3", "_step3$value", "treeExtract", "filter", "showLineNumbers", "some", "substring", "_iterator3", "pop"], "mappings": "41EAmBA,IAyJMA,EAAuB,SAACC,GAC5B,gBAAQC,GAEN,YAFa,IAAPA,IAAAA,EAAU,CAAE,GA1Ic,SAACD,EAAWE,GAC9C,GAAIA,IAAoBF,EAAUG,WAAWD,GAC3C,UAAUE,+BAA+BF,EAAe,sCAE5D,CAuIIG,CAA4BL,EAAWC,EAAQC,0BACvCI,GACNC,EAAMD,EAAM,UAAWE,EACzB,EAOA,SAASA,EAAQC,EAAMC,EAAOC,OAAQC,EAAAC,EACpC,GAAKF,GAA6B,QAAnBA,EAAOG,SAAsC,SAAjBL,EAAKK,QAAhD,CAKA,IAAIC,GAAkCH,MAAJH,GAAAG,OAAIA,EAAJH,EAAMO,WAANJ,EAAAA,EAAYG,QAAYF,MAAJJ,GAAAI,OAAIA,EAAJJ,EAAMQ,iBAANJ,EAAAA,EAAkBK,aAAc,GAElFT,EAAKQ,WAAWE,UACuB,kBAA9BV,EAAKQ,WAAWE,UACzBV,EAAKQ,WAAWE,UAAY,GAClBC,MAAMC,QAAQZ,EAAKQ,WAAWE,aACxCV,EAAKQ,WAAWE,UAAY,CAACV,EAAKQ,WAAWE,YAG/CV,EAAKQ,WAAWE,UAAY,GAG9B,IASIG,EAvGJC,EA8FIC,EAvLU,SAACf,GAGnB,IAFA,IAEqCgB,EAArCC,EAAAC,EAFkBlB,EAAKQ,WAAWE,aAEGM,EAAAC,KAAAE,MAAE,KAA5BC,EAAaJ,EAAAK,MACtB,GAAkC,cAA9BD,EAAcE,MAAM,EAAG,GACzB,OAAOF,EAAcE,MAAM,GAAGC,aAElC,CACA,OACF,IAAA,CA8KiBC,CAAYxB,GAYvB,IAVKe,GAAQvB,EAAQC,iBAEnBO,EAAKQ,WAAWE,UAAUe,KAAiBV,aAD3CA,EAAOvB,EAAQC,kBAGjBO,EAAKQ,WAAWE,UAAUe,KAAK,kBAM3BV,EACF,IAAIW,IAAAA,EACEC,EAEFA,SADFD,EAAIX,IAAAW,EAAME,SAAS,SACNb,EAAKc,MAAM,KAAK,GAEhBd,EAGbF,EAAgBtB,EAAUuC,UAAUC,EAAS/B,GAAO2B,GAEpDzB,EAAOM,WAAWE,WAAaR,EAAOM,WAAWE,WAAa,IAAIsB,OAChE,YAAcL,EAElB,CAAE,MAAOM,GACP,IAAIzC,EAAQ0C,gBAAiB,mBAAmBC,KAAKF,EAAIG,SAGvD,MAAMH,EAFNpB,EAAgBb,CAIpB,MAEAa,EAAgBb,EAGlBa,EAAcwB,UAnIdvB,EAAe,EAKK,SAAlBwB,EAAmBC,GACvB,OAAOA,EAAIC,OAAO,SAACC,EAAQzC,GACzB,GAAkB,SAAdA,EAAK0C,KAAiB,CACxB,IAAMrB,EAA+BrB,EAAKqB,MACpCsB,GAAYtB,EAAMuB,MAAM,QAAU,IAAIC,OAC5C,GAAiB,IAAbF,EACF3C,EAAK8C,SAAW,CAGdC,MAAO,CAAEC,KAAMlC,EAAcmC,OAAQ,GACrCC,IAAK,CAAEF,KAAMlC,EAAcmC,OAAQ,IAErCR,EAAOhB,KAAKzB,QAGZ,IADA,IACuCmD,EADjCC,EAAQ/B,EAAMQ,MAAM,MAC1BwB,EAAAnC,EAAwBkC,EAAME,aAASH,EAAAE,KAAAlC,MAAE,KAAAoC,EAAAJ,EAAA9B,MAA7BmC,EAACD,EAAEP,GAAAA,EAAIO,EAAA,GACjBd,EAAOhB,KAAK,CACViB,KAAM,OACNrB,MAAOmC,IAAMJ,EAAMP,OAAS,EAAIG,EAAOA,EAAO,KAC9CF,SAAU,CACRC,MAAO,CAAEC,KAAMlC,EAAe0C,EAAGP,OAAQ,GACzCC,IAAK,CAAEF,KAAMlC,EAAe0C,EAAGP,OAAQ,KAG7C,CAIF,OAFAnC,GAA8B6B,EAEvBF,CACT,CAEA,GAAIgB,OAAOC,UAAUC,eAAeC,KAAK5D,EAAM,YAAa,CAC1D,IAAM6D,EAAiB/C,EAQvB,OANAd,EAAKqC,SAAWC,EAAgBtC,EAAKqC,UACrCI,EAAOhB,KAAKzB,GACZA,EAAK8C,SAAW,CACdC,MAAO,CAAEC,KAAMa,EAAgBZ,OAAQ,GACvCC,IAAK,CAAEF,KAAMlC,EAAcmC,OAAQ,IAE9BR,CACT,CAGA,OADAA,EAAOhB,KAAKzB,GACLyC,CACT,EAAG,GACL,GAgFsD5B,EAAcwB,UAI9DxB,EAAciC,SADZjC,EAAcwB,SAASQ,OAAS,EACT,CACvBE,MAAO,CAAEC,KAAMnC,EAAcwB,SAAS,GAAGS,SAASC,MAAMC,KAAMC,OAAQ,GACtEC,IAAK,CACHF,KAAMnC,EAAcwB,SAASxB,EAAcwB,SAASQ,OAAS,GAAGC,SAASI,IAAIF,KAC7EC,OAAQ,IAIa,CACvBF,MAAO,CAAEC,KAAM,EAAGC,OAAQ,GAC1BC,IAAK,CAAEF,KAAM,EAAGC,OAAQ,IAa5B,IATA,IAS+Ca,EATzCC,EAlNsB,SAACzD,GACjC,IAAM0D,EAAK,cAELC,EAAa3D,EAChBuB,MAAM,KACNqC,IAAI,SAACC,UAAQA,EAAIC,MAAM,GACvBC,OACH,GAAIL,EAAG7B,KAAK8B,GAAa,CACvB,IAAMK,EAAiBN,EAAGO,KAAKN,GAAY,GACrCO,EAAcC,EAAYH,GAChC,OAAO,SAACrE,GAAU,OAAAuE,EAAY5C,SAAS3B,EAAQ,EAAE,CACnD,CACE,OAAa,WAAA,OAAA,CAAK,CAEtB,CAoMkCyE,CAA0BpE,GAChDqE,EA7LkB,SAACrE,GAC7B,IAAM0D,eAAEY,EAAG,yBAAgCxB,CAAAA,UAE3C,GAAIY,EAAG7B,KAAK7B,GAAO,CACjB,IAAAuE,EAEIb,EAAGO,KAAKjE,GACZ,OAAOwE,OAFUD,EAAfE,OAAU3B,MAGd,CACA,OACF,CAAA,CAmLiC4B,CAAsB1E,GAC3C2E,EA5KY,SAACC,GAEvB,IADA,IAAMC,EAAI,IAAIxE,MAAMuE,GACX1B,EAAI,EAAGA,EAAI0B,EAAQ1B,IAC1B2B,EAAE3B,GAAK,CACLd,KAAM,UACNrC,QAAS,OACTG,WAAY,CAAEE,UAAW,IACzB2B,SAAU,IAGd,OAAO8C,CACT,CAiK4BC,CAAgBvE,EAAciC,SAASI,IAAIF,MAE3DqC,EAA0B,CAC9B,wBACA,0BACA,2BACDC,EAAA,WACgD,IAAAC,EAAAC,EAAAC,EAAA3B,EAAAzC,MAArCmC,EAACiC,EAAEzC,GAAAA,EAAIyC,EAAA,GAEjBzC,EAAKxC,WAAWE,UAAY,CAAC,aAG7B,IAAMgF,EAAcC,EAClB9E,EACA,SAACb,UAASA,EAAK8C,SAASC,MAAMC,MAAQQ,EAAI,GAAKxD,EAAK8C,SAASI,IAAIF,MAAQQ,EAAI,CAAC,GAEhFR,EAAKX,SAAWqD,EAAYrD,UAIzB/B,EAAKiB,cAAcK,SAAS,kBAAkBL,iBAC7C/B,EAAQoG,iBACTP,EAAwBQ,KAAK,SAAC1B,GAAQ,OAAA7D,EAAKiB,cAAcK,SAASuC,EAAI,KAEvEnB,EAAKxC,WAAWwC,KAAO,EAAEQ,EAAImB,GAAoB5C,YACjDiB,EAAKxC,WAAWE,UAAUe,KAAK,gBAI7BsC,EAAoBP,IACtBR,EAAKxC,WAAWE,UAAUe,KAAK,mBAKrB,SAATV,UAAewE,EAAIxE,IAAAwE,EAAM3D,SAAS,WACA,MAAnCG,EAASiB,GAAM8C,UAAU,EAAG,GAE5B9C,EAAKxC,WAAWE,UAAUe,KAAK,YAErB,SAATV,GAAuB,OAARyE,EAAIzE,IAAAyE,EAAM5D,SAAS,WACA,MAAnCG,EAASiB,GAAM8C,UAAU,EAAG,IAE5B9C,EAAKxC,WAAWE,UAAUe,KAAK,WAEnC,EAtCAsE,EAAA7E,EAAwB+D,EAAc3B,aAASQ,EAAAiC,KAAA5E,MAAAmE,IA0C7CL,EAAcpC,OAAS,GACsC,KAA7Dd,EAASkD,EAAcA,EAAcpC,OAAS,IAAIuB,QAElDa,EAAce,MAGhBhG,EAAKqC,SAAW4C,CA/HhB,CAgIF,CACF,CACF"}