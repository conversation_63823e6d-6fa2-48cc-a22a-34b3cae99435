import{refractor as e}from"refractor/lib/common.js";import{visit as r}from"unist-util-visit";import{toString as t}from"hast-util-to-string";import{filter as n}from"unist-util-filter";import i from"parse-numeric-range";function o(){o=function(e,r){return new t(e,void 0,r)};var e=RegExp.prototype,r=new WeakMap;function t(e,n,i){var o=new RegExp(e,n);return r.set(o,i||r.get(e)),l(o,t.prototype)}function n(e,t){var n=r.get(t);return Object.keys(n).reduce(function(r,t){var i=n[t];if("number"==typeof i)r[t]=e[i];else{for(var o=0;void 0===e[i[o]]&&o+1<i.length;)o++;r[t]=e[i[o]]}return r},Object.create(null))}return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),r&&l(e,r)}(t,RegExp),t.prototype.exec=function(r){var t=e.exec.call(this,r);if(t){t.groups=n(t,this);var i=t.indices;i&&(i.groups=n(i,this))}return t},t.prototype[Symbol.replace]=function(t,i){if("string"==typeof i){var o=r.get(this);return e[Symbol.replace].call(this,t,i.replace(/\$<([^>]+)>/g,function(e,r){var t=o[r];return"$"+(Array.isArray(t)?t.join("$"):t)}))}if("function"==typeof i){var l=this;return e[Symbol.replace].call(this,t,function(){var e=arguments;return"object"!=typeof e[e.length-1]&&(e=[].slice.call(e)).push(n(e,l)),i.apply(this,e)})}return e[Symbol.replace].call(this,t,i)},o.apply(this,arguments)}function l(e,r){return l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,r){return e.__proto__=r,e},l(e,r)}function a(e,r){(null==r||r>e.length)&&(r=e.length);for(var t=0,n=new Array(r);t<r;t++)n[t]=e[t];return n}function s(e,r){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(t)return(t=t.call(e)).next.bind(t);if(Array.isArray(e)||(t=function(e,r){if(e){if("string"==typeof e)return a(e,r);var t=Object.prototype.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?a(e,r):void 0}}(e))||r&&e&&"number"==typeof e.length){t&&(e=t);var n=0;return function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u=function(e){return function(l){return void 0===l&&(l={}),function(e,r){if(r&&!e.registered(r))throw new Error('The default language "'+r+'" is not registered with refractor.')}(e,l.defaultLanguage),function(e){r(e,"element",a)};function a(r,a,u){var c,p;if(u&&"pre"===u.tagName&&"code"===r.tagName){var f=(null==r||null==(c=r.data)?void 0:c.meta)||(null==r||null==(p=r.properties)?void 0:p.metastring)||"";r.properties.className?"boolean"==typeof r.properties.className?r.properties.className=[]:Array.isArray(r.properties.className)||(r.properties.className=[r.properties.className]):r.properties.className=[];var m,h,d=function(e){for(var r,t=s(e.properties.className);!(r=t()).done;){var n=r.value;if("language-"===n.slice(0,9))return n.slice(9).toLowerCase()}return null}(r);if(!d&&l.defaultLanguage&&r.properties.className.push("language-"+(d=l.defaultLanguage)),r.properties.className.push("code-highlight"),d)try{var g,v;v=null!=(g=d)&&g.includes("diff-")?d.split("-")[1]:d,m=e.highlight(t(r),v),u.properties.className=(u.properties.className||[]).concat("language-"+v)}catch(e){if(!l.ignoreMissing||!/Unknown language/.test(e.message))throw e;m=r}else m=r;m.children=(h=1,function e(r){return r.reduce(function(r,t){if("text"===t.type){var n=t.value,i=(n.match(/\n/g)||"").length;if(0===i)t.position={start:{line:h,column:1},end:{line:h,column:1}},r.push(t);else for(var o,l=n.split("\n"),a=s(l.entries());!(o=a()).done;){var u=o.value,c=u[0],p=u[1];r.push({type:"text",value:c===l.length-1?p:p+"\n",position:{start:{line:h+c,column:1},end:{line:h+c,column:1}}})}return h+=i,r}if(Object.prototype.hasOwnProperty.call(t,"children")){var f=h;return t.children=e(t.children),r.push(t),t.position={start:{line:f,column:1},end:{line:h,column:1}},r}return r.push(t),r},[])})(m.children),m.position=m.children.length>0?{start:{line:m.children[0].position.start.line,column:0},end:{line:m.children[m.children.length-1].position.end.line,column:0}}:{start:{line:0,column:0},end:{line:0,column:0}};for(var y,b=function(e){var r=/{([\d,-]+)}/,t=e.split(",").map(function(e){return e.trim()}).join();if(r.test(t)){var n=r.exec(t)[1],o=i(n);return function(e){return o.includes(e+1)}}return function(){return!1}}(f),w=function(e){var r=/*#__PURE__*/o(/showLineNumbers=(\d+)/i,{lines:1});if(r.test(e)){var t=r.exec(e);return Number(t.groups.lines)}return 1}(f),N=function(e){for(var r=new Array(e),t=0;t<e;t++)r[t]={type:"element",tagName:"span",properties:{className:[]},children:[]};return r}(m.position.end.line),j=["showlinenumbers=false",'showlinenumbers="false"',"showlinenumbers={false}"],x=function(){var e,r,i=y.value,o=i[0],a=i[1];a.properties.className=["code-line"];var s=n(m,function(e){return e.position.start.line<=o+1&&e.position.end.line>=o+1});a.children=s.children,!f.toLowerCase().includes("showLineNumbers".toLowerCase())&&!l.showLineNumbers||j.some(function(e){return f.toLowerCase().includes(e)})||(a.properties.line=[(o+w).toString()],a.properties.className.push("line-number")),b(o)&&a.properties.className.push("highlight-line"),("diff"===d||null!=(e=d)&&e.includes("diff-"))&&"-"===t(a).substring(0,1)?a.properties.className.push("deleted"):("diff"===d||null!=(r=d)&&r.includes("diff-"))&&"+"===t(a).substring(0,1)&&a.properties.className.push("inserted")},O=s(N.entries());!(y=O()).done;)x();N.length>0&&""===t(N[N.length-1]).trim()&&N.pop(),r.children=N}}}}(e);export{u as default};
//# sourceMappingURL=common.es.js.map
