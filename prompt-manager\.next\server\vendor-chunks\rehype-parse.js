"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rehype-parse";
exports.ids = ["vendor-chunks/rehype-parse"];
exports.modules = {

/***/ "(ssr)/./node_modules/rehype-parse/lib/index.js":
/*!************************************************!*\
  !*** ./node_modules/rehype-parse/lib/index.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rehypeParse)\n/* harmony export */ });\n/* harmony import */ var hast_util_from_html__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-from-html */ \"(ssr)/./node_modules/hast-util-from-html/lib/index.js\");\n/**\n * @import {Root} from 'hast'\n * @import {Options as FromHtmlOptions} from 'hast-util-from-html'\n * @import {Parser, Processor} from 'unified'\n */\n\n/**\n * @typedef {Omit<FromHtmlOptions, 'onerror'> & RehypeParseFields} Options\n *   Configuration.\n *\n * @typedef RehypeParseFields\n *   Extra fields.\n * @property {boolean | null | undefined} [emitParseErrors=false]\n *   Whether to emit parse errors while parsing (default: `false`).\n *\n *   > 👉 **Note**: parse errors are currently being added to HTML.\n *   > Not all errors emitted by parse5 (or us) are specced yet.\n *   > Some documentation may still be missing.\n */\n\n\n\n/**\n * Plugin to add support for parsing from HTML.\n *\n * > 👉 **Note**: this is not an XML parser.\n * > It supports SVG as embedded in HTML.\n * > It does not support the features available in XML.\n * > Passing SVG files might break but fragments of modern SVG should be fine.\n * > Use [`xast-util-from-xml`][xast-util-from-xml] to parse XML.\n *\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {undefined}\n *   Nothing.\n */\nfunction rehypeParse(options) {\n  /** @type {Processor<Root>} */\n  // @ts-expect-error: TS in JSDoc generates wrong types if `this` is typed regularly.\n  const self = this\n  const {emitParseErrors, ...settings} = {...self.data('settings'), ...options}\n\n  self.parser = parser\n\n  /**\n   * @type {Parser<Root>}\n   */\n  function parser(document, file) {\n    return (0,hast_util_from_html__WEBPACK_IMPORTED_MODULE_0__.fromHtml)(document, {\n      ...settings,\n      onerror: emitParseErrors\n        ? function (message) {\n            if (file.path) {\n              message.name = file.path + ':' + message.name\n              message.file = file.path\n            }\n\n            file.messages.push(message)\n          }\n        : undefined\n    })\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-parse/lib/index.js\n");

/***/ })

};
;