'use client'

import { useState, useEffect } from 'react'
import { toast } from 'react-hot-toast'
import CodeEditor from '@uiw/react-textarea-code-editor'
import { Button } from '~/components/ui/Button'
import { Input } from '~/components/ui/Input'
import { Textarea } from '~/components/ui/Textarea'
import { Badge } from '~/components/ui/Badge'
import { generateRandomColor } from '~/lib/utils'
import { api } from '~/trpc/react'
import type { Prompt, Category } from '~/store'

interface PromptFormProps {
  prompt?: Prompt
  onSubmit?: (data: PromptFormData) => void
  onCancel?: () => void
  isLoading?: boolean
}

export interface PromptFormData {
  title: string
  content: string
  description?: string
  tags: string[]
  categoryId?: string
  isPublic: boolean
}

export function PromptForm({ prompt, onSubmit, onCancel, isLoading = false }: PromptFormProps) {
  const [formData, setFormData] = useState<PromptFormData>({
    title: prompt?.title || '',
    content: prompt?.content || '',
    description: prompt?.description || '',
    tags: prompt?.tags || [],
    categoryId: prompt?.categoryId || '',
    isPublic: prompt?.isPublic ?? true,
  })
  
  const [tagInput, setTagInput] = useState('')
  const [errors, setErrors] = useState<Record<string, string>>({})
  
  // 获取分类数据
  const { data: categories } = api.category.getAll.useQuery()
  
  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}
    
    if (!formData.title.trim()) {
      newErrors.title = '标题不能为空'
    } else if (formData.title.length > 100) {
      newErrors.title = '标题不能超过100个字符'
    }
    
    if (!formData.content.trim()) {
      newErrors.content = '内容不能为空'
    }
    
    if (formData.description && formData.description.length > 200) {
      newErrors.description = '描述不能超过200个字符'
    }
    
    if (formData.tags.length > 10) {
      newErrors.tags = '标签不能超过10个'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }
  
  // 处理表单提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      toast.error('请检查表单输入')
      return
    }
    
    onSubmit?.(formData)
  }
  
  // 处理标签输入
  const handleTagInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault()
      addTag()
    }
  }
  
  const addTag = () => {
    const tag = tagInput.trim()
    if (tag && !formData.tags.includes(tag) && formData.tags.length < 10) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag]
      }))
      setTagInput('')
    }
  }
  
  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
  }
  
  // 处理输入变化
  const handleInputChange = (field: keyof PromptFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* 标题 */}
      <div>
        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
          标题 <span className="text-red-500">*</span>
        </label>
        <Input
          value={formData.title}
          onChange={(e) => handleInputChange('title', e.target.value)}
          placeholder="输入提示词标题..."
          error={errors.title}
          maxLength={100}
        />
        <div className="text-xs text-slate-500 mt-1">
          {formData.title.length}/100
        </div>
      </div>
      
      {/* 描述 */}
      <div>
        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
          描述
        </label>
        <Textarea
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          placeholder="简短描述这个提示词的用途..."
          rows={3}
          error={errors.description}
          maxLength={200}
        />
        <div className="text-xs text-slate-500 mt-1">
          {(formData.description || '').length}/200
        </div>
      </div>
      
      {/* 分类 */}
      <div>
        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
          分类
        </label>
        <select
          value={formData.categoryId}
          onChange={(e) => handleInputChange('categoryId', e.target.value)}
          className="w-full h-9 px-3 py-1 text-sm border border-input bg-transparent rounded-md shadow-sm focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
        >
          <option value="">选择分类</option>
          {categories?.map((category) => (
            <option key={category.id} value={category.id}>
              {category.name}
            </option>
          ))}
        </select>
      </div>
      
      {/* 标签 */}
      <div>
        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
          标签
        </label>
        <div className="space-y-2">
          <div className="flex gap-2">
            <Input
              value={tagInput}
              onChange={(e) => setTagInput(e.target.value)}
              onKeyDown={handleTagInputKeyDown}
              placeholder="输入标签后按回车添加..."
              className="flex-1"
            />
            <Button
              type="button"
              variant="outline"
              onClick={addTag}
              disabled={!tagInput.trim() || formData.tags.length >= 10}
            >
              添加
            </Button>
          </div>
          
          {formData.tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {formData.tags.map((tag, index) => (
                <Badge
                  key={index}
                  variant="secondary"
                  className="cursor-pointer hover:bg-destructive hover:text-destructive-foreground"
                  onClick={() => removeTag(tag)}
                >
                  {tag}
                  <svg className="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </Badge>
              ))}
            </div>
          )}
          
          <div className="text-xs text-slate-500">
            {formData.tags.length}/10 个标签
          </div>
          {errors.tags && (
            <p className="text-sm text-destructive">{errors.tags}</p>
          )}
        </div>
      </div>
      
      {/* 内容编辑器 */}
      <div>
        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
          提示词内容 <span className="text-red-500">*</span>
        </label>
        <div className="border border-input rounded-md overflow-hidden">
          <CodeEditor
            value={formData.content}
            language="text"
            placeholder="输入您的提示词内容..."
            onChange={(evn) => handleInputChange('content', evn.target.value)}
            padding={15}
            style={{
              fontSize: 14,
              backgroundColor: 'transparent',
              fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',
              minHeight: '200px',
            }}
          />
        </div>
        {errors.content && (
          <p className="text-sm text-destructive mt-1">{errors.content}</p>
        )}
      </div>
      
      {/* 可见性设置 */}
      <div>
        <label className="flex items-center gap-2">
          <input
            type="checkbox"
            checked={formData.isPublic}
            onChange={(e) => handleInputChange('isPublic', e.target.checked)}
            className="rounded border-input"
          />
          <span className="text-sm text-slate-700 dark:text-slate-300">
            公开此提示词（其他用户可以查看和使用）
          </span>
        </label>
      </div>
      
      {/* 操作按钮 */}
      <div className="flex items-center gap-3 pt-4">
        <Button
          type="submit"
          loading={isLoading}
          className="flex-1 sm:flex-none"
        >
          {prompt ? '更新提示词' : '创建提示词'}
        </Button>
        
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
          className="flex-1 sm:flex-none"
        >
          取消
        </Button>
      </div>
    </form>
  )
}
