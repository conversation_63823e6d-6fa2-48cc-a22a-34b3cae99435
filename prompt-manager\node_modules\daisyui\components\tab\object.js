export default {".tabs":{"display":"flex","flex-wrap":"wrap","--tabs-height":"auto","--tabs-direction":"row","--tab-height":"calc(var(--size-field, 0.25rem) * 10)","height":"var(--tabs-height)","flex-direction":"var(--tabs-direction)"},".tab":{"position":"relative","display":"inline-flex","cursor":"pointer","appearance":"none","flex-wrap":"wrap","align-items":"center","justify-content":"center","text-align":"center","webkit-user-select":"none","user-select":"none","&:hover":{"@media (hover: hover)":{"color":"var(--color-base-content)"}},"--tab-p":"1rem","--tab-bg":"var(--color-base-100)","--tab-border-color":"var(--color-base-300)","--tab-radius-ss":"0","--tab-radius-se":"0","--tab-radius-es":"0","--tab-radius-ee":"0","--tab-order":"0","--tab-radius-min":"calc(0.75rem - var(--border))","border-color":"#0000","order":"var(--tab-order)","height":"var(--tab-height)","font-size":"0.875rem","padding-inline-start":"var(--tab-p)","padding-inline-end":"var(--tab-p)","&:is(input[type=\"radio\"])":{"min-width":"fit-content","&:after":{"content":"attr(aria-label)"}},"&:is(label)":{"position":"relative","input":{"position":"absolute","inset":"calc(0.25rem * 0)","cursor":"pointer","appearance":"none","opacity":"0%"}},"&:checked, &:is(label:has(:checked)), &:is(.tab-active, [aria-selected=\"true\"])":{"& + .tab-content":{"display":"block","height":"calc(100% - var(--tab-height) + var(--border))"}},"&:not(:checked, label:has(:checked), :hover, .tab-active, [aria-selected=\"true\"])":{"color":"color-mix(in oklab, var(--color-base-content) 50%, transparent)"},"&:not(input):empty":{"flex-grow":1,"cursor":"default"},"&:focus":{"--tw-outline-style":"none","outline-style":"none","@media (forced-colors: active)":{"outline":"2px solid transparent","outline-offset":"2px"}},"&:focus-visible, &:is(label:has(:checked:focus-visible))":{"outline":"2px solid currentColor","outline-offset":"-5px"},"&[disabled]":{"pointer-events":"none","opacity":"40%"}},".tab-disabled":{"pointer-events":"none","opacity":"40%"},".tabs-border":{".tab":{"--tab-border-color":"#0000 #0000 var(--tab-border-color) #0000","position":"relative","border-radius":"var(--radius-field)","&:before":{"--tw-content":"\"\"","content":"var(--tw-content)","background-color":"var(--tab-border-color)","transition":"background-color 0.2s ease","width":"80%","height":"3px","border-radius":"var(--radius-field)","bottom":"0","left":"10%","position":"absolute"},"&:is(.tab-active, [aria-selected=\"true\"]):not(.tab-disabled, [disabled]), &:is(input:checked), &:is(label:has(:checked))":{"&:before":{"--tab-border-color":"currentColor","border-top":"3px solid"}}}},".tabs-lift":{"--tabs-height":"auto","--tabs-direction":"row","> .tab":{"--tab-border":"0 0 var(--border) 0","--tab-radius-ss":"min(var(--radius-field), var(--tab-radius-min))","--tab-radius-se":"min(var(--radius-field), var(--tab-radius-min))","--tab-radius-es":"0","--tab-radius-ee":"0","--tab-paddings":"var(--border) var(--tab-p) 0 var(--tab-p)","--tab-border-colors":"#0000 #0000 var(--tab-border-color) #0000","--tab-corner-width":"calc(100% + min(var(--radius-field), var(--tab-radius-min)) * 2)","--tab-corner-height":"min(var(--radius-field), var(--tab-radius-min))","--tab-corner-position":"top left, top right","border-width":"var(--tab-border)","border-start-start-radius":"var(--tab-radius-ss)","border-start-end-radius":"var(--tab-radius-se)","border-end-start-radius":"var(--tab-radius-es)","border-end-end-radius":"var(--tab-radius-ee)","padding":"var(--tab-paddings)","border-color":"var(--tab-border-colors)","&:is(.tab-active, [aria-selected=\"true\"]):not(.tab-disabled, [disabled]), &:is(input:checked, label:has(:checked))":{"--tab-border":"var(--border) var(--border) 0 var(--border)","--tab-border-colors":"var(--tab-border-color) var(--tab-border-color) #0000\n        var(--tab-border-color)","--tab-paddings":"0 calc(var(--tab-p) - var(--border)) var(--border)\n        calc(var(--tab-p) - var(--border))","--tab-inset":"auto auto 0 auto","--tab-grad":"calc(69% - var(--border))","--radius-start":"radial-gradient(\n        circle at top left,\n        #0000 var(--tab-grad),\n        var(--tab-border-color) calc(var(--tab-grad) + 0.25px),\n        var(--tab-border-color) calc(var(--tab-grad) + var(--border)),\n        var(--tab-bg) calc(var(--tab-grad) + var(--border) + 0.25px)\n      )","--radius-end":"radial-gradient(\n        circle at top right,\n        #0000 var(--tab-grad),\n        var(--tab-border-color) calc(var(--tab-grad) + 0.25px),\n        var(--tab-border-color) calc(var(--tab-grad) + var(--border)),\n        var(--tab-bg) calc(var(--tab-grad) + var(--border) + 0.25px)\n      )","background-color":"var(--tab-bg)","&:before":{"z-index":1,"content":"\"\"","display":"block","position":"absolute","width":"var(--tab-corner-width)","height":"var(--tab-corner-height)","background-position":"var(--tab-corner-position)","background-image":"var(--radius-start), var(--radius-end)","background-size":"min(var(--radius-field), var(--tab-radius-min)) min(var(--radius-field), var(--tab-radius-min))","background-repeat":"no-repeat","inset":"var(--tab-inset)"},"&:first-child:before":{"--radius-start":"none"},"[dir=\"rtl\"] &:first-child:before":{"transform":"rotateY(180deg)"},"&:last-child:before":{"--radius-end":"none"},"[dir=\"rtl\"] &:last-child:before":{"transform":"rotateY(180deg)"}}},"&:has(.tab-content)":{"> .tab:first-child":{"&:not(.tab-active, [aria-selected=\"true\"])":{"--tab-border-colors":"var(--tab-border-color) var(--tab-border-color) #0000\n          var(--tab-border-color)"}}},".tab-content":{"--tabcontent-margin":"calc(-1 * var(--border)) 0 0 0","--tabcontent-radius-ss":"0","--tabcontent-radius-se":"var(--radius-box)","--tabcontent-radius-es":"var(--radius-box)","--tabcontent-radius-ee":"var(--radius-box)"},":checked, label:has(:checked), :is(.tab-active, [aria-selected=\"true\"])":{"& + .tab-content":{"&:nth-child(1), &:nth-child(n + 3)":{"--tabcontent-radius-ss":"var(--radius-box)"}}}},".tabs-top":{"--tabs-height":"auto","--tabs-direction":"row",".tab":{"--tab-order":"0","--tab-border":"0 0 var(--border) 0","--tab-radius-ss":"min(var(--radius-field), var(--tab-radius-min))","--tab-radius-se":"min(var(--radius-field), var(--tab-radius-min))","--tab-radius-es":"0","--tab-radius-ee":"0","--tab-paddings":"var(--border) var(--tab-p) 0 var(--tab-p)","--tab-border-colors":"#0000 #0000 var(--tab-border-color) #0000","--tab-corner-width":"calc(100% + min(var(--radius-field), var(--tab-radius-min)) * 2)","--tab-corner-height":"min(var(--radius-field), var(--tab-radius-min))","--tab-corner-position":"top left, top right","&:is(.tab-active, [aria-selected=\"true\"]):not(.tab-disabled, [disabled]), &:is(input:checked), &:is(label:has(:checked))":{"--tab-border":"var(--border) var(--border) 0 var(--border)","--tab-border-colors":"var(--tab-border-color) var(--tab-border-color) #0000\n        var(--tab-border-color)","--tab-paddings":"0 calc(var(--tab-p) - var(--border)) var(--border)\n        calc(var(--tab-p) - var(--border))","--tab-inset":"auto auto 0 auto","--radius-start":"radial-gradient(\n        circle at top left,\n        #0000 var(--tab-grad),\n        var(--tab-border-color) calc(var(--tab-grad) + 0.25px),\n        var(--tab-border-color) calc(var(--tab-grad) + var(--border)),\n        var(--tab-bg) calc(var(--tab-grad) + var(--border) + 0.25px)\n      )","--radius-end":"radial-gradient(\n        circle at top right,\n        #0000 var(--tab-grad),\n        var(--tab-border-color) calc(var(--tab-grad) + 0.25px),\n        var(--tab-border-color) calc(var(--tab-grad) + var(--border)),\n        var(--tab-bg) calc(var(--tab-grad) + var(--border) + 0.25px)\n      )"}},"&:has(.tab-content)":{"> .tab:first-child":{"&:not(.tab-active, [aria-selected=\"true\"])":{"--tab-border-colors":"var(--tab-border-color) var(--tab-border-color) #0000\n          var(--tab-border-color)"}}},".tab-content":{"--tabcontent-order":"1","--tabcontent-margin":"calc(-1 * var(--border)) 0 0 0","--tabcontent-radius-ss":"0","--tabcontent-radius-se":"var(--radius-box)","--tabcontent-radius-es":"var(--radius-box)","--tabcontent-radius-ee":"var(--radius-box)"},":checked, label:has(:checked), :is(.tab-active, [aria-selected=\"true\"])":{"& + .tab-content":{"&:nth-child(1), &:nth-child(n + 3)":{"--tabcontent-radius-ss":"var(--radius-box)"}}}},".tabs-bottom":{"--tabs-height":"auto","--tabs-direction":"row",".tab":{"--tab-order":"1","--tab-border":"var(--border) 0 0 0","--tab-radius-ss":"0","--tab-radius-se":"0","--tab-radius-es":"min(var(--radius-field), var(--tab-radius-min))","--tab-radius-ee":"min(var(--radius-field), var(--tab-radius-min))","--tab-border-colors":"var(--tab-border-color) #0000 #0000 #0000","--tab-paddings":"0 var(--tab-p) var(--border) var(--tab-p)","--tab-corner-width":"calc(100% + min(var(--radius-field), var(--tab-radius-min)) * 2)","--tab-corner-height":"min(var(--radius-field), var(--tab-radius-min))","--tab-corner-position":"top left, top right","&:is(.tab-active, [aria-selected=\"true\"]):not(.tab-disabled, [disabled]), &:is(input:checked), &:is(label:has(:checked))":{"--tab-border":"0 var(--border) var(--border) var(--border)","--tab-border-colors":"#0000 var(--tab-border-color) var(--tab-border-color)\n        var(--tab-border-color)","--tab-paddings":"var(--border) calc(var(--tab-p) - var(--border)) 0\n        calc(var(--tab-p) - var(--border))","--tab-inset":"0 auto auto auto","--radius-start":"radial-gradient(\n        circle at bottom left,\n        #0000 var(--tab-grad),\n        var(--tab-border-color) calc(var(--tab-grad) + 0.25px),\n        var(--tab-border-color) calc(var(--tab-grad) + var(--border)),\n        var(--tab-bg) calc(var(--tab-grad) + var(--border) + 0.25px)\n      )","--radius-end":"radial-gradient(\n        circle at bottom right,\n        #0000 var(--tab-grad),\n        var(--tab-border-color) calc(var(--tab-grad) + 0.25px),\n        var(--tab-border-color) calc(var(--tab-grad) + var(--border)),\n        var(--tab-bg) calc(var(--tab-grad) + var(--border) + 0.25px)\n      )"}},"&:has(.tab-content)":{"> .tab:first-child":{"&:not(.tab-active, [aria-selected=\"true\"])":{"--tab-border-colors":"#0000 var(--tab-border-color) var(--tab-border-color)\n          var(--tab-border-color)"}}},".tab-content":{"--tabcontent-order":"0","--tabcontent-margin":"0 0 calc(-1 * var(--border)) 0","--tabcontent-radius-ss":"var(--radius-box)","--tabcontent-radius-se":"var(--radius-box)","--tabcontent-radius-es":"0","--tabcontent-radius-ee":"var(--radius-box)"},"> :checked, > :is(label:has(:checked)), > :is(.tab-active, [aria-selected=\"true\"])":{"& + .tab-content:not(:nth-child(2))":{"--tabcontent-radius-es":"var(--radius-box)"}}},".tabs-box":{"background-color":"var(--color-base-200)","padding":"calc(0.25rem * 1)","--tabs-box-radius":"calc(var(--radius-field) + var(--radius-field) + var(--radius-field))","border-radius":"calc(var(--radius-field) + min(0.25rem, var(--tabs-box-radius)))","box-shadow":"0 -0.5px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 0.5px oklch(0% 0 0 / calc(var(--depth) * 0.05)) inset",".tab":{"border-radius":"var(--radius-field)","border-style":"none","&:focus-visible, &:is(label:has(:checked:focus-visible))":{"outline-offset":"2px"}},"> :is(.tab-active, [aria-selected=\"true\"]):not(.tab-disabled, [disabled]), > :is(input:checked), > :is(label:has(:checked))":{"background-color":"var(--tab-bg, var(--color-base-100))","box-shadow":"0 1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px 1px -1px color-mix(in oklab, var(--color-neutral) calc(var(--depth) * 50%), #0000), 0 1px 6px -4px color-mix(in oklab, var(--color-neutral) calc(var(--depth) * 100%), #0000)","@media (forced-colors: active)":{"border":"1px solid"}}},".tab-content":{"order":[1,"var(--tabcontent-order)"],"display":"none","border-color":"transparent","--tabcontent-radius-ss":"0","--tabcontent-radius-se":"0","--tabcontent-radius-es":"0","--tabcontent-radius-ee":"0","--tabcontent-order":"1","width":"100%","margin":"var(--tabcontent-margin)","border-width":"var(--border)","border-start-start-radius":"var(--tabcontent-radius-ss)","border-start-end-radius":"var(--tabcontent-radius-se)","border-end-start-radius":"var(--tabcontent-radius-es)","border-end-end-radius":"var(--tabcontent-radius-ee)"},".tabs-xs":{"--tab-height":"calc(var(--size-field, 0.25rem) * 6)",":where(.tab)":{"font-size":"0.75rem","--tab-p":"0.375rem","--tab-radius-min":"calc(0.5rem - var(--border))"}},".tabs-sm":{"--tab-height":"calc(var(--size-field, 0.25rem) * 8)",":where(.tab)":{"font-size":"0.875rem","--tab-p":"0.5rem","--tab-radius-min":"calc(0.5rem - var(--border))"}},".tabs-md":{"--tab-height":"calc(var(--size-field, 0.25rem) * 10)",":where(.tab)":{"font-size":"0.875rem","--tab-p":"0.75rem","--tab-radius-min":"calc(0.75rem - var(--border))"}},".tabs-lg":{"--tab-height":"calc(var(--size-field, 0.25rem) * 12)",":where(.tab)":{"font-size":"1.125rem","--tab-p":"1rem","--tab-radius-min":"calc(1.5rem - var(--border))"}},".tabs-xl":{"--tab-height":"calc(var(--size-field, 0.25rem) * 14)",":where(.tab)":{"font-size":"1.125rem","--tab-p":"1.25rem","--tab-radius-min":"calc(2rem - var(--border))"}}};