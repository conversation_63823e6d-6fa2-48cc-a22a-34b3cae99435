'use client'

import { toast } from 'react-hot-toast'
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>er, 
  ModalTitle, 
  ModalContent,
  ModalCloseButton 
} from '~/components/ui/Modal'
import { CategoryForm, type CategoryFormData } from '~/components/CategoryForm'
import { useModals } from '~/hooks/useStore'
import { api } from '~/trpc/react'

export function CreateCategoryModal() {
  const { modals, closeCreateCategory } = useModals()
  
  // 创建分类的mutation
  const createCategoryMutation = api.category.create.useMutation({
    onSuccess: () => {
      toast.success('分类创建成功！')
      closeCreateCategory()
      // 刷新分类列表
      void utils.category.getAll.invalidate()
      void utils.stats.getCategoryStats.invalidate()
    },
    onError: (error) => {
      toast.error(error.message || '创建失败，请重试')
    },
  })
  
  // 获取utils用于刷新数据
  const utils = api.useUtils()
  
  const handleSubmit = async (data: CategoryFormData) => {
    try {
      await createCategoryMutation.mutateAsync({
        name: data.name,
        description: data.description || undefined,
        color: data.color,
        icon: data.icon || undefined,
      })
    } catch (error) {
      // 错误已在onError中处理
    }
  }

  return (
    <Modal
      open={modals.createCategory}
      onClose={closeCreateCategory}
      size="lg"
    >
      <ModalHeader>
        <ModalTitle>创建新分类</ModalTitle>
        <ModalCloseButton onClose={closeCreateCategory} />
      </ModalHeader>

      <ModalContent>
        <CategoryForm
          onSubmit={handleSubmit}
          onCancel={closeCreateCategory}
          isLoading={createCategoryMutation.isPending}
        />
      </ModalContent>
    </Modal>
  )
}
