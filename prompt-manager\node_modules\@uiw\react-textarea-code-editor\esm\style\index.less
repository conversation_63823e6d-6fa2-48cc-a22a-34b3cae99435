@w-textarea:~ "w-tc-editor";

@media (prefers-color-scheme: dark) {
  .@{w-textarea} {
    --color-fg-default: #c9d1d9;
    --color-canvas-subtle: #161b22;
    --color-prettylights-syntax-comment: #8b949e;
    --color-prettylights-syntax-entity-tag: #7ee787;
    --color-prettylights-syntax-entity: #d2a8ff;
    --color-prettylights-syntax-sublimelinter-gutter-mark: #484f58;
    --color-prettylights-syntax-constant: #79c0ff;
    --color-prettylights-syntax-string: #a5d6ff;
    --color-prettylights-syntax-keyword: #ff7b72;
    --color-prettylights-syntax-markup-bold: #c9d1d9;
  }
}

@media (prefers-color-scheme: light) {
  .@{w-textarea} {
    --color-fg-default: #24292f;
    --color-canvas-subtle: #f6f8fa;
    --color-prettylights-syntax-comment: #6e7781;
    --color-prettylights-syntax-entity-tag: #116329;
    --color-prettylights-syntax-entity: #8250df;
    --color-prettylights-syntax-sublimelinter-gutter-mark: #8c959f;
    --color-prettylights-syntax-constant: #0550ae;
    --color-prettylights-syntax-string: #0a3069;
    --color-prettylights-syntax-keyword: #cf222e;
    --color-prettylights-syntax-markup-bold: #24292f;
  }
}

.@{w-textarea}[data-color-mode*='dark'],
[data-color-mode*='dark'] .@{w-textarea},
[data-color-mode*='dark'] .@{w-textarea}-var,
body[data-color-mode*='dark'] {
  --color-fg-default: #c9d1d9;
  --color-canvas-subtle: #161b22;
  --color-prettylights-syntax-comment: #8b949e;
  --color-prettylights-syntax-entity-tag: #7ee787;
  --color-prettylights-syntax-entity: #d2a8ff;
  --color-prettylights-syntax-sublimelinter-gutter-mark: #484f58;
  --color-prettylights-syntax-constant: #79c0ff;
  --color-prettylights-syntax-string: #a5d6ff;
  --color-prettylights-syntax-keyword: #ff7b72;
  --color-prettylights-syntax-markup-bold: #c9d1d9;
}

.@{w-textarea}[data-color-mode*='light'],
[data-color-mode*='light'] .@{w-textarea},
[data-color-mode*='light'] .@{w-textarea}-var,
body[data-color-mode*='light'] {
  --color-fg-default: #24292f;
  --color-canvas-subtle: #f6f8fa;
  --color-prettylights-syntax-comment: #6e7781;
  --color-prettylights-syntax-entity-tag: #116329;
  --color-prettylights-syntax-entity: #8250df;
  --color-prettylights-syntax-sublimelinter-gutter-mark: #8c959f;
  --color-prettylights-syntax-constant: #0550ae;
  --color-prettylights-syntax-string: #0a3069;
  --color-prettylights-syntax-keyword: #cf222e;
  --color-prettylights-syntax-markup-bold: #24292f;
}

.@{w-textarea} {
  font-family: inherit;
  font-size: 12px;
  color: var(--color-fg-default);
  background-color: var(--color-canvas-subtle);
  color: var(--color-fg-default);
  &-text,
  &-preview {
    min-height: 16px;
  }
  &-preview {
    pre {
      margin: 0;
      padding: 0;
      white-space: inherit;
      font-family: inherit;
      font-size: inherit;
      code {
        font-family: inherit;
      }
    }
  }

  code[class*='language-'],
  pre[class*='language-'] {
    .token.cdata,
    .token.comment,
    .token.doctype,
    .token.prolog {
      color: var(--color-prettylights-syntax-comment);
    }
    .token.punctuation {
      color: var(--color-prettylights-syntax-sublimelinter-gutter-mark);
    }
    .namespace {
      opacity: 0.7;
    }

    .token.boolean,
    .token.constant,
    .token.deleted,
    .token.number,
    .token.symbol {
      color: var(--color-prettylights-syntax-entity-tag);
    }

    .token.builtin,
    .token.char,
    .token.inserted,
    .token.selector,
    .token.string {
      color: var(--color-prettylights-syntax-constant);
    }

    .style .token.string,
    .token.entity,
    .token.property,
    .token.operator,
    .token.url {
      color: var(--color-prettylights-syntax-constant);
    }

    .token.atrule,
    .token.property-access .token.method,
    .token.keyword {
      color: var(--color-prettylights-syntax-keyword);
    }

    .token.function {
      color: var(--color-prettylights-syntax-string);
    }

    .token.important,
    .token.regex,
    .token.variable {
      color: var(--color-prettylights-syntax-string-regexp);
    }

    .token.bold,
    .token.important {
      color: var(--color-prettylights-syntax-markup-bold);
    }
    .token.tag {
      color: var(--color-prettylights-syntax-entity-tag);
    }
    .token.attr-value,
    .token.attr-name {
      color: var(--color-prettylights-syntax-constant);
    }
    .token.selector .class,
    .token.class-name {
      color: var(--color-prettylights-syntax-entity);
    }
  }
}
