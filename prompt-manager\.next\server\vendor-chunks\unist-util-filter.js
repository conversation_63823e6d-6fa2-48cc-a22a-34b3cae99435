"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/unist-util-filter";
exports.ids = ["vendor-chunks/unist-util-filter"];
exports.modules = {

/***/ "(ssr)/./node_modules/unist-util-filter/lib/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/unist-util-filter/lib/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   filter: () => (/* binding */ filter)\n/* harmony export */ });\n/* harmony import */ var unist_util_is__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-is */ \"(ssr)/./node_modules/unist-util-is/lib/index.js\");\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n *\n * @typedef {Exclude<import('unist-util-is').Test, undefined> | undefined} Test\n *   Test from `unist-util-is`.\n *\n *   Note: we have remove and add `undefined`, because otherwise when generating\n *   automatic `.d.ts` files, TS tries to flatten paths from a local perspective,\n *   which doesn’t work when publishing on npm.\n */\n\n/**\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean | null | undefined} [cascade=true]\n *   Whether to drop parent nodes if they had children, but all their children\n *   were filtered out (default: `true`).\n */\n\n\n\nconst own = {}.hasOwnProperty\n\n/**\n * Create a new `tree` of copies of all nodes that pass `test`.\n *\n * The tree is walked in *preorder* (NLR), visiting the node itself, then its\n * head, etc.\n *\n * @template {Node} Tree\n * @template {Test} Check\n *\n * @overload\n * @param {Tree} tree\n * @param {Options | null | undefined} options\n * @param {Check} test\n * @returns {import('./complex-types.js').Matches<Tree, Check>}\n *\n * @overload\n * @param {Tree} tree\n * @param {Check} test\n * @returns {import('./complex-types.js').Matches<Tree, Check>}\n *\n * @overload\n * @param {Tree} tree\n * @param {null | undefined} [options]\n * @returns {Tree}\n *\n * @param {Node} tree\n *   Tree to filter.\n * @param {Options | Test} [options]\n *   Configuration (optional).\n * @param {Test} [test]\n *   `unist-util-is` compatible test.\n * @returns {Node | undefined}\n *   New filtered tree.\n *\n *   `undefined` is returned if `tree` itself didn’t pass the test, or is\n *   cascaded away.\n */\nfunction filter(tree, options, test) {\n  const is = (0,unist_util_is__WEBPACK_IMPORTED_MODULE_0__.convert)(test || options)\n  const cascadeRaw =\n    options && typeof options === 'object' && 'cascade' in options\n      ? /** @type {boolean | null | undefined} */ (options.cascade)\n      : undefined\n  const cascade =\n    cascadeRaw === undefined || cascadeRaw === null ? true : cascadeRaw\n\n  return preorder(tree)\n\n  /**\n   * @param {Node} node\n   *   Current node.\n   * @param {number | undefined} [index]\n   *   Index of `node` in `parent`.\n   * @param {Parent | undefined} [parentNode]\n   *   Parent node.\n   * @returns {Node | undefined}\n   *   Shallow copy of `node`.\n   */\n  function preorder(node, index, parentNode) {\n    /** @type {Array<Node>} */\n    const children = []\n\n    if (!is(node, index, parentNode)) return undefined\n\n    if (parent(node)) {\n      let childIndex = -1\n\n      while (++childIndex < node.children.length) {\n        const result = preorder(node.children[childIndex], childIndex, node)\n\n        if (result) {\n          children.push(result)\n        }\n      }\n\n      if (cascade && node.children.length > 0 && children.length === 0) {\n        return undefined\n      }\n    }\n\n    // Create a shallow clone, using the new children.\n    /** @type {typeof node} */\n    // @ts-expect-error all the fields will be copied over.\n    const next = {}\n    /** @type {string} */\n    let key\n\n    for (key in node) {\n      if (own.call(node, key)) {\n        // @ts-expect-error: Looks like a record.\n        next[key] = key === 'children' ? children : node[key]\n      }\n    }\n\n    return next\n  }\n}\n\n/**\n * @param {Node} node\n * @returns {node is Parent}\n */\nfunction parent(node) {\n  return 'children' in node && node.children !== undefined\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unist-util-filter/lib/index.js\n");

/***/ })

};
;