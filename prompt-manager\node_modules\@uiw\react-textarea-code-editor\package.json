{"name": "@uiw/react-textarea-code-editor", "version": "3.1.1", "description": "A simple code editor with syntax highlighting.", "homepage": "https://uiwjs.github.io/react-textarea-code-editor/", "funding": "https://jaywcjlove.github.io/#/sponsor", "main": "cjs/index.js", "module": "esm/index.js", "exports": {"./README.md": "./README.md", "./package.json": "./package.json", "./dist.css": "./dist.css", ".": {"import": "./esm/index.js", "types": "./cjs/index.d.ts", "require": "./cjs/index.js"}, "./nohighlight": {"import": "./esm/index.nohighlight.js", "types": "./cjs/index.nohighlight.d.ts", "require": "./cjs/index.nohighlight.js"}}, "repository": {"type": "git", "url": "https://github.com/uiwjs/react-textarea-code-editor.git"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "files": ["nohighlight.d.ts", "dist.css", "dist", "esm", "cjs", "src"], "keywords": ["react", "textarea", "textarea-editor", "textarea-code-editor", "code-editor", "code", "highlighting", "uiw", "uiwjs", "editor"], "jest": {"testMatch": ["<rootDir>/src/__test__/*.{ts,tsx}"], "transformIgnorePatterns": ["<rootDir>/node_modules/?!(.*)"], "coverageReporters": ["lcov", "json-summary"]}, "peerDependencies": {"@babel/runtime": ">=7.10.0", "react": ">=16.9.0", "react-dom": ">=16.9.0"}, "dependencies": {"@babel/runtime": "^7.18.6", "rehype": "~13.0.0", "rehype-prism-plus": "2.0.0"}}