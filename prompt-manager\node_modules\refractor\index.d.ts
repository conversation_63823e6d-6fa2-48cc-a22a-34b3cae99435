export { refractor } from "./lib/common.js";
export type RefractorRoot = import("./lib/core.js").RefractorRoot;
export type RefractorElement = import("./lib/core.js").RefractorElement;
export type Text = import("hast").Text;
export type Grammar = import("prismjs").Grammar;
export type Syntax = import("./lib/core.js").Syntax;
export type Root = import("./lib/core.js").RefractorRoot;
//# sourceMappingURL=index.d.ts.map