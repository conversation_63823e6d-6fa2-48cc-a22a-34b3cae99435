/*! 🌼 daisyUI 5.0.45 - MIT License */ @layer utilities{.diff{-webkit-user-select:none;user-select:none;direction:ltr;grid-template-columns:auto 1fr;width:100%;display:grid;position:relative;overflow:hidden;container-type:inline-size;&:focus-visible,&:has(.diff-item-1:focus-visible){outline-style:var(--tw-outline-style);outline-offset:1px;outline-width:2px;outline-color:var(--color-base-content)}&:focus-visible{outline-style:var(--tw-outline-style);outline-offset:1px;outline-width:2px;outline-color:var(--color-base-content);& .diff-resizer{min-width:90cqi;max-width:90cqi}}&:has(.diff-item-2:focus-visible){outline-style:var(--tw-outline-style);outline-offset:1px;outline-width:2px;& .diff-resizer{min-width:10cqi;max-width:10cqi}}@supports (-webkit-overflow-scrolling:touch) and (overflow:-webkit-paged-x){&:focus{& .diff-resizer{min-width:10cqi;max-width:10cqi}}&:has(.diff-item-1:focus){& .diff-resizer{min-width:90cqi;max-width:90cqi}}}}.diff-resizer{z-index:1;resize:horizontal;opacity:0;cursor:ew-resize;transform-origin:100% 100%;clip-path:inset(calc(100% - .75rem) 0 0 calc(100% - .75rem));grid-row-start:1;grid-column-start:1;width:50cqi;min-width:1rem;max-width:calc(100cqi - 1rem);height:.5rem;transition:min-width .3s ease-out,max-width .3s ease-out;position:relative;top:50%;overflow:hidden;transform:scaleY(3)translate(.35rem,.08rem)}.diff-item-2{grid-row-start:1;grid-column-start:1;position:relative;&:after{pointer-events:none;z-index:2;background-color:color-mix(in oklab,var(--color-base-100)50%,transparent);border:2px solid var(--color-base-100);content:"";outline:1px solid color-mix(in oklab,var(--color-base-content)5%,#0000);outline-offset:-3px;backdrop-filter:blur(8px);border-radius:3.40282e38px;width:1.2rem;height:1.8rem;position:absolute;top:50%;bottom:0;right:1px;translate:50% -50%;box-shadow:0 1px 2px oklch(0% 0 0/.1)}&>*{pointer-events:none;object-fit:cover;object-position:center;width:100cqi;max-width:none;height:100%;position:absolute;top:0;bottom:0;left:0}@supports (-webkit-overflow-scrolling:touch) and (overflow:-webkit-paged-x){&:after{content:none}}}.diff-item-1{z-index:1;border-right:2px solid var(--color-base-100);grid-row-start:1;grid-column-start:1;position:relative;overflow:hidden;&>*{pointer-events:none;object-fit:cover;object-position:center;width:100cqi;max-width:none;height:100%;position:absolute;top:0;bottom:0;left:0}}@media (width>=640px){.sm\:diff{-webkit-user-select:none;user-select:none;direction:ltr;grid-template-columns:auto 1fr;width:100%;display:grid;position:relative;overflow:hidden;container-type:inline-size;&:focus-visible,&:has(.diff-item-1:focus-visible){outline-style:var(--tw-outline-style);outline-offset:1px;outline-width:2px;outline-color:var(--color-base-content)}&:focus-visible{outline-style:var(--tw-outline-style);outline-offset:1px;outline-width:2px;outline-color:var(--color-base-content);& .diff-resizer{min-width:90cqi;max-width:90cqi}}&:has(.diff-item-2:focus-visible){outline-style:var(--tw-outline-style);outline-offset:1px;outline-width:2px;& .diff-resizer{min-width:10cqi;max-width:10cqi}}@supports (-webkit-overflow-scrolling:touch) and (overflow:-webkit-paged-x){&:focus{& .diff-resizer{min-width:10cqi;max-width:10cqi}}&:has(.diff-item-1:focus){& .diff-resizer{min-width:90cqi;max-width:90cqi}}}}.sm\:diff-resizer{z-index:1;resize:horizontal;opacity:0;cursor:ew-resize;transform-origin:100% 100%;clip-path:inset(calc(100% - .75rem) 0 0 calc(100% - .75rem));grid-row-start:1;grid-column-start:1;width:50cqi;min-width:1rem;max-width:calc(100cqi - 1rem);height:.5rem;transition:min-width .3s ease-out,max-width .3s ease-out;position:relative;top:50%;overflow:hidden;transform:scaleY(3)translate(.35rem,.08rem)}.sm\:diff-item-2{grid-row-start:1;grid-column-start:1;position:relative;&:after{pointer-events:none;z-index:2;background-color:color-mix(in oklab,var(--color-base-100)50%,transparent);border:2px solid var(--color-base-100);content:"";outline:1px solid color-mix(in oklab,var(--color-base-content)5%,#0000);outline-offset:-3px;backdrop-filter:blur(8px);border-radius:3.40282e38px;width:1.2rem;height:1.8rem;position:absolute;top:50%;bottom:0;right:1px;translate:50% -50%;box-shadow:0 1px 2px oklch(0% 0 0/.1)}&>*{pointer-events:none;object-fit:cover;object-position:center;width:100cqi;max-width:none;height:100%;position:absolute;top:0;bottom:0;left:0}@supports (-webkit-overflow-scrolling:touch) and (overflow:-webkit-paged-x){&:after{content:none}}}.sm\:diff-item-1{z-index:1;border-right:2px solid var(--color-base-100);grid-row-start:1;grid-column-start:1;position:relative;overflow:hidden;&>*{pointer-events:none;object-fit:cover;object-position:center;width:100cqi;max-width:none;height:100%;position:absolute;top:0;bottom:0;left:0}}}@media (width>=768px){.md\:diff{-webkit-user-select:none;user-select:none;direction:ltr;grid-template-columns:auto 1fr;width:100%;display:grid;position:relative;overflow:hidden;container-type:inline-size;&:focus-visible,&:has(.diff-item-1:focus-visible){outline-style:var(--tw-outline-style);outline-offset:1px;outline-width:2px;outline-color:var(--color-base-content)}&:focus-visible{outline-style:var(--tw-outline-style);outline-offset:1px;outline-width:2px;outline-color:var(--color-base-content);& .diff-resizer{min-width:90cqi;max-width:90cqi}}&:has(.diff-item-2:focus-visible){outline-style:var(--tw-outline-style);outline-offset:1px;outline-width:2px;& .diff-resizer{min-width:10cqi;max-width:10cqi}}@supports (-webkit-overflow-scrolling:touch) and (overflow:-webkit-paged-x){&:focus{& .diff-resizer{min-width:10cqi;max-width:10cqi}}&:has(.diff-item-1:focus){& .diff-resizer{min-width:90cqi;max-width:90cqi}}}}.md\:diff-resizer{z-index:1;resize:horizontal;opacity:0;cursor:ew-resize;transform-origin:100% 100%;clip-path:inset(calc(100% - .75rem) 0 0 calc(100% - .75rem));grid-row-start:1;grid-column-start:1;width:50cqi;min-width:1rem;max-width:calc(100cqi - 1rem);height:.5rem;transition:min-width .3s ease-out,max-width .3s ease-out;position:relative;top:50%;overflow:hidden;transform:scaleY(3)translate(.35rem,.08rem)}.md\:diff-item-2{grid-row-start:1;grid-column-start:1;position:relative;&:after{pointer-events:none;z-index:2;background-color:color-mix(in oklab,var(--color-base-100)50%,transparent);border:2px solid var(--color-base-100);content:"";outline:1px solid color-mix(in oklab,var(--color-base-content)5%,#0000);outline-offset:-3px;backdrop-filter:blur(8px);border-radius:3.40282e38px;width:1.2rem;height:1.8rem;position:absolute;top:50%;bottom:0;right:1px;translate:50% -50%;box-shadow:0 1px 2px oklch(0% 0 0/.1)}&>*{pointer-events:none;object-fit:cover;object-position:center;width:100cqi;max-width:none;height:100%;position:absolute;top:0;bottom:0;left:0}@supports (-webkit-overflow-scrolling:touch) and (overflow:-webkit-paged-x){&:after{content:none}}}.md\:diff-item-1{z-index:1;border-right:2px solid var(--color-base-100);grid-row-start:1;grid-column-start:1;position:relative;overflow:hidden;&>*{pointer-events:none;object-fit:cover;object-position:center;width:100cqi;max-width:none;height:100%;position:absolute;top:0;bottom:0;left:0}}}@media (width>=1024px){.lg\:diff{-webkit-user-select:none;user-select:none;direction:ltr;grid-template-columns:auto 1fr;width:100%;display:grid;position:relative;overflow:hidden;container-type:inline-size;&:focus-visible,&:has(.diff-item-1:focus-visible){outline-style:var(--tw-outline-style);outline-offset:1px;outline-width:2px;outline-color:var(--color-base-content)}&:focus-visible{outline-style:var(--tw-outline-style);outline-offset:1px;outline-width:2px;outline-color:var(--color-base-content);& .diff-resizer{min-width:90cqi;max-width:90cqi}}&:has(.diff-item-2:focus-visible){outline-style:var(--tw-outline-style);outline-offset:1px;outline-width:2px;& .diff-resizer{min-width:10cqi;max-width:10cqi}}@supports (-webkit-overflow-scrolling:touch) and (overflow:-webkit-paged-x){&:focus{& .diff-resizer{min-width:10cqi;max-width:10cqi}}&:has(.diff-item-1:focus){& .diff-resizer{min-width:90cqi;max-width:90cqi}}}}.lg\:diff-resizer{z-index:1;resize:horizontal;opacity:0;cursor:ew-resize;transform-origin:100% 100%;clip-path:inset(calc(100% - .75rem) 0 0 calc(100% - .75rem));grid-row-start:1;grid-column-start:1;width:50cqi;min-width:1rem;max-width:calc(100cqi - 1rem);height:.5rem;transition:min-width .3s ease-out,max-width .3s ease-out;position:relative;top:50%;overflow:hidden;transform:scaleY(3)translate(.35rem,.08rem)}.lg\:diff-item-2{grid-row-start:1;grid-column-start:1;position:relative;&:after{pointer-events:none;z-index:2;background-color:color-mix(in oklab,var(--color-base-100)50%,transparent);border:2px solid var(--color-base-100);content:"";outline:1px solid color-mix(in oklab,var(--color-base-content)5%,#0000);outline-offset:-3px;backdrop-filter:blur(8px);border-radius:3.40282e38px;width:1.2rem;height:1.8rem;position:absolute;top:50%;bottom:0;right:1px;translate:50% -50%;box-shadow:0 1px 2px oklch(0% 0 0/.1)}&>*{pointer-events:none;object-fit:cover;object-position:center;width:100cqi;max-width:none;height:100%;position:absolute;top:0;bottom:0;left:0}@supports (-webkit-overflow-scrolling:touch) and (overflow:-webkit-paged-x){&:after{content:none}}}.lg\:diff-item-1{z-index:1;border-right:2px solid var(--color-base-100);grid-row-start:1;grid-column-start:1;position:relative;overflow:hidden;&>*{pointer-events:none;object-fit:cover;object-position:center;width:100cqi;max-width:none;height:100%;position:absolute;top:0;bottom:0;left:0}}}@media (width>=1280px){.xl\:diff{-webkit-user-select:none;user-select:none;direction:ltr;grid-template-columns:auto 1fr;width:100%;display:grid;position:relative;overflow:hidden;container-type:inline-size;&:focus-visible,&:has(.diff-item-1:focus-visible){outline-style:var(--tw-outline-style);outline-offset:1px;outline-width:2px;outline-color:var(--color-base-content)}&:focus-visible{outline-style:var(--tw-outline-style);outline-offset:1px;outline-width:2px;outline-color:var(--color-base-content);& .diff-resizer{min-width:90cqi;max-width:90cqi}}&:has(.diff-item-2:focus-visible){outline-style:var(--tw-outline-style);outline-offset:1px;outline-width:2px;& .diff-resizer{min-width:10cqi;max-width:10cqi}}@supports (-webkit-overflow-scrolling:touch) and (overflow:-webkit-paged-x){&:focus{& .diff-resizer{min-width:10cqi;max-width:10cqi}}&:has(.diff-item-1:focus){& .diff-resizer{min-width:90cqi;max-width:90cqi}}}}.xl\:diff-resizer{z-index:1;resize:horizontal;opacity:0;cursor:ew-resize;transform-origin:100% 100%;clip-path:inset(calc(100% - .75rem) 0 0 calc(100% - .75rem));grid-row-start:1;grid-column-start:1;width:50cqi;min-width:1rem;max-width:calc(100cqi - 1rem);height:.5rem;transition:min-width .3s ease-out,max-width .3s ease-out;position:relative;top:50%;overflow:hidden;transform:scaleY(3)translate(.35rem,.08rem)}.xl\:diff-item-2{grid-row-start:1;grid-column-start:1;position:relative;&:after{pointer-events:none;z-index:2;background-color:color-mix(in oklab,var(--color-base-100)50%,transparent);border:2px solid var(--color-base-100);content:"";outline:1px solid color-mix(in oklab,var(--color-base-content)5%,#0000);outline-offset:-3px;backdrop-filter:blur(8px);border-radius:3.40282e38px;width:1.2rem;height:1.8rem;position:absolute;top:50%;bottom:0;right:1px;translate:50% -50%;box-shadow:0 1px 2px oklch(0% 0 0/.1)}&>*{pointer-events:none;object-fit:cover;object-position:center;width:100cqi;max-width:none;height:100%;position:absolute;top:0;bottom:0;left:0}@supports (-webkit-overflow-scrolling:touch) and (overflow:-webkit-paged-x){&:after{content:none}}}.xl\:diff-item-1{z-index:1;border-right:2px solid var(--color-base-100);grid-row-start:1;grid-column-start:1;position:relative;overflow:hidden;&>*{pointer-events:none;object-fit:cover;object-position:center;width:100cqi;max-width:none;height:100%;position:absolute;top:0;bottom:0;left:0}}}@media (width>=1536px){.\32 xl\:diff{-webkit-user-select:none;user-select:none;direction:ltr;grid-template-columns:auto 1fr;width:100%;display:grid;position:relative;overflow:hidden;container-type:inline-size;&:focus-visible,&:has(.diff-item-1:focus-visible){outline-style:var(--tw-outline-style);outline-offset:1px;outline-width:2px;outline-color:var(--color-base-content)}&:focus-visible{outline-style:var(--tw-outline-style);outline-offset:1px;outline-width:2px;outline-color:var(--color-base-content);& .diff-resizer{min-width:90cqi;max-width:90cqi}}&:has(.diff-item-2:focus-visible){outline-style:var(--tw-outline-style);outline-offset:1px;outline-width:2px;& .diff-resizer{min-width:10cqi;max-width:10cqi}}@supports (-webkit-overflow-scrolling:touch) and (overflow:-webkit-paged-x){&:focus{& .diff-resizer{min-width:10cqi;max-width:10cqi}}&:has(.diff-item-1:focus){& .diff-resizer{min-width:90cqi;max-width:90cqi}}}}.\32 xl\:diff-resizer{z-index:1;resize:horizontal;opacity:0;cursor:ew-resize;transform-origin:100% 100%;clip-path:inset(calc(100% - .75rem) 0 0 calc(100% - .75rem));grid-row-start:1;grid-column-start:1;width:50cqi;min-width:1rem;max-width:calc(100cqi - 1rem);height:.5rem;transition:min-width .3s ease-out,max-width .3s ease-out;position:relative;top:50%;overflow:hidden;transform:scaleY(3)translate(.35rem,.08rem)}.\32 xl\:diff-item-2{grid-row-start:1;grid-column-start:1;position:relative;&:after{pointer-events:none;z-index:2;background-color:color-mix(in oklab,var(--color-base-100)50%,transparent);border:2px solid var(--color-base-100);content:"";outline:1px solid color-mix(in oklab,var(--color-base-content)5%,#0000);outline-offset:-3px;backdrop-filter:blur(8px);border-radius:3.40282e38px;width:1.2rem;height:1.8rem;position:absolute;top:50%;bottom:0;right:1px;translate:50% -50%;box-shadow:0 1px 2px oklch(0% 0 0/.1)}&>*{pointer-events:none;object-fit:cover;object-position:center;width:100cqi;max-width:none;height:100%;position:absolute;top:0;bottom:0;left:0}@supports (-webkit-overflow-scrolling:touch) and (overflow:-webkit-paged-x){&:after{content:none}}}.\32 xl\:diff-item-1{z-index:1;border-right:2px solid var(--color-base-100);grid-row-start:1;grid-column-start:1;position:relative;overflow:hidden;&>*{pointer-events:none;object-fit:cover;object-position:center;width:100cqi;max-width:none;height:100%;position:absolute;top:0;bottom:0;left:0}}}}