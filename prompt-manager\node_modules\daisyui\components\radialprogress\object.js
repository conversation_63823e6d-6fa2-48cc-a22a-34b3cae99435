export default {".radial-progress":{"position":"relative","display":"inline-grid","height":"var(--size)","width":"var(--size)","place-content":"center","border-radius":"calc(infinity * 1px)","background-color":"transparent","vertical-align":"middle","box-sizing":"content-box","--value":"0","--size":"5rem","--thickness":"calc(var(--size) / 10)","--radialprogress":"calc(var(--value) * 1%)","transition":"--radialprogress 0.3s linear","&:before":{"position":"absolute","inset":"calc(0.25rem * 0)","border-radius":"calc(infinity * 1px)","content":"\"\"","background":"radial-gradient(farthest-side, currentColor 98%, #0000) top/var(--thickness) var(--thickness) no-repeat, conic-gradient(currentColor var(--radialprogress), #0000 0)","webkit-mask":"radial-gradient( farthest-side, #0000 calc(100% - var(--thickness)), #000 calc(100% + 0.5px - var(--thickness)) )","mask":"radial-gradient( farthest-side, #0000 calc(100% - var(--thickness)), #000 calc(100% + 0.5px - var(--thickness)) )"},"&:after":{"position":"absolute","border-radius":"calc(infinity * 1px)","background-color":"currentColor","transition":"transform 0.3s linear","content":"\"\"","inset":"calc(50% - var(--thickness) / 2)","transform":"rotate(calc(var(--value) * 3.6deg - 90deg)) translate(calc(var(--size) / 2 - 50%))"}}};