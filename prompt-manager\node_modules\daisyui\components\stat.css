/*! 🌼 daisyUI 5.0.45 - MIT License */ @layer utilities{.stats{border-radius:var(--radius-box);grid-auto-flow:column;display:inline-grid;position:relative;overflow-x:auto}.stat{grid-template-columns:repeat(1,1fr);column-gap:1rem;width:100%;padding-block:1rem;padding-inline:1.5rem;display:inline-grid;&:not(:last-child){border-inline-end:var(--border)dashed color-mix(in oklab,currentColor 10%,#0000);border-block-end:none}}.stat-figure{grid-row:1/span 3;grid-column-start:2;place-self:center flex-end}.stat-title{white-space:nowrap;color:color-mix(in oklab,var(--color-base-content)60%,transparent);grid-column-start:1;font-size:.75rem}.stat-value{white-space:nowrap;grid-column-start:1;font-size:2rem;font-weight:800}.stat-desc{white-space:nowrap;color:color-mix(in oklab,var(--color-base-content)60%,transparent);grid-column-start:1;font-size:.75rem}.stat-actions{white-space:nowrap;grid-column-start:1}.stats-horizontal{grid-auto-flow:column;overflow-x:auto;& .stat:not(:last-child){border-inline-end:var(--border)dashed color-mix(in oklab,currentColor 10%,#0000);border-block-end:none}}.stats-vertical{grid-auto-flow:row;overflow-y:auto;& .stat:not(:last-child){border-inline-end:none;border-block-end:var(--border)dashed color-mix(in oklab,currentColor 10%,#0000)}}@media (width>=640px){.sm\:stats{border-radius:var(--radius-box);grid-auto-flow:column;display:inline-grid;position:relative;overflow-x:auto}.sm\:stat{grid-template-columns:repeat(1,1fr);column-gap:1rem;width:100%;padding-block:1rem;padding-inline:1.5rem;display:inline-grid;&:not(:last-child){border-inline-end:var(--border)dashed color-mix(in oklab,currentColor 10%,#0000);border-block-end:none}}.sm\:stat-figure{grid-row:1/span 3;grid-column-start:2;place-self:center flex-end}.sm\:stat-title{white-space:nowrap;color:color-mix(in oklab,var(--color-base-content)60%,transparent);grid-column-start:1;font-size:.75rem}.sm\:stat-value{white-space:nowrap;grid-column-start:1;font-size:2rem;font-weight:800}.sm\:stat-desc{white-space:nowrap;color:color-mix(in oklab,var(--color-base-content)60%,transparent);grid-column-start:1;font-size:.75rem}.sm\:stat-actions{white-space:nowrap;grid-column-start:1}.sm\:stats-horizontal{grid-auto-flow:column;overflow-x:auto;& .stat:not(:last-child){border-inline-end:var(--border)dashed color-mix(in oklab,currentColor 10%,#0000);border-block-end:none}}.sm\:stats-vertical{grid-auto-flow:row;overflow-y:auto;& .stat:not(:last-child){border-inline-end:none;border-block-end:var(--border)dashed color-mix(in oklab,currentColor 10%,#0000)}}}@media (width>=768px){.md\:stats{border-radius:var(--radius-box);grid-auto-flow:column;display:inline-grid;position:relative;overflow-x:auto}.md\:stat{grid-template-columns:repeat(1,1fr);column-gap:1rem;width:100%;padding-block:1rem;padding-inline:1.5rem;display:inline-grid;&:not(:last-child){border-inline-end:var(--border)dashed color-mix(in oklab,currentColor 10%,#0000);border-block-end:none}}.md\:stat-figure{grid-row:1/span 3;grid-column-start:2;place-self:center flex-end}.md\:stat-title{white-space:nowrap;color:color-mix(in oklab,var(--color-base-content)60%,transparent);grid-column-start:1;font-size:.75rem}.md\:stat-value{white-space:nowrap;grid-column-start:1;font-size:2rem;font-weight:800}.md\:stat-desc{white-space:nowrap;color:color-mix(in oklab,var(--color-base-content)60%,transparent);grid-column-start:1;font-size:.75rem}.md\:stat-actions{white-space:nowrap;grid-column-start:1}.md\:stats-horizontal{grid-auto-flow:column;overflow-x:auto;& .stat:not(:last-child){border-inline-end:var(--border)dashed color-mix(in oklab,currentColor 10%,#0000);border-block-end:none}}.md\:stats-vertical{grid-auto-flow:row;overflow-y:auto;& .stat:not(:last-child){border-inline-end:none;border-block-end:var(--border)dashed color-mix(in oklab,currentColor 10%,#0000)}}}@media (width>=1024px){.lg\:stats{border-radius:var(--radius-box);grid-auto-flow:column;display:inline-grid;position:relative;overflow-x:auto}.lg\:stat{grid-template-columns:repeat(1,1fr);column-gap:1rem;width:100%;padding-block:1rem;padding-inline:1.5rem;display:inline-grid;&:not(:last-child){border-inline-end:var(--border)dashed color-mix(in oklab,currentColor 10%,#0000);border-block-end:none}}.lg\:stat-figure{grid-row:1/span 3;grid-column-start:2;place-self:center flex-end}.lg\:stat-title{white-space:nowrap;color:color-mix(in oklab,var(--color-base-content)60%,transparent);grid-column-start:1;font-size:.75rem}.lg\:stat-value{white-space:nowrap;grid-column-start:1;font-size:2rem;font-weight:800}.lg\:stat-desc{white-space:nowrap;color:color-mix(in oklab,var(--color-base-content)60%,transparent);grid-column-start:1;font-size:.75rem}.lg\:stat-actions{white-space:nowrap;grid-column-start:1}.lg\:stats-horizontal{grid-auto-flow:column;overflow-x:auto;& .stat:not(:last-child){border-inline-end:var(--border)dashed color-mix(in oklab,currentColor 10%,#0000);border-block-end:none}}.lg\:stats-vertical{grid-auto-flow:row;overflow-y:auto;& .stat:not(:last-child){border-inline-end:none;border-block-end:var(--border)dashed color-mix(in oklab,currentColor 10%,#0000)}}}@media (width>=1280px){.xl\:stats{border-radius:var(--radius-box);grid-auto-flow:column;display:inline-grid;position:relative;overflow-x:auto}.xl\:stat{grid-template-columns:repeat(1,1fr);column-gap:1rem;width:100%;padding-block:1rem;padding-inline:1.5rem;display:inline-grid;&:not(:last-child){border-inline-end:var(--border)dashed color-mix(in oklab,currentColor 10%,#0000);border-block-end:none}}.xl\:stat-figure{grid-row:1/span 3;grid-column-start:2;place-self:center flex-end}.xl\:stat-title{white-space:nowrap;color:color-mix(in oklab,var(--color-base-content)60%,transparent);grid-column-start:1;font-size:.75rem}.xl\:stat-value{white-space:nowrap;grid-column-start:1;font-size:2rem;font-weight:800}.xl\:stat-desc{white-space:nowrap;color:color-mix(in oklab,var(--color-base-content)60%,transparent);grid-column-start:1;font-size:.75rem}.xl\:stat-actions{white-space:nowrap;grid-column-start:1}.xl\:stats-horizontal{grid-auto-flow:column;overflow-x:auto;& .stat:not(:last-child){border-inline-end:var(--border)dashed color-mix(in oklab,currentColor 10%,#0000);border-block-end:none}}.xl\:stats-vertical{grid-auto-flow:row;overflow-y:auto;& .stat:not(:last-child){border-inline-end:none;border-block-end:var(--border)dashed color-mix(in oklab,currentColor 10%,#0000)}}}@media (width>=1536px){.\32 xl\:stats{border-radius:var(--radius-box);grid-auto-flow:column;display:inline-grid;position:relative;overflow-x:auto}.\32 xl\:stat{grid-template-columns:repeat(1,1fr);column-gap:1rem;width:100%;padding-block:1rem;padding-inline:1.5rem;display:inline-grid;&:not(:last-child){border-inline-end:var(--border)dashed color-mix(in oklab,currentColor 10%,#0000);border-block-end:none}}.\32 xl\:stat-figure{grid-row:1/span 3;grid-column-start:2;place-self:center flex-end}.\32 xl\:stat-title{white-space:nowrap;color:color-mix(in oklab,var(--color-base-content)60%,transparent);grid-column-start:1;font-size:.75rem}.\32 xl\:stat-value{white-space:nowrap;grid-column-start:1;font-size:2rem;font-weight:800}.\32 xl\:stat-desc{white-space:nowrap;color:color-mix(in oklab,var(--color-base-content)60%,transparent);grid-column-start:1;font-size:.75rem}.\32 xl\:stat-actions{white-space:nowrap;grid-column-start:1}.\32 xl\:stats-horizontal{grid-auto-flow:column;overflow-x:auto;& .stat:not(:last-child){border-inline-end:var(--border)dashed color-mix(in oklab,currentColor 10%,#0000);border-block-end:none}}.\32 xl\:stats-vertical{grid-auto-flow:row;overflow-y:auto;& .stat:not(:last-child){border-inline-end:none;border-block-end:var(--border)dashed color-mix(in oklab,currentColor 10%,#0000)}}}}