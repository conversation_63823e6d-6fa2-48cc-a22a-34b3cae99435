{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/goober/dist/goober.modern.js"], "sourcesContent": ["let e={data:\"\"},t=t=>\"object\"==typeof window?((t?t.querySelector(\"#_goober\"):window._goober)||Object.assign((t||document.head).appendChild(document.createElement(\"style\")),{innerHTML:\" \",id:\"_goober\"})).firstChild:t||e,r=e=>{let r=t(e),l=r.data;return r.data=\"\",l},l=/(?:([\\u0080-\\uFFFF\\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\\s*)/g,a=/\\/\\*[^]*?\\*\\/|  +/g,n=/\\n+/g,o=(e,t)=>{let r=\"\",l=\"\",a=\"\";for(let n in e){let c=e[n];\"@\"==n[0]?\"i\"==n[1]?r=n+\" \"+c+\";\":l+=\"f\"==n[1]?o(c,n):n+\"{\"+o(c,\"k\"==n[1]?\"\":t)+\"}\":\"object\"==typeof c?l+=o(c,t?t.replace(/([^,])+/g,e=>n.replace(/([^,]*:\\S+\\([^)]*\\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+\" \"+t:t)):n):null!=c&&(n=/^--/.test(n)?n:n.replace(/[A-Z]/g,\"-$&\").toLowerCase(),a+=o.p?o.p(n,c):n+\":\"+c+\";\")}return r+(t&&a?t+\"{\"+a+\"}\":a)+l},c={},s=e=>{if(\"object\"==typeof e){let t=\"\";for(let r in e)t+=r+s(e[r]);return t}return e},i=(e,t,r,i,p)=>{let u=s(e),d=c[u]||(c[u]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return\"go\"+r})(u));if(!c[d]){let t=u!==e?e:(e=>{let t,r,o=[{}];for(;t=l.exec(e.replace(a,\"\"));)t[4]?o.shift():t[3]?(r=t[3].replace(n,\" \").trim(),o.unshift(o[0][r]=o[0][r]||{})):o[0][t[1]]=t[2].replace(n,\" \").trim();return o[0]})(e);c[d]=o(p?{[\"@keyframes \"+d]:t}:t,r?\"\":\".\"+d)}let f=r&&c.g?c.g:null;return r&&(c.g=c[d]),((e,t,r,l)=>{l?t.data=t.data.replace(l,e):-1===t.data.indexOf(e)&&(t.data=r?e+t.data:t.data+e)})(c[d],t,i,f),d},p=(e,t,r)=>e.reduce((e,l,a)=>{let n=t[a];if(n&&n.call){let e=n(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;n=t?\".\"+t:e&&\"object\"==typeof e?e.props?\"\":o(e,\"\"):!1===e?\"\":e}return e+l+(null==n?\"\":n)},\"\");function u(e){let r=this||{},l=e.call?e(r.p):e;return i(l.unshift?l.raw?p(l,[].slice.call(arguments,1),r.p):l.reduce((e,t)=>Object.assign(e,t&&t.call?t(r.p):t),{}):l,t(r.target),r.g,r.o,r.k)}let d,f,g,b=u.bind({g:1}),h=u.bind({k:1});function m(e,t,r,l){o.p=t,d=e,f=r,g=l}function j(e,t){let r=this||{};return function(){let l=arguments;function a(n,o){let c=Object.assign({},n),s=c.className||a.className;r.p=Object.assign({theme:f&&f()},c),r.o=/ *go\\d+/.test(s),c.className=u.apply(r,l)+(s?\" \"+s:\"\"),t&&(c.ref=o);let i=e;return e[0]&&(i=c.as||e,delete c.as),g&&i[0]&&g(c),d(i,c)}return t?t(a):a}}export{u as css,r as extractCss,b as glob,h as keyframes,m as setup,j as styled};\n"], "names": [], "mappings": ";;;;;;;;AAAA,IAAI,IAAE;IAAC,MAAK;AAAE,GAAE,IAAE,CAAA,IAAG,sCAAwB,0BAAyK,KAAG,GAAE,IAAE,CAAA;IAAI,IAAI,IAAE,EAAE,IAAG,IAAE,EAAE,IAAI;IAAC,OAAO,EAAE,IAAI,GAAC,IAAG;AAAC,GAAE,IAAE,qEAAoE,IAAE,sBAAqB,IAAE,QAAO,IAAE,CAAC,GAAE;IAAK,IAAI,IAAE,IAAG,IAAE,IAAG,IAAE;IAAG,IAAI,IAAI,KAAK,EAAE;QAAC,IAAI,IAAE,CAAC,CAAC,EAAE;QAAC,OAAK,CAAC,CAAC,EAAE,GAAC,OAAK,CAAC,CAAC,EAAE,GAAC,IAAE,IAAE,MAAI,IAAE,MAAI,KAAG,OAAK,CAAC,CAAC,EAAE,GAAC,EAAE,GAAE,KAAG,IAAE,MAAI,EAAE,GAAE,OAAK,CAAC,CAAC,EAAE,GAAC,KAAG,KAAG,MAAI,YAAU,OAAO,IAAE,KAAG,EAAE,GAAE,IAAE,EAAE,OAAO,CAAC,YAAW,CAAA,IAAG,EAAE,OAAO,CAAC,iCAAgC,CAAA,IAAG,IAAI,IAAI,CAAC,KAAG,EAAE,OAAO,CAAC,MAAK,KAAG,IAAE,IAAE,MAAI,IAAE,MAAI,KAAG,QAAM,KAAG,CAAC,IAAE,MAAM,IAAI,CAAC,KAAG,IAAE,EAAE,OAAO,CAAC,UAAS,OAAO,WAAW,IAAG,KAAG,EAAE,CAAC,GAAC,EAAE,CAAC,CAAC,GAAE,KAAG,IAAE,MAAI,IAAE,GAAG;IAAC;IAAC,OAAO,IAAE,CAAC,KAAG,IAAE,IAAE,MAAI,IAAE,MAAI,CAAC,IAAE;AAAC,GAAE,IAAE,CAAC,GAAE,IAAE,CAAA;IAAI,IAAG,YAAU,OAAO,GAAE;QAAC,IAAI,IAAE;QAAG,IAAI,IAAI,KAAK,EAAE,KAAG,IAAE,EAAE,CAAC,CAAC,EAAE;QAAE,OAAO;IAAC;IAAC,OAAO;AAAC,GAAE,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE;IAAK,IAAI,IAAE,EAAE,IAAG,IAAE,CAAC,CAAC,EAAE,IAAE,CAAC,CAAC,CAAC,EAAE,GAAC,CAAC,CAAA;QAAI,IAAI,IAAE,GAAE,IAAE;QAAG,MAAK,IAAE,EAAE,MAAM,EAAE,IAAE,MAAI,IAAE,EAAE,UAAU,CAAC,SAAO;QAAE,OAAM,OAAK;IAAC,CAAC,EAAE,EAAE;IAAE,IAAG,CAAC,CAAC,CAAC,EAAE,EAAC;QAAC,IAAI,IAAE,MAAI,IAAE,IAAE,CAAC,CAAA;YAAI,IAAI,GAAE,GAAE,IAAE;gBAAC,CAAC;aAAE;YAAC,MAAK,IAAE,EAAE,IAAI,CAAC,EAAE,OAAO,CAAC,GAAE,MAAM,CAAC,CAAC,EAAE,GAAC,EAAE,KAAK,KAAG,CAAC,CAAC,EAAE,GAAC,CAAC,IAAE,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,GAAE,KAAK,IAAI,IAAG,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAE,CAAC,EAAE,IAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,GAAE,KAAK,IAAI;YAAG,OAAO,CAAC,CAAC,EAAE;QAAA,CAAC,EAAE;QAAG,CAAC,CAAC,EAAE,GAAC,EAAE,IAAE;YAAC,CAAC,gBAAc,EAAE,EAAC;QAAC,IAAE,GAAE,IAAE,KAAG,MAAI;IAAE;IAAC,IAAI,IAAE,KAAG,EAAE,CAAC,GAAC,EAAE,CAAC,GAAC;IAAK,OAAO,KAAG,CAAC,EAAE,CAAC,GAAC,CAAC,CAAC,EAAE,GAAE,CAAC,CAAC,GAAE,GAAE,GAAE;QAAK,IAAE,EAAE,IAAI,GAAC,EAAE,IAAI,CAAC,OAAO,CAAC,GAAE,KAAG,CAAC,MAAI,EAAE,IAAI,CAAC,OAAO,CAAC,MAAI,CAAC,EAAE,IAAI,GAAC,IAAE,IAAE,EAAE,IAAI,GAAC,EAAE,IAAI,GAAC,CAAC;IAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAC,GAAE,GAAE,IAAG;AAAC,GAAE,IAAE,CAAC,GAAE,GAAE,IAAI,EAAE,MAAM,CAAC,CAAC,GAAE,GAAE;QAAK,IAAI,IAAE,CAAC,CAAC,EAAE;QAAC,IAAG,KAAG,EAAE,IAAI,EAAC;YAAC,IAAI,IAAE,EAAE,IAAG,IAAE,KAAG,EAAE,KAAK,IAAE,EAAE,KAAK,CAAC,SAAS,IAAE,MAAM,IAAI,CAAC,MAAI;YAAE,IAAE,IAAE,MAAI,IAAE,KAAG,YAAU,OAAO,IAAE,EAAE,KAAK,GAAC,KAAG,EAAE,GAAE,MAAI,CAAC,MAAI,IAAE,KAAG;QAAC;QAAC,OAAO,IAAE,IAAE,CAAC,QAAM,IAAE,KAAG,CAAC;IAAC,GAAE;AAAI,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,IAAI,IAAE,CAAC,GAAE,IAAE,EAAE,IAAI,GAAC,EAAE,EAAE,CAAC,IAAE;IAAE,OAAO,EAAE,EAAE,OAAO,GAAC,EAAE,GAAG,GAAC,EAAE,GAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,WAAU,IAAG,EAAE,CAAC,IAAE,EAAE,MAAM,CAAC,CAAC,GAAE,IAAI,OAAO,MAAM,CAAC,GAAE,KAAG,EAAE,IAAI,GAAC,EAAE,EAAE,CAAC,IAAE,IAAG,CAAC,KAAG,GAAE,EAAE,EAAE,MAAM,GAAE,EAAE,CAAC,EAAC,EAAE,CAAC,EAAC,EAAE,CAAC;AAAC;AAAC,IAAI,GAAE,GAAE,GAAE,IAAE,EAAE,IAAI,CAAC;IAAC,GAAE;AAAC,IAAG,IAAE,EAAE,IAAI,CAAC;IAAC,GAAE;AAAC;AAAG,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,EAAE,CAAC,GAAC,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE;AAAC;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,IAAI,IAAE,CAAC;IAAE,OAAO;QAAW,IAAI,IAAE;QAAU,SAAS,EAAE,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,OAAO,MAAM,CAAC,CAAC,GAAE,IAAG,IAAE,EAAE,SAAS,IAAE,EAAE,SAAS;YAAC,EAAE,CAAC,GAAC,OAAO,MAAM,CAAC;gBAAC,OAAM,KAAG;YAAG,GAAE,IAAG,EAAE,CAAC,GAAC,UAAU,IAAI,CAAC,IAAG,EAAE,SAAS,GAAC,EAAE,KAAK,CAAC,GAAE,KAAG,CAAC,IAAE,MAAI,IAAE,EAAE,GAAE,KAAG,CAAC,EAAE,GAAG,GAAC,CAAC;YAAE,IAAI,IAAE;YAAE,OAAO,CAAC,CAAC,EAAE,IAAE,CAAC,IAAE,EAAE,EAAE,IAAE,GAAE,OAAO,EAAE,EAAE,GAAE,KAAG,CAAC,CAAC,EAAE,IAAE,EAAE,IAAG,EAAE,GAAE;QAAE;QAAC,OAAO,IAAE,EAAE,KAAG;IAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/react-hot-toast/src/core/types.ts", "file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/react-hot-toast/src/core/utils.ts", "file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/react-hot-toast/src/core/store.ts", "file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/react-hot-toast/src/core/toast.ts", "file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/react-hot-toast/src/core/use-toaster.ts", "file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/react-hot-toast/src/components/toast-bar.tsx", "file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/react-hot-toast/src/components/toast-icon.tsx", "file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/react-hot-toast/src/components/error.tsx", "file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/react-hot-toast/src/components/loader.tsx", "file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/react-hot-toast/src/components/checkmark.tsx", "file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/react-hot-toast/src/components/toaster.tsx", "file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/react-hot-toast/src/index.ts"], "sourcesContent": ["import { CSSProperties } from 'react';\n\nexport type ToastType = 'success' | 'error' | 'loading' | 'blank' | 'custom';\nexport type ToastPosition =\n  | 'top-left'\n  | 'top-center'\n  | 'top-right'\n  | 'bottom-left'\n  | 'bottom-center'\n  | 'bottom-right';\n\nexport type Renderable = React.ReactElement | string | null;\n\nexport interface IconTheme {\n  primary: string;\n  secondary: string;\n}\n\nexport type ValueFunction<TValue, TArg> = (arg: TArg) => TValue;\nexport type ValueOrFunction<TValue, TArg> =\n  | TValue\n  | ValueFunction<TValue, TArg>;\n\nconst isFunction = <TValue, TArg>(\n  valOrFunction: ValueOrFunction<TValue, TArg>\n): valOrFunction is ValueFunction<TValue, TArg> =>\n  typeof valOrFunction === 'function';\n\nexport const resolveValue = <TValue, TArg>(\n  valOrFunction: ValueOrFunction<TValue, TArg>,\n  arg: TArg\n): TValue => (isFunction(valOrFunction) ? valOrFunction(arg) : valOrFunction);\n\nexport interface Toast {\n  type: ToastType;\n  id: string;\n  message: ValueOrFunction<Renderable, Toast>;\n  icon?: Renderable;\n  duration?: number;\n  pauseDuration: number;\n  position?: ToastPosition;\n  removeDelay?: number;\n\n  ariaProps: {\n    role: 'status' | 'alert';\n    'aria-live': 'assertive' | 'off' | 'polite';\n  };\n\n  style?: CSSProperties;\n  className?: string;\n  iconTheme?: IconTheme;\n\n  createdAt: number;\n  visible: boolean;\n  dismissed: boolean;\n  height?: number;\n}\n\nexport type ToastOptions = Partial<\n  Pick<\n    Toast,\n    | 'id'\n    | 'icon'\n    | 'duration'\n    | 'ariaProps'\n    | 'className'\n    | 'style'\n    | 'position'\n    | 'iconTheme'\n    | 'removeDelay'\n  >\n>;\n\nexport type DefaultToastOptions = ToastOptions & {\n  [key in ToastType]?: ToastOptions;\n};\n\nexport interface ToasterProps {\n  position?: ToastPosition;\n  toastOptions?: DefaultToastOptions;\n  reverseOrder?: boolean;\n  gutter?: number;\n  containerStyle?: React.CSSProperties;\n  containerClassName?: string;\n  children?: (toast: Toast) => React.ReactElement;\n}\n\nexport interface ToastWrapperProps {\n  id: string;\n  className?: string;\n  style?: React.CSSProperties;\n  onHeightUpdate: (id: string, height: number) => void;\n  children?: React.ReactNode;\n}\n", "export const genId = (() => {\n  let count = 0;\n  return () => {\n    return (++count).toString();\n  };\n})();\n\nexport const prefersReducedMotion = (() => {\n  // Cache result\n  let shouldReduceMotion: boolean | undefined = undefined;\n\n  return () => {\n    if (shouldReduceMotion === undefined && typeof window !== 'undefined') {\n      const mediaQuery = matchMedia('(prefers-reduced-motion: reduce)');\n      shouldReduceMotion = !mediaQuery || mediaQuery.matches;\n    }\n    return shouldReduceMotion;\n  };\n})();\n", "import { useEffect, useState, useRef } from 'react';\nimport { DefaultToastOptions, Toast, ToastType } from './types';\n\nconst TOAST_LIMIT = 20;\n\nexport enum ActionType {\n  ADD_TOAST,\n  UPDATE_TOAST,\n  UPSERT_TOAST,\n  DISMISS_TOAST,\n  REMOVE_TOAST,\n  START_PAUSE,\n  END_PAUSE,\n}\n\ntype Action =\n  | {\n      type: ActionType.ADD_TOAST;\n      toast: Toast;\n    }\n  | {\n      type: ActionType.UPSERT_TOAST;\n      toast: Toast;\n    }\n  | {\n      type: ActionType.UPDATE_TOAST;\n      toast: Partial<Toast>;\n    }\n  | {\n      type: ActionType.DISMISS_TOAST;\n      toastId?: string;\n    }\n  | {\n      type: ActionType.REMOVE_TOAST;\n      toastId?: string;\n    }\n  | {\n      type: ActionType.START_PAUSE;\n      time: number;\n    }\n  | {\n      type: ActionType.END_PAUSE;\n      time: number;\n    };\n\ninterface State {\n  toasts: Toast[];\n  pausedAt: number | undefined;\n}\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case ActionType.ADD_TOAST:\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      };\n\n    case ActionType.UPDATE_TOAST:\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      };\n\n    case ActionType.UPSERT_TOAST:\n      const { toast } = action;\n      return reducer(state, {\n        type: state.toasts.find((t) => t.id === toast.id)\n          ? ActionType.UPDATE_TOAST\n          : ActionType.ADD_TOAST,\n        toast,\n      });\n\n    case ActionType.DISMISS_TOAST:\n      const { toastId } = action;\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                dismissed: true,\n                visible: false,\n              }\n            : t\n        ),\n      };\n    case ActionType.REMOVE_TOAST:\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        };\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      };\n\n    case ActionType.START_PAUSE:\n      return {\n        ...state,\n        pausedAt: action.time,\n      };\n\n    case ActionType.END_PAUSE:\n      const diff = action.time - (state.pausedAt || 0);\n\n      return {\n        ...state,\n        pausedAt: undefined,\n        toasts: state.toasts.map((t) => ({\n          ...t,\n          pauseDuration: t.pauseDuration + diff,\n        })),\n      };\n  }\n};\n\nconst listeners: Array<(state: State) => void> = [];\n\nlet memoryState: State = { toasts: [], pausedAt: undefined };\n\nexport const dispatch = (action: Action) => {\n  memoryState = reducer(memoryState, action);\n  listeners.forEach((listener) => {\n    listener(memoryState);\n  });\n};\n\nexport const defaultTimeouts: {\n  [key in ToastType]: number;\n} = {\n  blank: 4000,\n  error: 4000,\n  success: 2000,\n  loading: Infinity,\n  custom: 4000,\n};\n\nexport const useStore = (toastOptions: DefaultToastOptions = {}): State => {\n  const [state, setState] = useState<State>(memoryState);\n  const initial = useRef(memoryState);\n\n  // TODO: Switch to useSyncExternalStore when targeting React 18+\n  useEffect(() => {\n    if (initial.current !== memoryState) {\n      setState(memoryState);\n    }\n    listeners.push(setState);\n    return () => {\n      const index = listeners.indexOf(setState);\n      if (index > -1) {\n        listeners.splice(index, 1);\n      }\n    };\n  }, []);\n\n  const mergedToasts = state.toasts.map((t) => ({\n    ...toastOptions,\n    ...toastOptions[t.type],\n    ...t,\n    removeDelay:\n      t.removeDelay ||\n      toastOptions[t.type]?.removeDelay ||\n      toastOptions?.removeDelay,\n    duration:\n      t.duration ||\n      toastOptions[t.type]?.duration ||\n      toastOptions?.duration ||\n      defaultTimeouts[t.type],\n    style: {\n      ...toastOptions.style,\n      ...toastOptions[t.type]?.style,\n      ...t.style,\n    },\n  }));\n\n  return {\n    ...state,\n    toasts: mergedToasts,\n  };\n};\n", "import {\n  Renderable,\n  Toast,\n  ToastOptions,\n  ToastType,\n  DefaultToastOptions,\n  ValueOrFunction,\n  resolveValue,\n} from './types';\nimport { genId } from './utils';\nimport { dispatch, ActionType } from './store';\n\ntype Message = ValueOrFunction<Renderable, Toast>;\n\ntype ToastHandler = (message: Message, options?: ToastOptions) => string;\n\nconst createToast = (\n  message: Message,\n  type: ToastType = 'blank',\n  opts?: ToastOptions\n): Toast => ({\n  createdAt: Date.now(),\n  visible: true,\n  dismissed: false,\n  type,\n  ariaProps: {\n    role: 'status',\n    'aria-live': 'polite',\n  },\n  message,\n  pauseDuration: 0,\n  ...opts,\n  id: opts?.id || genId(),\n});\n\nconst createHandler =\n  (type?: ToastType): ToastHandler =>\n  (message, options) => {\n    const toast = createToast(message, type, options);\n    dispatch({ type: ActionType.UPSERT_TOAST, toast });\n    return toast.id;\n  };\n\nconst toast = (message: Message, opts?: ToastOptions) =>\n  createHandler('blank')(message, opts);\n\ntoast.error = createHandler('error');\ntoast.success = createHandler('success');\ntoast.loading = createHandler('loading');\ntoast.custom = createHandler('custom');\n\ntoast.dismiss = (toastId?: string) => {\n  dispatch({\n    type: ActionType.DISMISS_TOAST,\n    toastId,\n  });\n};\n\ntoast.remove = (toastId?: string) =>\n  dispatch({ type: ActionType.REMOVE_TOAST, toastId });\n\ntoast.promise = <T>(\n  promise: Promise<T> | (() => Promise<T>),\n  msgs: {\n    loading: Renderable;\n    success?: ValueOrFunction<Renderable, T>;\n    error?: ValueOrFunction<Renderable, any>;\n  },\n  opts?: DefaultToastOptions\n) => {\n  const id = toast.loading(msgs.loading, { ...opts, ...opts?.loading });\n\n  if (typeof promise === 'function') {\n    promise = promise();\n  }\n\n  promise\n    .then((p) => {\n      const successMessage = msgs.success\n        ? resolveValue(msgs.success, p)\n        : undefined;\n\n      if (successMessage) {\n        toast.success(successMessage, {\n          id,\n          ...opts,\n          ...opts?.success,\n        });\n      } else {\n        toast.dismiss(id);\n      }\n      return p;\n    })\n    .catch((e) => {\n      const errorMessage = msgs.error ? resolveValue(msgs.error, e) : undefined;\n\n      if (errorMessage) {\n        toast.error(errorMessage, {\n          id,\n          ...opts,\n          ...opts?.error,\n        });\n      } else {\n        toast.dismiss(id);\n      }\n    });\n\n  return promise;\n};\n\nexport { toast };\n", "import { useEffect, useCallback } from 'react';\nimport { dispatch, ActionType, useStore } from './store';\nimport { toast } from './toast';\nimport { DefaultToastOptions, Toast, ToastPosition } from './types';\n\nconst updateHeight = (toastId: string, height: number) => {\n  dispatch({\n    type: ActionType.UPDATE_TOAST,\n    toast: { id: toastId, height },\n  });\n};\nconst startPause = () => {\n  dispatch({\n    type: ActionType.START_PAUSE,\n    time: Date.now(),\n  });\n};\n\nconst toastTimeouts = new Map<Toast['id'], ReturnType<typeof setTimeout>>();\n\nexport const REMOVE_DELAY = 1000;\n\nconst addToRemoveQueue = (toastId: string, removeDelay = REMOVE_DELAY) => {\n  if (toastTimeouts.has(toastId)) {\n    return;\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId);\n    dispatch({\n      type: ActionType.REMOVE_TOAST,\n      toastId: toastId,\n    });\n  }, removeDelay);\n\n  toastTimeouts.set(toastId, timeout);\n};\n\nexport const useToaster = (toastOptions?: DefaultToastOptions) => {\n  const { toasts, pausedAt } = useStore(toastOptions);\n\n  useEffect(() => {\n    if (pausedAt) {\n      return;\n    }\n\n    const now = Date.now();\n    const timeouts = toasts.map((t) => {\n      if (t.duration === Infinity) {\n        return;\n      }\n\n      const durationLeft =\n        (t.duration || 0) + t.pauseDuration - (now - t.createdAt);\n\n      if (durationLeft < 0) {\n        if (t.visible) {\n          toast.dismiss(t.id);\n        }\n        return;\n      }\n      return setTimeout(() => toast.dismiss(t.id), durationLeft);\n    });\n\n    return () => {\n      timeouts.forEach((timeout) => timeout && clearTimeout(timeout));\n    };\n  }, [toasts, pausedAt]);\n\n  const endPause = useCallback(() => {\n    if (pausedAt) {\n      dispatch({ type: ActionType.END_PAUSE, time: Date.now() });\n    }\n  }, [pausedAt]);\n\n  const calculateOffset = useCallback(\n    (\n      toast: Toast,\n      opts?: {\n        reverseOrder?: boolean;\n        gutter?: number;\n        defaultPosition?: ToastPosition;\n      }\n    ) => {\n      const { reverseOrder = false, gutter = 8, defaultPosition } = opts || {};\n\n      const relevantToasts = toasts.filter(\n        (t) =>\n          (t.position || defaultPosition) ===\n            (toast.position || defaultPosition) && t.height\n      );\n      const toastIndex = relevantToasts.findIndex((t) => t.id === toast.id);\n      const toastsBefore = relevantToasts.filter(\n        (toast, i) => i < toastIndex && toast.visible\n      ).length;\n\n      const offset = relevantToasts\n        .filter((t) => t.visible)\n        .slice(...(reverseOrder ? [toastsBefore + 1] : [0, toastsBefore]))\n        .reduce((acc, t) => acc + (t.height || 0) + gutter, 0);\n\n      return offset;\n    },\n    [toasts]\n  );\n\n  useEffect(() => {\n    // Add dismissed toasts to remove queue\n    toasts.forEach((toast) => {\n      if (toast.dismissed) {\n        addToRemoveQueue(toast.id, toast.removeDelay);\n      } else {\n        // If toast becomes visible again, remove it from the queue\n        const timeout = toastTimeouts.get(toast.id);\n        if (timeout) {\n          clearTimeout(timeout);\n          toastTimeouts.delete(toast.id);\n        }\n      }\n    });\n  }, [toasts]);\n\n  return {\n    toasts,\n    handlers: {\n      updateHeight,\n      startPause,\n      endPause,\n      calculateOffset,\n    },\n  };\n};\n", "import * as React from 'react';\nimport { styled, keyframes } from 'goober';\n\nimport { Toast, ToastPosition, resolveValue, Renderable } from '../core/types';\nimport { ToastIcon } from './toast-icon';\nimport { prefersReducedMotion } from '../core/utils';\n\nconst enterAnimation = (factor: number) => `\n0% {transform: translate3d(0,${factor * -200}%,0) scale(.6); opacity:.5;}\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\n`;\n\nconst exitAnimation = (factor: number) => `\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\n100% {transform: translate3d(0,${factor * -150}%,-1px) scale(.6); opacity:0;}\n`;\n\nconst fadeInAnimation = `0%{opacity:0;} 100%{opacity:1;}`;\nconst fadeOutAnimation = `0%{opacity:1;} 100%{opacity:0;}`;\n\nconst ToastBarBase = styled('div')`\n  display: flex;\n  align-items: center;\n  background: #fff;\n  color: #363636;\n  line-height: 1.3;\n  will-change: transform;\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);\n  max-width: 350px;\n  pointer-events: auto;\n  padding: 8px 10px;\n  border-radius: 8px;\n`;\n\nconst Message = styled('div')`\n  display: flex;\n  justify-content: center;\n  margin: 4px 10px;\n  color: inherit;\n  flex: 1 1 auto;\n  white-space: pre-line;\n`;\n\ninterface ToastBarProps {\n  toast: Toast;\n  position?: ToastPosition;\n  style?: React.CSSProperties;\n  children?: (components: {\n    icon: Renderable;\n    message: Renderable;\n  }) => Renderable;\n}\n\nconst getAnimationStyle = (\n  position: ToastPosition,\n  visible: boolean\n): React.CSSProperties => {\n  const top = position.includes('top');\n  const factor = top ? 1 : -1;\n\n  const [enter, exit] = prefersReducedMotion()\n    ? [fadeInAnimation, fadeOutAnimation]\n    : [enterAnimation(factor), exitAnimation(factor)];\n\n  return {\n    animation: visible\n      ? `${keyframes(enter)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`\n      : `${keyframes(exit)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`,\n  };\n};\n\nexport const ToastBar: React.FC<ToastBarProps> = React.memo(\n  ({ toast, position, style, children }) => {\n    const animationStyle: React.CSSProperties = toast.height\n      ? getAnimationStyle(\n          toast.position || position || 'top-center',\n          toast.visible\n        )\n      : { opacity: 0 };\n\n    const icon = <ToastIcon toast={toast} />;\n    const message = (\n      <Message {...toast.ariaProps}>\n        {resolveValue(toast.message, toast)}\n      </Message>\n    );\n\n    return (\n      <ToastBarBase\n        className={toast.className}\n        style={{\n          ...animationStyle,\n          ...style,\n          ...toast.style,\n        }}\n      >\n        {typeof children === 'function' ? (\n          children({\n            icon,\n            message,\n          })\n        ) : (\n          <>\n            {icon}\n            {message}\n          </>\n        )}\n      </ToastBarBase>\n    );\n  }\n);\n", "import * as React from 'react';\nimport { styled, keyframes } from 'goober';\n\nimport { Toast } from '../core/types';\nimport { ErrorIcon, ErrorTheme } from './error';\nimport { LoaderIcon, LoaderTheme } from './loader';\nimport { CheckmarkIcon, CheckmarkTheme } from './checkmark';\n\nconst StatusWrapper = styled('div')`\n  position: absolute;\n`;\n\nconst IndicatorWrapper = styled('div')`\n  position: relative;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-width: 20px;\n  min-height: 20px;\n`;\n\nconst enter = keyframes`\nfrom {\n  transform: scale(0.6);\n  opacity: 0.4;\n}\nto {\n  transform: scale(1);\n  opacity: 1;\n}`;\n\nexport const AnimatedIconWrapper = styled('div')`\n  position: relative;\n  transform: scale(0.6);\n  opacity: 0.4;\n  min-width: 20px;\n  animation: ${enter} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n`;\n\nexport type IconThemes = Partial<{\n  success: CheckmarkTheme;\n  error: ErrorTheme;\n  loading: LoaderTheme;\n}>;\n\nexport const ToastIcon: React.FC<{\n  toast: Toast;\n}> = ({ toast }) => {\n  const { icon, type, iconTheme } = toast;\n  if (icon !== undefined) {\n    if (typeof icon === 'string') {\n      return <AnimatedIconWrapper>{icon}</AnimatedIconWrapper>;\n    } else {\n      return icon;\n    }\n  }\n\n  if (type === 'blank') {\n    return null;\n  }\n\n  return (\n    <IndicatorWrapper>\n      <LoaderIcon {...iconTheme} />\n      {type !== 'loading' && (\n        <StatusWrapper>\n          {type === 'error' ? (\n            <ErrorIcon {...iconTheme} />\n          ) : (\n            <CheckmarkIcon {...iconTheme} />\n          )}\n        </StatusWrapper>\n      )}\n    </IndicatorWrapper>\n  );\n};\n", "import { styled, keyframes } from 'goober';\n\nconst circleAnimation = keyframes`\nfrom {\n  transform: scale(0) rotate(45deg);\n\topacity: 0;\n}\nto {\n transform: scale(1) rotate(45deg);\n  opacity: 1;\n}`;\n\nconst firstLineAnimation = keyframes`\nfrom {\n  transform: scale(0);\n  opacity: 0;\n}\nto {\n  transform: scale(1);\n  opacity: 1;\n}`;\n\nconst secondLineAnimation = keyframes`\nfrom {\n  transform: scale(0) rotate(90deg);\n\topacity: 0;\n}\nto {\n  transform: scale(1) rotate(90deg);\n\topacity: 1;\n}`;\n\nexport interface ErrorTheme {\n  primary?: string;\n  secondary?: string;\n}\n\nexport const ErrorIcon = styled('div')<ErrorTheme>`\n  width: 20px;\n  opacity: 0;\n  height: 20px;\n  border-radius: 10px;\n  background: ${(p) => p.primary || '#ff4b4b'};\n  position: relative;\n  transform: rotate(45deg);\n\n  animation: ${circleAnimation} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n  animation-delay: 100ms;\n\n  &:after,\n  &:before {\n    content: '';\n    animation: ${firstLineAnimation} 0.15s ease-out forwards;\n    animation-delay: 150ms;\n    position: absolute;\n    border-radius: 3px;\n    opacity: 0;\n    background: ${(p) => p.secondary || '#fff'};\n    bottom: 9px;\n    left: 4px;\n    height: 2px;\n    width: 12px;\n  }\n\n  &:before {\n    animation: ${secondLineAnimation} 0.15s ease-out forwards;\n    animation-delay: 180ms;\n    transform: rotate(90deg);\n  }\n`;\n", "import { styled, keyframes } from 'goober';\n\nconst rotate = keyframes`\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n`;\n\nexport interface LoaderTheme {\n  primary?: string;\n  secondary?: string;\n}\n\nexport const LoaderIcon = styled('div')<LoaderTheme>`\n  width: 12px;\n  height: 12px;\n  box-sizing: border-box;\n  border: 2px solid;\n  border-radius: 100%;\n  border-color: ${(p) => p.secondary || '#e0e0e0'};\n  border-right-color: ${(p) => p.primary || '#616161'};\n  animation: ${rotate} 1s linear infinite;\n`;\n", "import { styled, keyframes } from 'goober';\n\nconst circleAnimation = keyframes`\nfrom {\n  transform: scale(0) rotate(45deg);\n\topacity: 0;\n}\nto {\n  transform: scale(1) rotate(45deg);\n\topacity: 1;\n}`;\n\nconst checkmarkAnimation = keyframes`\n0% {\n\theight: 0;\n\twidth: 0;\n\topacity: 0;\n}\n40% {\n  height: 0;\n\twidth: 6px;\n\topacity: 1;\n}\n100% {\n  opacity: 1;\n  height: 10px;\n}`;\n\nexport interface CheckmarkTheme {\n  primary?: string;\n  secondary?: string;\n}\n\nexport const CheckmarkIcon = styled('div')<CheckmarkTheme>`\n  width: 20px;\n  opacity: 0;\n  height: 20px;\n  border-radius: 10px;\n  background: ${(p) => p.primary || '#61d345'};\n  position: relative;\n  transform: rotate(45deg);\n\n  animation: ${circleAnimation} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n  animation-delay: 100ms;\n  &:after {\n    content: '';\n    box-sizing: border-box;\n    animation: ${checkmarkAnimation} 0.2s ease-out forwards;\n    opacity: 0;\n    animation-delay: 200ms;\n    position: absolute;\n    border-right: 2px solid;\n    border-bottom: 2px solid;\n    border-color: ${(p) => p.secondary || '#fff'};\n    bottom: 6px;\n    left: 6px;\n    height: 10px;\n    width: 6px;\n  }\n`;\n", "import { css, setup } from 'goober';\nimport * as React from 'react';\nimport {\n  resolveValue,\n  ToasterProps,\n  ToastPosition,\n  ToastWrapperProps,\n} from '../core/types';\nimport { useToaster } from '../core/use-toaster';\nimport { prefersReducedMotion } from '../core/utils';\nimport { ToastBar } from './toast-bar';\n\nsetup(React.createElement);\n\nconst ToastWrapper = ({\n  id,\n  className,\n  style,\n  onHeightUpdate,\n  children,\n}: ToastWrapperProps) => {\n  const ref = React.useCallback(\n    (el: HTMLElement | null) => {\n      if (el) {\n        const updateHeight = () => {\n          const height = el.getBoundingClientRect().height;\n          onHeightUpdate(id, height);\n        };\n        updateHeight();\n        new MutationObserver(updateHeight).observe(el, {\n          subtree: true,\n          childList: true,\n          characterData: true,\n        });\n      }\n    },\n    [id, onHeightUpdate]\n  );\n\n  return (\n    <div ref={ref} className={className} style={style}>\n      {children}\n    </div>\n  );\n};\n\nconst getPositionStyle = (\n  position: ToastPosition,\n  offset: number\n): React.CSSProperties => {\n  const top = position.includes('top');\n  const verticalStyle: React.CSSProperties = top ? { top: 0 } : { bottom: 0 };\n  const horizontalStyle: React.CSSProperties = position.includes('center')\n    ? {\n        justifyContent: 'center',\n      }\n    : position.includes('right')\n    ? {\n        justifyContent: 'flex-end',\n      }\n    : {};\n  return {\n    left: 0,\n    right: 0,\n    display: 'flex',\n    position: 'absolute',\n    transition: prefersReducedMotion()\n      ? undefined\n      : `all 230ms cubic-bezier(.21,1.02,.73,1)`,\n    transform: `translateY(${offset * (top ? 1 : -1)}px)`,\n    ...verticalStyle,\n    ...horizontalStyle,\n  };\n};\n\nconst activeClass = css`\n  z-index: 9999;\n  > * {\n    pointer-events: auto;\n  }\n`;\n\nconst DEFAULT_OFFSET = 16;\n\nexport const Toaster: React.FC<ToasterProps> = ({\n  reverseOrder,\n  position = 'top-center',\n  toastOptions,\n  gutter,\n  children,\n  containerStyle,\n  containerClassName,\n}) => {\n  const { toasts, handlers } = useToaster(toastOptions);\n\n  return (\n    <div\n      id=\"_rht_toaster\"\n      style={{\n        position: 'fixed',\n        zIndex: 9999,\n        top: DEFAULT_OFFSET,\n        left: DEFAULT_OFFSET,\n        right: DEFAULT_OFFSET,\n        bottom: DEFAULT_OFFSET,\n        pointerEvents: 'none',\n        ...containerStyle,\n      }}\n      className={containerClassName}\n      onMouseEnter={handlers.startPause}\n      onMouseLeave={handlers.endPause}\n    >\n      {toasts.map((t) => {\n        const toastPosition = t.position || position;\n        const offset = handlers.calculateOffset(t, {\n          reverseOrder,\n          gutter,\n          defaultPosition: position,\n        });\n        const positionStyle = getPositionStyle(toastPosition, offset);\n\n        return (\n          <ToastWrapper\n            id={t.id}\n            key={t.id}\n            onHeightUpdate={handlers.updateHeight}\n            className={t.visible ? activeClass : ''}\n            style={positionStyle}\n          >\n            {t.type === 'custom' ? (\n              resolveValue(t.message, t)\n            ) : children ? (\n              children(t)\n            ) : (\n              <ToastBar toast={t} position={toastPosition} />\n            )}\n          </ToastWrapper>\n        );\n      })}\n    </div>\n  );\n};\n", "import { toast } from './core/toast';\n\nexport * from './headless';\n\nexport { ToastBar } from './components/toast-bar';\nexport { ToastIcon } from './components/toast-icon';\nexport { Toaster } from './components/toaster';\nexport { CheckmarkIcon } from './components/checkmark';\nexport { ErrorIcon } from './components/error';\nexport { LoaderIcon } from './components/loader';\n\nexport { toast };\nexport default toast;\n"], "names": ["isFunction", "valOrFunction", "resolveValue", "arg", "genId", "count", "prefersReducedMotion", "shouldReduceMotion", "mediaQuery", "useEffect", "useState", "useRef", "TOAST_LIMIT", "reducer", "state", "action", "TOAST_LIMIT", "t", "toast", "toastId", "diff", "listeners", "memoryState", "dispatch", "listener", "defaultTimeouts", "useStore", "toastOptions", "setState", "useState", "initial", "useRef", "useEffect", "index", "mergedToasts", "_a", "_b", "_c", "createToast", "message", "type", "opts", "genId", "createHandler", "options", "toast", "dispatch", "toastId", "promise", "msgs", "id", "p", "successMessage", "resolveValue", "e", "errorMessage", "useEffect", "useCallback", "updateHeight", "toastId", "height", "dispatch", "startPause", "toastTimeouts", "REMOVE_DELAY", "addToRemoveQueue", "<PERSON><PERSON><PERSON><PERSON>", "timeout", "useToaster", "toastOptions", "toasts", "pausedAt", "useStore", "useEffect", "now", "timeouts", "t", "durationLeft", "toast", "endPause", "useCallback", "calculateOffset", "opts", "reverseOrder", "gutter", "defaultPosition", "relevantToasts", "toastIndex", "toastsBefore", "i", "acc", "React", "styled", "keyframes", "React", "styled", "keyframes", "styled", "keyframes", "circleAnimation", "firstLineAnimation", "secondLineAnimation", "ErrorIcon", "p", "styled", "keyframes", "rotate", "LoaderIcon", "p", "styled", "keyframes", "circleAnimation", "checkmarkAnimation", "CheckmarkIcon", "p", "StatusWrapper", "styled", "IndicatorWrapper", "enter", "keyframes", "AnimatedIconWrapper", "ToastIcon", "toast", "icon", "type", "iconTheme", "LoaderIcon", "ErrorIcon", "CheckmarkIcon", "enterAnimation", "factor", "exitAnimation", "fadeInAnimation", "fadeOutAnimation", "ToastBarBase", "styled", "Message", "getAnimationStyle", "position", "visible", "enter", "exit", "prefersReducedMotion", "keyframes", "ToastBar", "toast", "style", "children", "animationStyle", "icon", "ToastIcon", "message", "resolveValue", "css", "setup", "React", "setup", "ToastWrapper", "id", "className", "style", "onHeightUpdate", "children", "ref", "el", "updateHeight", "height", "getPositionStyle", "position", "offset", "top", "verticalStyle", "horizontalStyle", "prefersReducedMotion", "activeClass", "css", "DEFAULT_OFFSET", "Toaster", "reverseOrder", "toastOptions", "gutter", "containerStyle", "containerClassName", "toasts", "handlers", "useToaster", "t", "toastPosition", "positionStyle", "resolveValue", "ToastBar", "src_default", "toast"], "mappings": ";;;;;;;;;;;;;AEAA,OAAS,aAAAS,EAAW,YAAAC,EAAU,UAAAC,MAAc;AGC5C,OAAS,UAAAiF,EAAQ,aAAAC,MAAiB,SCDlC,UAAYC,MAAW,QACvB,OAAS,UAAAC,EAAQ,aAAAC,OAAiB,SCDlC,OAAS,UAAAC,GAAQ,aAAAC,MAAiB;;APuBlC,IAAMlG,IACJC,KAEA,OAAOA,KAAkB,YAEdC,IAAe,CAC1BD,GACAE,IACYH,EAAWC,CAAa,IAAIA,EAAcE,CAAG,IAAIF;AC/BxD,IAAMG,IAAAA,CAAS,IAAM;IAC1B,IAAIC,IAAQ;IACZ,OAAO,IAAA,CACG,EAAEA,CAAAA,EAAO,QAAA,CAAS;AAE9B,CAAA,EAAG,GAEUC,IAAAA,CAAwB,IAAM;IAEzC,IAAIC;IAEJ,OAAO,IAAM;QACX,IAAIA,MAAuB,KAAA,KAAa,OAAO,OAAW,KAAa;YACrE,IAAMC,IAAa,WAAW,kCAAkC;YAChED,IAAqB,CAACC,KAAcA,EAAW,OAAA;QAAA;QAEjD,OAAOD;IACT;AACF,CAAA,EAAG;;ACfH,IAAMK,IAAc;AA+Cb,IAAMC,IAAU,CAACC,GAAcC,IAA0B;IAC9D,OAAQA,EAAO,IAAA,CAAM;QACnB,IAAK,CAAA;YACH,OAAO;gBACL,GAAGD,CAAAA;gBACH,QAAQ;oBAACC,EAAO,KAAA,CAAO;uBAAGD,EAAM,MAAM;iBAAA,CAAE,KAAA,CAAM,GAAGE,CAAW;YAC9D;QAEF,IAAK,CAAA;YACH,OAAO;gBACL,GAAGF,CAAAA;gBACH,QAAQA,EAAM,MAAA,CAAO,GAAA,EAAKG,IACxBA,EAAE,EAAA,KAAOF,EAAO,KAAA,CAAM,EAAA,GAAK;wBAAE,GAAGE,CAAAA;wBAAG,GAAGF,EAAO;oBAAM,IAAIE,CACzD;YACF;QAEF,IAAK,CAAA;YACH,IAAM,EAAE,OAAAC,CAAM,EAAA,GAAIH;YAClB,OAAOF,EAAQC,GAAO;gBACpB,MAAMA,EAAM,MAAA,CAAO,IAAA,EAAMG,IAAMA,EAAE,EAAA,KAAOC,EAAM,EAAE,IAC5C,IACA;gBACJ,OAAAA;YACF,CAAC;QAEH,IAAK,CAAA;YACH,IAAM,EAAE,SAAAC,CAAQ,EAAA,GAAIJ;YAEpB,OAAO;gBACL,GAAGD,CAAAA;gBACH,QAAQA,EAAM,MAAA,CAAO,GAAA,EAAKG,IACxBA,EAAE,EAAA,KAAOE,KAAWA,MAAY,KAAA,IAC5B;wBACE,GAAGF,CAAAA;wBACH,WAAW,CAAA;wBACX,SAAS,CAAA;oBACX,IACAA,CACN;YACF;QACF,IAAK,CAAA;YACH,OAAIF,EAAO,OAAA,KAAY,KAAA,IACd;gBACL,GAAGD,CAAAA;gBACH,QAAQ,CAAC;YACX,IAEK;gBACL,GAAGA,CAAAA;gBACH,QAAQA,EAAM,MAAA,CAAO,MAAA,EAAQG,IAAMA,EAAE,EAAA,KAAOF,EAAO,OAAO;YAC5D;QAEF,IAAK,CAAA;YACH,OAAO;gBACL,GAAGD,CAAAA;gBACH,UAAUC,EAAO;YACnB;QAEF,IAAK,CAAA;YACH,IAAMK,IAAOL,EAAO,IAAA,GAAA,CAAQD,EAAM,QAAA,IAAY,CAAA;YAE9C,OAAO;gBACL,GAAGA,CAAAA;gBACH,UAAU,KAAA;gBACV,QAAQA,EAAM,MAAA,CAAO,GAAA,EAAKG,IAAAA,CAAO;wBAC/B,GAAGA,CAAAA;wBACH,eAAeA,EAAE,aAAA,GAAgBG;oBACnC,CAAA,CAAE;YACJ;IACJ;AACF,GAEMC,IAA2C,CAAC,CAAA,EAE9CC,IAAqB;IAAE,QAAQ,CAAC,CAAA;IAAG,UAAU,KAAA;AAAU,GAE9CC,KAAYR,GAAmB;IAC1CO,IAAcT,EAAQS,GAAaP,CAAM,GACzCM,EAAU,OAAA,EAASG,GAAa;QAC9BA,EAASF,CAAW;IACtB,CAAC;AACH,GAEaG,IAET;IACF,OAAO;IACP,OAAO;IACP,SAAS;IACT,SAAS,IAAA;IACT,QAAQ;AACV,GAEaC,IAAW,CAACC,IAAoC,CAAC,CAAA,GAAa;IACzE,IAAM,CAACb,GAAOc,CAAQ,CAAA,wNAAIC,EAAgBP,CAAW,GAC/CQ,KAAUC,kNAAAA,EAAOT,CAAW;0NAGlCU,EAAU,IAAA,CACJF,EAAQ,OAAA,KAAYR,KACtBM,EAASN,CAAW,GAEtBD,EAAU,IAAA,CAAKO,CAAQ,GAChB,IAAM;YACX,IAAMK,IAAQZ,EAAU,OAAA,CAAQO,CAAQ;YACpCK,IAAQ,CAAA,KACVZ,EAAU,MAAA,CAAOY,GAAO,CAAC;QAE7B,CAAA,GACC,CAAC,CAAC;IAEL,IAAMC,IAAepB,EAAM,MAAA,CAAO,GAAA,EAAKG,GAAG;QAjK5C,IAAAkB,GAAAC,GAAAC;QAiKgD,OAAA;YAC5C,GAAGV,CAAAA;YACH,GAAGA,CAAAA,CAAaV,EAAE,IAAI,CAAA;YACtB,GAAGA,CAAAA;YACH,aACEA,EAAE,WAAA,IAAA,CAAA,CACFkB,IAAAR,CAAAA,CAAaV,EAAE,IAAI,CAAA,KAAnB,OAAA,KAAA,IAAAkB,EAAsB,WAAA,KAAA,CACtBR,KAAA,OAAA,KAAA,IAAAA,EAAc,WAAA;YAChB,UACEV,EAAE,QAAA,IAAA,CAAA,CACFmB,IAAAT,CAAAA,CAAaV,EAAE,IAAI,CAAA,KAAnB,OAAA,KAAA,IAAAmB,EAAsB,QAAA,KAAA,CACtBT,KAAA,OAAA,KAAA,IAAAA,EAAc,QAAA,KACdF,CAAAA,CAAgBR,EAAE,IAAI,CAAA;YACxB,OAAO;gBACL,GAAGU,EAAa,KAAA;gBAChB,GAAA,CAAGU,IAAAV,CAAAA,CAAaV,EAAE,IAAI,CAAA,KAAnB,OAAA,KAAA,IAAAoB,EAAsB,KAAA;gBACzB,GAAGpB,EAAE;YACP;QACF;IAAA,CAAE;IAEF,OAAO;QACL,GAAGH,CAAAA;QACH,QAAQoB;IACV;AACF;ACzKA,IAAMI,IAAc,CAClBC,GACAC,IAAkB,OAAA,EAClBC,IAAAA,CACW;QACX,WAAW,KAAK,GAAA,CAAI;QACpB,SAAS,CAAA;QACT,WAAW,CAAA;QACX,MAAAD;QACA,WAAW;YACT,MAAM;YACN,aAAa;QACf;QACA,SAAAD;QACA,eAAe;QACf,GAAGE,CAAAA;QACH,IAAA,CAAIA,KAAA,OAAA,KAAA,IAAAA,EAAM,EAAA,KAAMC,EAAM;IACxB,CAAA,GAEMC,KACHH,IACD,CAACD,GAASK,IAAY;QACpB,IAAMC,IAAQP,EAAYC,GAASC,GAAMI,CAAO;QAChD,OAAAE,EAAS;YAAE,MAAA;YAA+B,OAAAD;QAAM,CAAC,GAC1CA,EAAM;IACf,GAEIA,IAAQ,CAACN,GAAkBE,IAC/BE,EAAc,OAAO,EAAEJ,GAASE,CAAI;AAEtCI,EAAM,KAAA,GAAQF,EAAc,OAAO;AACnCE,EAAM,OAAA,GAAUF,EAAc,SAAS;AACvCE,EAAM,OAAA,GAAUF,EAAc,SAAS;AACvCE,EAAM,MAAA,GAASF,EAAc,QAAQ;AAErCE,EAAM,OAAA,IAAWE,GAAqB;IACpCD,EAAS;QACP,MAAA;QACA,SAAAC;IACF,CAAC;AACH;AAEAF,EAAM,MAAA,GAAUE,KACdD,EAAS;QAAE,MAAA;QAA+B,SAAAC;IAAQ,CAAC;AAErDF,EAAM,OAAA,GAAU,CACdG,GACAC,GAKAR,IACG;IACH,IAAMS,IAAKL,EAAM,OAAA,CAAQI,EAAK,OAAA,EAAS;QAAE,GAAGR,CAAAA;QAAM,GAAGA,KAAA,OAAA,KAAA,IAAAA,EAAM;IAAQ,CAAC;IAEpE,OAAI,OAAOO,KAAY,cAAA,CACrBA,IAAUA,EAAQ,CAAA,GAGpBA,EACG,IAAA,EAAMG,GAAM;QACX,IAAMC,IAAiBH,EAAK,OAAA,GACxBI,EAAaJ,EAAK,OAAA,EAASE,CAAC,IAC5B,KAAA;QAEJ,OAAIC,IACFP,EAAM,OAAA,CAAQO,GAAgB;YAC5B,IAAAF;YACA,GAAGT,CAAAA;YACH,GAAGA,KAAA,OAAA,KAAA,IAAAA,EAAM;QACX,CAAC,IAEDI,EAAM,OAAA,CAAQK,CAAE,GAEXC;IACT,CAAC,EACA,KAAA,EAAOG,GAAM;QACZ,IAAMC,IAAeN,EAAK,KAAA,GAAQI,EAAaJ,EAAK,KAAA,EAAOK,CAAC,IAAI,KAAA;QAE5DC,IACFV,EAAM,KAAA,CAAMU,GAAc;YACxB,IAAAL;YACA,GAAGT,CAAAA;YACH,GAAGA,KAAA,OAAA,KAAA,IAAAA,EAAM,KACX,CAAC;aAEDI,EAAM,OAAA,CAAQK,CAAE;IAEpB,CAAC,GAEIF;AACT,EC5GA,OAAS,aAAAQ,EAAW,eAAAC,MAAmB;;AAKvC,IAAMC,IAAe,CAACC,GAAiBC,IAAmB;IACxDC,EAAS;QACP,MAAA;QACA,OAAO;YAAE,IAAIF;YAAS,QAAAC;QAAO;IAC/B,CAAC;AACH,GACME,IAAa,IAAM;IACvBD,EAAS;QACP,MAAA;QACA,MAAM,KAAK,GAAA,CAAI;IACjB,CAAC;AACH,GAEME,IAAgB,IAAI,KAEbC,IAAe,KAEtBC,KAAmB,CAACN,GAAiBO,IAAcF,CAAAA,GAAiB;IACxE,IAAID,EAAc,GAAA,CAAIJ,CAAO,GAC3B;IAGF,IAAMQ,IAAU,WAAW,IAAM;QAC/BJ,EAAc,MAAA,CAAOJ,CAAO,GAC5BE,EAAS;YACP,MAAA;YACA,SAASF;QACX,CAAC;IACH,GAAGO,CAAW;IAEdH,EAAc,GAAA,CAAIJ,GAASQ,CAAO;AACpC,GAEaC,KAAcC,GAAuC;IAChE,IAAM,EAAE,QAAAC,CAAAA,EAAQ,UAAAC,CAAS,EAAA,GAAIC,EAASH,CAAY;KAElDI,qNAAAA,EAAU,IAAM;QACd,IAAIF,GACF;QAGF,IAAMG,IAAM,KAAK,GAAA,CAAI,GACfC,IAAWL,EAAO,GAAA,EAAKM,GAAM;YACjC,IAAIA,EAAE,QAAA,KAAa,IAAA,GACjB;YAGF,IAAMC,IAAAA,CACHD,EAAE,QAAA,IAAY,CAAA,IAAKA,EAAE,aAAA,GAAA,CAAiBF,IAAME,EAAE,SAAA;YAEjD,IAAIC,IAAe,GAAG;gBAChBD,EAAE,OAAA,IACJE,EAAM,OAAA,CAAQF,EAAE,EAAE;gBAEpB;YAAA;YAEF,OAAO,WAAW,IAAME,EAAM,OAAA,CAAQF,EAAE,EAAE,GAAGC,CAAY;QAC3D,CAAC;QAED,OAAO,IAAM;YACXF,EAAS,OAAA,EAASR,IAAYA,KAAW,aAAaA,CAAO,CAAC;QAChE;IACF,GAAG;QAACG;QAAQC,CAAQ;KAAC;IAErB,IAAMQ,4NAAWC,EAAY,IAAM;QAC7BT,KACFV,EAAS;YAAE,MAAA;YAA4B,MAAM,KAAK,GAAA,CAAI;QAAE,CAAC;IAE7D,GAAG;QAACU,CAAQ;KAAC,GAEPU,4NAAkBD,EACtB,CACEF,GACAI,IAKG;QACH,IAAM,EAAE,cAAAC,IAAe,CAAA,CAAA,EAAO,QAAAC,IAAS,CAAA,EAAG,iBAAAC,CAAgB,EAAA,GAAIH,KAAQ,CAAC,GAEjEI,IAAiBhB,EAAO,MAAA,EAC3BM,IAAAA,CACEA,EAAE,QAAA,IAAYS,CAAAA,MAAAA,CACZP,EAAM,QAAA,IAAYO,CAAAA,KAAoBT,EAAE,MAC/C,GACMW,IAAaD,EAAe,SAAA,EAAWV,IAAMA,EAAE,EAAA,KAAOE,EAAM,EAAE,GAC9DU,IAAeF,EAAe,MAAA,CAClC,CAACR,GAAOW,IAAMA,IAAIF,KAAcT,EAAM,OACxC,EAAE,MAAA;QAOF,OALeQ,EACZ,MAAA,EAAQV,IAAMA,EAAE,OAAO,EACvB,KAAA,CAAM,GAAIO,IAAe;YAACK,IAAe,CAAC;SAAA,GAAI;YAAC;YAAGA,CAAY;SAAE,EAChE,MAAA,CAAO,CAACE,GAAKd,IAAMc,IAAAA,CAAOd,EAAE,MAAA,IAAU,CAAA,IAAKQ,GAAQ,CAAC;IAGzD,GACA;QAACd,CAAM;KACT;IAEA,6NAAAG,EAAU,IAAM;QAEdH,EAAO,OAAA,EAASQ,GAAU;YACxB,IAAIA,EAAM,SAAA,EACRb,GAAiBa,EAAM,EAAA,EAAIA,EAAM,WAAW;iBACvC;gBAEL,IAAMX,IAAUJ,EAAc,GAAA,CAAIe,EAAM,EAAE;gBACtCX,KAAAA,CACF,aAAaA,CAAO,GACpBJ,EAAc,MAAA,CAAOe,EAAM,EAAE,CAAA;YAAA;QAGnC,CAAC;IACH,GAAG;QAACR,CAAM;KAAC,GAEJ;QACL,QAAAA;QACA,UAAU;YACR,cAAAZ;YACA,YAAAI;YACA,UAAAiB;YACA,iBAAAE;QACF;IACF;AACF,ECnIA,UAAYU,MAAW;;;;;;AEEvB,IAAMQ,wJAAkBD,YAAAA,CAAAA;;;;;;;;CAAA,CAAA,EAUlBE,wJAAqBF,YAAAA,CAAAA;;;;;;;;CAAA,CAAA,EAUrBG,wJAAsBH,YAAAA,CAAAA;;;;;;;;CAAA,CAAA,EAefI,2JAAYL,SAAAA,EAAO,KAAK,CAAA,CAAA;;;;;cAAA,GAKpBM,IAAMA,EAAE,OAAA,IAAW,UAAA;;;;aAAA,EAIrBJ,GAAAA;;;;;;;eAAA,EAOEC,GAAAA;;;;;gBAAA,GAKEG,IAAMA,EAAE,SAAA,IAAa,OAAA;;;;;;;;eAAA,EAQvBF,GAAAA;;;;EClEjB,OAAS,UAAAG,GAAQ,aAAAC,OAAiB;;AAElC,IAAMC,wJAASD,YAAAA,CAAAA;;;;;;;AAAA,CAAA,EAcFE,IAAaH,gKAAAA,EAAO,KAAK,CAAA,CAAA;;;;;;gBAAA,GAMnBI,IAAMA,EAAE,SAAA,IAAa,UAAA;sBAAA,GACfA,IAAMA,EAAE,OAAA,IAAW,UAAA;aAAA,EAC7BF,GAAAA;ECxBf,OAAS,UAAAG,GAAQ,aAAAC,MAAiB;;AAElC,IAAMC,wJAAkBD,YAAAA,CAAAA;;;;;;;;CAAA,CAAA,EAUlBE,wJAAqBF,YAAAA,CAAAA;;;;;;;;;;;;;;CAAA,CAAA,EAqBdG,2JAAgBJ,SAAAA,EAAO,KAAK,CAAA,CAAA;;;;;cAAA,GAKxBK,IAAMA,EAAE,OAAA,IAAW,UAAA;;;;aAAA,EAIrBH,GAAAA;;;;;;eAAA,EAMEC,GAAAA;;;;;;kBAAA,EAMIE,KAAMA,EAAE,SAAA,IAAa,OAAA;;;;;;;AH9C1C,IAAMC,qKAAgBC,EAAO,KAAK,CAAA,CAAA;;AAAA,CAAA,EAI5BC,2JAAmBD,UAAAA,EAAO,KAAK,CAAA,CAAA;;;;;;;AAAA,CAAA,EAS/BE,wJAAQC,YAAAA,CAAAA;;;;;;;;CAAA,CAAA,EAUDC,SAAsBJ,4JAAAA,EAAO,KAAK,CAAA,CAAA;;;;;aAAA,EAKhCE,GAAAA;;AAAA,CAAA,EAUFG,IAER,CAAC,EAAE,OAAAC,CAAM,EAAA,GAAM;IAClB,IAAM,EAAE,MAAAC,CAAAA,EAAM,MAAAC,CAAAA,EAAM,WAAAC,CAAU,EAAA,GAAIH;IAClC,OAAIC,MAAS,KAAA,IACP,OAAOA,KAAS,gNACX,iBAAA,CAACH,IAAA,MAAqBG,CAAK,IAE3BA,IAIPC,MAAS,UACJ,6MAIP,gBAAA,CAACP,IAAA,4MACC,gBAAA,CAACS,GAAA;QAAY,GAAGD,CAAAA;IAAAA,CAAW,GAC1BD,MAAS,aACR,sNAAA,CAACT,IAAA,MACES,MAAS,gNACR,gBAAA,CAACG,GAAA;QAAW,GAAGF,CAAAA;IAAAA,CAAW,0MAE1B,gBAAA,CAACG,GAAA;QAAe,GAAGH,CAAAA;IAAAA,CAAW,CAElC,CAEJ;AAEJ;ADrEA,IAAMI,MAAkBC,IAAmB,CAAA;6BAAA,EACZA,IAAS,CAAA,IAAA;;AAAA,CAAA,EAIlCC,MAAiBD,IAAmB,CAAA;;+BAAA,EAETA,IAAS,CAAA,IAAA;AAAA,CAAA,EAGpCE,KAAkB,mCAClBC,KAAmB,mCAEnBC,SAAeC,4JAAAA,EAAO,KAAK,CAAA,CAAA;;;;;;;;;;;;AAAA,CAAA,EAc3BC,qKAAUD,EAAO,KAAK,CAAA,CAAA;;;;;;;AAAA,CAAA,EAmBtBE,KAAoB,CACxBC,GACAC,IACwB;IAExB,IAAMT,IADMQ,EAAS,QAAA,CAAS,KAAK,IACd,IAAI,CAAA,GAEnB,CAACE,GAAOC,CAAI,CAAA,GAAIC,EAAqB,IACvC;QAACV;QAAiBC,EAAgB;KAAA,GAClC;QAACJ,GAAeC,CAAM;QAAGC,GAAcD,CAAM,CAAC;KAAA;IAElD,OAAO;QACL,WAAWS,IACP,IAAGI,kKAAAA,EAAUH,CAAK,EAAA,4CAAA,CAAA,GAClB,sKAAGG,EAAUF,CAAI,EAAA,0CAAA;IACvB;AACF,GAEaG,0MAA0C,OAAA,CACrD,CAAC,EAAE,OAAAC,CAAAA,EAAO,UAAAP,CAAAA,EAAU,OAAAQ,CAAAA,EAAO,UAAAC,CAAS,EAAA,GAAM;IACxC,IAAMC,IAAsCH,EAAM,MAAA,GAC9CR,GACEQ,EAAM,QAAA,IAAYP,KAAY,cAC9BO,EAAM,OACR,IACA;QAAE,SAAS;IAAE,GAEXI,0MAAO,gBAAA,CAACC,GAAA;QAAU,OAAOL;IAAAA,CAAO,GAChCM,yMACJ,iBAAA,CAACf,IAAA;QAAS,GAAGS,EAAM,SAAA;IAAA,GAChBO,EAAaP,EAAM,OAAA,EAASA,CAAK,CACpC;IAGF,6MACE,gBAAA,CAACX,IAAA;QACC,WAAWW,EAAM,SAAA;QACjB,OAAO;YACL,GAAGG,CAAAA;YACH,GAAGF,CAAAA;YACH,GAAGD,EAAM;QACX;IAAA,GAEC,OAAOE,KAAa,aACnBA,EAAS;QACP,MAAAE;QACA,SAAAE;IACF,CAAC,0MAED,gBAAA,CAAA,qMAAA,CAAA,WAAA,EAAA,MACGF,GACAE,CACH,CAEJ;AAEJ,CACF,EK9GA,OAAS,OAAAE,GAAK,SAAAC,OAAa,SAC3B,UAAYC,MAAW;;;uJAWvBC,QAAAA,wMAAY,gBAAa;AAEzB,IAAMC,KAAe,CAAC,EACpB,IAAAC,CAAAA,EACA,WAAAC,CAAAA,EACA,OAAAC,CAAAA,EACA,gBAAAC,CAAAA,EACA,UAAAC,CACF,EAAA,GAAyB;IACvB,IAAMC,0MAAY,cAAA,EACfC,GAA2B;QAC1B,IAAIA,GAAI;YACN,IAAMC,IAAe,IAAM;gBACzB,IAAMC,IAASF,EAAG,qBAAA,CAAsB,EAAE,MAAA;gBAC1CH,EAAeH,GAAIQ,CAAM;YAC3B;YACAD,EAAa,GACb,IAAI,iBAAiBA,CAAY,EAAE,OAAA,CAAQD,GAAI;gBAC7C,SAAS,CAAA;gBACT,WAAW,CAAA;gBACX,eAAe,CAAA;YACjB,CAAC;QAAA;IAEL,GACA;QAACN;QAAIG,CAAc;KACrB;IAEA,OACE,sNAAA,CAAC,OAAA;QAAI,KAAKE;QAAK,WAAWJ;QAAW,OAAOC;IAAAA,GACzCE,CACH;AAEJ,GAEMK,KAAmB,CACvBC,GACAC,IACwB;IACxB,IAAMC,IAAMF,EAAS,QAAA,CAAS,KAAK,GAC7BG,IAAqCD,IAAM;QAAE,KAAK;IAAE,IAAI;QAAE,QAAQ;IAAE,GACpEE,IAAuCJ,EAAS,QAAA,CAAS,QAAQ,IACnE;QACE,gBAAgB;IAClB,IACAA,EAAS,QAAA,CAAS,OAAO,IACzB;QACE,gBAAgB;IAClB,IACA,CAAC;IACL,OAAO;QACL,MAAM;QACN,OAAO;QACP,SAAS;QACT,UAAU;QACV,YAAYK,EAAqB,IAC7B,KAAA,IACA;QACJ,WAAW,CAAA,WAAA,EAAcJ,IAAAA,CAAUC,IAAM,IAAI,CAAA,CAAA,EAAA,GAAA,CAAA;QAC7C,GAAGC,CAAAA;QACH,GAAGC;IACL;AACF,GAEME,uJAAcC,OAAAA,CAAAA;;;;;AAAA,CAAA,EAOdC,IAAiB,IAEVC,KAAkC,CAAC,EAC9C,cAAAC,CAAAA,EACA,UAAAV,IAAW,YAAA,EACX,cAAAW,CAAAA,EACA,QAAAC,CAAAA,EACA,UAAAlB,CAAAA,EACA,gBAAAmB,CAAAA,EACA,oBAAAC,CACF,EAAA,GAAM;IACJ,IAAM,EAAE,QAAAC,CAAAA,EAAQ,UAAAC,CAAS,EAAA,GAAIC,EAAWN,CAAY;IAEpD,6MACE,gBAAA,CAAC,OAAA;QACC,IAAG;QACH,OAAO;YACL,UAAU;YACV,QAAQ;YACR,KAAKH;YACL,MAAMA;YACN,OAAOA;YACP,QAAQA;YACR,eAAe;YACf,GAAGK,CACL;;QACA,WAAWC;QACX,cAAcE,EAAS,UAAA;QACvB,cAAcA,EAAS,QAAA;IAAA,GAEtBD,EAAO,GAAA,EAAKG,GAAM;QACjB,IAAMC,IAAgBD,EAAE,QAAA,IAAYlB,GAC9BC,IAASe,EAAS,eAAA,CAAgBE,GAAG;YACzC,cAAAR;YACA,QAAAE;YACA,iBAAiBZ;QACnB,CAAC,GACKoB,IAAgBrB,GAAiBoB,GAAelB,CAAM;QAE5D,4MACE,iBAAA,CAACZ,IAAA;YACC,IAAI6B,EAAE,EAAA;YACN,KAAKA,EAAE,EAAA;YACP,gBAAgBF,EAAS,YAAA;YACzB,WAAWE,EAAE,OAAA,GAAUZ,KAAc;YACrC,OAAOc;QAAAA,GAENF,EAAE,IAAA,KAAS,WACVG,EAAaH,EAAE,OAAA,EAASA,CAAC,IACvBxB,IACFA,EAASwB,CAAC,0MAEV,gBAAA,CAACI,GAAA;YAAS,OAAOJ;YAAG,UAAUC;QAAAA,CAAe,CAEjD;IAEJ,CAAC,CACH;AAEJ;ACjIA,IAAOI,KAAQC", "debugId": null}}, {"offset": {"line": 673, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/%40tanstack/react-query/src/QueryClientProvider.tsx"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\nimport type { QueryClient } from '@tanstack/query-core'\n\nexport const QueryClientContext = React.createContext<QueryClient | undefined>(\n  undefined,\n)\n\nexport const useQueryClient = (queryClient?: QueryClient) => {\n  const client = React.useContext(QueryClientContext)\n\n  if (queryClient) {\n    return queryClient\n  }\n\n  if (!client) {\n    throw new Error('No QueryClient set, use QueryClientProvider to set one')\n  }\n\n  return client\n}\n\nexport type QueryClientProviderProps = {\n  client: QueryClient\n  children?: React.ReactNode\n}\n\nexport const QueryClientProvider = ({\n  client,\n  children,\n}: QueryClientProviderProps): React.JSX.Element => {\n  React.useEffect(() => {\n    client.mount()\n    return () => {\n      client.unmount()\n    }\n  }, [client])\n\n  return (\n    <QueryClientContext.Provider value={client}>\n      {children}\n    </QueryClientContext.Provider>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AACA,YAAY,WAAW;AAuCnB;;;;AAnCG,IAAM,2NAA2B,gBAAA,CACtC,KAAA;AAGK,IAAM,iBAAiB,CAAC,gBAA8B;IAC3D,MAAM,+MAAe,aAAA,CAAW,kBAAkB;IAElD,IAAI,aAAa;QACf,OAAO;IACT;IAEA,IAAI,CAAC,QAAQ;QACX,MAAM,IAAI,MAAM,wDAAwD;IAC1E;IAEA,OAAO;AACT;AAOO,IAAM,sBAAsB,CAAC,EAClC,MAAA,EACA,QAAA,EACF,KAAmD;0MAC3C,YAAA,CAAU,MAAM;QACpB,OAAO,KAAA,CAAM;QACb,OAAO,MAAM;YACX,OAAO,OAAA,CAAQ;QACjB;IACF,GAAG;QAAC,MAAM;KAAC;IAEX,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,mBAAmB,QAAA,EAAnB;QAA4B,OAAO;QACjC;IAAA,CACH;AAEJ", "debugId": null}}, {"offset": {"line": 715, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/%40tanstack/react-query/src/infiniteQueryOptions.ts"], "sourcesContent": ["import type {\n  DataTag,\n  DefaultError,\n  InfiniteData,\n  InitialDataFunction,\n  NonUndefinedGuard,\n  Omit<PERSON>eyof,\n  Query<PERSON>ey,\n  SkipToken,\n} from '@tanstack/query-core'\nimport type { UseInfiniteQueryOptions } from './types'\n\nexport type UndefinedInitialDataInfiniteOptions<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n> = UseInfiniteQueryOptions<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey,\n  TPageParam\n> & {\n  initialData?:\n    | undefined\n    | NonUndefinedGuard<InfiniteData<TQueryFnData, TPageParam>>\n    | InitialDataFunction<\n        NonUndefinedGuard<InfiniteData<TQueryFnData, TPageParam>>\n      >\n}\n\nexport type UnusedSkipTokenInfiniteOptions<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQ<PERSON>y<PERSON>ey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n> = OmitKeyof<\n  UseInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey, TPageParam>,\n  'queryFn'\n> & {\n  queryFn?: Exclude<\n    UseInfiniteQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >['queryFn'],\n    SkipToken | undefined\n  >\n}\n\nexport type DefinedInitialDataInfiniteOptions<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n> = UseInfiniteQueryOptions<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey,\n  TPageParam\n> & {\n  initialData:\n    | NonUndefinedGuard<InfiniteData<TQueryFnData, TPageParam>>\n    | (() => NonUndefinedGuard<InfiniteData<TQueryFnData, TPageParam>>)\n    | undefined\n}\n\nexport function infiniteQueryOptions<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: DefinedInitialDataInfiniteOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n): DefinedInitialDataInfiniteOptions<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey,\n  TPageParam\n> & {\n  queryKey: DataTag<TQueryKey, InfiniteData<TQueryFnData>, TError>\n}\n\nexport function infiniteQueryOptions<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: UnusedSkipTokenInfiniteOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n): UnusedSkipTokenInfiniteOptions<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey,\n  TPageParam\n> & {\n  queryKey: DataTag<TQueryKey, InfiniteData<TQueryFnData>, TError>\n}\n\nexport function infiniteQueryOptions<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: UndefinedInitialDataInfiniteOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n): UndefinedInitialDataInfiniteOptions<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey,\n  TPageParam\n> & {\n  queryKey: DataTag<TQueryKey, InfiniteData<TQueryFnData>, TError>\n}\n\nexport function infiniteQueryOptions(options: unknown) {\n  return options\n}\n"], "names": [], "mappings": ";;;;AAkJO,SAAS,qBAAqB,OAAA,EAAkB;IACrD,OAAO;AACT", "debugId": null}}, {"offset": {"line": 728, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/%40tanstack/react-query/src/queryOptions.ts"], "sourcesContent": ["import type {\n  DataTag,\n  DefaultError,\n  InitialDataFunction,\n  NonUndefinedGuard,\n  OmitKeyof,\n  QueryFunction,\n  QueryKey,\n  SkipToken,\n} from '@tanstack/query-core'\nimport type { UseQueryOptions } from './types'\n\nexport type UndefinedInitialDataOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> = UseQueryOptions<TQueryFnData, TError, TData, TQueryKey> & {\n  initialData?:\n    | undefined\n    | InitialDataFunction<NonUndefinedGuard<TQueryFnData>>\n    | NonUndefinedGuard<TQueryFnData>\n}\n\nexport type UnusedSkipTokenOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> = OmitKeyof<\n  UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  'queryFn'\n> & {\n  queryFn?: Exclude<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>['queryFn'],\n    SkipToken | undefined\n  >\n}\n\nexport type DefinedInitialDataOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> = Omit<UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>, 'queryFn'> & {\n  initialData:\n    | NonUndefinedGuard<TQueryFnData>\n    | (() => NonUndefinedGuard<TQueryFnData>)\n  queryFn?: QueryFunction<TQueryFnData, TQueryKey>\n}\n\nexport function queryOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: DefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n): DefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey> & {\n  queryKey: DataTag<TQueryKey, TQueryFnData, TError>\n}\n\nexport function queryOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UnusedSkipTokenOptions<TQueryFnData, TError, TData, TQueryKey>,\n): UnusedSkipTokenOptions<TQueryFnData, TError, TData, TQueryKey> & {\n  queryKey: DataTag<TQueryKey, TQueryFnData, TError>\n}\n\nexport function queryOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UndefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n): UndefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey> & {\n  queryKey: DataTag<TQueryKey, TQueryFnData, TError>\n}\n\nexport function queryOptions(options: unknown) {\n  return options\n}\n"], "names": [], "mappings": ";;;;AAoFO,SAAS,aAAa,OAAA,EAAkB;IAC7C,OAAO;AACT", "debugId": null}}, {"offset": {"line": 741, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/%40tanstack/react-query/src/QueryErrorResetBoundary.tsx"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\n// CONTEXT\nexport type QueryErrorResetFunction = () => void\nexport type QueryErrorIsResetFunction = () => boolean\nexport type QueryErrorClearResetFunction = () => void\n\nexport interface QueryErrorResetBoundaryValue {\n  clearReset: QueryErrorClearResetFunction\n  isReset: QueryErrorIsResetFunction\n  reset: QueryErrorResetFunction\n}\n\nfunction createValue(): QueryErrorResetBoundaryValue {\n  let isReset = false\n  return {\n    clearReset: () => {\n      isReset = false\n    },\n    reset: () => {\n      isReset = true\n    },\n    isReset: () => {\n      return isReset\n    },\n  }\n}\n\nconst QueryErrorResetBoundaryContext = React.createContext(createValue())\n\n// HOOK\n\nexport const useQueryErrorResetBoundary = () =>\n  React.useContext(QueryErrorResetBoundaryContext)\n\n// COMPONENT\n\nexport type QueryErrorResetBoundaryFunction = (\n  value: QueryErrorResetBoundaryValue,\n) => React.ReactNode\n\nexport interface QueryErrorResetBoundaryProps {\n  children: QueryErrorResetBoundaryFunction | React.ReactNode\n}\n\nexport const QueryErrorResetBoundary = ({\n  children,\n}: QueryErrorResetBoundaryProps) => {\n  const [value] = React.useState(() => createValue())\n  return (\n    <QueryErrorResetBoundaryContext.Provider value={value}>\n      {typeof children === 'function' ? children(value) : children}\n    </QueryErrorResetBoundaryContext.Provider>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA,YAAY,WAAW;AAkDnB;;;;AArCJ,SAAS,cAA4C;IACnD,IAAI,UAAU;IACd,OAAO;QACL,YAAY,MAAM;YAChB,UAAU;QACZ;QACA,OAAO,MAAM;YACX,UAAU;QACZ;QACA,SAAS,MAAM;YACb,OAAO;QACT;IACF;AACF;AAEA,IAAM,uOAAuC,gBAAA,CAAc,YAAY,CAAC;AAIjE,IAAM,6BAA6B,0MAClC,aAAA,CAAW,8BAA8B;AAY1C,IAAM,0BAA0B,CAAC,EACtC,QAAA,EACF,KAAoC;IAClC,MAAM,CAAC,KAAK,CAAA,yMAAU,WAAA,CAAS,IAAM,YAAY,CAAC;IAClD,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,+BAA+B,QAAA,EAA/B;QAAwC;QACtC,UAAA,OAAO,aAAa,aAAa,SAAS,KAAK,IAAI;IAAA,CACtD;AAEJ", "debugId": null}}, {"offset": {"line": 780, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/%40tanstack/react-query/src/errorBoundaryUtils.ts"], "sourcesContent": ["'use client'\nimport * as React from 'react'\nimport { shouldThrowError } from '@tanstack/query-core'\nimport type {\n  DefaultedQueryObserverOptions,\n  Query,\n  QueryKey,\n  QueryObserverResult,\n  ThrowOnError,\n} from '@tanstack/query-core'\nimport type { QueryErrorResetBoundaryValue } from './QueryErrorResetBoundary'\n\nexport const ensurePreventErrorBoundaryRetry = <\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>(\n  options: DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) => {\n  if (\n    options.suspense ||\n    options.throwOnError ||\n    options.experimental_prefetchInRender\n  ) {\n    // Prevent retrying failed query if the error boundary has not been reset yet\n    if (!errorResetBoundary.isReset()) {\n      options.retryOnMount = false\n    }\n  }\n}\n\nexport const useClearResetErrorBoundary = (\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) => {\n  React.useEffect(() => {\n    errorResetBoundary.clearReset()\n  }, [errorResetBoundary])\n}\n\nexport const getHasError = <\n  TData,\n  TError,\n  TQueryFnData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>({\n  result,\n  errorResetBoundary,\n  throwOnError,\n  query,\n  suspense,\n}: {\n  result: QueryObserverResult<TData, TError>\n  errorResetBoundary: QueryErrorResetBoundaryValue\n  throwOnError: ThrowOnError<TQueryFnData, TError, TQueryData, TQueryKey>\n  query: Query<TQueryFnData, TError, TQueryData, TQueryKey> | undefined\n  suspense: boolean | undefined\n}) => {\n  return (\n    result.isError &&\n    !errorResetBoundary.isReset() &&\n    !result.isFetching &&\n    query &&\n    ((suspense && result.data === undefined) ||\n      shouldThrowError(throwOnError, [result.error, query]))\n  )\n}\n"], "names": [], "mappings": ";;;;;;AACA,YAAY,WAAW;AACvB,SAAS,wBAAwB;;;;AAU1B,IAAM,kCAAkC,CAO7C,SAOA,uBACG;IACH,IACE,QAAQ,QAAA,IACR,QAAQ,YAAA,IACR,QAAQ,6BAAA,EACR;QAEA,IAAI,CAAC,mBAAmB,OAAA,CAAQ,GAAG;YACjC,QAAQ,YAAA,GAAe;QACzB;IACF;AACF;AAEO,IAAM,6BAA6B,CACxC,uBACG;0MACG,YAAA,CAAU,MAAM;QACpB,mBAAmB,UAAA,CAAW;IAChC,GAAG;QAAC,kBAAkB;KAAC;AACzB;AAEO,IAAM,cAAc,CAMzB,EACA,MAAA,EACA,kBAAA,EACA,YAAA,EACA,KAAA,EACA,QAAA,EACF,KAMM;IACJ,OACE,OAAO,OAAA,IACP,CAAC,mBAAmB,OAAA,CAAQ,KAC5B,CAAC,OAAO,UAAA,IACR,SAAA,CACE,YAAY,OAAO,IAAA,KAAS,KAAA,iLAC5B,mBAAA,EAAiB,cAAc;QAAC,OAAO,KAAA;QAAO,KAAK;KAAC,CAAA;AAE1D", "debugId": null}}, {"offset": {"line": 817, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/%40tanstack/react-query/src/IsRestoringProvider.ts"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\nconst IsRestoringContext = React.createContext(false)\n\nexport const useIsRestoring = () => React.useContext(IsRestoringContext)\nexport const IsRestoringProvider = IsRestoringContext.Provider\n"], "names": [], "mappings": ";;;;;AACA,YAAY,WAAW;;;AAEvB,IAAM,2NAA2B,gBAAA,CAAc,KAAK;AAE7C,IAAM,iBAAiB,0MAAY,aAAA,CAAW,kBAAkB;AAChE,IAAM,sBAAsB,mBAAmB,QAAA", "debugId": null}}, {"offset": {"line": 834, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/%40tanstack/react-query/src/suspense.ts"], "sourcesContent": ["import type {\n  DefaultError,\n  DefaultedQueryObserverOptions,\n  Query,\n  QueryKey,\n  QueryObserver,\n  QueryObserverResult,\n} from '@tanstack/query-core'\nimport type { QueryErrorResetBoundaryValue } from './QueryErrorResetBoundary'\n\nexport const defaultThrowOnError = <\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  _error: TError,\n  query: Query<TQueryFnData, TError, TData, TQueryKey>,\n) => query.state.data === undefined\n\nexport const ensureSuspenseTimers = (\n  defaultedOptions: DefaultedQueryObserverOptions<any, any, any, any, any>,\n) => {\n  if (defaultedOptions.suspense) {\n    // Handle staleTime to ensure minimum 1000ms in Suspense mode\n    // This prevents unnecessary refetching when components remount after suspending\n\n    const clamp = (value: number | 'static' | undefined) =>\n      value === 'static' ? value : Math.max(value ?? 1000, 1000)\n\n    const originalStaleTime = defaultedOptions.staleTime\n    defaultedOptions.staleTime =\n      typeof originalStaleTime === 'function'\n        ? (...args) => clamp(originalStaleTime(...args))\n        : clamp(originalStaleTime)\n\n    if (typeof defaultedOptions.gcTime === 'number') {\n      defaultedOptions.gcTime = Math.max(defaultedOptions.gcTime, 1000)\n    }\n  }\n}\n\nexport const willFetch = (\n  result: QueryObserverResult<any, any>,\n  isRestoring: boolean,\n) => result.isLoading && result.isFetching && !isRestoring\n\nexport const shouldSuspend = (\n  defaultedOptions:\n    | DefaultedQueryObserverOptions<any, any, any, any, any>\n    | undefined,\n  result: QueryObserverResult<any, any>,\n) => defaultedOptions?.suspense && result.isPending\n\nexport const fetchOptimistic = <\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>(\n  defaultedOptions: DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  observer: QueryObserver<TQueryFnData, TError, TData, TQueryData, TQueryKey>,\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) =>\n  observer.fetchOptimistic(defaultedOptions).catch(() => {\n    errorResetBoundary.clearReset()\n  })\n"], "names": [], "mappings": ";;;;;;;;AAUO,IAAM,sBAAsB,CAMjC,QACA,QACG,MAAM,KAAA,CAAM,IAAA,KAAS,KAAA;AAEnB,IAAM,uBAAuB,CAClC,qBACG;IACH,IAAI,iBAAiB,QAAA,EAAU;QAI7B,MAAM,QAAQ,CAAC,QACb,UAAU,WAAW,QAAQ,KAAK,GAAA,CAAI,SAAS,KAAM,GAAI;QAE3D,MAAM,oBAAoB,iBAAiB,SAAA;QAC3C,iBAAiB,SAAA,GACf,OAAO,sBAAsB,aACzB,CAAA,GAAI,OAAS,MAAM,kBAAkB,GAAG,IAAI,CAAC,IAC7C,MAAM,iBAAiB;QAE7B,IAAI,OAAO,iBAAiB,MAAA,KAAW,UAAU;YAC/C,iBAAiB,MAAA,GAAS,KAAK,GAAA,CAAI,iBAAiB,MAAA,EAAQ,GAAI;QAClE;IACF;AACF;AAEO,IAAM,YAAY,CACvB,QACA,cACG,OAAO,SAAA,IAAa,OAAO,UAAA,IAAc,CAAC;AAExC,IAAM,gBAAgB,CAC3B,kBAGA,SACG,kBAAkB,YAAY,OAAO,SAAA;AAEnC,IAAM,kBAAkB,CAO7B,kBAOA,UACA,qBAEA,SAAS,eAAA,CAAgB,gBAAgB,EAAE,KAAA,CAAM,MAAM;QACrD,mBAAmB,UAAA,CAAW;IAChC,CAAC", "debugId": null}}, {"offset": {"line": 864, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/%40tanstack/react-query/src/useBaseQuery.ts"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\nimport { isServer, noop, notifyManager } from '@tanstack/query-core'\nimport { useQueryClient } from './QueryClientProvider'\nimport { useQueryErrorResetBoundary } from './QueryErrorResetBoundary'\nimport {\n  ensurePreventErrorBoundaryRetry,\n  getHasError,\n  useClearResetErrorBoundary,\n} from './errorBoundaryUtils'\nimport { useIsRestoring } from './IsRestoringProvider'\nimport {\n  ensureSuspenseTimers,\n  fetchOptimistic,\n  shouldSuspend,\n  willFetch,\n} from './suspense'\nimport type {\n  QueryClient,\n  QueryKey,\n  QueryObserver,\n  QueryObserverResult,\n} from '@tanstack/query-core'\nimport type { UseBaseQueryOptions } from './types'\n\nexport function useBaseQuery<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  <PERSON><PERSON><PERSON>y<PERSON>ey extends QueryKey,\n>(\n  options: UseBaseQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  Observer: typeof QueryObserver,\n  queryClient?: QueryClient,\n): QueryObserverResult<TData, TError> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof options !== 'object' || Array.isArray(options)) {\n      throw new Error(\n        'Bad argument type. Starting with v5, only the \"Object\" form is allowed when calling query related functions. Please use the error stack to find the culprit call. More info here: https://tanstack.com/query/latest/docs/react/guides/migrating-to-v5#supports-a-single-signature-one-object',\n      )\n    }\n  }\n\n  const isRestoring = useIsRestoring()\n  const errorResetBoundary = useQueryErrorResetBoundary()\n  const client = useQueryClient(queryClient)\n  const defaultedOptions = client.defaultQueryOptions(options)\n\n  ;(client.getDefaultOptions().queries as any)?._experimental_beforeQuery?.(\n    defaultedOptions,\n  )\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (!defaultedOptions.queryFn) {\n      console.error(\n        `[${defaultedOptions.queryHash}]: No queryFn was passed as an option, and no default queryFn was found. The queryFn parameter is only optional when using a default queryFn. More info here: https://tanstack.com/query/latest/docs/framework/react/guides/default-query-function`,\n      )\n    }\n  }\n\n  // Make sure results are optimistically set in fetching state before subscribing or updating options\n  defaultedOptions._optimisticResults = isRestoring\n    ? 'isRestoring'\n    : 'optimistic'\n\n  ensureSuspenseTimers(defaultedOptions)\n  ensurePreventErrorBoundaryRetry(defaultedOptions, errorResetBoundary)\n\n  useClearResetErrorBoundary(errorResetBoundary)\n\n  // this needs to be invoked before creating the Observer because that can create a cache entry\n  const isNewCacheEntry = !client\n    .getQueryCache()\n    .get(defaultedOptions.queryHash)\n\n  const [observer] = React.useState(\n    () =>\n      new Observer<TQueryFnData, TError, TData, TQueryData, TQueryKey>(\n        client,\n        defaultedOptions,\n      ),\n  )\n\n  // note: this must be called before useSyncExternalStore\n  const result = observer.getOptimisticResult(defaultedOptions)\n\n  const shouldSubscribe = !isRestoring && options.subscribed !== false\n  React.useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) => {\n        const unsubscribe = shouldSubscribe\n          ? observer.subscribe(notifyManager.batchCalls(onStoreChange))\n          : noop\n\n        // Update result to make sure we did not miss any query updates\n        // between creating the observer and subscribing to it.\n        observer.updateResult()\n\n        return unsubscribe\n      },\n      [observer, shouldSubscribe],\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult(),\n  )\n\n  React.useEffect(() => {\n    observer.setOptions(defaultedOptions)\n  }, [defaultedOptions, observer])\n\n  // Handle suspense\n  if (shouldSuspend(defaultedOptions, result)) {\n    throw fetchOptimistic(defaultedOptions, observer, errorResetBoundary)\n  }\n\n  // Handle error boundary\n  if (\n    getHasError({\n      result,\n      errorResetBoundary,\n      throwOnError: defaultedOptions.throwOnError,\n      query: client\n        .getQueryCache()\n        .get<\n          TQueryFnData,\n          TError,\n          TQueryData,\n          TQueryKey\n        >(defaultedOptions.queryHash),\n      suspense: defaultedOptions.suspense,\n    })\n  ) {\n    throw result.error\n  }\n\n  ;(client.getDefaultOptions().queries as any)?._experimental_afterQuery?.(\n    defaultedOptions,\n    result,\n  )\n\n  if (\n    defaultedOptions.experimental_prefetchInRender &&\n    !isServer &&\n    willFetch(result, isRestoring)\n  ) {\n    const promise = isNewCacheEntry\n      ? // Fetch immediately on render in order to ensure `.promise` is resolved even if the component is unmounted\n        fetchOptimistic(defaultedOptions, observer, errorResetBoundary)\n      : // subscribe to the \"cache promise\" so that we can finalize the currentThenable once data comes in\n        client.getQueryCache().get(defaultedOptions.queryHash)?.promise\n\n    promise?.catch(noop).finally(() => {\n      // `.updateResult()` will trigger `.#currentThenable` to finalize\n      observer.updateResult()\n    })\n  }\n\n  // Handle result property usage tracking\n  return !defaultedOptions.notifyOnChangeProps\n    ? observer.trackResult(result)\n    : result\n}\n"], "names": [], "mappings": ";;;;AACA,YAAY,WAAW;AAEvB,SAAS,UAAU,MAAM,qBAAqB;;AAC9C,SAAS,sBAAsB;AAC/B,SAAS,kCAAkC;AAC3C;AAKA,SAAS,sBAAsB;AAC/B;;;;;;;;;AAcO,SAAS,aAOd,OAAA,EAOA,QAAA,EACA,WAAA,EACoC;IACpC,IAAI,QAAQ,IAAI,aAAa,WAAc;QACzC,IAAI,OAAO,YAAY,YAAY,MAAM,OAAA,CAAQ,OAAO,GAAG;YACzD,MAAM,IAAI,MACR;QAEJ;IACF;IAEA,MAAM,yMAAc,iBAAA,CAAe;IACnC,MAAM,oNAAqB,6BAAA,CAA2B;IACtD,MAAM,oMAAS,iBAAA,EAAe,WAAW;IACzC,MAAM,mBAAmB,OAAO,mBAAA,CAAoB,OAAO;IAEzD,OAAO,iBAAA,CAAkB,EAAE,OAAA,EAAiB,4BAC5C;IAGF,IAAI,QAAQ,IAAI,aAAa,WAAc;QACzC,IAAI,CAAC,iBAAiB,OAAA,EAAS;YAC7B,QAAQ,KAAA,CACN,CAAA,CAAA,EAAI,iBAAiB,SAAS,CAAA,kPAAA,CAAA;QAElC;IACF;IAGA,iBAAiB,kBAAA,GAAqB,cAClC,gBACA;IAEJ,CAAA,GAAA,2KAAA,CAAA,uBAAA,EAAqB,gBAAgB;IACrC,CAAA,GAAA,qLAAA,CAAA,kCAAA,EAAgC,kBAAkB,kBAAkB;IAEpE,CAAA,GAAA,qLAAA,CAAA,6BAAA,EAA2B,kBAAkB;IAG7C,MAAM,kBAAkB,CAAC,OACtB,aAAA,CAAc,EACd,GAAA,CAAI,iBAAiB,SAAS;IAEjC,MAAM,CAAC,QAAQ,CAAA,yMAAU,WAAA,CACvB,IACE,IAAI,SACF,QACA;IAKN,MAAM,SAAS,SAAS,mBAAA,CAAoB,gBAAgB;IAE5D,MAAM,kBAAkB,CAAC,eAAe,QAAQ,UAAA,KAAe;0MACzD,uBAAA,uMACE,cAAA,CACJ,CAAC,kBAAkB;QACjB,MAAM,cAAc,kBAChB,SAAS,SAAA,iLAAU,gBAAA,CAAc,UAAA,CAAW,aAAa,CAAC,4KAC1D,OAAA;QAIJ,SAAS,YAAA,CAAa;QAEtB,OAAO;IACT,GACA;QAAC;QAAU,eAAe;KAAA,GAE5B,IAAM,SAAS,gBAAA,CAAiB,GAChC,IAAM,SAAS,gBAAA,CAAiB;0MAG5B,YAAA,CAAU,MAAM;QACpB,SAAS,UAAA,CAAW,gBAAgB;IACtC,GAAG;QAAC;QAAkB,QAAQ;KAAC;IAG/B,KAAI,+LAAA,EAAc,kBAAkB,MAAM,GAAG;QAC3C,sLAAM,kBAAA,EAAgB,kBAAkB,UAAU,kBAAkB;IACtE;IAGA,IACE,wMAAA,EAAY;QACV;QACA;QACA,cAAc,iBAAiB,YAAA;QAC/B,OAAO,OACJ,aAAA,CAAc,EACd,GAAA,CAKC,iBAAiB,SAAS;QAC9B,UAAU,iBAAiB,QAAA;IAC7B,CAAC,GACD;QACA,MAAM,OAAO,KAAA;IACf;;IAEE,OAAO,iBAAA,CAAkB,EAAE,OAAA,EAAiB,2BAC5C,kBACA;IAGF,IACE,iBAAiB,6BAAA,IACjB,yKAAC,WAAA,oLACD,YAAA,EAAU,QAAQ,WAAW,GAC7B;QACA,MAAM,UAAU,kBAAA,2GAAA;wLAEZ,kBAAA,EAAgB,kBAAkB,UAAU,kBAAkB,IAAA,kGAAA;QAE9D,OAAO,aAAA,CAAc,EAAE,GAAA,CAAI,iBAAiB,SAAS,GAAG;QAE5D,SAAS,8KAAM,OAAI,EAAE,QAAQ,MAAM;YAEjC,SAAS,YAAA,CAAa;QACxB,CAAC;IACH;IAGA,OAAO,CAAC,iBAAiB,mBAAA,GACrB,SAAS,WAAA,CAAY,MAAM,IAC3B;AACN", "debugId": null}}, {"offset": {"line": 952, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/%40tanstack/react-query/src/useInfiniteQuery.ts"], "sourcesContent": ["'use client'\nimport { InfiniteQueryObserver } from '@tanstack/query-core'\nimport { useBaseQuery } from './useBaseQuery'\nimport type {\n  DefaultError,\n  InfiniteData,\n  QueryClient,\n  QueryKey,\n  QueryObserver,\n} from '@tanstack/query-core'\nimport type {\n  DefinedUseInfiniteQueryResult,\n  UseInfiniteQueryOptions,\n  UseInfiniteQueryResult,\n} from './types'\nimport type {\n  DefinedInitialDataInfiniteOptions,\n  UndefinedInitialDataInfiniteOptions,\n} from './infiniteQueryOptions'\n\nexport function useInfiniteQuery<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: DefinedInitialDataInfiniteOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n  queryClient?: QueryClient,\n): DefinedUseInfiniteQueryResult<TData, TError>\n\nexport function useInfiniteQuery<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: UndefinedInitialDataInfiniteOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n  queryClient?: QueryClient,\n): UseInfiniteQueryResult<TData, TError>\n\nexport function useInfiniteQuery<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: UseInfiniteQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n  queryClient?: QueryClient,\n): UseInfiniteQueryResult<TData, TError>\n\nexport function useInfiniteQuery(\n  options: UseInfiniteQueryOptions,\n  queryClient?: QueryClient,\n) {\n  return useBaseQuery(\n    options,\n    InfiniteQueryObserver as typeof QueryObserver,\n    queryClient,\n  )\n}\n"], "names": [], "mappings": ";;;;AACA,SAAS,6BAA6B;AACtC,SAAS,oBAAoB;;;;AAqEtB,SAAS,iBACd,OAAA,EACA,WAAA,EACA;IACA,2LAAO,eAAA,EACL,iMACA,wBAAA,EACA;AAEJ", "debugId": null}}, {"offset": {"line": 970, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/%40tanstack/react-query/src/useMutation.ts"], "sourcesContent": ["'use client'\nimport * as React from 'react'\nimport {\n  MutationObserver,\n  noop,\n  notifyManager,\n  shouldThrowError,\n} from '@tanstack/query-core'\nimport { useQueryClient } from './QueryClientProvider'\nimport type {\n  UseMutateFunction,\n  UseMutationOptions,\n  UseMutationResult,\n} from './types'\nimport type { DefaultError, QueryClient } from '@tanstack/query-core'\n\n// HOOK\n\nexport function useMutation<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TContext = unknown,\n>(\n  options: UseMutationOptions<TData, TError, TVariables, TContext>,\n  queryClient?: QueryClient,\n): UseMutationResult<TData, TError, TVariables, TContext> {\n  const client = useQueryClient(queryClient)\n\n  const [observer] = React.useState(\n    () =>\n      new MutationObserver<TData, TError, TVariables, TContext>(\n        client,\n        options,\n      ),\n  )\n\n  React.useEffect(() => {\n    observer.setOptions(options)\n  }, [observer, options])\n\n  const result = React.useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        observer.subscribe(notifyManager.batchCalls(onStoreChange)),\n      [observer],\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult(),\n  )\n\n  const mutate = React.useCallback<\n    UseMutateFunction<TData, TError, TVariables, TContext>\n  >(\n    (variables, mutateOptions) => {\n      observer.mutate(variables, mutateOptions).catch(noop)\n    },\n    [observer],\n  )\n\n  if (\n    result.error &&\n    shouldThrowError(observer.options.throwOnError, [result.error])\n  ) {\n    throw result.error\n  }\n\n  return { ...result, mutate, mutateAsync: result.mutate }\n}\n"], "names": [], "mappings": ";;;;AACA,YAAY,WAAW;;;AACvB;AAMA,SAAS,sBAAsB;;;;;AAUxB,SAAS,YAMd,OAAA,EACA,WAAA,EACwD;IACxD,MAAM,oMAAS,iBAAA,EAAe,WAAW;IAEzC,MAAM,CAAC,QAAQ,CAAA,yMAAU,WAAA,CACvB,IACE,uLAAI,mBAAA,CACF,QACA;0MAIA,YAAA,CAAU,MAAM;QACpB,SAAS,UAAA,CAAW,OAAO;IAC7B,GAAG;QAAC;QAAU,OAAO;KAAC;IAEtB,MAAM,+MAAe,uBAAA,uMACb,cAAA,CACJ,CAAC,gBACC,SAAS,SAAA,iLAAU,gBAAA,CAAc,UAAA,CAAW,aAAa,CAAC,GAC5D;QAAC,QAAQ;KAAA,GAEX,IAAM,SAAS,gBAAA,CAAiB,GAChC,IAAM,SAAS,gBAAA,CAAiB;IAGlC,MAAM,+MAAe,cAAA,CAGnB,CAAC,WAAW,kBAAkB;QAC5B,SAAS,MAAA,CAAO,WAAW,aAAa,EAAE,KAAA,CAAM,+KAAI;IACtD,GACA;QAAC,QAAQ;KAAA;IAGX,IACE,OAAO,KAAA,gLACP,mBAAA,EAAiB,SAAS,OAAA,CAAQ,YAAA,EAAc;QAAC,OAAO,KAAK;KAAC,GAC9D;QACA,MAAM,OAAO,KAAA;IACf;IAEA,OAAO;QAAE,GAAG,MAAA;QAAQ;QAAQ,aAAa,OAAO,MAAA;IAAO;AACzD", "debugId": null}}, {"offset": {"line": 1017, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/%40tanstack/react-query/src/usePrefetchInfiniteQuery.tsx"], "sourcesContent": ["import { useQueryClient } from './QueryClientProvider'\nimport type {\n  DefaultError,\n  FetchInfiniteQueryOptions,\n  QueryClient,\n  QueryKey,\n} from '@tanstack/query-core'\n\nexport function usePrefetchInfiniteQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: FetchInfiniteQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n  queryClient?: QueryClient,\n) {\n  const client = useQueryClient(queryClient)\n\n  if (!client.getQueryState(options.queryKey)) {\n    client.prefetchInfiniteQuery(options)\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,sBAAsB;;AAQxB,SAAS,yBAOd,OAAA,EAOA,WAAA,EACA;IACA,MAAM,oMAAS,iBAAA,EAAe,WAAW;IAEzC,IAAI,CAAC,OAAO,aAAA,CAAc,QAAQ,QAAQ,GAAG;QAC3C,OAAO,qBAAA,CAAsB,OAAO;IACtC;AACF", "debugId": null}}, {"offset": {"line": 1035, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/%40tanstack/react-query/src/usePrefetchQuery.tsx"], "sourcesContent": ["import { useQueryClient } from './QueryClientProvider'\nimport type { DefaultError, QueryClient, QueryKey } from '@tanstack/query-core'\nimport type { UsePrefetchQueryOptions } from './types'\n\nexport function usePrefetchQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UsePrefetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  queryClient?: QueryClient,\n) {\n  const client = useQueryClient(queryClient)\n\n  if (!client.getQueryState(options.queryKey)) {\n    client.prefetchQuery(options)\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,sBAAsB;;AAIxB,SAAS,iBAMd,OAAA,EACA,WAAA,EACA;IACA,MAAM,oMAAS,iBAAA,EAAe,WAAW;IAEzC,IAAI,CAAC,OAAO,aAAA,CAAc,QAAQ,QAAQ,GAAG;QAC3C,OAAO,aAAA,CAAc,OAAO;IAC9B;AACF", "debugId": null}}, {"offset": {"line": 1053, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/%40tanstack/react-query/src/useQueries.ts"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\nimport {\n  QueriesObserver,\n  QueryObserver,\n  noop,\n  notifyManager,\n} from '@tanstack/query-core'\nimport { useQueryClient } from './QueryClientProvider'\nimport { useIsRestoring } from './IsRestoringProvider'\nimport { useQueryErrorResetBoundary } from './QueryErrorResetBoundary'\nimport {\n  ensurePreventErrorBoundaryRetry,\n  getHasError,\n  useClearResetErrorBoundary,\n} from './errorBoundaryUtils'\nimport {\n  ensureSuspenseTimers,\n  fetchOptimistic,\n  shouldSuspend,\n  willFetch,\n} from './suspense'\nimport type {\n  DefinedUseQueryResult,\n  UseQueryOptions,\n  UseQueryResult,\n} from './types'\nimport type {\n  DefaultError,\n  OmitKeyof,\n  QueriesObserverOptions,\n  QueriesPlaceholderDataFunction,\n  QueryClient,\n  QueryFunction,\n  QueryKey,\n  QueryObserverOptions,\n  ThrowOnError,\n} from '@tanstack/query-core'\n\n// This defines the `UseQueryOptions` that are accepted in `QueriesOptions` & `GetOptions`.\n// `placeholderData` function always gets undefined passed\ntype UseQueryOptionsForUseQueries<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> = OmitKeyof<\n  UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  'placeholderData' | 'subscribed'\n> & {\n  placeholderData?: TQueryFnData | QueriesPlaceholderDataFunction<TQueryFnData>\n}\n\n// Avoid TS depth-limit error in case of large array literal\ntype MAXIMUM_DEPTH = 20\n\n// Widen the type of the symbol to enable type inference even if skipToken is not immutable.\ntype SkipTokenForUseQueries = symbol\n\ntype GetUseQueryOptionsForUseQueries<T> =\n  // Part 1: responsible for applying explicit type parameter to function arguments, if object { queryFnData: TQueryFnData, error: TError, data: TData }\n  T extends {\n    queryFnData: infer TQueryFnData\n    error?: infer TError\n    data: infer TData\n  }\n    ? UseQueryOptionsForUseQueries<TQueryFnData, TError, TData>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n      ? UseQueryOptionsForUseQueries<TQueryFnData, TError>\n      : T extends { data: infer TData; error?: infer TError }\n        ? UseQueryOptionsForUseQueries<unknown, TError, TData>\n        : // Part 2: responsible for applying explicit type parameter to function arguments, if tuple [TQueryFnData, TError, TData]\n          T extends [infer TQueryFnData, infer TError, infer TData]\n          ? UseQueryOptionsForUseQueries<TQueryFnData, TError, TData>\n          : T extends [infer TQueryFnData, infer TError]\n            ? UseQueryOptionsForUseQueries<TQueryFnData, TError>\n            : T extends [infer TQueryFnData]\n              ? UseQueryOptionsForUseQueries<TQueryFnData>\n              : // Part 3: responsible for inferring and enforcing type if no explicit parameter was provided\n                T extends {\n                    queryFn?:\n                      | QueryFunction<infer TQueryFnData, infer TQueryKey>\n                      | SkipTokenForUseQueries\n                    select?: (data: any) => infer TData\n                    throwOnError?: ThrowOnError<any, infer TError, any, any>\n                  }\n                ? UseQueryOptionsForUseQueries<\n                    TQueryFnData,\n                    unknown extends TError ? DefaultError : TError,\n                    unknown extends TData ? TQueryFnData : TData,\n                    TQueryKey\n                  >\n                : // Fallback\n                  UseQueryOptionsForUseQueries\n\n// A defined initialData setting should return a DefinedUseQueryResult rather than UseQueryResult\ntype GetDefinedOrUndefinedQueryResult<T, TData, TError = unknown> = T extends {\n  initialData?: infer TInitialData\n}\n  ? unknown extends TInitialData\n    ? UseQueryResult<TData, TError>\n    : TInitialData extends TData\n      ? DefinedUseQueryResult<TData, TError>\n      : TInitialData extends () => infer TInitialDataResult\n        ? unknown extends TInitialDataResult\n          ? UseQueryResult<TData, TError>\n          : TInitialDataResult extends TData\n            ? DefinedUseQueryResult<TData, TError>\n            : UseQueryResult<TData, TError>\n        : UseQueryResult<TData, TError>\n  : UseQueryResult<TData, TError>\n\ntype GetUseQueryResult<T> =\n  // Part 1: responsible for mapping explicit type parameter to function result, if object\n  T extends { queryFnData: any; error?: infer TError; data: infer TData }\n    ? GetDefinedOrUndefinedQueryResult<T, TData, TError>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n      ? GetDefinedOrUndefinedQueryResult<T, TQueryFnData, TError>\n      : T extends { data: infer TData; error?: infer TError }\n        ? GetDefinedOrUndefinedQueryResult<T, TData, TError>\n        : // Part 2: responsible for mapping explicit type parameter to function result, if tuple\n          T extends [any, infer TError, infer TData]\n          ? GetDefinedOrUndefinedQueryResult<T, TData, TError>\n          : T extends [infer TQueryFnData, infer TError]\n            ? GetDefinedOrUndefinedQueryResult<T, TQueryFnData, TError>\n            : T extends [infer TQueryFnData]\n              ? GetDefinedOrUndefinedQueryResult<T, TQueryFnData>\n              : // Part 3: responsible for mapping inferred type to results, if no explicit parameter was provided\n                T extends {\n                    queryFn?:\n                      | QueryFunction<infer TQueryFnData, any>\n                      | SkipTokenForUseQueries\n                    select?: (data: any) => infer TData\n                    throwOnError?: ThrowOnError<any, infer TError, any, any>\n                  }\n                ? GetDefinedOrUndefinedQueryResult<\n                    T,\n                    unknown extends TData ? TQueryFnData : TData,\n                    unknown extends TError ? DefaultError : TError\n                  >\n                : // Fallback\n                  UseQueryResult\n\n/**\n * QueriesOptions reducer recursively unwraps function arguments to infer/enforce type param\n */\nexport type QueriesOptions<\n  T extends Array<any>,\n  TResults extends Array<any> = [],\n  TDepth extends ReadonlyArray<number> = [],\n> = TDepth['length'] extends MAXIMUM_DEPTH\n  ? Array<UseQueryOptionsForUseQueries>\n  : T extends []\n    ? []\n    : T extends [infer Head]\n      ? [...TResults, GetUseQueryOptionsForUseQueries<Head>]\n      : T extends [infer Head, ...infer Tails]\n        ? QueriesOptions<\n            [...Tails],\n            [...TResults, GetUseQueryOptionsForUseQueries<Head>],\n            [...TDepth, 1]\n          >\n        : ReadonlyArray<unknown> extends T\n          ? T\n          : // If T is *some* array but we couldn't assign unknown[] to it, then it must hold some known/homogenous type!\n            // use this to infer the param types in the case of Array.map() argument\n            T extends Array<\n                UseQueryOptionsForUseQueries<\n                  infer TQueryFnData,\n                  infer TError,\n                  infer TData,\n                  infer TQueryKey\n                >\n              >\n            ? Array<\n                UseQueryOptionsForUseQueries<\n                  TQueryFnData,\n                  TError,\n                  TData,\n                  TQueryKey\n                >\n              >\n            : // Fallback\n              Array<UseQueryOptionsForUseQueries>\n\n/**\n * QueriesResults reducer recursively maps type param to results\n */\nexport type QueriesResults<\n  T extends Array<any>,\n  TResults extends Array<any> = [],\n  TDepth extends ReadonlyArray<number> = [],\n> = TDepth['length'] extends MAXIMUM_DEPTH\n  ? Array<UseQueryResult>\n  : T extends []\n    ? []\n    : T extends [infer Head]\n      ? [...TResults, GetUseQueryResult<Head>]\n      : T extends [infer Head, ...infer Tails]\n        ? QueriesResults<\n            [...Tails],\n            [...TResults, GetUseQueryResult<Head>],\n            [...TDepth, 1]\n          >\n        : { [K in keyof T]: GetUseQueryResult<T[K]> }\n\nexport function useQueries<\n  T extends Array<any>,\n  TCombinedResult = QueriesResults<T>,\n>(\n  {\n    queries,\n    ...options\n  }: {\n    queries:\n      | readonly [...QueriesOptions<T>]\n      | readonly [...{ [K in keyof T]: GetUseQueryOptionsForUseQueries<T[K]> }]\n    combine?: (result: QueriesResults<T>) => TCombinedResult\n    subscribed?: boolean\n  },\n  queryClient?: QueryClient,\n): TCombinedResult {\n  const client = useQueryClient(queryClient)\n  const isRestoring = useIsRestoring()\n  const errorResetBoundary = useQueryErrorResetBoundary()\n\n  const defaultedQueries = React.useMemo(\n    () =>\n      queries.map((opts) => {\n        const defaultedOptions = client.defaultQueryOptions(\n          opts as QueryObserverOptions,\n        )\n\n        // Make sure the results are already in fetching state before subscribing or updating options\n        defaultedOptions._optimisticResults = isRestoring\n          ? 'isRestoring'\n          : 'optimistic'\n\n        return defaultedOptions\n      }),\n    [queries, client, isRestoring],\n  )\n\n  defaultedQueries.forEach((query) => {\n    ensureSuspenseTimers(query)\n    ensurePreventErrorBoundaryRetry(query, errorResetBoundary)\n  })\n\n  useClearResetErrorBoundary(errorResetBoundary)\n\n  const [observer] = React.useState(\n    () =>\n      new QueriesObserver<TCombinedResult>(\n        client,\n        defaultedQueries,\n        options as QueriesObserverOptions<TCombinedResult>,\n      ),\n  )\n\n  // note: this must be called before useSyncExternalStore\n  const [optimisticResult, getCombinedResult, trackResult] =\n    observer.getOptimisticResult(\n      defaultedQueries,\n      (options as QueriesObserverOptions<TCombinedResult>).combine,\n    )\n\n  const shouldSubscribe = !isRestoring && options.subscribed !== false\n  React.useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        shouldSubscribe\n          ? observer.subscribe(notifyManager.batchCalls(onStoreChange))\n          : noop,\n      [observer, shouldSubscribe],\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult(),\n  )\n\n  React.useEffect(() => {\n    observer.setQueries(\n      defaultedQueries,\n      options as QueriesObserverOptions<TCombinedResult>,\n    )\n  }, [defaultedQueries, options, observer])\n\n  const shouldAtLeastOneSuspend = optimisticResult.some((result, index) =>\n    shouldSuspend(defaultedQueries[index], result),\n  )\n\n  const suspensePromises = shouldAtLeastOneSuspend\n    ? optimisticResult.flatMap((result, index) => {\n        const opts = defaultedQueries[index]\n\n        if (opts) {\n          const queryObserver = new QueryObserver(client, opts)\n          if (shouldSuspend(opts, result)) {\n            return fetchOptimistic(opts, queryObserver, errorResetBoundary)\n          } else if (willFetch(result, isRestoring)) {\n            void fetchOptimistic(opts, queryObserver, errorResetBoundary)\n          }\n        }\n        return []\n      })\n    : []\n\n  if (suspensePromises.length > 0) {\n    throw Promise.all(suspensePromises)\n  }\n  const firstSingleResultWhichShouldThrow = optimisticResult.find(\n    (result, index) => {\n      const query = defaultedQueries[index]\n      return (\n        query &&\n        getHasError({\n          result,\n          errorResetBoundary,\n          throwOnError: query.throwOnError,\n          query: client.getQueryCache().get(query.queryHash),\n          suspense: query.suspense,\n        })\n      )\n    },\n  )\n\n  if (firstSingleResultWhichShouldThrow?.error) {\n    throw firstSingleResultWhichShouldThrow.error\n  }\n\n  return getCombinedResult(trackResult())\n}\n"], "names": [], "mappings": ";;;;AACA,YAAY,WAAW;AAEvB;;;;AAMA,SAAS,sBAAsB;AAC/B,SAAS,sBAAsB;AAC/B,SAAS,kCAAkC;AAC3C;AAKA;;;;;;;;;AA8LO,SAAS,WAId,EACE,OAAA,EACA,GAAG,SACL,EAOA,WAAA,EACiB;IACjB,MAAM,oMAAS,iBAAA,EAAe,WAAW;IACzC,MAAM,yMAAc,iBAAA,CAAe;IACnC,MAAM,oNAAqB,6BAAA,CAA2B;IAEtD,MAAM,yNAAyB,UAAA,CAC7B,IACE,QAAQ,GAAA,CAAI,CAAC,SAAS;YACpB,MAAM,mBAAmB,OAAO,mBAAA,CAC9B;YAIF,iBAAiB,kBAAA,GAAqB,cAClC,gBACA;YAEJ,OAAO;QACT,CAAC,GACH;QAAC;QAAS;QAAQ,WAAW;KAAA;IAG/B,iBAAiB,OAAA,CAAQ,CAAC,UAAU;QAClC,CAAA,GAAA,2KAAA,CAAA,uBAAA,EAAqB,KAAK;QAC1B,CAAA,GAAA,qLAAA,CAAA,kCAAA,EAAgC,OAAO,kBAAkB;IAC3D,CAAC;IAED,CAAA,GAAA,qLAAA,CAAA,6BAAA,EAA2B,kBAAkB;IAE7C,MAAM,CAAC,QAAQ,CAAA,GAAU,iNAAA,CACvB,IACE,sLAAI,kBAAA,CACF,QACA,kBACA;IAKN,MAAM,CAAC,kBAAkB,mBAAmB,WAAW,CAAA,GACrD,SAAS,mBAAA,CACP,kBACC,QAAoD,OAAA;IAGzD,MAAM,kBAAkB,CAAC,eAAe,QAAQ,UAAA,KAAe;0MACzD,uBAAA,uMACE,cAAA,CACJ,CAAC,gBACC,kBACI,SAAS,SAAA,CAAU,gMAAA,CAAc,UAAA,CAAW,aAAa,CAAC,4KAC1D,OAAA,EACN;QAAC;QAAU,eAAe;KAAA,GAE5B,IAAM,SAAS,gBAAA,CAAiB,GAChC,IAAM,SAAS,gBAAA,CAAiB;IAG5B,kNAAA,CAAU,MAAM;QACpB,SAAS,UAAA,CACP,kBACA;IAEJ,GAAG;QAAC;QAAkB;QAAS,QAAQ;KAAC;IAExC,MAAM,0BAA0B,iBAAiB,IAAA,CAAK,CAAC,QAAQ,wLAC7D,gBAAA,EAAc,gBAAA,CAAiB,KAAK,CAAA,EAAG,MAAM;IAG/C,MAAM,mBAAmB,0BACrB,iBAAiB,OAAA,CAAQ,CAAC,QAAQ,UAAU;QAC1C,MAAM,OAAO,gBAAA,CAAiB,KAAK,CAAA;QAEnC,IAAI,MAAM;YACR,MAAM,gBAAgB,oLAAI,gBAAA,CAAc,QAAQ,IAAI;YACpD,oLAAI,gBAAA,EAAc,MAAM,MAAM,GAAG;gBAC/B,uLAAO,kBAAA,EAAgB,MAAM,eAAe,kBAAkB;YAChE,OAAA,oLAAW,YAAA,EAAU,QAAQ,WAAW,GAAG;gBACzC,qLAAK,kBAAA,EAAgB,MAAM,eAAe,kBAAkB;YAC9D;QACF;QACA,OAAO,CAAC,CAAA;IACV,CAAC,IACD,CAAC,CAAA;IAEL,IAAI,iBAAiB,MAAA,GAAS,GAAG;QAC/B,MAAM,QAAQ,GAAA,CAAI,gBAAgB;IACpC;IACA,MAAM,oCAAoC,iBAAiB,IAAA,CACzD,CAAC,QAAQ,UAAU;QACjB,MAAM,QAAQ,gBAAA,CAAiB,KAAK,CAAA;QACpC,OACE,mMACA,cAAA,EAAY;YACV;YACA;YACA,cAAc,MAAM,YAAA;YACpB,OAAO,OAAO,aAAA,CAAc,EAAE,GAAA,CAAI,MAAM,SAAS;YACjD,UAAU,MAAM,QAAA;QAClB,CAAC;IAEL;IAGF,IAAI,mCAAmC,OAAO;QAC5C,MAAM,kCAAkC,KAAA;IAC1C;IAEA,OAAO,kBAAkB,YAAY,CAAC;AACxC", "debugId": null}}, {"offset": {"line": 1144, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/%40tanstack/react-query/src/useQuery.ts"], "sourcesContent": ["'use client'\nimport { QueryObserver } from '@tanstack/query-core'\nimport { useBaseQuery } from './useBaseQuery'\nimport type {\n  DefaultError,\n  NoInfer,\n  QueryClient,\n  QueryKey,\n} from '@tanstack/query-core'\nimport type {\n  DefinedUseQueryResult,\n  UseQueryOptions,\n  UseQueryResult,\n} from './types'\nimport type {\n  DefinedInitialDataOptions,\n  UndefinedInitialDataOptions,\n} from './queryOptions'\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: DefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n  queryClient?: QueryClient,\n): DefinedUseQueryResult<NoInfer<TData>, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  T<PERSON><PERSON><PERSON><PERSON><PERSON> extends QueryKey = QueryKey,\n>(\n  options: UndefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n  queryClient?: QueryClient,\n): UseQueryResult<NoInfer<TData>, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  queryClient?: QueryClient,\n): UseQueryResult<NoInfer<TData>, TError>\n\nexport function useQuery(options: UseQueryOptions, queryClient?: QueryClient) {\n  return useBaseQuery(options, QueryObserver, queryClient)\n}\n"], "names": [], "mappings": ";;;;AACA,SAAS,qBAAqB;AAC9B,SAAS,oBAAoB;;;;AA+CtB,SAAS,SAAS,OAAA,EAA0B,WAAA,EAA2B;IAC5E,2LAAO,eAAA,EAAa,yLAAS,gBAAA,EAAe,WAAW;AACzD", "debugId": null}}, {"offset": {"line": 1162, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/%40tanstack/react-query/src/useSuspenseInfiniteQuery.ts"], "sourcesContent": ["'use client'\nimport { InfiniteQueryObserver, skipToken } from '@tanstack/query-core'\nimport { useBaseQuery } from './useBaseQuery'\nimport { defaultThrowOnError } from './suspense'\nimport type {\n  DefaultError,\n  InfiniteData,\n  InfiniteQueryObserverSuccessResult,\n  QueryClient,\n  QueryKey,\n  QueryObserver,\n} from '@tanstack/query-core'\nimport type {\n  UseSuspenseInfiniteQueryOptions,\n  UseSuspenseInfiniteQueryResult,\n} from './types'\n\nexport function useSuspenseInfiniteQuery<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: UseSuspenseInfiniteQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n  queryClient?: QueryClient,\n): UseSuspenseInfiniteQueryResult<TData, TError> {\n  if (process.env.NODE_ENV !== 'production') {\n    if ((options.queryFn as any) === skipToken) {\n      console.error('skipToken is not allowed for useSuspenseInfiniteQuery')\n    }\n  }\n\n  return useBaseQuery(\n    {\n      ...options,\n      enabled: true,\n      suspense: true,\n      throwOnError: defaultThrowOnError,\n    },\n    InfiniteQueryObserver as typeof QueryObserver,\n    queryClient,\n  ) as InfiniteQueryObserverSuccessResult<TData, TError>\n}\n"], "names": [], "mappings": ";;;;;AACA,SAAS,uBAAuB,iBAAiB;AACjD,SAAS,oBAAoB;AAC7B,SAAS,2BAA2B;;;;;AAc7B,SAAS,yBAOd,OAAA,EAOA,WAAA,EAC+C;IAC/C,IAAI,QAAQ,IAAI,aAAa,WAAc;QACzC,IAAK,QAAQ,OAAA,6KAAoB,YAAA,EAAW;YAC1C,QAAQ,KAAA,CAAM,uDAAuD;QACvE;IACF;IAEA,2LAAO,eAAA,EACL;QACE,GAAG,OAAA;QACH,SAAS;QACT,UAAU;QACV,0LAAc,sBAAA;IAChB,2LACA,wBAAA,EACA;AAEJ", "debugId": null}}, {"offset": {"line": 1193, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/%40tanstack/react-query/src/useSuspenseQueries.ts"], "sourcesContent": ["'use client'\nimport { skipToken } from '@tanstack/query-core'\nimport { useQueries } from './useQueries'\nimport { defaultThrowOnError } from './suspense'\nimport type { UseSuspenseQueryOptions, UseSuspenseQueryResult } from './types'\nimport type {\n  DefaultError,\n  QueryClient,\n  QueryFunction,\n  ThrowOnError,\n} from '@tanstack/query-core'\n\n// Avoid TS depth-limit error in case of large array literal\ntype MAXIMUM_DEPTH = 20\n\n// Widen the type of the symbol to enable type inference even if skipToken is not immutable.\ntype SkipTokenForUseQueries = symbol\n\ntype GetUseSuspenseQueryOptions<T> =\n  // Part 1: responsible for applying explicit type parameter to function arguments, if object { queryFnData: TQueryFnData, error: TError, data: TData }\n  T extends {\n    queryFnData: infer TQueryFnData\n    error?: infer TError\n    data: infer TData\n  }\n    ? UseSuspenseQueryOptions<TQueryFnData, TError, TData>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n      ? UseSuspenseQueryOptions<TQueryFnData, TError>\n      : T extends { data: infer TData; error?: infer TError }\n        ? UseSuspenseQueryOptions<unknown, TError, TData>\n        : // Part 2: responsible for applying explicit type parameter to function arguments, if tuple [TQueryFnData, TError, TData]\n          T extends [infer TQueryFnData, infer TError, infer TData]\n          ? UseSuspenseQueryOptions<TQueryFnData, TError, TData>\n          : T extends [infer TQueryFnData, infer TError]\n            ? UseSuspenseQueryOptions<TQueryFnData, TError>\n            : T extends [infer TQueryFnData]\n              ? UseSuspenseQueryOptions<TQueryFnData>\n              : // Part 3: responsible for inferring and enforcing type if no explicit parameter was provided\n                T extends {\n                    queryFn?:\n                      | QueryFunction<infer TQueryFnData, infer TQueryKey>\n                      | SkipTokenForUseQueries\n                    select?: (data: any) => infer TData\n                    throwOnError?: ThrowOnError<any, infer TError, any, any>\n                  }\n                ? UseSuspenseQueryOptions<\n                    TQueryFnData,\n                    TError,\n                    TData,\n                    TQueryKey\n                  >\n                : T extends {\n                      queryFn?:\n                        | QueryFunction<infer TQueryFnData, infer TQueryKey>\n                        | SkipTokenForUseQueries\n                      throwOnError?: ThrowOnError<any, infer TError, any, any>\n                    }\n                  ? UseSuspenseQueryOptions<\n                      TQueryFnData,\n                      TError,\n                      TQueryFnData,\n                      TQueryKey\n                    >\n                  : // Fallback\n                    UseSuspenseQueryOptions\n\ntype GetUseSuspenseQueryResult<T> =\n  // Part 1: responsible for mapping explicit type parameter to function result, if object\n  T extends { queryFnData: any; error?: infer TError; data: infer TData }\n    ? UseSuspenseQueryResult<TData, TError>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n      ? UseSuspenseQueryResult<TQueryFnData, TError>\n      : T extends { data: infer TData; error?: infer TError }\n        ? UseSuspenseQueryResult<TData, TError>\n        : // Part 2: responsible for mapping explicit type parameter to function result, if tuple\n          T extends [any, infer TError, infer TData]\n          ? UseSuspenseQueryResult<TData, TError>\n          : T extends [infer TQueryFnData, infer TError]\n            ? UseSuspenseQueryResult<TQueryFnData, TError>\n            : T extends [infer TQueryFnData]\n              ? UseSuspenseQueryResult<TQueryFnData>\n              : // Part 3: responsible for mapping inferred type to results, if no explicit parameter was provided\n                T extends {\n                    queryFn?:\n                      | QueryFunction<infer TQueryFnData, any>\n                      | SkipTokenForUseQueries\n                    select?: (data: any) => infer TData\n                    throwOnError?: ThrowOnError<any, infer TError, any, any>\n                  }\n                ? UseSuspenseQueryResult<\n                    unknown extends TData ? TQueryFnData : TData,\n                    unknown extends TError ? DefaultError : TError\n                  >\n                : T extends {\n                      queryFn?:\n                        | QueryFunction<infer TQueryFnData, any>\n                        | SkipTokenForUseQueries\n                      throwOnError?: ThrowOnError<any, infer TError, any, any>\n                    }\n                  ? UseSuspenseQueryResult<\n                      TQueryFnData,\n                      unknown extends TError ? DefaultError : TError\n                    >\n                  : // Fallback\n                    UseSuspenseQueryResult\n\n/**\n * SuspenseQueriesOptions reducer recursively unwraps function arguments to infer/enforce type param\n */\nexport type SuspenseQueriesOptions<\n  T extends Array<any>,\n  TResults extends Array<any> = [],\n  TDepth extends ReadonlyArray<number> = [],\n> = TDepth['length'] extends MAXIMUM_DEPTH\n  ? Array<UseSuspenseQueryOptions>\n  : T extends []\n    ? []\n    : T extends [infer Head]\n      ? [...TResults, GetUseSuspenseQueryOptions<Head>]\n      : T extends [infer Head, ...infer Tails]\n        ? SuspenseQueriesOptions<\n            [...Tails],\n            [...TResults, GetUseSuspenseQueryOptions<Head>],\n            [...TDepth, 1]\n          >\n        : Array<unknown> extends T\n          ? T\n          : // If T is *some* array but we couldn't assign unknown[] to it, then it must hold some known/homogenous type!\n            // use this to infer the param types in the case of Array.map() argument\n            T extends Array<\n                UseSuspenseQueryOptions<\n                  infer TQueryFnData,\n                  infer TError,\n                  infer TData,\n                  infer TQueryKey\n                >\n              >\n            ? Array<\n                UseSuspenseQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n              >\n            : // Fallback\n              Array<UseSuspenseQueryOptions>\n\n/**\n * SuspenseQueriesResults reducer recursively maps type param to results\n */\nexport type SuspenseQueriesResults<\n  T extends Array<any>,\n  TResults extends Array<any> = [],\n  TDepth extends ReadonlyArray<number> = [],\n> = TDepth['length'] extends MAXIMUM_DEPTH\n  ? Array<UseSuspenseQueryResult>\n  : T extends []\n    ? []\n    : T extends [infer Head]\n      ? [...TResults, GetUseSuspenseQueryResult<Head>]\n      : T extends [infer Head, ...infer Tails]\n        ? SuspenseQueriesResults<\n            [...Tails],\n            [...TResults, GetUseSuspenseQueryResult<Head>],\n            [...TDepth, 1]\n          >\n        : { [K in keyof T]: GetUseSuspenseQueryResult<T[K]> }\n\nexport function useSuspenseQueries<\n  T extends Array<any>,\n  TCombinedResult = SuspenseQueriesResults<T>,\n>(\n  options: {\n    queries:\n      | readonly [...SuspenseQueriesOptions<T>]\n      | readonly [...{ [K in keyof T]: GetUseSuspenseQueryOptions<T[K]> }]\n    combine?: (result: SuspenseQueriesResults<T>) => TCombinedResult\n  },\n  queryClient?: QueryClient,\n): TCombinedResult\n\nexport function useSuspenseQueries<\n  T extends Array<any>,\n  TCombinedResult = SuspenseQueriesResults<T>,\n>(\n  options: {\n    queries: readonly [...SuspenseQueriesOptions<T>]\n    combine?: (result: SuspenseQueriesResults<T>) => TCombinedResult\n  },\n  queryClient?: QueryClient,\n): TCombinedResult\n\nexport function useSuspenseQueries(options: any, queryClient?: QueryClient) {\n  return useQueries(\n    {\n      ...options,\n      queries: options.queries.map((query: any) => {\n        if (process.env.NODE_ENV !== 'production') {\n          if (query.queryFn === skipToken) {\n            console.error('skipToken is not allowed for useSuspenseQueries')\n          }\n        }\n\n        return {\n          ...query,\n          suspense: true,\n          throwOnError: defaultThrowOnError,\n          enabled: true,\n          placeholderData: undefined,\n        }\n      }),\n    },\n    queryClient,\n  )\n}\n"], "names": [], "mappings": ";;;;AACA,SAAS,iBAAiB;AAC1B,SAAS,kBAAkB;AAC3B,SAAS,2BAA2B;;;;;AAyL7B,SAAS,mBAAmB,OAAA,EAAc,WAAA,EAA2B;IAC1E,QAAO,8LAAA,EACL;QACE,GAAG,OAAA;QACH,SAAS,QAAQ,OAAA,CAAQ,GAAA,CAAI,CAAC,UAAe;YAC3C,IAAI,QAAQ,IAAI,aAAa,WAAc;gBACzC,IAAI,MAAM,OAAA,4KAAY,aAAA,EAAW;oBAC/B,QAAQ,KAAA,CAAM,iDAAiD;gBACjE;YACF;YAEA,OAAO;gBACL,GAAG,KAAA;gBACH,UAAU;gBACV,0LAAc,sBAAA;gBACd,SAAS;gBACT,iBAAiB,KAAA;YACnB;QACF,CAAC;IACH,GACA;AAEJ", "debugId": null}}, {"offset": {"line": 1229, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/%40tanstack/react-query/src/useSuspenseQuery.ts"], "sourcesContent": ["'use client'\nimport { QueryObserver, skipToken } from '@tanstack/query-core'\nimport { useBaseQuery } from './useBaseQuery'\nimport { defaultThrowOnError } from './suspense'\nimport type { UseSuspenseQueryOptions, UseSuspenseQueryResult } from './types'\nimport type { DefaultError, QueryClient, QueryKey } from '@tanstack/query-core'\n\nexport function useSuspenseQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UseSuspenseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  queryClient?: QueryClient,\n): UseSuspenseQueryResult<TData, TError> {\n  if (process.env.NODE_ENV !== 'production') {\n    if ((options.queryFn as any) === skipToken) {\n      console.error('skipToken is not allowed for useSuspenseQuery')\n    }\n  }\n\n  return useBaseQuery(\n    {\n      ...options,\n      enabled: true,\n      suspense: true,\n      throwOnError: defaultThrowOnError,\n      placeholderData: undefined,\n    },\n    QueryObserver,\n    queryClient,\n  ) as UseSuspenseQueryResult<TData, TError>\n}\n"], "names": [], "mappings": ";;;;;AACA,SAAS,eAAe,iBAAiB;AACzC,SAAS,oBAAoB;AAC7B,SAAS,2BAA2B;;;;;AAI7B,SAAS,iBAMd,OAAA,EACA,WAAA,EACuC;IACvC,IAAI,QAAQ,IAAI,aAAa,WAAc;QACzC,IAAK,QAAQ,OAAA,6KAAoB,YAAA,EAAW;YAC1C,QAAQ,KAAA,CAAM,+CAA+C;QAC/D;IACF;IAEA,2LAAO,eAAA,EACL;QACE,GAAG,OAAA;QACH,SAAS;QACT,UAAU;QACV,0LAAc,sBAAA;QACd,iBAAiB,KAAA;IACnB,mLACA,gBAAA,EACA;AAEJ", "debugId": null}}, {"offset": {"line": 1261, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@trpc/react-query/dist/getQueryKey-BY58RNzP.mjs", "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/objectWithoutPropertiesLoose.js", "file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/objectWithoutProperties.js", "file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/typeof.js", "file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/toPrimitive.js", "file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/toPropertyKey.js", "file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/defineProperty.js", "file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/objectSpread2.js", "file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/%40trpc/react-query/src/internals/getQueryKey.ts"], "sourcesContent": ["function _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (e.includes(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nmodule.exports = _objectWithoutPropertiesLoose, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var objectWithoutPropertiesLoose = require(\"./objectWithoutPropertiesLoose.js\");\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var s = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < s.length; r++) o = s[r], t.includes(o) || {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nmodule.exports = _objectWithoutProperties, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return module.exports = _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports, _typeof(o);\n}\nmodule.exports = _typeof, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nmodule.exports = toPrimitive, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nvar toPrimitive = require(\"./toPrimitive.js\");\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nmodule.exports = toPropertyKey, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var toPropertyKey = require(\"./toPropertyKey.js\");\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nmodule.exports = _defineProperty, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var defineProperty = require(\"./defineProperty.js\");\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nmodule.exports = _objectSpread2, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "import { skipToken } from '@tanstack/react-query';\nimport {\n  isObject,\n  type DeepPartial,\n} from '@trpc/server/unstable-core-do-not-import';\nimport type { DecoratedMutation, DecoratedQuery } from '../createTRPCReact';\nimport type { DecorateRouterRecord } from '../shared';\n\nexport type QueryType = 'any' | 'infinite' | 'query';\n\nexport type TRPCQueryKey = [\n  readonly string[],\n  { input?: unknown; type?: Exclude<QueryType, 'any'> }?,\n];\n\nexport type TRPCMutationKey = [readonly string[]]; // = [TRPCQueryKey[0]]\n\ntype ProcedureOrRouter =\n  | DecoratedMutation<any>\n  | DecoratedQuery<any>\n  | DecorateRouterRecord<any, any>;\n\n/**\n * To allow easy interactions with groups of related queries, such as\n * invalidating all queries of a router, we use an array as the path when\n * storing in tanstack query.\n **/\nexport function getQueryKeyInternal(\n  path: readonly string[],\n  input: unknown,\n  type: QueryType,\n): TRPCQueryKey {\n  // Construct a query key that is easy to destructure and flexible for\n  // partial selecting etc.\n  // https://github.com/trpc/trpc/issues/3128\n\n  // some parts of the path may be dot-separated, split them up\n  const splitPath = path.flatMap((part) => part.split('.'));\n\n  if (!input && (!type || type === 'any')) {\n    // this matches also all mutations (see `getMutationKeyInternal`)\n\n    // for `utils.invalidate()` to match all queries (including vanilla react-query)\n    // we don't want nested array if path is empty, i.e. `[]` instead of `[[]]`\n    return splitPath.length ? [splitPath] : ([] as unknown as TRPCQueryKey);\n  }\n\n  if (\n    type === 'infinite' &&\n    isObject(input) &&\n    ('direction' in input || 'cursor' in input)\n  ) {\n    const {\n      cursor: _,\n      direction: __,\n      ...inputWithoutCursorAndDirection\n    } = input;\n    return [\n      splitPath,\n      {\n        input: inputWithoutCursorAndDirection,\n        type: 'infinite',\n      },\n    ];\n  }\n  return [\n    splitPath,\n    {\n      ...(typeof input !== 'undefined' &&\n        input !== skipToken && { input: input }),\n      ...(type && type !== 'any' && { type: type }),\n    },\n  ];\n}\n\nexport function getMutationKeyInternal(path: readonly string[]) {\n  return getQueryKeyInternal(path, undefined, 'any') as TRPCMutationKey;\n}\n\ntype GetInfiniteQueryInput<\n  TProcedureInput,\n  TInputWithoutCursorAndDirection = Omit<\n    TProcedureInput,\n    'cursor' | 'direction'\n  >,\n> = keyof TInputWithoutCursorAndDirection extends never\n  ? undefined\n  : DeepPartial<TInputWithoutCursorAndDirection> | undefined;\n\n/** @internal */\nexport type GetQueryProcedureInput<TProcedureInput> = TProcedureInput extends {\n  cursor?: any;\n}\n  ? GetInfiniteQueryInput<TProcedureInput>\n  : DeepPartial<TProcedureInput> | undefined;\n\ntype GetParams<TProcedureOrRouter extends ProcedureOrRouter> =\n  TProcedureOrRouter extends DecoratedQuery<infer $Def>\n    ? [input?: GetQueryProcedureInput<$Def['input']>, type?: QueryType]\n    : [];\n\n/**\n * Method to extract the query key for a procedure\n * @param procedureOrRouter - procedure or AnyRouter\n * @param input - input to procedureOrRouter\n * @param type - defaults to `any`\n * @see https://trpc.io/docs/v11/getQueryKey\n */\nexport function getQueryKey<TProcedureOrRouter extends ProcedureOrRouter>(\n  procedureOrRouter: TProcedureOrRouter,\n  ..._params: GetParams<TProcedureOrRouter>\n) {\n  const [input, type] = _params;\n\n  // @ts-expect-error - we don't expose _def on the type layer\n  const path = procedureOrRouter._def().path as string[];\n  const queryKey = getQueryKeyInternal(path, input, type ?? 'any');\n  return queryKey;\n}\n\n// TODO: look over if we can't use a single type\nexport type QueryKeyKnown<TInput, TType extends Exclude<QueryType, 'any'>> = [\n  string[],\n  { input?: GetQueryProcedureInput<TInput>; type: TType }?,\n];\n\n/**\n * Method to extract the mutation key for a procedure\n * @param procedure - procedure\n * @see https://trpc.io/docs/v11/getQueryKey#mutations\n */\nexport function getMutationKey<TProcedure extends DecoratedMutation<any>>(\n  procedure: TProcedure,\n) {\n  // @ts-expect-error - we don't expose _def on the type layer\n  const path = procedure._def().path as string[];\n  return getMutationKeyInternal(path);\n}\n"], "names": ["_objectWithoutProperties", "_typeof", "o", "_typeof", "toPrimitive", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "r", "path: readonly string[]", "input: unknown", "type: QueryType", "procedureOrRouter: TProcedureOrRouter", "procedure: TProcedure"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAAA,SAAS,8BAA8B,CAAA,EAAG,CAAA,EAAG;YAC3C,IAAI,QAAQ,EAAG,CAAA,OAAO,CAAE;YACxB,IAAI,IAAI,CAAE;YACV,IAAK,IAAI,KAAK,EAAG,KAAI,EAAE,EAAC,cAAA,CAAe,IAAA,CAAK,GAAG,EAAE,EAAE;gBACjD,IAAI,EAAE,QAAA,CAAS,EAAE,CAAE,CAAA;gBACnB,CAAA,CAAE,EAAA,GAAK,CAAA,CAAE,EAAA;YACV;YACD,OAAO;QACR;QACD,OAAO,OAAA,GAAU,+BAA+B,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;QCTrH,IAAI,+BAAA;QACJ,SAASA,2BAAyB,CAAA,EAAG,CAAA,EAAG;YACtC,IAAI,QAAQ,EAAG,CAAA,OAAO,CAAE;YACxB,IAAI,GACF,GACA,IAAI,6BAA6B,GAAG,EAAE;YACxC,IAAI,OAAO,qBAAA,EAAuB;gBAChC,IAAI,IAAI,OAAO,qBAAA,CAAsB,EAAE;gBACvC,IAAK,IAAI,GAAG,IAAI,EAAE,MAAA,EAAQ,IAAK,IAAI,CAAA,CAAE,EAAA,EAAI,EAAE,QAAA,CAAS,EAAE,KAAI,EAAE,EAAC,oBAAA,CAAqB,IAAA,CAAK,GAAG,EAAE,IAAA,CAAK,CAAA,CAAE,EAAA,GAAK,CAAA,CAAE,EAAA;YAC3G;YACD,OAAO;QACR;QACD,OAAO,OAAA,GAAUA,4BAA0B,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;QCZhH,SAASC,UAAQ,CAAA,EAAG;YAClB;YAEA,OAAO,OAAO,OAAA,GAAUA,YAAU,cAAA,OAAqB,UAAU,YAAA,OAAmB,OAAO,QAAA,GAAW,SAAUC,GAAAA,EAAG;gBACjH,OAAA,OAAcA;YACf,IAAG,SAAUA,GAAAA,EAAG;gBACf,OAAOA,OAAK,cAAA,OAAqB,UAAUA,IAAE,WAAA,KAAgB,UAAUA,QAAM,OAAO,SAAA,GAAY,WAAA,OAAkBA;YACnH,GAAE,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA,EAAS,UAAQ,EAAE;QAC5F;QACD,OAAO,OAAA,GAAUD,WAAS,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;QCT/F,IAAIE,YAAAA,gBAAAA,CAAiC,UAAA;QACrC,SAASC,cAAY,CAAA,EAAG,CAAA,EAAG;YACzB,IAAI,YAAY,UAAQ,EAAE,IAAA,CAAK,EAAG,CAAA,OAAO;YACzC,IAAI,IAAI,CAAA,CAAE,OAAO,WAAA,CAAA;YACjB,IAAA,KAAS,MAAM,GAAG;gBAChB,IAAI,IAAI,EAAE,IAAA,CAAK,GAAG,KAAK,UAAU;gBACjC,IAAI,YAAY,UAAQ,EAAE,CAAE,CAAA,OAAO;gBACnC,MAAM,IAAI,UAAU;YACrB;YACD,OAAO,CAAC,aAAa,IAAI,SAAS,MAAA,EAAQ,EAAE;QAC7C;QACD,OAAO,OAAA,GAAUA,eAAa,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;QCXnG,IAAI,UAAA,gBAAA,CAAiC,UAAA;QACrC,IAAI,cAAA;QACJ,SAASC,gBAAc,CAAA,EAAG;YACxB,IAAI,IAAI,YAAY,GAAG,SAAS;YAChC,OAAO,YAAY,QAAQ,EAAE,GAAG,IAAI,IAAI;QACzC;QACD,OAAO,OAAA,GAAUA,iBAAe,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;QCNrG,IAAI,gBAAA;QACJ,SAAS,gBAAgB,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG;YAChC,OAAA,CAAQ,IAAI,cAAc,EAAE,KAAK,IAAI,OAAO,cAAA,CAAe,GAAG,GAAG;gBAC/D,OAAO;gBACP,YAAA,CAAa;gBACb,cAAA,CAAe;gBACf,UAAA,CAAW;YACZ,EAAC,GAAG,CAAA,CAAE,EAAA,GAAK,GAAG;QAChB;QACD,OAAO,OAAA,GAAU,iBAAiB,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;QCTvG,IAAI,iBAAA;QACJ,SAAS,QAAQ,CAAA,EAAG,CAAA,EAAG;YACrB,IAAI,IAAI,OAAO,IAAA,CAAK,EAAE;YACtB,IAAI,OAAO,qBAAA,EAAuB;gBAChC,IAAI,IAAI,OAAO,qBAAA,CAAsB,EAAE;gBACvC,KAAA,CAAM,IAAI,EAAE,MAAA,CAAO,SAAUC,GAAAA,EAAG;oBAC9B,OAAO,OAAO,wBAAA,CAAyB,GAAGA,IAAE,CAAC,UAAA;gBAC9C,EAAC,GAAG,EAAE,IAAA,CAAK,KAAA,CAAM,GAAG,EAAE;YACxB;YACD,OAAO;QACR;QACD,SAAS,eAAe,CAAA,EAAG;YACzB,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAA,EAAQ,IAAK;gBACzC,IAAI,IAAI,QAAQ,SAAA,CAAU,EAAA,GAAK,SAAA,CAAU,EAAA,GAAK,CAAE;gBAChD,IAAI,IAAI,QAAQ,OAAO,EAAE,EAAA,CAAG,EAAE,CAAC,OAAA,CAAQ,SAAUA,GAAAA,EAAG;oBAClD,eAAe,GAAGA,KAAG,CAAA,CAAEA,IAAAA,CAAG;gBAC3B,EAAC,GAAG,OAAO,yBAAA,GAA4B,OAAO,gBAAA,CAAiB,GAAG,OAAO,yBAAA,CAA0B,EAAE,CAAC,GAAG,QAAQ,OAAO,EAAE,CAAC,CAAC,OAAA,CAAQ,SAAUA,GAAAA,EAAG;oBAChJ,OAAO,cAAA,CAAe,GAAGA,KAAG,OAAO,wBAAA,CAAyB,GAAGA,IAAE,CAAC;gBACnE,EAAC;YACH;YACD,OAAO;QACR;QACD,OAAO,OAAA,GAAU,gBAAgB,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;;IC+BhG;IACA;CAAA;;;;;IA3BN,SAAgB,oBACdC,IAAAA,EACAC,KAAAA,EACAC,IAAAA,EACc;IAMd,MAAM,YAAY,KAAK,OAAA,CAAQ,CAAC,OAAS,KAAK,KAAA,CAAM,IAAI,CAAC;IAEzD,IAAA,CAAK,SAAA,CAAA,CAAW,QAAQ,SAAS,KAAA,EAK/B,CAAA,OAAO,UAAU,MAAA,GAAS;QAAC,SAAU;KAAA,GAAI,CAAE,CAAA;IAG7C,IACE,SAAS,eACT,6KAAA,EAAS,MAAM,IAAA,CACd,eAAe,SAAS,YAAY,KAAA,GACrC;QACA,MAAM,EACJ,QAAQ,CAAA,EACR,WAAW,EAAA,EAEZ,GAAA,OADI,iCAAA,CAAA,GAAA,+BAAA,OAAA,EACD,OAAA;QACJ,OAAO;YACL;YACA;gBACE,OAAO;gBACP,MAAM;YACP,CACF;SAAA;IACF;IACD,OAAO;QACL;QAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,OAEa,UAAU,eACnB,kLAAU,YAAA,IAAa;YAAS;QAAO,IACrC,QAAQ,SAAS,SAAS;YAAQ;QAAM,EAE/C;KAAA;AACF;AAED,SAAgB,uBAAuBF,IAAAA,EAAyB;IAC9D,OAAO,oBAAoB,MAAA,KAAA,GAAiB,MAAM;AACnD;;;;;;;GA+BD,SAAgB,YACdG,iBAAAA,EACA,GAAG,OAAA,EACH;IACA,MAAM,CAAC,OAAO,KAAK,GAAG;IAGtB,MAAM,OAAO,kBAAkB,IAAA,EAAM,CAAC,IAAA;IACtC,MAAM,WAAW,oBAAoB,MAAM,OAAO,SAAA,QAAA,SAAA,KAAA,IAAA,OAAQ,MAAM;IAChE,OAAO;AACR;;;;;GAaD,SAAgB,eACdC,SAAAA,EACA;IAEA,MAAM,OAAO,UAAU,IAAA,EAAM,CAAC,IAAA;IAC9B,OAAO,uBAAuB,KAAK;AACpC", "debugId": null}}, {"offset": {"line": 1490, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@trpc/react-query/dist/shared-JtnEvJvB.mjs", "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/%40trpc/react-query/src/shared/proxy/decorationProxy.ts", "file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/%40trpc/react-query/src/internals/context.tsx", "file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/%40trpc/react-query/src/shared/proxy/utilsProxy.ts", "file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/%40trpc/react-query/src/shared/proxy/useQueriesProxy.ts", "file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/%40trpc/react-query/src/internals/getClientArgs.ts", "file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/asyncIterator.js", "file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/%40trpc/react-query/src/internals/trpcResult.ts", "file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/%40trpc/react-query/src/utils/createUtilityFunctions.ts", "file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/%40trpc/react-query/src/shared/hooks/createHooksInternal.tsx", "file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/%40trpc/react-query/src/shared/queryClient.ts"], "sourcesContent": ["import type { AnyRouter } from '@trpc/server/unstable-core-do-not-import';\nimport { createRecursiveProxy } from '@trpc/server/unstable-core-do-not-import';\nimport type { CreateReactQueryHooks } from '../hooks/createHooksInternal';\n\n/**\n * Create proxy for decorating procedures\n * @internal\n */\nexport function createReactDecoration<\n  TRouter extends AnyRouter,\n  TSSRContext = unknown,\n>(hooks: CreateReactQueryHooks<TRouter, TSSRContext>) {\n  return createRecursiveProxy(({ path, args }) => {\n    const pathCopy = [...path];\n\n    // The last arg is for instance `.useMutation` or `.useQuery()`\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    const lastArg = pathCopy.pop()!;\n\n    if (lastArg === 'useMutation') {\n      return (hooks as any)[lastArg](pathCopy, ...args);\n    }\n\n    if (lastArg === '_def') {\n      return {\n        path: pathCopy,\n      };\n    }\n\n    const [input, ...rest] = args;\n    const opts = rest[0] ?? {};\n\n    return (hooks as any)[lastArg](pathCopy, input, opts);\n  });\n}\n", "import type {\n  CancelOptions,\n  FetchInfiniteQueryOptions,\n  FetchQueryOptions,\n  InfiniteData,\n  InvalidateOptions,\n  InvalidateQueryFilters,\n  MutationOptions,\n  QueryClient,\n  QueryFilters,\n  QueryKey,\n  RefetchOptions,\n  RefetchQueryFilters,\n  ResetOptions,\n  SetDataOptions,\n  Updater,\n} from '@tanstack/react-query';\nimport type {\n  TRPCClient,\n  TRPCClientError,\n  TRPCRequestOptions,\n  TRPCUntypedClient,\n} from '@trpc/client';\nimport type {\n  AnyClientTypes,\n  AnyRouter,\n  DistributiveOmit,\n} from '@trpc/server/unstable-core-do-not-import';\nimport * as React from 'react';\nimport type {\n  DefinedTRPCInfiniteQueryOptionsIn,\n  DefinedTRPCInfiniteQueryOptionsOut,\n  DefinedTRPCQueryOptionsIn,\n  DefinedTRPCQueryOptionsOut,\n  ExtractCursorType,\n  UndefinedTRPCInfiniteQueryOptionsIn,\n  UndefinedTRPCInfiniteQueryOptionsOut,\n  UndefinedTRPCQueryOptionsIn,\n  UndefinedTRPCQueryOptionsOut,\n} from '../shared';\nimport type { TRPCMutationKey, TRPCQueryKey } from './getQueryKey';\n\ninterface TRPCUseUtilsOptions {\n  /**\n   * tRPC-related options\n   */\n  trpc?: TRPCRequestOptions;\n}\nexport interface TRPCFetchQueryOptions<TOutput, TError>\n  extends DistributiveOmit<FetchQueryOptions<TOutput, TError>, 'queryKey'>,\n    TRPCUseUtilsOptions {\n  //\n}\n\nexport type TRPCFetchInfiniteQueryOptions<TInput, TOutput, TError> =\n  DistributiveOmit<\n    FetchInfiniteQueryOptions<\n      TOutput,\n      TError,\n      TOutput,\n      TRPCQueryKey,\n      ExtractCursorType<TInput>\n    >,\n    'queryKey' | 'initialPageParam'\n  > &\n    TRPCUseUtilsOptions & {\n      initialCursor?: ExtractCursorType<TInput>;\n    };\n\n/** @internal */\nexport type SSRState = 'mounted' | 'mounting' | 'prepass' | false;\n\nexport interface TRPCContextPropsBase<TRouter extends AnyRouter, TSSRContext> {\n  /**\n   * The `TRPCClient`\n   */\n  client: TRPCUntypedClient<TRouter>;\n  /**\n   * The SSR context when server-side rendering\n   * @default null\n   */\n  ssrContext?: TSSRContext | null;\n  /**\n   * State of SSR hydration.\n   * - `false` if not using SSR.\n   * - `prepass` when doing a prepass to fetch queries' data\n   * - `mounting` before TRPCProvider has been rendered on the client\n   * - `mounted` when the TRPCProvider has been rendered on the client\n   * @default false\n   */\n  ssrState?: SSRState;\n  /**\n   * @deprecated pass abortOnUnmount to `createTRPCReact` instead\n   * Abort loading query calls when unmounting a component - usually when navigating to a new page\n   * @default false\n   */\n  abortOnUnmount?: boolean;\n}\n\n/**\n * @internal\n */\nexport type DecoratedTRPCContextProps<\n  TRouter extends AnyRouter,\n  TSSRContext,\n> = TRPCContextPropsBase<TRouter, TSSRContext> & {\n  client: TRPCClient<TRouter>;\n};\n\nexport interface TRPCContextProps<TRouter extends AnyRouter, TSSRContext>\n  extends TRPCContextPropsBase<TRouter, TSSRContext> {\n  /**\n   * The react-query `QueryClient`\n   */\n  queryClient: QueryClient;\n}\n\nexport const contextProps: (keyof TRPCContextPropsBase<any, any>)[] = [\n  'client',\n  'ssrContext',\n  'ssrState',\n  'abortOnUnmount',\n];\n\n/**\n * @internal\n */\nexport interface TRPCContextState<\n  TRouter extends AnyRouter,\n  TSSRContext = undefined,\n> extends Required<TRPCContextProps<TRouter, TSSRContext>>,\n    TRPCQueryUtils<TRouter> {}\n\n/**\n * @internal\n */\nexport interface TRPCQueryUtils<TRouter extends AnyRouter> {\n  /**\n   * @see https://tanstack.com/query/latest/docs/framework/react/reference/queryOptions#queryoptions\n   */\n  queryOptions(\n    path: readonly string[], // <-- look into if needed\n    queryKey: TRPCQueryKey,\n    opts?: UndefinedTRPCQueryOptionsIn<\n      unknown,\n      unknown,\n      TRPCClientError<AnyClientTypes>\n    >,\n  ): UndefinedTRPCQueryOptionsOut<\n    unknown,\n    unknown,\n    TRPCClientError<AnyClientTypes>\n  >;\n  queryOptions(\n    path: readonly string[], // <-- look into if needed\n    queryKey: TRPCQueryKey,\n    opts: DefinedTRPCQueryOptionsIn<\n      unknown,\n      unknown,\n      TRPCClientError<AnyClientTypes>\n    >,\n  ): DefinedTRPCQueryOptionsOut<\n    unknown,\n    unknown,\n    TRPCClientError<AnyClientTypes>\n  >;\n\n  /**\n   * @see https://tanstack.com/query/latest/docs/framework/react/reference/infiniteQueryOptions#infinitequeryoptions\n   */\n  infiniteQueryOptions(\n    path: readonly string[], // <-- look into if needed\n    queryKey: TRPCQueryKey,\n    opts: UndefinedTRPCInfiniteQueryOptionsIn<\n      unknown,\n      unknown,\n      unknown,\n      TRPCClientError<AnyClientTypes>\n    >,\n  ): UndefinedTRPCInfiniteQueryOptionsOut<\n    unknown,\n    unknown,\n    unknown,\n    TRPCClientError<AnyClientTypes>\n  >;\n  infiniteQueryOptions(\n    path: readonly string[], // <-- look into if needed\n    queryKey: TRPCQueryKey,\n    opts: DefinedTRPCInfiniteQueryOptionsIn<\n      unknown,\n      unknown,\n      unknown,\n      TRPCClientError<AnyClientTypes>\n    >,\n  ): DefinedTRPCInfiniteQueryOptionsOut<\n    unknown,\n    unknown,\n    unknown,\n    TRPCClientError<AnyClientTypes>\n  >;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientfetchquery\n   */\n  fetchQuery: (\n    queryKey: TRPCQueryKey,\n    opts?: TRPCFetchQueryOptions<unknown, TRPCClientError<TRouter>>,\n  ) => Promise<unknown>;\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientfetchinfinitequery\n   */\n  fetchInfiniteQuery: (\n    queryKey: TRPCQueryKey,\n    opts?: TRPCFetchInfiniteQueryOptions<\n      unknown,\n      unknown,\n      TRPCClientError<TRouter>\n    >,\n  ) => Promise<InfiniteData<unknown, unknown>>;\n  /**\n   * @see https://tanstack.com/query/v5/docs/framework/react/guides/prefetching\n   */\n  prefetchQuery: (\n    queryKey: TRPCQueryKey,\n    opts?: TRPCFetchQueryOptions<unknown, TRPCClientError<TRouter>>,\n  ) => Promise<void>;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientprefetchinfinitequery\n   */\n  prefetchInfiniteQuery: (\n    queryKey: TRPCQueryKey,\n    opts?: TRPCFetchInfiniteQueryOptions<\n      unknown,\n      unknown,\n      TRPCClientError<TRouter>\n    >,\n  ) => Promise<void>;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientensurequerydata\n   */\n  ensureQueryData: (\n    queryKey: TRPCQueryKey,\n    opts?: TRPCFetchQueryOptions<unknown, TRPCClientError<TRouter>>,\n  ) => Promise<unknown>;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/framework/react/guides/query-invalidation\n   */\n  invalidateQueries: (\n    queryKey: TRPCQueryKey,\n    filters?: InvalidateQueryFilters<TRPCQueryKey>,\n    options?: InvalidateOptions,\n  ) => Promise<void>;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientresetqueries\n   */\n  resetQueries: (\n    queryKey: TRPCQueryKey,\n    filters?: QueryFilters<TRPCQueryKey>,\n    options?: ResetOptions,\n  ) => Promise<void>;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientrefetchqueries\n   */\n  refetchQueries: (\n    queryKey: TRPCQueryKey,\n    filters?: RefetchQueryFilters<TRPCQueryKey>,\n    options?: RefetchOptions,\n  ) => Promise<void>;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/framework/react/guides/query-cancellation\n   */\n  cancelQuery: (\n    queryKey: TRPCQueryKey,\n    options?: CancelOptions,\n  ) => Promise<void>;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientsetquerydata\n   */\n  setQueryData: (\n    queryKey: TRPCQueryKey,\n    updater: Updater<unknown, unknown>,\n    options?: SetDataOptions,\n  ) => void;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientsetqueriesdata\n   */\n  setQueriesData: (\n    queryKey: TRPCQueryKey,\n    filters: QueryFilters,\n    updater: Updater<unknown, unknown>,\n    options?: SetDataOptions,\n  ) => [QueryKey, unknown][];\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientgetquerydata\n   */\n  getQueryData: (queryKey: TRPCQueryKey) => unknown;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientsetquerydata\n   */\n  setInfiniteQueryData: (\n    queryKey: TRPCQueryKey,\n    updater: Updater<\n      InfiniteData<unknown> | undefined,\n      InfiniteData<unknown> | undefined\n    >,\n    options?: SetDataOptions,\n  ) => void;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientgetquerydata\n   */\n  getInfiniteQueryData: (\n    queryKey: TRPCQueryKey,\n  ) => InfiniteData<unknown> | undefined;\n\n  /**\n   * @see https://tanstack.com/query/latest/docs/reference/QueryClient/#queryclientsetmutationdefaults\n   */\n  setMutationDefaults: (\n    mutationKey: TRPCMutationKey,\n    options:\n      | MutationOptions\n      | ((args: {\n          canonicalMutationFn: (input: unknown) => Promise<unknown>;\n        }) => MutationOptions),\n  ) => void;\n\n  /**\n   * @see https://tanstack.com/query/latest/docs/reference/QueryClient#queryclientgetmutationdefaults\n   */\n  getMutationDefaults: (\n    mutationKey: TRPCMutationKey,\n  ) => MutationOptions | undefined;\n\n  /**\n   * @see https://tanstack.com/query/latest/docs/reference/QueryClient#queryclientismutating\n   */\n  isMutating: (filters: { mutationKey: TRPCMutationKey }) => number;\n}\nexport const TRPCContext = React.createContext?.(null as any);\n", "import type {\n  CancelOptions,\n  InfiniteData,\n  InvalidateOptions,\n  InvalidateQueryFilters,\n  Query,\n  QueryFilters,\n  QueryKey,\n  RefetchOptions,\n  RefetchQueryFilters,\n  ResetOptions,\n  SetDataOptions,\n  SkipToken,\n  Updater,\n} from '@tanstack/react-query';\nimport type { TRPCClientError } from '@trpc/client';\nimport { createTRPCClientProxy } from '@trpc/client';\nimport type {\n  AnyMutationProcedure,\n  AnyQueryProcedure,\n  AnyRootTypes,\n  AnyRouter,\n  DeepPartial,\n  inferProcedureInput,\n  inferProcedureOutput,\n  inferTransformedProcedureOutput,\n  ProtectedIntersection,\n  RouterRecord,\n} from '@trpc/server/unstable-core-do-not-import';\nimport {\n  createFlatProxy,\n  createRecursiveProxy,\n} from '@trpc/server/unstable-core-do-not-import';\nimport type {\n  DecoratedTRPCContextProps,\n  TRPCContextState,\n  TRPCFetchInfiniteQueryOptions,\n  TRPCFetchQueryOptions,\n  TRPCQueryUtils,\n} from '../../internals/context';\nimport { contextProps } from '../../internals/context';\nimport type { QueryKeyKnown, QueryType } from '../../internals/getQueryKey';\nimport {\n  getMutationKeyInternal,\n  getQueryKeyInternal,\n} from '../../internals/getQueryKey';\nimport type { InferMutationOptions } from '../../utils/inferReactQueryProcedure';\nimport type { ExtractCursorType } from '../hooks/types';\nimport type {\n  DefinedTRPCInfiniteQueryOptionsIn,\n  DefinedTRPCInfiniteQueryOptionsOut,\n  DefinedTRPCQueryOptionsIn,\n  DefinedTRPCQueryOptionsOut,\n  UndefinedTRPCInfiniteQueryOptionsIn,\n  UndefinedTRPCInfiniteQueryOptionsOut,\n  UndefinedTRPCQueryOptionsIn,\n  UndefinedTRPCQueryOptionsOut,\n  UnusedSkipTokenTRPCInfiniteQueryOptionsIn,\n  UnusedSkipTokenTRPCInfiniteQueryOptionsOut,\n  UnusedSkipTokenTRPCQueryOptionsIn,\n  UnusedSkipTokenTRPCQueryOptionsOut,\n} from '../types';\n\nexport type DecorateQueryProcedure<\n  TRoot extends AnyRootTypes,\n  TProcedure extends AnyQueryProcedure,\n> = {\n  /**\n   * @see https://tanstack.com/query/latest/docs/framework/react/reference/queryOptions#queryoptions\n   */\n  queryOptions<\n    TQueryFnData extends inferTransformedProcedureOutput<TRoot, TProcedure>,\n    TData = TQueryFnData,\n  >(\n    input: inferProcedureInput<TProcedure> | SkipToken,\n    opts: DefinedTRPCQueryOptionsIn<\n      TQueryFnData,\n      TData,\n      TRPCClientError<TRoot>\n    >,\n  ): DefinedTRPCQueryOptionsOut<TQueryFnData, TData, TRPCClientError<TRoot>>;\n  /**\n   * @see https://tanstack.com/query/latest/docs/framework/react/reference/queryOptions#queryoptions\n   */\n  queryOptions<\n    TQueryFnData extends inferTransformedProcedureOutput<TRoot, TProcedure>,\n    TData = TQueryFnData,\n  >(\n    input: inferProcedureInput<TProcedure> | SkipToken,\n    opts?: UnusedSkipTokenTRPCQueryOptionsIn<\n      TQueryFnData,\n      TData,\n      TRPCClientError<TRoot>\n    >,\n  ): UnusedSkipTokenTRPCQueryOptionsOut<\n    TQueryFnData,\n    TData,\n    TRPCClientError<TRoot>\n  >;\n  /**\n   * @see https://tanstack.com/query/latest/docs/framework/react/reference/queryOptions#queryoptions\n   */\n  queryOptions<\n    TQueryFnData extends inferTransformedProcedureOutput<TRoot, TProcedure>,\n    TData = TQueryFnData,\n  >(\n    input: inferProcedureInput<TProcedure> | SkipToken,\n    opts?: UndefinedTRPCQueryOptionsIn<\n      TQueryFnData,\n      TData,\n      TRPCClientError<TRoot>\n    >,\n  ): UndefinedTRPCQueryOptionsOut<TQueryFnData, TData, TRPCClientError<TRoot>>;\n\n  /**\n   * @see https://tanstack.com/query/latest/docs/framework/react/reference/infiniteQueryOptions#infinitequeryoptions\n   */\n  infiniteQueryOptions<\n    TQueryFnData extends inferTransformedProcedureOutput<TRoot, TProcedure>,\n    TData = TQueryFnData,\n  >(\n    input: inferProcedureInput<TProcedure> | SkipToken,\n    opts: DefinedTRPCInfiniteQueryOptionsIn<\n      inferProcedureInput<TProcedure>,\n      TQueryFnData,\n      TData,\n      TRPCClientError<TRoot>\n    >,\n  ): DefinedTRPCInfiniteQueryOptionsOut<\n    inferProcedureInput<TProcedure>,\n    TQueryFnData,\n    TData,\n    TRPCClientError<TRoot>\n  >;\n  /**\n   * @see https://tanstack.com/query/latest/docs/framework/react/reference/infiniteQueryOptions#infinitequeryoptions\n   */\n  infiniteQueryOptions<\n    TQueryFnData extends inferTransformedProcedureOutput<TRoot, TProcedure>,\n    TData = TQueryFnData,\n  >(\n    input: inferProcedureInput<TProcedure>,\n    opts: UnusedSkipTokenTRPCInfiniteQueryOptionsIn<\n      inferProcedureInput<TProcedure>,\n      TQueryFnData,\n      TData,\n      TRPCClientError<TRoot>\n    >,\n  ): UnusedSkipTokenTRPCInfiniteQueryOptionsOut<\n    inferProcedureInput<TProcedure>,\n    TQueryFnData,\n    TData,\n    TRPCClientError<TRoot>\n  >;\n  /**\n   * @see https://tanstack.com/query/latest/docs/framework/react/reference/infiniteQueryOptions#infinitequeryoptions\n   */\n  infiniteQueryOptions<\n    TQueryFnData extends inferTransformedProcedureOutput<TRoot, TProcedure>,\n    TData = TQueryFnData,\n  >(\n    input: inferProcedureInput<TProcedure> | SkipToken,\n    opts?: UndefinedTRPCInfiniteQueryOptionsIn<\n      inferProcedureInput<TProcedure>,\n      TQueryFnData,\n      TData,\n      TRPCClientError<TRoot>\n    >,\n  ): UndefinedTRPCInfiniteQueryOptionsOut<\n    inferProcedureInput<TProcedure>,\n    TQueryFnData,\n    TData,\n    TRPCClientError<TRoot>\n  >;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientfetchquery\n   */\n  fetch(\n    input: inferProcedureInput<TProcedure>,\n    opts?: TRPCFetchQueryOptions<\n      inferTransformedProcedureOutput<TRoot, TProcedure>,\n      TRPCClientError<TRoot>\n    >,\n  ): Promise<inferTransformedProcedureOutput<TRoot, TProcedure>>;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientfetchinfinitequery\n   */\n  fetchInfinite(\n    input: inferProcedureInput<TProcedure>,\n    opts?: TRPCFetchInfiniteQueryOptions<\n      inferProcedureInput<TProcedure>,\n      inferTransformedProcedureOutput<TRoot, TProcedure>,\n      TRPCClientError<TRoot>\n    >,\n  ): Promise<\n    InfiniteData<\n      inferTransformedProcedureOutput<TRoot, TProcedure>,\n      NonNullable<ExtractCursorType<inferProcedureInput<TProcedure>>> | null\n    >\n  >;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientprefetchquery\n   */\n  prefetch(\n    input: inferProcedureInput<TProcedure>,\n    opts?: TRPCFetchQueryOptions<\n      inferTransformedProcedureOutput<TRoot, TProcedure>,\n      TRPCClientError<TRoot>\n    >,\n  ): Promise<void>;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientprefetchinfinitequery\n   */\n  prefetchInfinite(\n    input: inferProcedureInput<TProcedure>,\n    opts?: TRPCFetchInfiniteQueryOptions<\n      inferProcedureInput<TProcedure>,\n      inferTransformedProcedureOutput<TRoot, TProcedure>,\n      TRPCClientError<TRoot>\n    >,\n  ): Promise<void>;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientensurequerydata\n   */\n  ensureData(\n    input: inferProcedureInput<TProcedure>,\n    opts?: TRPCFetchQueryOptions<\n      inferTransformedProcedureOutput<TRoot, TProcedure>,\n      TRPCClientError<TRoot>\n    >,\n  ): Promise<inferTransformedProcedureOutput<TRoot, TProcedure>>;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientinvalidatequeries\n   */\n  invalidate(\n    input?: DeepPartial<inferProcedureInput<TProcedure>>,\n    filters?: Omit<InvalidateQueryFilters, 'predicate'> & {\n      predicate?: (\n        query: Query<\n          inferProcedureOutput<TProcedure>,\n          TRPCClientError<TRoot>,\n          inferTransformedProcedureOutput<TRoot, TProcedure>,\n          QueryKeyKnown<\n            inferProcedureInput<TProcedure>,\n            inferProcedureInput<TProcedure> extends { cursor?: any } | void\n              ? 'infinite'\n              : 'query'\n          >\n        >,\n      ) => boolean;\n    },\n    options?: InvalidateOptions,\n  ): Promise<void>;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientrefetchqueries\n   */\n  refetch(\n    input?: inferProcedureInput<TProcedure>,\n    filters?: RefetchQueryFilters,\n    options?: RefetchOptions,\n  ): Promise<void>;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientcancelqueries\n   */\n  cancel(\n    input?: inferProcedureInput<TProcedure>,\n    options?: CancelOptions,\n  ): Promise<void>;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientresetqueries\n   */\n  reset(\n    input?: inferProcedureInput<TProcedure>,\n    options?: ResetOptions,\n  ): Promise<void>;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientsetquerydata\n   */\n  setData(\n    /**\n     * The input of the procedure\n     */\n    input: inferProcedureInput<TProcedure>,\n    updater: Updater<\n      inferTransformedProcedureOutput<TRoot, TProcedure> | undefined,\n      inferTransformedProcedureOutput<TRoot, TProcedure> | undefined\n    >,\n    options?: SetDataOptions,\n  ): void;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientsetquerydata\n   */\n  setQueriesData(\n    /**\n     * The input of the procedure\n     */\n    input: inferProcedureInput<TProcedure>,\n    filters: QueryFilters,\n    updater: Updater<\n      inferTransformedProcedureOutput<TRoot, TProcedure> | undefined,\n      inferTransformedProcedureOutput<TRoot, TProcedure> | undefined\n    >,\n    options?: SetDataOptions,\n  ): [QueryKey, inferTransformedProcedureOutput<TRoot, TProcedure>];\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientsetquerydata\n   */\n  setInfiniteData(\n    input: inferProcedureInput<TProcedure>,\n    updater: Updater<\n      | InfiniteData<\n          inferTransformedProcedureOutput<TRoot, TProcedure>,\n          NonNullable<ExtractCursorType<inferProcedureInput<TProcedure>>> | null\n        >\n      | undefined,\n      | InfiniteData<\n          inferTransformedProcedureOutput<TRoot, TProcedure>,\n          NonNullable<ExtractCursorType<inferProcedureInput<TProcedure>>> | null\n        >\n      | undefined\n    >,\n    options?: SetDataOptions,\n  ): void;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientgetquerydata\n   */\n  getData(\n    input?: inferProcedureInput<TProcedure>,\n  ): inferTransformedProcedureOutput<TRoot, TProcedure> | undefined;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientgetquerydata\n   */\n  getInfiniteData(\n    input?: inferProcedureInput<TProcedure>,\n  ):\n    | InfiniteData<\n        inferTransformedProcedureOutput<TRoot, TProcedure>,\n        NonNullable<ExtractCursorType<inferProcedureInput<TProcedure>>> | null\n      >\n    | undefined;\n};\n\ntype DecorateMutationProcedure<\n  TRoot extends AnyRootTypes,\n  TProcedure extends AnyMutationProcedure,\n> = {\n  setMutationDefaults<TMeta = unknown>(\n    options:\n      | InferMutationOptions<TRoot, TProcedure, TMeta>\n      | ((args: {\n          canonicalMutationFn: NonNullable<\n            InferMutationOptions<TRoot, TProcedure>['mutationFn']\n          >;\n        }) => InferMutationOptions<TRoot, TProcedure, TMeta>),\n  ): void;\n\n  getMutationDefaults(): InferMutationOptions<TRoot, TProcedure> | undefined;\n\n  isMutating(): number;\n};\n\n/**\n * this is the type that is used to add in procedures that can be used on\n * an entire router\n */\ntype DecorateRouter = {\n  /**\n   * Invalidate the full router\n   * @see https://trpc.io/docs/v10/useContext#query-invalidation\n   * @see https://tanstack.com/query/v5/docs/framework/react/guides/query-invalidation\n   */\n  invalidate(\n    input?: undefined,\n    filters?: InvalidateQueryFilters,\n    options?: InvalidateOptions,\n  ): Promise<void>;\n};\n\n/**\n * @internal\n */\nexport type DecoratedProcedureUtilsRecord<\n  TRoot extends AnyRootTypes,\n  TRecord extends RouterRecord,\n> = DecorateRouter & {\n  [TKey in keyof TRecord]: TRecord[TKey] extends infer $Value\n    ? $Value extends AnyQueryProcedure\n      ? DecorateQueryProcedure<TRoot, $Value>\n      : $Value extends AnyMutationProcedure\n        ? DecorateMutationProcedure<TRoot, $Value>\n        : $Value extends RouterRecord\n          ? DecoratedProcedureUtilsRecord<TRoot, $Value> & DecorateRouter\n          : never\n    : never;\n}; // Add functions that should be available at utils root\n\ntype AnyDecoratedProcedure = DecorateQueryProcedure<any, any> &\n  DecorateMutationProcedure<any, any>;\n\nexport type CreateReactUtils<\n  TRouter extends AnyRouter,\n  TSSRContext,\n> = ProtectedIntersection<\n  DecoratedTRPCContextProps<TRouter, TSSRContext>,\n  DecoratedProcedureUtilsRecord<\n    TRouter['_def']['_config']['$types'],\n    TRouter['_def']['record']\n  >\n>;\n\nexport type CreateQueryUtils<TRouter extends AnyRouter> =\n  DecoratedProcedureUtilsRecord<\n    TRouter['_def']['_config']['$types'],\n    TRouter['_def']['record']\n  >;\n\nexport const getQueryType = (\n  utilName: keyof AnyDecoratedProcedure,\n): QueryType => {\n  switch (utilName) {\n    case 'queryOptions':\n    case 'fetch':\n    case 'ensureData':\n    case 'prefetch':\n    case 'getData':\n    case 'setData':\n    case 'setQueriesData':\n      return 'query';\n\n    case 'infiniteQueryOptions':\n    case 'fetchInfinite':\n    case 'prefetchInfinite':\n    case 'getInfiniteData':\n    case 'setInfiniteData':\n      return 'infinite';\n\n    case 'setMutationDefaults':\n    case 'getMutationDefaults':\n    case 'isMutating':\n    case 'cancel':\n    case 'invalidate':\n    case 'refetch':\n    case 'reset':\n      return 'any';\n  }\n};\n\n/**\n * @internal\n */\nfunction createRecursiveUtilsProxy<TRouter extends AnyRouter>(\n  context: TRPCQueryUtils<TRouter>,\n) {\n  return createRecursiveProxy<CreateQueryUtils<TRouter>>((opts) => {\n    const path = [...opts.path];\n    const utilName = path.pop() as keyof AnyDecoratedProcedure;\n    const args = [...opts.args] as Parameters<\n      AnyDecoratedProcedure[typeof utilName]\n    >;\n    const input = args.shift(); // args can now be spread when input removed\n    const queryType = getQueryType(utilName);\n    const queryKey = getQueryKeyInternal(path, input, queryType);\n\n    const contextMap: Record<keyof AnyDecoratedProcedure, () => unknown> = {\n      infiniteQueryOptions: () =>\n        context.infiniteQueryOptions(path, queryKey, args[0]),\n      queryOptions: () => context.queryOptions(path, queryKey, ...args),\n      /**\n       * DecorateQueryProcedure\n       */\n      fetch: () => context.fetchQuery(queryKey, ...args),\n      fetchInfinite: () => context.fetchInfiniteQuery(queryKey, args[0]),\n      prefetch: () => context.prefetchQuery(queryKey, ...args),\n      prefetchInfinite: () => context.prefetchInfiniteQuery(queryKey, args[0]),\n      ensureData: () => context.ensureQueryData(queryKey, ...args),\n      invalidate: () => context.invalidateQueries(queryKey, ...args),\n      reset: () => context.resetQueries(queryKey, ...args),\n      refetch: () => context.refetchQueries(queryKey, ...args),\n      cancel: () => context.cancelQuery(queryKey, ...args),\n      setData: () => {\n        context.setQueryData(queryKey, args[0], args[1]);\n      },\n      setQueriesData: () =>\n        context.setQueriesData(queryKey, args[0], args[1], args[2]),\n      setInfiniteData: () => {\n        context.setInfiniteQueryData(queryKey, args[0], args[1]);\n      },\n      getData: () => context.getQueryData(queryKey),\n      getInfiniteData: () => context.getInfiniteQueryData(queryKey),\n      /**\n       * DecorateMutationProcedure\n       */\n      setMutationDefaults: () =>\n        context.setMutationDefaults(getMutationKeyInternal(path), input),\n      getMutationDefaults: () =>\n        context.getMutationDefaults(getMutationKeyInternal(path)),\n      isMutating: () =>\n        context.isMutating({ mutationKey: getMutationKeyInternal(path) }),\n    };\n\n    return contextMap[utilName]();\n  });\n}\n\n/**\n * @internal\n */\nexport function createReactQueryUtils<TRouter extends AnyRouter, TSSRContext>(\n  context: TRPCContextState<AnyRouter, TSSRContext>,\n) {\n  type CreateReactUtilsReturnType = CreateReactUtils<TRouter, TSSRContext>;\n\n  const clientProxy = createTRPCClientProxy(context.client);\n\n  const proxy = createRecursiveUtilsProxy(\n    context,\n  ) as CreateReactUtilsReturnType;\n\n  return createFlatProxy<CreateReactUtilsReturnType>((key) => {\n    const contextName = key as (typeof contextProps)[number];\n    if (contextName === 'client') {\n      return clientProxy;\n    }\n    if (contextProps.includes(contextName)) {\n      return context[contextName];\n    }\n\n    return proxy[key];\n  });\n}\n\n/**\n * @internal\n */\nexport function createQueryUtilsProxy<TRouter extends AnyRouter>(\n  context: TRPCQueryUtils<TRouter>,\n): CreateQueryUtils<TRouter> {\n  return createRecursiveUtilsProxy(context);\n}\n", "import type { QueryOptions } from '@tanstack/react-query';\nimport type { TRPCClient } from '@trpc/client';\nimport {\n  getUntypedClient,\n  TRPCUntypedClient,\n  type TRPCClientError,\n} from '@trpc/client';\nimport type {\n  AnyProcedure,\n  AnyQueryProcedure,\n  AnyRootTypes,\n  AnyRouter,\n  inferProcedureInput,\n  inferTransformedProcedureOutput,\n  RouterRecord,\n} from '@trpc/server/unstable-core-do-not-import';\nimport { createRecursiveProxy } from '@trpc/server/unstable-core-do-not-import';\nimport { getQueryKeyInternal } from '../../internals/getQueryKey';\nimport type {\n  TrpcQueryOptionsForUseQueries,\n  TrpcQueryOptionsForUseSuspenseQueries,\n} from '../../internals/useQueries';\nimport type { TRPCUseQueryBaseOptions } from '../hooks/types';\n\ntype GetQueryOptions<\n  TRoot extends AnyRootTypes,\n  TProcedure extends AnyProcedure,\n> = <TData = inferTransformedProcedureOutput<TRoot, TProcedure>>(\n  input: inferProcedureInput<TProcedure>,\n  opts?: TrpcQueryOptionsForUseQueries<\n    inferTransformedProcedureOutput<TRoot, TProcedure>,\n    TData,\n    TRPCClientError<TRoot>\n  >,\n) => TrpcQueryOptionsForUseQueries<\n  inferTransformedProcedureOutput<TRoot, TProcedure>,\n  TData,\n  TRPCClientError<TRoot>\n>;\n\n/**\n * @internal\n */\nexport type UseQueriesProcedureRecord<\n  TRoot extends AnyRootTypes,\n  TRecord extends RouterRecord,\n> = {\n  [TKey in keyof TRecord]: TRecord[TKey] extends infer $Value\n    ? $Value extends AnyQueryProcedure\n      ? GetQueryOptions<TRoot, $Value>\n      : $Value extends RouterRecord\n        ? UseQueriesProcedureRecord<TRoot, $Value>\n        : never\n    : never;\n};\n\ntype GetSuspenseQueryOptions<\n  TRoot extends AnyRootTypes,\n  TProcedure extends AnyQueryProcedure,\n> = <TData = inferTransformedProcedureOutput<TRoot, TProcedure>>(\n  input: inferProcedureInput<TProcedure>,\n  opts?: TrpcQueryOptionsForUseSuspenseQueries<\n    inferTransformedProcedureOutput<TRoot, TProcedure>,\n    TData,\n    TRPCClientError<TRoot>\n  >,\n) => TrpcQueryOptionsForUseSuspenseQueries<\n  inferTransformedProcedureOutput<TRoot, TProcedure>,\n  TData,\n  TRPCClientError<TRoot>\n>;\n\n/**\n * @internal\n */\nexport type UseSuspenseQueriesProcedureRecord<\n  TRoot extends AnyRootTypes,\n  TRecord extends RouterRecord,\n> = {\n  [TKey in keyof TRecord]: TRecord[TKey] extends infer $Value\n    ? $Value extends AnyQueryProcedure\n      ? GetSuspenseQueryOptions<TRoot, $Value>\n      : $Value extends RouterRecord\n        ? UseSuspenseQueriesProcedureRecord<TRoot, $Value>\n        : never\n    : never;\n};\n\n/**\n * Create proxy for `useQueries` options\n * @internal\n */\nexport function createUseQueries<TRouter extends AnyRouter>(\n  client: TRPCUntypedClient<TRouter> | TRPCClient<TRouter>,\n) {\n  const untypedClient: TRPCUntypedClient<TRouter> =\n    client instanceof TRPCUntypedClient ? client : getUntypedClient(client);\n\n  return createRecursiveProxy<\n    UseQueriesProcedureRecord<\n      TRouter['_def']['_config']['$types'],\n      TRouter['_def']['record']\n    >\n  >((opts) => {\n    const arrayPath = opts.path;\n    const dotPath = arrayPath.join('.');\n    const [input, _opts] = opts.args as [\n      unknown,\n      Partial<QueryOptions> & TRPCUseQueryBaseOptions,\n    ];\n\n    const options: QueryOptions = {\n      queryKey: getQueryKeyInternal(arrayPath, input, 'query'),\n      queryFn: () => {\n        return untypedClient.query(dotPath, input, _opts?.trpc);\n      },\n      ..._opts,\n    };\n\n    return options;\n  });\n}\n", "import type { TRPCQuery<PERSON>ey } from './getQueryKey';\n\n/**\n * @internal\n */\nexport function getClientArgs<TOptions>(\n  queryKey: TRPCQuery<PERSON>ey,\n  opts: TOptions,\n  infiniteParams?: {\n    pageParam: any;\n    direction: 'forward' | 'backward';\n  },\n) {\n  const path = queryKey[0];\n  let input = queryKey[1]?.input;\n  if (infiniteParams) {\n    input = {\n      ...(input ?? {}),\n      ...(infiniteParams.pageParam ? { cursor: infiniteParams.pageParam } : {}),\n      direction: infiniteParams.direction,\n    };\n  }\n  return [path.join('.'), input, (opts as any)?.trpc] as const;\n}\n", "function _asyncIterator(r) {\n  var n,\n    t,\n    o,\n    e = 2;\n  for (\"undefined\" != typeof Symbol && (t = Symbol.asyncIterator, o = Symbol.iterator); e--;) {\n    if (t && null != (n = r[t])) return n.call(r);\n    if (o && null != (n = r[o])) return new AsyncFromSyncIterator(n.call(r));\n    t = \"@@asyncIterator\", o = \"@@iterator\";\n  }\n  throw new TypeError(\"Object is not async iterable\");\n}\nfunction AsyncFromSyncIterator(r) {\n  function AsyncFromSyncIteratorContinuation(r) {\n    if (Object(r) !== r) return Promise.reject(new TypeError(r + \" is not an object.\"));\n    var n = r.done;\n    return Promise.resolve(r.value).then(function (r) {\n      return {\n        value: r,\n        done: n\n      };\n    });\n  }\n  return AsyncFromSyncIterator = function AsyncFromSyncIterator(r) {\n    this.s = r, this.n = r.next;\n  }, AsyncFromSyncIterator.prototype = {\n    s: null,\n    n: null,\n    next: function next() {\n      return AsyncFromSyncIteratorContinuation(this.n.apply(this.s, arguments));\n    },\n    \"return\": function _return(r) {\n      var n = this.s[\"return\"];\n      return void 0 === n ? Promise.resolve({\n        value: r,\n        done: !0\n      }) : AsyncFromSyncIteratorContinuation(n.apply(this.s, arguments));\n    },\n    \"throw\": function _throw(r) {\n      var n = this.s[\"return\"];\n      return void 0 === n ? Promise.reject(r) : AsyncFromSyncIteratorContinuation(n.apply(this.s, arguments));\n    }\n  }, new AsyncFromSyncIterator(r);\n}\nmodule.exports = _asyncIterator, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "import type { QueryClient } from '@tanstack/react-query';\nimport * as React from 'react';\nimport type { TRPCQueryOptionsResult } from '../shared';\nimport type { TRPCHookResult } from '../shared/hooks/types';\nimport type { TRPCQueryKey } from './getQueryKey';\n\nexport function createTRPCOptionsResult(value: {\n  path: readonly string[];\n}): TRPCQueryOptionsResult['trpc'] {\n  const path = value.path.join('.');\n\n  return {\n    path,\n  };\n}\n\n/**\n * Makes a stable reference of the `trpc` prop\n */\nexport function useHookResult(value: {\n  path: readonly string[];\n}): TRPCHookResult['trpc'] {\n  const result = createTRPCOptionsResult(value);\n  return React.useMemo(() => result, [result]);\n}\n\n/**\n * @internal\n */\nexport async function buildQueryFromAsyncIterable(\n  asyncIterable: AsyncIterable<unknown>,\n  queryClient: QueryClient,\n  queryKey: TRPCQuery<PERSON>ey,\n) {\n  const queryCache = queryClient.getQueryCache();\n\n  const query = queryCache.build(queryClient, {\n    queryKey,\n  });\n\n  query.setState({\n    data: [],\n    status: 'success',\n  });\n\n  const aggregate: unknown[] = [];\n  for await (const value of asyncIterable) {\n    aggregate.push(value);\n\n    query.setState({\n      data: [...aggregate],\n    });\n  }\n  return aggregate;\n}\n", "import type { QueryFunctionContext } from '@tanstack/react-query';\nimport {\n  infiniteQueryOptions,\n  queryOptions,\n  skipToken,\n  type QueryClient,\n} from '@tanstack/react-query';\nimport type { TRPCClient, TRPCClientError } from '@trpc/client';\nimport { getUntypedClient, TRPCUntypedClient } from '@trpc/client';\nimport type { AnyRouter } from '@trpc/server/unstable-core-do-not-import';\nimport { isAsyncIterable } from '@trpc/server/unstable-core-do-not-import';\nimport type { AnyClientTypes } from '@trpc/server/unstable-core-do-not-import/clientish/inferrable';\nimport { getClientArgs } from '../internals/getClientArgs';\nimport type { TRPCQueryKey } from '../internals/getQueryKey';\nimport {\n  buildQueryFromAsyncIterable,\n  createTRPCOptionsResult,\n} from '../internals/trpcResult';\nimport type { DefinedTRPCQueryOptionsOut } from '../shared';\nimport { type TRPCQueryUtils } from '../shared';\n\nexport interface CreateQueryUtilsOptions<TRouter extends AnyRouter> {\n  /**\n   * The `TRPCClient`\n   */\n  client: TRPCClient<TRouter> | TRPCUntypedClient<TRouter>;\n  /**\n   * The `QueryClient` from `react-query`\n   */\n  queryClient: QueryClient;\n}\n\n/**\n * Creates a set of utility functions that can be used to interact with `react-query`\n * @param opts the `TRPCClient` and `QueryClient` to use\n * @returns a set of utility functions that can be used to interact with `react-query`\n * @internal\n */\nexport function createUtilityFunctions<TRouter extends AnyRouter>(\n  opts: CreateQueryUtilsOptions<TRouter>,\n): TRPCQueryUtils<TRouter> {\n  const { client, queryClient } = opts;\n  const untypedClient =\n    client instanceof TRPCUntypedClient ? client : getUntypedClient(client);\n\n  return {\n    infiniteQueryOptions: (path, queryKey, opts) => {\n      const inputIsSkipToken = queryKey[1]?.input === skipToken;\n\n      const queryFn = async (\n        queryFnContext: QueryFunctionContext<TRPCQueryKey, unknown>,\n      ): Promise<unknown> => {\n        const actualOpts = {\n          ...opts,\n          trpc: {\n            ...opts?.trpc,\n            ...(opts?.trpc?.abortOnUnmount\n              ? { signal: queryFnContext.signal }\n              : { signal: null }),\n          },\n        };\n\n        const result = await untypedClient.query(\n          ...getClientArgs(queryKey, actualOpts, {\n            direction: queryFnContext.direction,\n            pageParam: queryFnContext.pageParam,\n          }),\n        );\n\n        return result;\n      };\n\n      return Object.assign(\n        infiniteQueryOptions({\n          ...opts,\n          initialData: opts?.initialData as any,\n          queryKey,\n          queryFn: inputIsSkipToken ? skipToken : queryFn,\n          initialPageParam: (opts?.initialCursor as any) ?? null,\n        }),\n        { trpc: createTRPCOptionsResult({ path }) },\n      );\n    },\n\n    queryOptions: (path, queryKey, opts) => {\n      const inputIsSkipToken = queryKey[1]?.input === skipToken;\n\n      const queryFn = async (\n        queryFnContext: QueryFunctionContext<TRPCQueryKey>,\n      ): Promise<unknown> => {\n        const actualOpts = {\n          ...opts,\n          trpc: {\n            ...opts?.trpc,\n            ...(opts?.trpc?.abortOnUnmount\n              ? { signal: queryFnContext.signal }\n              : { signal: null }),\n          },\n        };\n\n        const result = await untypedClient.query(\n          ...getClientArgs(queryKey, actualOpts),\n        );\n\n        if (isAsyncIterable(result)) {\n          return buildQueryFromAsyncIterable(result, queryClient, queryKey);\n        }\n\n        return result;\n      };\n\n      return Object.assign(\n        queryOptions({\n          ...opts,\n          initialData: opts?.initialData,\n          queryKey,\n          queryFn: inputIsSkipToken ? skipToken : queryFn,\n        }),\n        { trpc: createTRPCOptionsResult({ path }) },\n      ) as DefinedTRPCQueryOptionsOut<\n        unknown,\n        unknown,\n        TRPCClientError<AnyClientTypes>\n      >;\n    },\n\n    fetchQuery: (queryKey, opts) => {\n      return queryClient.fetchQuery({\n        ...opts,\n        queryKey,\n        queryFn: () => untypedClient.query(...getClientArgs(queryKey, opts)),\n      });\n    },\n\n    fetchInfiniteQuery: (queryKey, opts) => {\n      return queryClient.fetchInfiniteQuery({\n        ...opts,\n        queryKey,\n        queryFn: ({ pageParam, direction }) => {\n          return untypedClient.query(\n            ...getClientArgs(queryKey, opts, { pageParam, direction }),\n          );\n        },\n        initialPageParam: opts?.initialCursor ?? null,\n      });\n    },\n\n    prefetchQuery: (queryKey, opts) => {\n      return queryClient.prefetchQuery({\n        ...opts,\n        queryKey,\n        queryFn: () => untypedClient.query(...getClientArgs(queryKey, opts)),\n      });\n    },\n\n    prefetchInfiniteQuery: (queryKey, opts) => {\n      return queryClient.prefetchInfiniteQuery({\n        ...opts,\n        queryKey,\n        queryFn: ({ pageParam, direction }) => {\n          return untypedClient.query(\n            ...getClientArgs(queryKey, opts, { pageParam, direction }),\n          );\n        },\n        initialPageParam: opts?.initialCursor ?? null,\n      });\n    },\n\n    ensureQueryData: (queryKey, opts) => {\n      return queryClient.ensureQueryData({\n        ...opts,\n        queryKey,\n        queryFn: () => untypedClient.query(...getClientArgs(queryKey, opts)),\n      });\n    },\n\n    invalidateQueries: (queryKey, filters, options) => {\n      return queryClient.invalidateQueries(\n        {\n          ...filters,\n          queryKey,\n        },\n        options,\n      );\n    },\n    resetQueries: (queryKey, filters, options) => {\n      return queryClient.resetQueries(\n        {\n          ...filters,\n          queryKey,\n        },\n        options,\n      );\n    },\n\n    refetchQueries: (queryKey, filters, options) => {\n      return queryClient.refetchQueries(\n        {\n          ...filters,\n          queryKey,\n        },\n        options,\n      );\n    },\n\n    cancelQuery: (queryKey, options) => {\n      return queryClient.cancelQueries(\n        {\n          queryKey,\n        },\n        options,\n      );\n    },\n\n    setQueryData: (queryKey, updater, options) => {\n      return queryClient.setQueryData(queryKey, updater as any, options);\n    },\n\n    // eslint-disable-next-line max-params\n    setQueriesData: (queryKey, filters, updater, options) => {\n      return queryClient.setQueriesData(\n        {\n          ...filters,\n          queryKey,\n        },\n        updater,\n        options,\n      );\n    },\n\n    getQueryData: (queryKey) => {\n      return queryClient.getQueryData(queryKey);\n    },\n\n    setInfiniteQueryData: (queryKey, updater, options) => {\n      return queryClient.setQueryData(queryKey, updater as any, options);\n    },\n\n    getInfiniteQueryData: (queryKey) => {\n      return queryClient.getQueryData(queryKey);\n    },\n\n    setMutationDefaults: (mutationKey, options) => {\n      const path = mutationKey[0];\n      const canonicalMutationFn = (input: unknown) => {\n        return untypedClient.mutation(\n          ...getClientArgs([path, { input }], opts),\n        );\n      };\n      return queryClient.setMutationDefaults(\n        mutationKey,\n        typeof options === 'function'\n          ? options({ canonicalMutationFn })\n          : options,\n      );\n    },\n\n    getMutationDefaults: (mutationKey) => {\n      return queryClient.getMutationDefaults(mutationKey);\n    },\n\n    isMutating: (filters) => {\n      return queryClient.isMutating({\n        ...filters,\n        exact: true,\n      });\n    },\n  };\n}\n", "// TODO: Look into fixing react-compiler support\n/* eslint-disable react-hooks/react-compiler */\nimport {\n  useInfiniteQuery as __useInfiniteQuery,\n  useMutation as __useMutation,\n  usePrefetchInfiniteQuery as __usePrefetchInfiniteQuery,\n  useQueries as __useQueries,\n  useQuery as __useQuery,\n  useSuspenseInfiniteQuery as __useSuspenseInfiniteQuery,\n  useSuspenseQueries as __useSuspenseQueries,\n  useSuspenseQuery as __useSuspenseQuery,\n  usePrefetchQuery as _usePrefetchQuery,\n  hashKey,\n  skipToken,\n} from '@tanstack/react-query';\nimport type { TRPCClientErrorLike } from '@trpc/client';\nimport {\n  createTRPCClient,\n  getUntypedClient,\n  TRPCUntypedClient,\n} from '@trpc/client';\nimport type { Unsubscribable } from '@trpc/server/observable';\nimport type { AnyRouter } from '@trpc/server/unstable-core-do-not-import';\nimport { isAsyncIterable } from '@trpc/server/unstable-core-do-not-import';\nimport * as React from 'react';\nimport type { SSRState, TRPCContextState } from '../../internals/context';\nimport { TRPCContext } from '../../internals/context';\nimport { getClientArgs } from '../../internals/getClientArgs';\nimport type { TRPCQueryKey } from '../../internals/getQueryKey';\nimport {\n  getMutationKeyInternal,\n  getQueryKeyInternal,\n} from '../../internals/getQueryKey';\nimport {\n  buildQueryFromAsyncIterable,\n  useHookResult,\n} from '../../internals/trpcResult';\nimport type {\n  TRPCUseQueries,\n  TRPCUseSuspenseQueries,\n} from '../../internals/useQueries';\nimport { createUtilityFunctions } from '../../utils/createUtilityFunctions';\nimport { createUseQueries } from '../proxy/useQueriesProxy';\nimport type { CreateTRPCReactOptions, UseMutationOverride } from '../types';\nimport type {\n  TRPCProvider,\n  TRPCQueryOptions,\n  TRPCSubscriptionConnectingResult,\n  TRPCSubscriptionIdleResult,\n  TRPCSubscriptionResult,\n  UseTRPCInfiniteQueryOptions,\n  UseTRPCInfiniteQueryResult,\n  UseTRPCMutationOptions,\n  UseTRPCMutationResult,\n  UseTRPCPrefetchInfiniteQueryOptions,\n  UseTRPCPrefetchQueryOptions,\n  UseTRPCQueryOptions,\n  UseTRPCQueryResult,\n  UseTRPCSubscriptionOptions,\n  UseTRPCSuspenseInfiniteQueryOptions,\n  UseTRPCSuspenseInfiniteQueryResult,\n  UseTRPCSuspenseQueryOptions,\n  UseTRPCSuspenseQueryResult,\n} from './types';\n\nconst trackResult = <T extends object>(\n  result: T,\n  onTrackResult: (key: keyof T) => void,\n): T => {\n  const trackedResult = new Proxy(result, {\n    get(target, prop) {\n      onTrackResult(prop as keyof T);\n      return target[prop as keyof T];\n    },\n  });\n\n  return trackedResult;\n};\n\n/**\n * @internal\n */\nexport function createRootHooks<\n  TRouter extends AnyRouter,\n  TSSRContext = unknown,\n>(config?: CreateTRPCReactOptions<TRouter>) {\n  const mutationSuccessOverride: UseMutationOverride['onSuccess'] =\n    config?.overrides?.useMutation?.onSuccess ??\n    ((options) => options.originalFn());\n\n  type TError = TRPCClientErrorLike<TRouter>;\n\n  type ProviderContext = TRPCContextState<TRouter, TSSRContext>;\n\n  const Context = (config?.context ??\n    TRPCContext) as React.Context<ProviderContext>;\n\n  const createClient = createTRPCClient<TRouter>;\n\n  const TRPCProvider: TRPCProvider<TRouter, TSSRContext> = (props) => {\n    const { abortOnUnmount = false, queryClient, ssrContext } = props;\n    const [ssrState, setSSRState] = React.useState<SSRState>(\n      props.ssrState ?? false,\n    );\n\n    const client: TRPCUntypedClient<TRouter> =\n      props.client instanceof TRPCUntypedClient\n        ? props.client\n        : getUntypedClient(props.client);\n\n    const fns = React.useMemo(\n      () =>\n        createUtilityFunctions({\n          client,\n          queryClient,\n        }),\n      [client, queryClient],\n    );\n\n    const contextValue = React.useMemo<ProviderContext>(\n      () => ({\n        abortOnUnmount,\n        queryClient,\n        client,\n        ssrContext: ssrContext ?? null,\n        ssrState,\n        ...fns,\n      }),\n      [abortOnUnmount, client, fns, queryClient, ssrContext, ssrState],\n    );\n\n    React.useEffect(() => {\n      // Only updating state to `mounted` if we are using SSR.\n      // This makes it so we don't have an unnecessary re-render when opting out of SSR.\n      setSSRState((state) => (state ? 'mounted' : false));\n    }, []);\n    return (\n      <Context.Provider value={contextValue}>{props.children}</Context.Provider>\n    );\n  };\n\n  function useContext() {\n    const context = React.useContext(Context);\n\n    if (!context) {\n      throw new Error(\n        'Unable to find tRPC Context. Did you forget to wrap your App inside `withTRPC` HoC?',\n      );\n    }\n    return context;\n  }\n\n  /**\n   * Hack to make sure errors return `status`='error` when doing SSR\n   * @see https://github.com/trpc/trpc/pull/1645\n   */\n  function useSSRQueryOptionsIfNeeded<\n    TOptions extends { retryOnMount?: boolean } | undefined,\n  >(queryKey: TRPCQueryKey, opts: TOptions): TOptions {\n    const { queryClient, ssrState } = useContext();\n    return ssrState &&\n      ssrState !== 'mounted' &&\n      queryClient.getQueryCache().find({ queryKey })?.state.status === 'error'\n      ? {\n          retryOnMount: false,\n          ...opts,\n        }\n      : opts;\n  }\n\n  function useQuery(\n    path: readonly string[],\n    input: unknown,\n    opts?: UseTRPCQueryOptions<unknown, unknown, TError>,\n  ): UseTRPCQueryResult<unknown, TError> {\n    const context = useContext();\n    const { abortOnUnmount, client, ssrState, queryClient, prefetchQuery } =\n      context;\n    const queryKey = getQueryKeyInternal(path, input, 'query');\n\n    const defaultOpts = queryClient.getQueryDefaults(queryKey);\n\n    const isInputSkipToken = input === skipToken;\n\n    if (\n      typeof window === 'undefined' &&\n      ssrState === 'prepass' &&\n      opts?.trpc?.ssr !== false &&\n      (opts?.enabled ?? defaultOpts?.enabled) !== false &&\n      !isInputSkipToken &&\n      !queryClient.getQueryCache().find({ queryKey })\n    ) {\n      void prefetchQuery(queryKey, opts as any);\n    }\n    const ssrOpts = useSSRQueryOptionsIfNeeded(queryKey, {\n      ...defaultOpts,\n      ...opts,\n    });\n\n    const shouldAbortOnUnmount =\n      opts?.trpc?.abortOnUnmount ?? config?.abortOnUnmount ?? abortOnUnmount;\n\n    const hook = __useQuery(\n      {\n        ...ssrOpts,\n        queryKey: queryKey as any,\n        queryFn: isInputSkipToken\n          ? input\n          : async (queryFunctionContext) => {\n              const actualOpts = {\n                ...ssrOpts,\n                trpc: {\n                  ...ssrOpts?.trpc,\n                  ...(shouldAbortOnUnmount\n                    ? { signal: queryFunctionContext.signal }\n                    : { signal: null }),\n                },\n              };\n\n              const result = await client.query(\n                ...getClientArgs(queryKey, actualOpts),\n              );\n\n              if (isAsyncIterable(result)) {\n                return buildQueryFromAsyncIterable(\n                  result,\n                  queryClient,\n                  queryKey,\n                );\n              }\n              return result;\n            },\n      },\n      queryClient,\n    ) as UseTRPCQueryResult<unknown, TError>;\n\n    hook.trpc = useHookResult({\n      path,\n    });\n\n    return hook;\n  }\n\n  function usePrefetchQuery(\n    path: string[],\n    input: unknown,\n    opts?: UseTRPCPrefetchQueryOptions<unknown, unknown, TError>,\n  ): void {\n    const context = useContext();\n    const queryKey = getQueryKeyInternal(path, input, 'query');\n\n    const isInputSkipToken = input === skipToken;\n\n    const shouldAbortOnUnmount =\n      opts?.trpc?.abortOnUnmount ??\n      config?.abortOnUnmount ??\n      context.abortOnUnmount;\n\n    _usePrefetchQuery({\n      ...opts,\n      queryKey: queryKey as any,\n      queryFn: isInputSkipToken\n        ? input\n        : (queryFunctionContext) => {\n            const actualOpts = {\n              trpc: {\n                ...opts?.trpc,\n                ...(shouldAbortOnUnmount\n                  ? { signal: queryFunctionContext.signal }\n                  : {}),\n              },\n            };\n\n            return context.client.query(...getClientArgs(queryKey, actualOpts));\n          },\n    });\n  }\n\n  function useSuspenseQuery(\n    path: readonly string[],\n    input: unknown,\n    opts?: UseTRPCSuspenseQueryOptions<unknown, unknown, TError>,\n  ): UseTRPCSuspenseQueryResult<unknown, TError> {\n    const context = useContext();\n    const queryKey = getQueryKeyInternal(path, input, 'query');\n\n    const shouldAbortOnUnmount =\n      opts?.trpc?.abortOnUnmount ??\n      config?.abortOnUnmount ??\n      context.abortOnUnmount;\n\n    const hook = __useSuspenseQuery(\n      {\n        ...opts,\n        queryKey: queryKey as any,\n        queryFn: (queryFunctionContext) => {\n          const actualOpts = {\n            ...opts,\n            trpc: {\n              ...opts?.trpc,\n              ...(shouldAbortOnUnmount\n                ? { signal: queryFunctionContext.signal }\n                : { signal: null }),\n            },\n          };\n\n          return context.client.query(...getClientArgs(queryKey, actualOpts));\n        },\n      },\n      context.queryClient,\n    ) as UseTRPCQueryResult<unknown, TError>;\n\n    hook.trpc = useHookResult({\n      path,\n    });\n\n    return [hook.data, hook as any];\n  }\n\n  function useMutation(\n    path: readonly string[],\n    opts?: UseTRPCMutationOptions<unknown, TError, unknown, unknown>,\n  ): UseTRPCMutationResult<unknown, TError, unknown, unknown> {\n    const { client, queryClient } = useContext();\n\n    const mutationKey = getMutationKeyInternal(path);\n\n    const defaultOpts = queryClient.defaultMutationOptions(\n      queryClient.getMutationDefaults(mutationKey),\n    );\n\n    const hook = __useMutation(\n      {\n        ...opts,\n        mutationKey: mutationKey,\n        mutationFn: (input) => {\n          return client.mutation(...getClientArgs([path, { input }], opts));\n        },\n        onSuccess(...args) {\n          const originalFn = () =>\n            opts?.onSuccess?.(...args) ?? defaultOpts?.onSuccess?.(...args);\n\n          return mutationSuccessOverride({\n            originalFn,\n            queryClient,\n            meta: opts?.meta ?? defaultOpts?.meta ?? {},\n          });\n        },\n      },\n      queryClient,\n    ) as UseTRPCMutationResult<unknown, TError, unknown, unknown>;\n\n    hook.trpc = useHookResult({\n      path,\n    });\n\n    return hook;\n  }\n  const initialStateIdle: Omit<TRPCSubscriptionIdleResult<unknown>, 'reset'> = {\n    data: undefined,\n    error: null,\n    status: 'idle',\n  };\n\n  const initialStateConnecting: Omit<\n    TRPCSubscriptionConnectingResult<unknown, TError>,\n    'reset'\n  > = {\n    data: undefined,\n    error: null,\n    status: 'connecting',\n  };\n\n  /* istanbul ignore next -- @preserve */\n  function useSubscription(\n    path: readonly string[],\n    input: unknown,\n    opts: UseTRPCSubscriptionOptions<unknown, TError>,\n  ) {\n    const enabled = opts?.enabled ?? input !== skipToken;\n    const queryKey = hashKey(getQueryKeyInternal(path, input, 'any'));\n    const { client } = useContext();\n\n    const optsRef = React.useRef<typeof opts>(opts);\n    React.useEffect(() => {\n      optsRef.current = opts;\n    });\n\n    type $Result = TRPCSubscriptionResult<unknown, TError>;\n\n    const [trackedProps] = React.useState(new Set<keyof $Result>([]));\n\n    const addTrackedProp = React.useCallback(\n      (key: keyof $Result) => {\n        trackedProps.add(key);\n      },\n      [trackedProps],\n    );\n\n    const currentSubscriptionRef = React.useRef<Unsubscribable>(null);\n\n    const updateState = React.useCallback(\n      (callback: (prevState: $Result) => $Result) => {\n        const prev = resultRef.current;\n        const next = (resultRef.current = callback(prev));\n\n        let shouldUpdate = false;\n        for (const key of trackedProps) {\n          if (prev[key] !== next[key]) {\n            shouldUpdate = true;\n            break;\n          }\n        }\n        if (shouldUpdate) {\n          setState(trackResult(next, addTrackedProp));\n        }\n      },\n      [addTrackedProp, trackedProps],\n    );\n\n    const reset = React.useCallback((): void => {\n      // unsubscribe from the previous subscription\n      currentSubscriptionRef.current?.unsubscribe();\n\n      if (!enabled) {\n        updateState(() => ({ ...initialStateIdle, reset }));\n        return;\n      }\n      updateState(() => ({ ...initialStateConnecting, reset }));\n      const subscription = client.subscription(\n        path.join('.'),\n        input ?? undefined,\n        {\n          onStarted: () => {\n            optsRef.current.onStarted?.();\n            updateState((prev) => ({\n              ...prev,\n              status: 'pending',\n              error: null,\n            }));\n          },\n          onData: (data) => {\n            optsRef.current.onData?.(data);\n            updateState((prev) => ({\n              ...prev,\n              status: 'pending',\n              data,\n              error: null,\n            }));\n          },\n          onError: (error) => {\n            optsRef.current.onError?.(error);\n            updateState((prev) => ({\n              ...prev,\n              status: 'error',\n              error,\n            }));\n          },\n          onConnectionStateChange: (result) => {\n            updateState((prev) => {\n              switch (result.state) {\n                case 'idle':\n                  return {\n                    ...prev,\n                    status: result.state,\n                    error: null,\n                    data: undefined,\n                  };\n                case 'connecting':\n                  return {\n                    ...prev,\n                    error: result.error,\n                    status: result.state,\n                  };\n\n                case 'pending':\n                  // handled when data is / onStarted\n                  return prev;\n              }\n            });\n          },\n          onComplete: () => {\n            optsRef.current.onComplete?.();\n\n            // In the case of WebSockets, the connection might not be idle so `onConnectionStateChange` will not be called until the connection is closed.\n            // In this case, we need to set the state to idle manually.\n            updateState((prev) => ({\n              ...prev,\n              status: 'idle',\n              error: null,\n              data: undefined,\n            }));\n\n            // (We might want to add a `connectionState` to the state to track the connection state separately)\n          },\n        },\n      );\n\n      currentSubscriptionRef.current = subscription;\n\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [client, queryKey, enabled, updateState]);\n    React.useEffect(() => {\n      reset();\n\n      return () => {\n        currentSubscriptionRef.current?.unsubscribe();\n      };\n    }, [reset]);\n\n    const resultRef = React.useRef<$Result>(\n      enabled\n        ? { ...initialStateConnecting, reset }\n        : { ...initialStateIdle, reset },\n    );\n\n    const [state, setState] = React.useState<$Result>(\n      trackResult(resultRef.current, addTrackedProp),\n    );\n\n    return state;\n  }\n\n  function useInfiniteQuery(\n    path: readonly string[],\n    input: unknown,\n    opts: UseTRPCInfiniteQueryOptions<unknown, unknown, TError>,\n  ): UseTRPCInfiniteQueryResult<unknown, TError, unknown> {\n    const {\n      client,\n      ssrState,\n      prefetchInfiniteQuery,\n      queryClient,\n      abortOnUnmount,\n    } = useContext();\n    const queryKey = getQueryKeyInternal(path, input, 'infinite');\n\n    const defaultOpts = queryClient.getQueryDefaults(queryKey);\n\n    const isInputSkipToken = input === skipToken;\n\n    if (\n      typeof window === 'undefined' &&\n      ssrState === 'prepass' &&\n      opts?.trpc?.ssr !== false &&\n      (opts?.enabled ?? defaultOpts?.enabled) !== false &&\n      !isInputSkipToken &&\n      !queryClient.getQueryCache().find({ queryKey })\n    ) {\n      void prefetchInfiniteQuery(queryKey, { ...defaultOpts, ...opts } as any);\n    }\n\n    const ssrOpts = useSSRQueryOptionsIfNeeded(queryKey, {\n      ...defaultOpts,\n      ...opts,\n    });\n\n    // request option should take priority over global\n    const shouldAbortOnUnmount = opts?.trpc?.abortOnUnmount ?? abortOnUnmount;\n\n    const hook = __useInfiniteQuery(\n      {\n        ...ssrOpts,\n        initialPageParam: opts.initialCursor ?? null,\n        persister: opts.persister,\n        queryKey: queryKey as any,\n        queryFn: isInputSkipToken\n          ? input\n          : (queryFunctionContext) => {\n              const actualOpts = {\n                ...ssrOpts,\n                trpc: {\n                  ...ssrOpts?.trpc,\n                  ...(shouldAbortOnUnmount\n                    ? { signal: queryFunctionContext.signal }\n                    : { signal: null }),\n                },\n              };\n\n              return client.query(\n                ...getClientArgs(queryKey, actualOpts, {\n                  pageParam:\n                    queryFunctionContext.pageParam ?? opts.initialCursor,\n                  direction: queryFunctionContext.direction,\n                }),\n              );\n            },\n      },\n      queryClient,\n    ) as UseTRPCInfiniteQueryResult<unknown, TError, unknown>;\n\n    hook.trpc = useHookResult({\n      path,\n    });\n    return hook;\n  }\n\n  function usePrefetchInfiniteQuery(\n    path: string[],\n    input: unknown,\n    opts: UseTRPCPrefetchInfiniteQueryOptions<unknown, unknown, TError>,\n  ): void {\n    const context = useContext();\n    const queryKey = getQueryKeyInternal(path, input, 'infinite');\n\n    const defaultOpts = context.queryClient.getQueryDefaults(queryKey);\n\n    const isInputSkipToken = input === skipToken;\n\n    const ssrOpts = useSSRQueryOptionsIfNeeded(queryKey, {\n      ...defaultOpts,\n      ...opts,\n    });\n\n    // request option should take priority over global\n    const shouldAbortOnUnmount =\n      opts?.trpc?.abortOnUnmount ?? context.abortOnUnmount;\n\n    __usePrefetchInfiniteQuery({\n      ...opts,\n      initialPageParam: opts.initialCursor ?? null,\n      queryKey,\n      queryFn: isInputSkipToken\n        ? input\n        : (queryFunctionContext) => {\n            const actualOpts = {\n              ...ssrOpts,\n              trpc: {\n                ...ssrOpts?.trpc,\n                ...(shouldAbortOnUnmount\n                  ? { signal: queryFunctionContext.signal }\n                  : {}),\n              },\n            };\n\n            return context.client.query(\n              ...getClientArgs(queryKey, actualOpts, {\n                pageParam: queryFunctionContext.pageParam ?? opts.initialCursor,\n                direction: queryFunctionContext.direction,\n              }),\n            );\n          },\n    });\n  }\n\n  function useSuspenseInfiniteQuery(\n    path: readonly string[],\n    input: unknown,\n    opts: UseTRPCSuspenseInfiniteQueryOptions<unknown, unknown, TError>,\n  ): UseTRPCSuspenseInfiniteQueryResult<unknown, TError, unknown> {\n    const context = useContext();\n    const queryKey = getQueryKeyInternal(path, input, 'infinite');\n\n    const defaultOpts = context.queryClient.getQueryDefaults(queryKey);\n\n    const ssrOpts = useSSRQueryOptionsIfNeeded(queryKey, {\n      ...defaultOpts,\n      ...opts,\n    });\n\n    // request option should take priority over global\n    const shouldAbortOnUnmount =\n      opts?.trpc?.abortOnUnmount ?? context.abortOnUnmount;\n\n    const hook = __useSuspenseInfiniteQuery(\n      {\n        ...opts,\n        initialPageParam: opts.initialCursor ?? null,\n        queryKey,\n        queryFn: (queryFunctionContext) => {\n          const actualOpts = {\n            ...ssrOpts,\n            trpc: {\n              ...ssrOpts?.trpc,\n              ...(shouldAbortOnUnmount\n                ? { signal: queryFunctionContext.signal }\n                : {}),\n            },\n          };\n\n          return context.client.query(\n            ...getClientArgs(queryKey, actualOpts, {\n              pageParam: queryFunctionContext.pageParam ?? opts.initialCursor,\n              direction: queryFunctionContext.direction,\n            }),\n          );\n        },\n      },\n      context.queryClient,\n    ) as UseTRPCInfiniteQueryResult<unknown, TError, unknown>;\n\n    hook.trpc = useHookResult({\n      path,\n    });\n\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    return [hook.data!, hook as any];\n  }\n\n  const useQueries: TRPCUseQueries<TRouter> = (queriesCallback, options) => {\n    const { ssrState, queryClient, prefetchQuery, client } = useContext();\n\n    const proxy = createUseQueries(client);\n\n    const queries = queriesCallback(proxy);\n\n    if (typeof window === 'undefined' && ssrState === 'prepass') {\n      for (const query of queries) {\n        const queryOption = query as TRPCQueryOptions<any, any>;\n        if (\n          queryOption.trpc?.ssr !== false &&\n          !queryClient.getQueryCache().find({ queryKey: queryOption.queryKey })\n        ) {\n          void prefetchQuery(queryOption.queryKey, queryOption as any);\n        }\n      }\n    }\n\n    return __useQueries(\n      {\n        queries: queries.map((query) => ({\n          ...query,\n          queryKey: (query as TRPCQueryOptions<any, any>).queryKey,\n        })),\n        combine: options?.combine as any,\n      },\n      queryClient,\n    );\n  };\n\n  const useSuspenseQueries: TRPCUseSuspenseQueries<TRouter> = (\n    queriesCallback,\n  ) => {\n    const { queryClient, client } = useContext();\n\n    const proxy = createUseQueries(client);\n\n    const queries = queriesCallback(proxy);\n\n    const hook = __useSuspenseQueries(\n      {\n        queries: queries.map((query) => ({\n          ...query,\n          queryFn: query.queryFn,\n          queryKey: (query as TRPCQueryOptions<any, any>).queryKey,\n        })),\n      },\n      queryClient,\n    );\n\n    return [hook.map((h) => h.data), hook] as any;\n  };\n\n  return {\n    Provider: TRPCProvider,\n    createClient,\n    useContext,\n    useUtils: useContext,\n    useQuery,\n    usePrefetchQuery,\n    useSuspenseQuery,\n    useQueries,\n    useSuspenseQueries,\n    useMutation,\n    useSubscription,\n    useInfiniteQuery,\n    usePrefetchInfiniteQuery,\n    useSuspenseInfiniteQuery,\n  };\n}\n\n/**\n * Infer the type of a `createReactQueryHooks` function\n * @internal\n */\nexport type CreateReactQueryHooks<\n  TRouter extends AnyRouter,\n  TSSRContext = unknown,\n> = ReturnType<typeof createRootHooks<TRouter, TSSRContext>>;\n", "import type { QueryClientConfig } from '@tanstack/react-query';\nimport { QueryClient } from '@tanstack/react-query';\n\n/**\n * @internal\n */\nexport type CreateTRPCReactQueryClientConfig =\n  | {\n      queryClient?: QueryClient;\n      queryClientConfig?: never;\n    }\n  | {\n      queryClientConfig?: QueryClientConfig;\n      queryClient?: never;\n    };\n\n/**\n * @internal\n */\nexport const getQueryClient = (config: CreateTRPCReactQueryClientConfig) =>\n  config.queryClient ?? new QueryClient(config.queryClientConfig);\n"], "names": ["hooks: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><TR<PERSON><PERSON>, T<PERSON>RContext>", "contextProps: (keyof TRPCContextPropsBase<any, any>)[]", "React", "utilName: keyof AnyDecoratedProcedure", "context: TRPCQueryUtils<TRouter>", "contextMap: Record<keyof AnyDecoratedProcedure, () => unknown>", "context: TRPCContextState<AnyRouter, TSSRContext>", "client: TRPCUntypedClient<TRouter> | TRPCClient<TRouter>", "untypedClient: TRPCUntypedClient<TRouter>", "options: QueryOptions", "queryKey: TRPCQueryKey", "opts: TOptions", "infiniteParams?: {\n    pageParam: any;\n    direction: 'forward' | 'backward';\n  }", "_asyncIterator", "r", "AsyncFromSyncIterator", "value: {\n  path: readonly string[];\n}", "asyncIterable: AsyncIterable<unknown>", "queryClient: QueryClient", "queryKey: TRPCQueryKey", "aggregate: unknown[]", "opts: Create<PERSON>ueryUtilsOptions<TRouter>", "opts", "queryFnContext: QueryFunctionContext<TRPCQueryKey, unknown>", "queryFnContext: QueryFunctionContext<TRPCQueryKey>", "input: unknown", "result: T", "onTrackResult: (key: keyof T) => void", "config?: CreateTRPCReactOptions<TRouter>", "mutationSuccessOverride: UseMutationOverride['onSuccess']", "TRPCProvider: T<PERSON><PERSON>rov<PERSON><TR<PERSON><PERSON>, TSSRContext>", "client: TRPCUntypedClient<TRouter>", "queryKey: TRPCQueryKey", "opts: TOptions", "useQuery", "path: readonly string[]", "input: unknown", "opts?: UseTRPCQueryOptions<unknown, unknown, TError>", "usePrefetchQuery", "path: string[]", "opts?: UseTRPCPrefetchQueryOptions<unknown, unknown, TError>", "useSuspenseQuery", "opts?: UseTRPCSuspenseQueryOptions<unknown, unknown, TError>", "useMutation", "opts?: UseTRPCMutationOptions<unknown, TError, unknown, unknown>", "initialStateIdle: Omit<TRPCSubscriptionIdleResult<unknown>, 'reset'>", "initialStateConnecting: Omit<\n    TRPCSubscriptionConnectingResult<unknown, TError>,\n    'reset'\n  >", "opts: UseTRPCSubscriptionOptions<unknown, TError>", "key: keyof $Result", "callback: (prevState: $Result) => $Result", "useInfiniteQuery", "opts: UseTRPCInfiniteQueryOptions<unknown, unknown, TError>", "usePrefetchInfiniteQuery", "opts: UseTRPCPrefetchInfiniteQueryOptions<unknown, unknown, TError>", "useSuspenseInfiniteQuery", "opts: UseTRPCSuspenseInfiniteQueryOptions<unknown, unknown, TError>", "useQueries: TRPCUseQueries<TRouter>", "useSuspenseQueries: TRPCUseSuspenseQueries<TRouter>", "config: CreateTRPCReactQueryClientConfig"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAQA,SAAgB,sBAGdA,KAAAA,EAAoD;IACpD,kLAAO,uBAAA,EAAqB,CAAC,EAAE,IAAA,EAAM,IAAA,EAAM,KAAK;;QAC9C,MAAM,WAAW,CAAC;eAAG,IAAK;SAAA;QAI1B,MAAM,UAAU,SAAS,GAAA,EAAK;QAE9B,IAAI,YAAY,cACd,CAAA,OAAQ,KAAA,CAAc,QAAA,CAAS,UAAU,GAAG,KAAK;QAGnD,IAAI,YAAY,OACd,CAAA,OAAO;YACL,MAAM;QACP;QAGH,MAAM,CAAC,OAAO,GAAG,KAAK,GAAG;QACzB,MAAM,OAAA,CAAA,SAAO,IAAA,CAAK,EAAA,MAAA,QAAA,WAAA,KAAA,IAAA,SAAM,CAAE;QAE1B,OAAQ,KAAA,CAAc,QAAA,CAAS,UAAU,OAAO,KAAK;IACtD,EAAC;AACH;;;;ACmFD,MAAaC,eAAyD;IACpE;IACA;IACA;IACA;CACD;AAmOD,MAAa,cAAA,CAAA,uBAAcC,sMAAM,aAAA,MAAA,QAAA,yBAAA,KAAA,IAAA,KAAA,IAAN,qBAAA,IAAA,CAAA,uMAAsB,KAAY;;;ACiF7D,MAAa,eAAe,CAC1BC,aACc;IACd,OAAQ,UAAR;QACE,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK,iBACH;YAAA,OAAO;QAET,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK,kBACH;YAAA,OAAO;QAET,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK,QACH;YAAA,OAAO;IACV;AACF;;;GAKD,SAAS,0BACPC,OAAAA,EACA;IACA,kLAAO,uBAAA,EAAgD,CAAC,SAAS;QAC/D,MAAM,OAAO,CAAC;eAAG,KAAK,IAAK;SAAA;QAC3B,MAAM,WAAW,KAAK,GAAA,EAAK;QAC3B,MAAM,OAAO,CAAC;eAAG,KAAK,IAAK;SAAA;QAG3B,MAAM,QAAQ,KAAK,KAAA,EAAO;QAC1B,MAAM,YAAY,aAAa,SAAS;QACxC,MAAM,4LAAW,sBAAA,EAAoB,MAAM,OAAO,UAAU;QAE5D,MAAMC,aAAiE;YACrE,sBAAsB,IACpB,QAAQ,oBAAA,CAAqB,MAAM,UAAU,IAAA,CAAK,EAAA,CAAG;YACvD,cAAc,IAAM,QAAQ,YAAA,CAAa,MAAM,UAAU,GAAG,KAAK;YAIjE,OAAO,IAAM,QAAQ,UAAA,CAAW,UAAU,GAAG,KAAK;YAClD,eAAe,IAAM,QAAQ,kBAAA,CAAmB,UAAU,IAAA,CAAK,EAAA,CAAG;YAClE,UAAU,IAAM,QAAQ,aAAA,CAAc,UAAU,GAAG,KAAK;YACxD,kBAAkB,IAAM,QAAQ,qBAAA,CAAsB,UAAU,IAAA,CAAK,EAAA,CAAG;YACxE,YAAY,IAAM,QAAQ,eAAA,CAAgB,UAAU,GAAG,KAAK;YAC5D,YAAY,IAAM,QAAQ,iBAAA,CAAkB,UAAU,GAAG,KAAK;YAC9D,OAAO,IAAM,QAAQ,YAAA,CAAa,UAAU,GAAG,KAAK;YACpD,SAAS,IAAM,QAAQ,cAAA,CAAe,UAAU,GAAG,KAAK;YACxD,QAAQ,IAAM,QAAQ,WAAA,CAAY,UAAU,GAAG,KAAK;YACpD,SAAS,MAAM;gBACb,QAAQ,YAAA,CAAa,UAAU,IAAA,CAAK,EAAA,EAAI,IAAA,CAAK,EAAA,CAAG;YACjD;YACD,gBAAgB,IACd,QAAQ,cAAA,CAAe,UAAU,IAAA,CAAK,EAAA,EAAI,IAAA,CAAK,EAAA,EAAI,IAAA,CAAK,EAAA,CAAG;YAC7D,iBAAiB,MAAM;gBACrB,QAAQ,oBAAA,CAAqB,UAAU,IAAA,CAAK,EAAA,EAAI,IAAA,CAAK,EAAA,CAAG;YACzD;YACD,SAAS,IAAM,QAAQ,YAAA,CAAa,SAAS;YAC7C,iBAAiB,IAAM,QAAQ,oBAAA,CAAqB,SAAS;YAI7D,qBAAqB,IACnB,QAAQ,mBAAA,CAAoB,0MAAA,EAAuB,KAAK,EAAE,MAAM;YAClE,qBAAqB,IACnB,QAAQ,mBAAA,kLAAoB,yBAAA,EAAuB,KAAK,CAAC;YAC3D,YAAY,IACV,QAAQ,UAAA,CAAW;oBAAE,8LAAa,yBAAA,EAAuB,KAAK;gBAAE,EAAC;QACpE;QAED,OAAO,UAAA,CAAW,SAAA,EAAW;IAC9B,EAAC;AACH;;;GAKD,SAAgB,sBACdC,OAAAA,EACA;IAGA,MAAM,qLAAc,wBAAA,EAAsB,QAAQ,MAAA,CAAO;IAEzD,MAAM,QAAQ,0BACZ,QACD;IAED,kLAAO,kBAAA,EAA4C,CAAC,QAAQ;QAC1D,MAAM,cAAc;QACpB,IAAI,gBAAgB,SAClB,CAAA,OAAO;QAET,IAAI,aAAa,QAAA,CAAS,YAAY,CACpC,CAAA,OAAO,OAAA,CAAQ,YAAA;QAGjB,OAAO,KAAA,CAAM,IAAA;IACd,EAAC;AACH;;;GAKD,SAAgB,sBACdF,OAAAA,EAC2B;IAC3B,OAAO,0BAA0B,QAAQ;AAC1C;;;;;;;GC5cD,SAAgB,iBACdG,MAAAA,EACA;IACA,MAAMC,gBACJ,qLAAkB,oBAAA,GAAoB,UAAS,yLAAA,EAAiB,OAAO;IAEzE,kLAAO,uBAAA,EAKL,CAAC,SAAS;QACV,MAAM,YAAY,KAAK,IAAA;QACvB,MAAM,UAAU,UAAU,IAAA,CAAK,IAAI;QACnC,MAAM,CAAC,OAAO,MAAM,GAAG,KAAK,IAAA;QAK5B,MAAMC,UAAAA,CAAAA,GAAAA,uBAAAA,OAAAA,EAAAA;YACJ,UAAU,uMAAA,EAAoB,WAAW,OAAO,QAAQ;YACxD,SAAS,MAAM;gBACb,OAAO,cAAc,KAAA,CAAM,SAAS,OAAA,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAO,MAAO,IAAA,CAAK;YACxD;WACE;QAGL,OAAO;IACR,EAAC;AACH;;;;;;GCpHD,SAAgB,cACdC,QAAAA,EACAC,IAAAA,EACAC,cAAAA,EAIA;;IACA,MAAM,OAAO,QAAA,CAAS,EAAA;IACtB,IAAI,QAAA,CAAA,aAAQ,QAAA,CAAS,EAAA,MAAA,QAAA,eAAA,KAAA,IAAA,KAAA,IAAA,WAAI,KAAA;IACzB,IAAI,gBAAgB;;QAClB,QAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,CAAA,SACM,KAAA,MAAA,QAAA,WAAA,KAAA,IAAA,SAAS,CAAE,IACX,eAAe,SAAA,GAAY;YAAE,QAAQ,eAAe,SAAA;QAAW,IAAG,CAAE,IAAA,CAAA,GAAA;YACxE,WAAW,eAAe,SAAA;QAAA;IAE7B;IACD,OAAO;QAAC,KAAK,IAAA,CAAK,IAAI;QAAE;oDAAQ,KAAc,IAAA;KAAK;AACpD;;;;;QCvBD,SAASC,iBAAe,CAAA,EAAG;YACzB,IAAI,GACF,GACA,GACA,IAAI;YACN,IAAK,eAAA,OAAsB,UAAA,CAAW,IAAI,OAAO,aAAA,EAAe,IAAI,OAAO,QAAA,GAAW,KAAM;gBAC1F,IAAI,KAAK,QAAA,CAAS,IAAI,CAAA,CAAE,EAAA,EAAK,CAAA,OAAO,EAAE,IAAA,CAAK,EAAE;gBAC7C,IAAI,KAAK,QAAA,CAAS,IAAI,CAAA,CAAE,EAAA,EAAK,CAAA,OAAO,IAAI,sBAAsB,EAAE,IAAA,CAAK,EAAE;gBACvE,IAAI,mBAAmB,IAAI;YAC5B;YACD,MAAM,IAAI,UAAU;QACrB;QACD,SAAS,sBAAsB,CAAA,EAAG;YAChC,SAAS,kCAAkCC,GAAAA,EAAG;gBAC5C,IAAI,OAAOA,IAAE,KAAKA,IAAG,CAAA,OAAO,QAAQ,MAAA,CAAO,IAAI,UAAUA,MAAI,sBAAsB;gBACnF,IAAI,IAAIA,IAAE,IAAA;gBACV,OAAO,QAAQ,OAAA,CAAQA,IAAE,KAAA,CAAM,CAAC,IAAA,CAAK,SAAUA,GAAAA,EAAG;oBAChD,OAAO;wBACL,OAAOA;wBACP,MAAM;oBACP;gBACF,EAAC;YACH;YACD,OAAO,wBAAwB,SAASC,wBAAsBD,GAAAA,EAAG;gBAC/D,IAAA,CAAK,CAAA,GAAIA,KAAG,IAAA,CAAK,CAAA,GAAIA,IAAE,IAAA;YACxB,GAAE,sBAAsB,SAAA,GAAY;gBACnC,GAAG;gBACH,GAAG;gBACH,MAAM,SAAS,OAAO;oBACpB,OAAO,kCAAkC,IAAA,CAAK,CAAA,CAAE,KAAA,CAAM,IAAA,CAAK,CAAA,EAAG,UAAU,CAAC;gBAC1E;gBACD,UAAU,SAAS,QAAQA,GAAAA,EAAG;oBAC5B,IAAI,IAAI,IAAA,CAAK,CAAA,CAAE,SAAA;oBACf,OAAA,KAAY,MAAM,IAAI,QAAQ,OAAA,CAAQ;wBACpC,OAAOA;wBACP,MAAA,CAAO;oBACR,EAAC,GAAG,kCAAkC,EAAE,KAAA,CAAM,IAAA,CAAK,CAAA,EAAG,UAAU,CAAC;gBACnE;gBACD,SAAS,SAAS,OAAOA,GAAAA,EAAG;oBAC1B,IAAI,IAAI,IAAA,CAAK,CAAA,CAAE,SAAA;oBACf,OAAA,KAAY,MAAM,IAAI,QAAQ,MAAA,CAAOA,IAAE,GAAG,kCAAkC,EAAE,KAAA,CAAM,IAAA,CAAK,CAAA,EAAG,UAAU,CAAC;gBACxG;YACF,GAAE,IAAI,sBAAsB;QAC9B;QACD,OAAO,OAAA,GAAUD,kBAAgB,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;ACtCtG,SAAgB,wBAAwBG,KAAAA,EAEL;IACjC,MAAM,OAAO,MAAM,IAAA,CAAK,IAAA,CAAK,IAAI;IAEjC,OAAO;QACL;IACD;AACF;;;GAKD,SAAgB,cAAcA,KAAAA,EAEH;IACzB,MAAM,SAAS,wBAAwB,MAAM;IAC7C,OAAO,sMAAM,OAAA,CAAQ,IAAM,QAAQ;QAAC,MAAO;KAAA,CAAC;AAC7C;;;GAKD,eAAsB,4BACpBC,aAAAA,EACAC,WAAAA,EACAC,QAAAA,EACA;IACA,MAAM,aAAa,YAAY,aAAA,EAAe;IAE9C,MAAM,QAAQ,WAAW,KAAA,CAAM,aAAa;QAC1C;IACD,EAAC;IAEF,MAAM,QAAA,CAAS;QACb,MAAM,CAAE,CAAA;QACR,QAAQ;IACT,EAAC;IAEF,MAAMC,YAAuB,CAAE,CAAA;;;;;8DACL,gBAAA,OAAA,4BAAA,CAAA,CAAA,QAAA,MAAA,UAAA,IAAA,EAAA,EAAA,IAAA,EAAA,4BAAA,MAAA;kBAAT,QAAA,MAAA,KAAA;YAAwB;gBACvC,UAAU,IAAA,CAAK,MAAM;gBAErB,MAAM,QAAA,CAAS;oBACb,MAAM,CAAC;2BAAG,SAAU;qBAAA;gBACrB,EAAC;YACH;;;;;;;;;;;;IACD,OAAO;AACR;;;;;;;;;GChBD,SAAgB,uBACdC,IAAAA,EACyB;IACzB,MAAM,EAAE,MAAA,EAAQ,WAAA,EAAa,GAAG;IAChC,MAAM,gBACJ,kBAAkB,uLAAA,GAAoB,gLAAS,mBAAA,EAAiB,OAAO;IAEzE,OAAO;QACL,sBAAsB,CAAC,MAAM,UAAUC,WAAS;;YAC9C,MAAM,mBAAA,CAAA,CAAA,aAAmB,QAAA,CAAS,EAAA,MAAA,QAAA,eAAA,KAAA,IAAA,KAAA,IAAA,WAAI,KAAA,8KAAU,YAAA;YAEhD,MAAM,UAAU,OACdC,mBACqB;;gBACrB,MAAM,aAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GACDD,SAAAA,CAAAA,GAAAA;oBACH,MAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,WAAA,QAAA,WAAA,KAAA,IAAA,KAAA,IACKA,OAAM,IAAA,GAAA,CAAA,WAAA,QAAA,WAAA,KAAA,KAAA,CAAA,aACLA,OAAM,IAAA,MAAA,QAAA,eAAA,KAAA,IAAA,KAAA,IAAA,WAAM,cAAA,IACZ;wBAAE,QAAQ,eAAe,MAAA;oBAAQ,IACjC;wBAAE,QAAQ;oBAAM;gBAAA;gBAIxB,MAAM,SAAS,MAAM,cAAc,KAAA,CACjC,GAAG,cAAc,UAAU,YAAY;oBACrC,WAAW,eAAe,SAAA;oBAC1B,WAAW,eAAe,SAAA;gBAC3B,EAAC,CACH;gBAED,OAAO;YACR;YAED,OAAO,OAAO,MAAA,CACZ,mNAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GACKA,SAAAA,CAAAA,GAAAA;gBACH,aAAA,WAAA,QAAA,WAAA,KAAA,IAAA,KAAA,IAAaA,OAAM,WAAA;gBACnB;gBACA,SAAS,mBAAmB,oLAAA,GAAY;gBACxC,kBAAA,CAAA,OAAA,WAAA,QAAA,WAAA,KAAA,IAAA,KAAA,IAAmBA,OAAM,aAAA,MAAA,QAAA,SAAA,KAAA,IAAA,OAAyB;eAClD,EACF;gBAAE,MAAM,wBAAwB;oBAAE;gBAAM,EAAC;YAAE,EAC5C;QACF;QAED,cAAc,CAAC,MAAM,UAAUA,WAAS;;YACtC,MAAM,mBAAA,CAAA,CAAA,cAAmB,QAAA,CAAS,EAAA,MAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAA,YAAI,KAAA,8KAAU,YAAA;YAEhD,MAAM,UAAU,OACdE,mBACqB;;gBACrB,MAAM,aAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GACDF,SAAAA,CAAAA,GAAAA;oBACH,MAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,WAAA,QAAA,WAAA,KAAA,IAAA,KAAA,IACKA,OAAM,IAAA,GAAA,CAAA,WAAA,QAAA,WAAA,KAAA,KAAA,CAAA,cACLA,OAAM,IAAA,MAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAA,YAAM,cAAA,IACZ;wBAAE,QAAQ,eAAe,MAAA;oBAAQ,IACjC;wBAAE,QAAQ;oBAAM;gBAAA;gBAIxB,MAAM,SAAS,MAAM,cAAc,KAAA,CACjC,GAAG,cAAc,UAAU,WAAW,CACvC;gBAED,uKAAI,kBAAA,EAAgB,OAAO,CACzB,CAAA,OAAO,4BAA4B,QAAQ,aAAa,SAAS;gBAGnE,OAAO;YACR;YAED,OAAO,OAAO,MAAA,qLACZ,eAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GACKA,SAAAA,CAAAA,GAAAA;gBACH,aAAA,WAAA,QAAA,WAAA,KAAA,IAAA,KAAA,IAAaA,OAAM,WAAA;gBACnB;gBACA,SAAS,0LAAmB,aAAA,GAAY;eACxC,EACF;gBAAE,MAAM,wBAAwB;oBAAE;gBAAM,EAAC;YAAE,EAC5C;QAKF;QAED,YAAY,CAAC,UAAUA,WAAS;YAC9B,OAAO,YAAY,UAAA,CAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GACdA,SAAAA,CAAAA,GAAAA;gBACH;gBACA,SAAS,IAAM,cAAc,KAAA,CAAM,GAAG,cAAc,UAAUA,OAAK,CAAC;eACpE;QACH;QAED,oBAAoB,CAAC,UAAUA,WAAS;;YACtC,OAAO,YAAY,kBAAA,CAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GACdA,SAAAA,CAAAA,GAAAA;gBACH;gBACA,SAAS,CAAC,EAAE,SAAA,EAAW,SAAA,EAAW,KAAK;oBACrC,OAAO,cAAc,KAAA,CACnB,GAAG,cAAc,UAAUA,QAAM;wBAAE;wBAAW;oBAAW,EAAC,CAC3D;gBACF;gBACD,kBAAA,CAAA,sBAAA,WAAA,QAAA,WAAA,KAAA,IAAA,KAAA,IAAkBA,OAAM,aAAA,MAAA,QAAA,wBAAA,KAAA,IAAA,sBAAiB;eACzC;QACH;QAED,eAAe,CAAC,UAAUA,WAAS;YACjC,OAAO,YAAY,aAAA,CAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GACdA,SAAAA,CAAAA,GAAAA;gBACH;gBACA,SAAS,IAAM,cAAc,KAAA,CAAM,GAAG,cAAc,UAAUA,OAAK,CAAC;eACpE;QACH;QAED,uBAAuB,CAAC,UAAUA,WAAS;;YACzC,OAAO,YAAY,qBAAA,CAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GACdA,SAAAA,CAAAA,GAAAA;gBACH;gBACA,SAAS,CAAC,EAAE,SAAA,EAAW,SAAA,EAAW,KAAK;oBACrC,OAAO,cAAc,KAAA,CACnB,GAAG,cAAc,UAAUA,QAAM;wBAAE;wBAAW;oBAAW,EAAC,CAC3D;gBACF;gBACD,kBAAA,CAAA,uBAAA,WAAA,QAAA,WAAA,KAAA,IAAA,KAAA,IAAkBA,OAAM,aAAA,MAAA,QAAA,yBAAA,KAAA,IAAA,uBAAiB;eACzC;QACH;QAED,iBAAiB,CAAC,UAAUA,WAAS;YACnC,OAAO,YAAY,eAAA,CAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GACdA,SAAAA,CAAAA,GAAAA;gBACH;gBACA,SAAS,IAAM,cAAc,KAAA,CAAM,GAAG,cAAc,UAAUA,OAAK,CAAC;eACpE;QACH;QAED,mBAAmB,CAAC,UAAU,SAAS,YAAY;YACjD,OAAO,YAAY,iBAAA,CAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAEZ,UAAA,CAAA,GAAA;gBACH;YAAA,IAEF,QACD;QACF;QACD,cAAc,CAAC,UAAU,SAAS,YAAY;YAC5C,OAAO,YAAY,YAAA,CAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAEZ,UAAA,CAAA,GAAA;gBACH;YAAA,IAEF,QACD;QACF;QAED,gBAAgB,CAAC,UAAU,SAAS,YAAY;YAC9C,OAAO,YAAY,cAAA,CAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAEZ,UAAA,CAAA,GAAA;gBACH;YAAA,IAEF,QACD;QACF;QAED,aAAa,CAAC,UAAU,YAAY;YAClC,OAAO,YAAY,aAAA,CACjB;gBACE;YACD,GACD,QACD;QACF;QAED,cAAc,CAAC,UAAU,SAAS,YAAY;YAC5C,OAAO,YAAY,YAAA,CAAa,UAAU,SAAgB,QAAQ;QACnE;QAGD,gBAAgB,CAAC,UAAU,SAAS,SAAS,YAAY;YACvD,OAAO,YAAY,cAAA,CAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAEZ,UAAA,CAAA,GAAA;gBACH;YAAA,IAEF,SACA,QACD;QACF;QAED,cAAc,CAAC,aAAa;YAC1B,OAAO,YAAY,YAAA,CAAa,SAAS;QAC1C;QAED,sBAAsB,CAAC,UAAU,SAAS,YAAY;YACpD,OAAO,YAAY,YAAA,CAAa,UAAU,SAAgB,QAAQ;QACnE;QAED,sBAAsB,CAAC,aAAa;YAClC,OAAO,YAAY,YAAA,CAAa,SAAS;QAC1C;QAED,qBAAqB,CAAC,aAAa,YAAY;YAC7C,MAAM,OAAO,WAAA,CAAY,EAAA;YACzB,MAAM,sBAAsB,CAACG,UAAmB;gBAC9C,OAAO,cAAc,QAAA,CACnB,GAAG,cAAc;oBAAC;oBAAM;wBAAE;oBAAO,CAAC;iBAAA,EAAE,KAAK,CAC1C;YACF;YACD,OAAO,YAAY,mBAAA,CACjB,aAAA,OACO,YAAY,aACf,QAAQ;gBAAE;YAAqB,EAAC,GAChC,QACL;QACF;QAED,qBAAqB,CAAC,gBAAgB;YACpC,OAAO,YAAY,mBAAA,CAAoB,YAAY;QACpD;QAED,YAAY,CAAC,YAAY;YACvB,OAAO,YAAY,UAAA,CAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GACd,UAAA,CAAA,GAAA;gBACH,OAAO;YAAA,GACP;QACH;IACF;AACF;;;;AC3MD,MAAM,cAAc,CAClBC,QACAC,kBACM;IACN,MAAM,gBAAgB,IAAI,MAAM,QAAQ;QACtC,KAAI,MAAA,EAAQ,IAAA,EAAM;YAChB,cAAc,KAAgB;YAC9B,OAAO,MAAA,CAAO,KAAA;QACf;IACF;IAED,OAAO;AACR;;;GAKD,SAAgB,gBAGdC,MAAAA,EAA0C;;IAC1C,MAAMC,0BAAAA,CAAAA,wBAAAA,WAAAA,QAAAA,WAAAA,KAAAA,KAAAA,CAAAA,oBACJ,OAAQ,SAAA,MAAA,QAAA,sBAAA,KAAA,KAAA,CAAA,oBAAA,kBAAW,WAAA,MAAA,QAAA,sBAAA,KAAA,IAAA,KAAA,IAAA,kBAAa,SAAA,MAAA,QAAA,0BAAA,KAAA,IAAA,wBAC/B,CAAC,UAAY,QAAQ,UAAA,EAAY;IAMpC,MAAM,UAAA,CAAA,kBAAA,WAAA,QAAA,WAAA,KAAA,IAAA,KAAA,IAAW,OAAQ,OAAA,MAAA,QAAA,oBAAA,KAAA,IAAA,kBACvB;IAEF,MAAM,kLAAe,mBAAA;IAErB,MAAMC,eAAmD,CAAC,UAAU;;QAClE,MAAM,EAAE,iBAAiB,KAAA,EAAO,WAAA,EAAa,UAAA,EAAY,GAAG;QAC5D,MAAM,CAAC,UAAU,YAAY,GAAG,sMAAM,QAAA,CAAA,CAAA,kBACpC,MAAM,QAAA,MAAA,QAAA,oBAAA,KAAA,IAAA,kBAAY,MACnB;QAED,MAAMC,SACJ,MAAM,MAAA,+KAAkB,oBAAA,GACpB,MAAM,MAAA,GACN,0LAAA,EAAiB,MAAM,MAAA,CAAO;QAEpC,MAAM,MAAM,sMAAM,OAAA,CAChB,IACE,uBAAuB;gBACrB;gBACA;YACD,EAAC,EACJ;YAAC;YAAQ,WAAY;SAAA,CACtB;QAED,MAAM,eAAe,sMAAM,OAAA,CACzB,IAAA,CAAA,GAAA,qBAAA,OAAA,EAAA;gBACE;gBACA;gBACA;gBACA,YAAY,eAAA,QAAA,eAAA,KAAA,IAAA,aAAc;gBAC1B;eACG,MAEL;YAAC;YAAgB;YAAQ;YAAK;YAAa;YAAY;SAAS,CACjE;QAED,sMAAM,SAAA,CAAU,MAAM;YAGpB,YAAY,CAAC,QAAW,QAAQ,YAAY,MAAO;QACpD,GAAE,CAAE,CAAA,CAAC;QACN,OAAA,aAAA,GACE,kOAAA,EAAC,QAAQ,QAAA,EAAA;YAAS,OAAO;sBAAe,MAAM,QAAA;UAA4B;IAE7E;IAED,SAAS,aAAa;QACpB,MAAM,UAAU,sMAAM,UAAA,CAAW,QAAQ;QAEzC,IAAA,CAAK,QACH,CAAA,MAAM,IAAI,MACR;QAGJ,OAAO;IACR;;;;IAMD,SAAS,2BAEPC,QAAAA,EAAwBC,IAAAA,EAA0B;;QAClD,MAAM,EAAE,WAAA,EAAa,QAAA,EAAU,GAAG,YAAY;QAC9C,OAAO,YACL,aAAa,aAAA,CAAA,CAAA,wBACb,YAAY,aAAA,EAAe,CAAC,IAAA,CAAK;YAAE;QAAU,EAAC,MAAA,QAAA,0BAAA,KAAA,IAAA,KAAA,IAAA,sBAAE,KAAA,CAAM,MAAA,MAAW,UAAA,CAAA,GAAA,qBAAA,OAAA,EAAA;YAE7D,cAAc;QAAA,GACX,QAEL;IACL;IAED,SAASC,WACPC,IAAAA,EACAC,KAAAA,EACAC,IAAAA,EACqC;;QACrC,MAAM,UAAU,YAAY;QAC5B,MAAM,EAAE,cAAA,EAAgB,MAAA,EAAQ,QAAA,EAAU,WAAA,EAAa,aAAA,EAAe,GACpE;QACF,MAAM,4LAAW,sBAAA,EAAoB,MAAM,OAAO,QAAQ;QAE1D,MAAM,cAAc,YAAY,gBAAA,CAAiB,SAAS;QAE1D,MAAM,mBAAmB,kLAAU,YAAA;QAEnC,IAAA,OACS,SAAW,eAClB,aAAa,aAAA,CAAA,SAAA,QAAA,SAAA,KAAA,KAAA,CAAA,aACb,KAAM,IAAA,MAAA,QAAA,eAAA,KAAA,IAAA,KAAA,IAAA,WAAM,GAAA,MAAQ,SAAA,CAAA,CAAA,gBAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IACnB,KAAM,OAAA,MAAA,QAAA,kBAAA,KAAA,IAAA,gBAAA,gBAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAW,YAAa,OAAA,MAAa,SAAA,CAC3C,oBAAA,CACA,YAAY,aAAA,EAAe,CAAC,IAAA,CAAK;YAAE;QAAU,EAAC,CAE/C,CAAK,cAAc,UAAU,KAAY;QAE3C,MAAM,UAAU,2BAA2B,UAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACtC,cACA,MACH;QAEF,MAAM,uBAAA,CAAA,OAAA,CAAA,wBAAA,SAAA,QAAA,SAAA,KAAA,KAAA,CAAA,cACJ,KAAM,IAAA,MAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAA,YAAM,cAAA,MAAA,QAAA,0BAAA,KAAA,IAAA,wBAAA,WAAA,QAAA,WAAA,KAAA,IAAA,KAAA,IAAkB,OAAQ,cAAA,MAAA,QAAA,SAAA,KAAA,IAAA,OAAkB;QAE1D,MAAM,uLAAO,WAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAEN,UAAA,CAAA,GAAA;YACO;YACV,SAAS,mBACL,QACA,OAAO,yBAAyB;gBAC9B,MAAM,aAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACD,UAAA,CAAA,GAAA;oBACH,MAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IACK,QAAS,IAAA,GACR,uBACA;wBAAE,QAAQ,qBAAqB,MAAA;oBAAQ,IACvC;wBAAE,QAAQ;oBAAM;gBAAA;gBAIxB,MAAM,SAAS,MAAM,OAAO,KAAA,CAC1B,GAAG,cAAc,UAAU,WAAW,CACvC;gBAED,QAAI,iLAAA,EAAgB,OAAO,CACzB,CAAA,OAAO,4BACL,QACA,aACA,SACD;gBAEH,OAAO;YACR;YAEP,YACD;QAED,KAAK,IAAA,GAAO,cAAc;YACxB;QACD,EAAC;QAEF,OAAO;IACR;IAED,SAASC,mBACPC,IAAAA,EACAH,KAAAA,EACAI,IAAAA,EACM;;QACN,MAAM,UAAU,YAAY;QAC5B,MAAM,4LAAW,sBAAA,EAAoB,MAAM,OAAO,QAAQ;QAE1D,MAAM,mBAAmB,kLAAU,YAAA;QAEnC,MAAM,uBAAA,CAAA,QAAA,CAAA,yBAAA,SAAA,QAAA,SAAA,KAAA,KAAA,CAAA,cACJ,KAAM,IAAA,MAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAA,YAAM,cAAA,MAAA,QAAA,2BAAA,KAAA,IAAA,yBAAA,WAAA,QAAA,WAAA,KAAA,IAAA,KAAA,IACZ,OAAQ,cAAA,MAAA,QAAA,UAAA,KAAA,IAAA,QACR,QAAQ,cAAA;QAEV,CAAA,GAAA,mLAAA,CAAA,mBAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACK,OAAA,CAAA,GAAA;YACO;YACV,SAAS,mBACL,QACA,CAAC,yBAAyB;gBACxB,MAAM,aAAa;oBACjB,MAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IACK,KAAM,IAAA,GACL,uBACA;wBAAE,QAAQ,qBAAqB,MAAA;oBAAQ,IACvC,CAAE;gBAET;gBAED,OAAO,QAAQ,MAAA,CAAO,KAAA,CAAM,GAAG,cAAc,UAAU,WAAW,CAAC;YACpE;WACL;IACH;IAED,SAASC,mBACPN,IAAAA,EACAC,KAAAA,EACAM,IAAAA,EAC6C;;QAC7C,MAAM,UAAU,YAAY;QAC5B,MAAM,eAAW,mMAAA,EAAoB,MAAM,OAAO,QAAQ;QAE1D,MAAM,uBAAA,CAAA,QAAA,CAAA,yBAAA,SAAA,QAAA,SAAA,KAAA,KAAA,CAAA,cACJ,KAAM,IAAA,MAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAA,YAAM,cAAA,MAAA,QAAA,2BAAA,KAAA,IAAA,yBAAA,WAAA,QAAA,WAAA,KAAA,IAAA,KAAA,IACZ,OAAQ,cAAA,MAAA,QAAA,UAAA,KAAA,IAAA,QACR,QAAQ,cAAA;QAEV,MAAM,OAAO,2MAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAEN,OAAA,CAAA,GAAA;YACO;YACV,SAAS,CAAC,yBAAyB;gBACjC,MAAM,aAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACD,OAAA,CAAA,GAAA;oBACH,MAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IACK,KAAM,IAAA,GACL,uBACA;wBAAE,QAAQ,qBAAqB,MAAA;oBAAQ,IACvC;wBAAE,QAAQ;oBAAM;gBAAA;gBAIxB,OAAO,QAAQ,MAAA,CAAO,KAAA,CAAM,GAAG,cAAc,UAAU,WAAW,CAAC;YACpE;YAEH,QAAQ,WAAA,CACT;QAED,KAAK,IAAA,GAAO,cAAc;YACxB;QACD,EAAC;QAEF,OAAO;YAAC,KAAK,IAAA;YAAM,IAAY;SAAA;IAChC;IAED,SAASC,cACPR,IAAAA,EACAS,IAAAA,EAC0D;QAC1D,MAAM,EAAE,MAAA,EAAQ,WAAA,EAAa,GAAG,YAAY;QAE5C,MAAM,+LAAc,yBAAA,EAAuB,KAAK;QAEhD,MAAM,cAAc,YAAY,sBAAA,CAC9B,YAAY,mBAAA,CAAoB,YAAY,CAC7C;QAED,MAAM,0LAAO,cAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAEN,OAAA,CAAA,GAAA;YACU;YACb,YAAY,CAAC,UAAU;gBACrB,OAAO,OAAO,QAAA,CAAS,GAAG,cAAc;oBAAC;oBAAM;wBAAE;oBAAO,CAAC;iBAAA,EAAE,KAAK,CAAC;YAClE;YACD,WAAU,GAAG,IAAA,EAAM;;gBACjB,MAAM,aAAa,MACjB;;2GAAM,SAAA,MAAA,QAAA,qBAAA,KAAA,IAAA,KAAA,IAAN,iBAAA,IAAA,CAAA,MAAkB,GAAG,KAAK,MAAA,QAAA,oBAAA,KAAA,IAAA,kBAAA,gBAAA,QAAA,gBAAA,KAAA,KAAA,CAAA,wBAAI,YAAa,SAAA,MAAA,QAAA,0BAAA,KAAA,IAAA,KAAA,IAAb,sBAAA,IAAA,CAAA,aAAyB,GAAG,KAAK;;gBAEjE,OAAO,wBAAwB;oBAC7B;oBACA;oBACA,MAAA,CAAA,QAAA,CAAA,aAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAM,KAAM,IAAA,MAAA,QAAA,eAAA,KAAA,IAAA,aAAA,gBAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAQ,YAAa,IAAA,MAAA,QAAA,UAAA,KAAA,IAAA,QAAQ,CAAE;gBAC5C,EAAC;YACH;YAEH,YACD;QAED,KAAK,IAAA,GAAO,cAAc;YACxB;QACD,EAAC;QAEF,OAAO;IACR;IACD,MAAMC,mBAAuE;QAC3E,MAAA,KAAA;QACA,OAAO;QACP,QAAQ;IACT;IAED,MAAMC,yBAGF;QACF,MAAA,KAAA;QACA,OAAO;QACP,QAAQ;IACT;4CAGD,SAAS,gBACPX,IAAAA,EACAC,KAAAA,EACAW,IAAAA,EACA;;QACA,MAAM,UAAA,CAAA,iBAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAU,KAAM,OAAA,MAAA,QAAA,mBAAA,KAAA,IAAA,iBAAW,kLAAU,YAAA;QAC3C,MAAM,uLAAW,UAAA,EAAQ,uMAAA,EAAoB,MAAM,OAAO,MAAM,CAAC;QACjE,MAAM,EAAE,MAAA,EAAQ,GAAG,YAAY;QAE/B,MAAM,UAAU,sMAAM,MAAA,CAAoB,KAAK;QAC/C,sMAAM,SAAA,CAAU,MAAM;YACpB,QAAQ,OAAA,GAAU;QACnB,EAAC;QAIF,MAAM,CAAC,aAAa,GAAG,sMAAM,QAAA,CAAS,IAAI,IAAmB,CAAE,CAAA,EAAE;QAEjE,MAAM,iBAAiB,sMAAM,WAAA,CAC3B,CAACC,QAAuB;YACtB,aAAa,GAAA,CAAI,IAAI;QACtB,GACD;YAAC,YAAa;SAAA,CACf;QAED,MAAM,yBAAyB,sMAAM,MAAA,CAAuB,KAAK;QAEjE,MAAM,cAAc,sMAAM,WAAA,CACxB,CAACC,aAA8C;YAC7C,MAAM,OAAO,UAAU,OAAA;YACvB,MAAM,OAAQ,UAAU,OAAA,GAAU,SAAS,KAAK;YAEhD,IAAI,eAAe;YACnB,KAAK,MAAM,OAAO,aAChB,IAAI,IAAA,CAAK,IAAA,KAAS,IAAA,CAAK,IAAA,EAAM;gBAC3B,eAAe;gBACf;YACD;YAEH,IAAI,aACF,CAAA,SAAS,YAAY,MAAM,eAAe,CAAC;QAE9C,GACD;YAAC;YAAgB,YAAa;SAAA,CAC/B;QAED,MAAM,QAAQ,sMAAM,WAAA,CAAY,MAAY;;YAE1C,CAAA,wBAAA,uBAAuB,OAAA,MAAA,QAAA,0BAAA,KAAA,KAAvB,sBAAgC,WAAA,EAAa;YAE7C,IAAA,CAAK,SAAS;gBACZ,YAAY,IAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAY,mBAAA,CAAA,GAAA;wBAAkB;oBAAA,GAAS;gBACnD;YACD;YACD,YAAY,IAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAY,yBAAA,CAAA,GAAA;oBAAwB;gBAAA,GAAS;YACzD,MAAM,eAAe,OAAO,YAAA,CAC1B,KAAK,IAAA,CAAK,IAAI,EACd,UAAA,QAAA,UAAA,KAAA,IAAA,QAAA,KAAA,GACA;gBACE,WAAW,MAAM;;oBACf,CAAA,wBAAA,CAAA,mBAAA,QAAQ,OAAA,EAAQ,SAAA,MAAA,QAAA,0BAAA,KAAA,KAAhB,sBAAA,IAAA,CAAA,iBAA6B;oBAC7B,YAAY,CAAC,OAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACR,OAAA,CAAA,GAAA;4BACH,QAAQ;4BACR,OAAO;2BACN;gBACJ;gBACD,QAAQ,CAAC,SAAS;;oBAChB,CAAA,wBAAA,CAAA,oBAAA,QAAQ,OAAA,EAAQ,MAAA,MAAA,QAAA,0BAAA,KAAA,KAAhB,sBAAA,IAAA,CAAA,mBAAyB,KAAK;oBAC9B,YAAY,CAAC,OAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACR,OAAA,CAAA,GAAA;4BACH,QAAQ;4BACR;4BACA,OAAO;2BACN;gBACJ;gBACD,SAAS,CAAC,UAAU;;oBAClB,CAAA,wBAAA,CAAA,oBAAA,QAAQ,OAAA,EAAQ,OAAA,MAAA,QAAA,0BAAA,KAAA,KAAhB,sBAAA,IAAA,CAAA,mBAA0B,MAAM;oBAChC,YAAY,CAAC,OAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACR,OAAA,CAAA,GAAA;4BACH,QAAQ;4BACR;2BACC;gBACJ;gBACD,yBAAyB,CAAC,WAAW;oBACnC,YAAY,CAAC,SAAS;wBACpB,OAAQ,OAAO,KAAA,EAAf;4BACE,KAAK,OACH;gCAAA,OAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACK,OAAA,CAAA,GAAA;oCACH,QAAQ,OAAO,KAAA;oCACf,OAAO;oCACP,MAAA,KAAA;;4BAEJ,KAAK,aACH;gCAAA,OAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACK,OAAA,CAAA,GAAA;oCACH,OAAO,OAAO,KAAA;oCACd,QAAQ,OAAO,KAAA;;4BAGnB,KAAK,UAEH;gCAAA,OAAO;wBACV;oBACF,EAAC;gBACH;gBACD,YAAY,MAAM;;oBAChB,CAAA,wBAAA,CAAA,oBAAA,QAAQ,OAAA,EAAQ,UAAA,MAAA,QAAA,0BAAA,KAAA,KAAhB,sBAAA,IAAA,CAAA,kBAA8B;oBAI9B,YAAY,CAAC,OAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACR,OAAA,CAAA,GAAA;4BACH,QAAQ;4BACR,OAAO;4BACP,MAAA,KAAA;2BACC;gBAGJ;YACF,EACF;YAED,uBAAuB,OAAA,GAAU;QAGlC,GAAE;YAAC;YAAQ;YAAU;YAAS;SAAY,CAAC;QAC5C,sMAAM,SAAA,CAAU,MAAM;YACpB,OAAO;YAEP,OAAO,MAAM;;gBACX,CAAA,yBAAA,uBAAuB,OAAA,MAAA,QAAA,2BAAA,KAAA,KAAvB,uBAAgC,WAAA,EAAa;YAC9C;QACF,GAAE;YAAC,KAAM;SAAA,CAAC;QAEX,MAAM,YAAY,sMAAM,MAAA,CACtB,UAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACS,yBAAA,CAAA,GAAA;YAAwB;QAAA,KAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACxB,mBAAA,CAAA,GAAA;YAAkB;QAAA,GAC5B;QAED,MAAM,CAAC,OAAO,SAAS,GAAG,sMAAM,QAAA,CAC9B,YAAY,UAAU,OAAA,EAAS,eAAe,CAC/C;QAED,OAAO;IACR;IAED,SAASC,mBACPf,IAAAA,EACAC,KAAAA,EACAe,IAAAA,EACsD;;QACtD,MAAM,EACJ,MAAA,EACA,QAAA,EACA,qBAAA,EACA,WAAA,EACA,cAAA,EACD,GAAG,YAAY;QAChB,MAAM,4LAAW,sBAAA,EAAoB,MAAM,OAAO,WAAW;QAE7D,MAAM,cAAc,YAAY,gBAAA,CAAiB,SAAS;QAE1D,MAAM,mBAAmB,kLAAU,YAAA;QAEnC,IAAA,OACS,SAAW,eAClB,aAAa,aAAA,CAAA,SAAA,QAAA,SAAA,KAAA,KAAA,CAAA,cACb,KAAM,IAAA,MAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAA,YAAM,GAAA,MAAQ,SAAA,CAAA,CAAA,iBAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IACnB,KAAM,OAAA,MAAA,QAAA,mBAAA,KAAA,IAAA,iBAAA,gBAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAW,YAAa,OAAA,MAAa,SAAA,CAC3C,oBAAA,CACA,YAAY,aAAA,EAAe,CAAC,IAAA,CAAK;YAAE;QAAU,EAAC,CAE/C,CAAK,sBAAsB,UAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAe,cAAgB,MAAc;QAG1E,MAAM,UAAU,2BAA2B,UAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACtC,cACA,MACH;QAGF,MAAM,uBAAA,CAAA,yBAAA,SAAA,QAAA,SAAA,KAAA,KAAA,CAAA,cAAuB,KAAM,IAAA,MAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAA,YAAM,cAAA,MAAA,QAAA,2BAAA,KAAA,IAAA,yBAAkB;QAE3D,MAAM,+LAAO,mBAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAEN,UAAA,CAAA,GAAA;YACH,kBAAA,CAAA,sBAAkB,KAAK,aAAA,MAAA,QAAA,wBAAA,KAAA,IAAA,sBAAiB;YACxC,WAAW,KAAK,SAAA;YACN;YACV,SAAS,mBACL,QACA,CAAC,yBAAyB;;gBACxB,MAAM,aAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACD,UAAA,CAAA,GAAA;oBACH,MAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IACK,QAAS,IAAA,GACR,uBACA;wBAAE,QAAQ,qBAAqB,MAAA;oBAAQ,IACvC;wBAAE,QAAQ;oBAAM;gBAAA;gBAIxB,OAAO,OAAO,KAAA,CACZ,GAAG,cAAc,UAAU,YAAY;oBACrC,WAAA,CAAA,wBACE,qBAAqB,SAAA,MAAA,QAAA,0BAAA,KAAA,IAAA,wBAAa,KAAK,aAAA;oBACzC,WAAW,qBAAqB,SAAA;gBACjC,EAAC,CACH;YACF;YAEP,YACD;QAED,KAAK,IAAA,GAAO,cAAc;YACxB;QACD,EAAC;QACF,OAAO;IACR;IAED,SAASC,2BACPb,IAAAA,EACAH,KAAAA,EACAiB,IAAAA,EACM;;QACN,MAAM,UAAU,YAAY;QAC5B,MAAM,eAAW,mMAAA,EAAoB,MAAM,OAAO,WAAW;QAE7D,MAAM,cAAc,QAAQ,WAAA,CAAY,gBAAA,CAAiB,SAAS;QAElE,MAAM,mBAAmB,kLAAU,YAAA;QAEnC,MAAM,UAAU,2BAA2B,UAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACtC,cACA,MACH;QAGF,MAAM,uBAAA,CAAA,yBAAA,SAAA,QAAA,SAAA,KAAA,KAAA,CAAA,cACJ,KAAM,IAAA,MAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAA,YAAM,cAAA,MAAA,QAAA,2BAAA,KAAA,IAAA,yBAAkB,QAAQ,cAAA;QAExC,CAAA,GAAA,2LAAA,CAAA,2BAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACK,OAAA,CAAA,GAAA;YACH,kBAAA,CAAA,uBAAkB,KAAK,aAAA,MAAA,QAAA,yBAAA,KAAA,IAAA,uBAAiB;YACxC;YACA,SAAS,mBACL,QACA,CAAC,yBAAyB;;gBACxB,MAAM,aAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACD,UAAA,CAAA,GAAA;oBACH,MAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IACK,QAAS,IAAA,GACR,uBACA;wBAAE,QAAQ,qBAAqB,MAAA;oBAAQ,IACvC,CAAE;gBAAA;gBAIV,OAAO,QAAQ,MAAA,CAAO,KAAA,CACpB,GAAG,cAAc,UAAU,YAAY;oBACrC,WAAA,CAAA,yBAAW,qBAAqB,SAAA,MAAA,QAAA,2BAAA,KAAA,IAAA,yBAAa,KAAK,aAAA;oBAClD,WAAW,qBAAqB,SAAA;gBACjC,EAAC,CACH;YACF;WACL;IACH;IAED,SAASC,2BACPnB,IAAAA,EACAC,KAAAA,EACAmB,IAAAA,EAC8D;;QAC9D,MAAM,UAAU,YAAY;QAC5B,MAAM,4LAAW,sBAAA,EAAoB,MAAM,OAAO,WAAW;QAE7D,MAAM,cAAc,QAAQ,WAAA,CAAY,gBAAA,CAAiB,SAAS;QAElE,MAAM,UAAU,2BAA2B,UAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACtC,cACA,MACH;QAGF,MAAM,uBAAA,CAAA,yBAAA,SAAA,QAAA,SAAA,KAAA,KAAA,CAAA,cACJ,KAAM,IAAA,MAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAA,YAAM,cAAA,MAAA,QAAA,2BAAA,KAAA,IAAA,yBAAkB,QAAQ,cAAA;QAExC,MAAM,QAAO,0NAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAEN,OAAA,CAAA,GAAA;YACH,kBAAA,CAAA,uBAAkB,KAAK,aAAA,MAAA,QAAA,yBAAA,KAAA,IAAA,uBAAiB;YACxC;YACA,SAAS,CAAC,yBAAyB;;gBACjC,MAAM,aAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACD,UAAA,CAAA,GAAA;oBACH,MAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IACK,QAAS,IAAA,GACR,uBACA;wBAAE,QAAQ,qBAAqB,MAAA;oBAAQ,IACvC,CAAE;gBAAA;gBAIV,OAAO,QAAQ,MAAA,CAAO,KAAA,CACpB,GAAG,cAAc,UAAU,YAAY;oBACrC,WAAA,CAAA,yBAAW,qBAAqB,SAAA,MAAA,QAAA,2BAAA,KAAA,IAAA,yBAAa,KAAK,aAAA;oBAClD,WAAW,qBAAqB,SAAA;gBACjC,EAAC,CACH;YACF;YAEH,QAAQ,WAAA,CACT;QAED,KAAK,IAAA,GAAO,cAAc;YACxB;QACD,EAAC;QAGF,OAAO;YAAC,KAAK,IAAA;YAAO,IAAY;SAAA;IACjC;IAED,MAAMC,eAAsC,CAAC,iBAAiB,YAAY;QACxE,MAAM,EAAE,QAAA,EAAU,WAAA,EAAa,aAAA,EAAe,MAAA,EAAQ,GAAG,YAAY;QAErE,MAAM,QAAQ,iBAAiB,OAAO;QAEtC,MAAM,UAAU,gBAAgB,MAAM;QAEtC,IAAA,OAAW,SAAW,eAAe,aAAa,UAChD,CAAA,KAAK,MAAM,SAAS,QAAS;;YAC3B,MAAM,cAAc;YACpB,IAAA,CAAA,CAAA,oBACE,YAAY,IAAA,MAAA,QAAA,sBAAA,KAAA,IAAA,KAAA,IAAA,kBAAM,GAAA,MAAQ,SAAA,CACzB,YAAY,aAAA,EAAe,CAAC,IAAA,CAAK;gBAAE,UAAU,YAAY,QAAA;YAAU,EAAC,CAErE,CAAK,cAAc,YAAY,QAAA,EAAU,YAAmB;QAE/D;QAGH,yLAAO,aAAA,EACL;YACE,SAAS,QAAQ,GAAA,CAAI,CAAC,QAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACjB,QAAA,CAAA,GAAA;oBACH,UAAW,MAAqC,QAAA;gBAAA,GAC/C;YACH,SAAA,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IAAS,QAAS,OAAA;QACnB,GACD,YACD;IACF;IAED,MAAMC,uBAAsD,CAC1D,oBACG;QACH,MAAM,EAAE,WAAA,EAAa,MAAA,EAAQ,GAAG,YAAY;QAE5C,MAAM,QAAQ,iBAAiB,OAAO;QAEtC,MAAM,UAAU,gBAAgB,MAAM;QAEtC,MAAM,iMAAO,qBAAA,EACX;YACE,SAAS,QAAQ,GAAA,CAAI,CAAC,QAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACjB,QAAA,CAAA,GAAA;oBACH,SAAS,MAAM,OAAA;oBACf,UAAW,MAAqC,QAAA;mBAC/C;QACJ,GACD,YACD;QAED,OAAO;YAAC,KAAK,GAAA,CAAI,CAAC,IAAM,EAAE,IAAA,CAAK;YAAE,IAAK;SAAA;IACvC;IAED,OAAO;QACL,UAAU;QACV;QACA;QACA,UAAU;QACV,UAAA;QACA,kBAAA;QACA,kBAAA;QACA,YAAA;QACA,oBAAA;QACA,aAAA;QACA;QACA,kBAAA;QACA,0BAAA;QACA,0BAAA;IACD;AACF;;;;;GC9uBD,MAAa,iBAAiB,CAACC,WAC7B;;yCAAO,WAAA,MAAA,QAAA,wBAAA,KAAA,IAAA,sBAAe,kLAAI,cAAA,CAAY,OAAO,iBAAA;AAAkB", "debugId": null}}, {"offset": {"line": 2433, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@trpc/react-query/dist/index.mjs", "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/%40trpc/react-query/src/createTRPCReact.tsx", "file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/%40trpc/react-query/src/createTRPCQueryUtils.tsx"], "sourcesContent": ["import type {\n  DefinedInitialDataInfiniteOptions,\n  DefinedUseInfiniteQueryResult,\n  InfiniteData,\n  SkipToken,\n  UndefinedInitialDataInfiniteOptions,\n  UseInfiniteQueryOptions,\n  UseInfiniteQueryResult,\n  UseSuspenseInfiniteQueryOptions,\n  UseSuspenseInfiniteQueryResult,\n  UseSuspenseQueryResult,\n} from '@tanstack/react-query';\nimport type { createTRPCClient, TRPCClientErrorLike } from '@trpc/client';\nimport type {\n  AnyProcedure,\n  AnyRootTypes,\n  AnyRouter,\n  inferAsyncIterableYield,\n  inferProcedureInput,\n  inferTransformedProcedureOutput,\n  ProcedureType,\n  ProtectedIntersection,\n  RouterRecord,\n  Simplify,\n} from '@trpc/server/unstable-core-do-not-import';\nimport { createFlatProxy } from '@trpc/server/unstable-core-do-not-import';\nimport * as React from 'react';\nimport type {\n  TRPCUseQueries,\n  TRPCUseSuspenseQueries,\n} from './internals/useQueries';\nimport type {\n  CreateReactUtils,\n  TRPCFetchInfiniteQueryOptions,\n  TRPCFetchQueryOptions,\n} from './shared';\nimport { createReactDecoration, createReactQueryUtils } from './shared';\nimport type { CreateReactQueryHooks } from './shared/hooks/createHooksInternal';\nimport { createRootHooks } from './shared/hooks/createHooksInternal';\nimport type {\n  DefinedUseTRPCQueryOptions,\n  DefinedUseTRPCQueryResult,\n  TRPCHookResult,\n  TRPCProvider,\n  TRPCSubscriptionResult,\n  TRPCUseQueryBaseOptions,\n  UseTRPCMutationOptions,\n  UseTRPCMutationResult,\n  UseTRPCQueryOptions,\n  UseTRPCQueryResult,\n  UseTRPCSubscriptionOptions,\n  UseTRPCSuspenseQueryOptions,\n} from './shared/hooks/types';\nimport type { CreateTRPCReactOptions } from './shared/types';\n\ntype ResolverDef = {\n  input: any;\n  output: any;\n  transformer: boolean;\n  errorShape: any;\n};\n/**\n * @internal\n */\nexport interface ProcedureUseQuery<TDef extends ResolverDef> {\n  <TQueryFnData extends TDef['output'] = TDef['output'], TData = TQueryFnData>(\n    input: TDef['input'] | SkipToken,\n    opts: DefinedUseTRPCQueryOptions<\n      TQueryFnData,\n      TData,\n      TRPCClientErrorLike<{\n        errorShape: TDef['errorShape'];\n        transformer: TDef['transformer'];\n      }>,\n      TDef['output']\n    >,\n  ): DefinedUseTRPCQueryResult<\n    TData,\n    TRPCClientErrorLike<{\n      errorShape: TDef['errorShape'];\n      transformer: TDef['transformer'];\n    }>\n  >;\n\n  <TQueryFnData extends TDef['output'] = TDef['output'], TData = TQueryFnData>(\n    input: TDef['input'] | SkipToken,\n    opts?: UseTRPCQueryOptions<\n      TQueryFnData,\n      TData,\n      TRPCClientErrorLike<TDef>,\n      TDef['output']\n    >,\n  ): UseTRPCQueryResult<TData, TRPCClientErrorLike<TDef>>;\n}\n\n/**\n * @internal\n */\nexport type ProcedureUsePrefetchQuery<TDef extends ResolverDef> = (\n  input: TDef['input'] | SkipToken,\n  opts?: TRPCFetchQueryOptions<TDef['output'], TRPCClientErrorLike<TDef>>,\n) => void;\n\n/**\n * @remark `void` is here due to https://github.com/trpc/trpc/pull/4374\n */\ntype CursorInput = {\n  cursor?: any;\n} | void;\n\ntype ReservedInfiniteQueryKeys = 'cursor' | 'direction';\ntype InfiniteInput<TInput> =\n  | Omit<TInput, ReservedInfiniteQueryKeys>\n  | SkipToken;\n\ntype inferCursorType<TInput> = TInput extends { cursor?: any }\n  ? TInput['cursor']\n  : unknown;\n\ntype makeInfiniteQueryOptions<TCursor, TOptions> = Omit<\n  TOptions,\n  'queryKey' | 'initialPageParam' | 'queryFn' | 'queryHash' | 'queryHashFn'\n> &\n  TRPCUseQueryBaseOptions & {\n    initialCursor?: TCursor;\n  };\n\ntype trpcInfiniteData<TDef extends ResolverDef> = Simplify<\n  InfiniteData<TDef['output'], inferCursorType<TDef['input']>>\n>;\n// references from react-query\n// 1st\n// declare function useInfiniteQuery<\n//   TQueryFnData,\n//   TError = DefaultError,\n//   TData = InfiniteData<TQueryFnData>,\n//   TQueryKey extends QueryKey = QueryKey,\n//   TPageParam = unknown,\n// >(\n//   options: DefinedInitialDataInfiniteOptions<\n//     TQueryFnData,\n//     TError,\n//     TData,\n//     TQueryKey,\n//     TPageParam\n//   >,\n//   queryClient?: QueryClient,\n// ): DefinedUseInfiniteQueryResult<TData, TError>;\n// 2nd\n// declare function useInfiniteQuery<\n//   TQueryFnData,\n//   TError = DefaultError,\n//   TData = InfiniteData<TQueryFnData>,\n//   TQueryKey extends QueryKey = QueryKey,\n//   TPageParam = unknown,\n// >(\n//   options: UndefinedInitialDataInfiniteOptions<\n//     TQueryFnData,\n//     TError,\n//     TData,\n//     TQueryKey,\n//     TPageParam\n//   >,\n//   queryClient?: QueryClient,\n// ): UseInfiniteQueryResult<TData, TError>;\n// 3rd\n// declare function useInfiniteQuery<\n//   TQueryFnData,\n//   TError = DefaultError,\n//   TData = InfiniteData<TQueryFnData>,\n//   TQueryKey extends QueryKey = QueryKey,\n//   TPageParam = unknown,\n// >(\n//   options: UseInfiniteQueryOptions<\n//     TQueryFnData,\n//     TError,\n//     TData,\n//     TQueryFnData,\n//     TQueryKey,\n//     TPageParam\n//   >,\n//   queryClient?: QueryClient,\n// ): UseInfiniteQueryResult<TData, TError>;\n\nexport interface useTRPCInfiniteQuery<TDef extends ResolverDef> {\n  // 1st\n  <TData = trpcInfiniteData<TDef>>(\n    input: InfiniteInput<TDef['input']>,\n    opts: makeInfiniteQueryOptions<\n      inferCursorType<TDef['input']>,\n      DefinedInitialDataInfiniteOptions<\n        //     TQueryFnData,\n        TDef['output'],\n        //     TError,\n        TRPCClientErrorLike<TDef>,\n        //     TData,\n        TData,\n        //     TQueryKey,\n        any,\n        //     TPageParam\n        inferCursorType<TDef['input']>\n      >\n    >,\n  ): TRPCHookResult &\n    DefinedUseInfiniteQueryResult<TData, TRPCClientErrorLike<TDef>>;\n\n  // 2nd\n  <TData = trpcInfiniteData<TDef>>(\n    input: InfiniteInput<TDef['input']>,\n    opts?: makeInfiniteQueryOptions<\n      inferCursorType<TDef['input']>,\n      UndefinedInitialDataInfiniteOptions<\n        //     TQueryFnData,\n        TDef['output'],\n        //     TError,\n        TRPCClientErrorLike<TDef>,\n        //     TData,\n        TData,\n        //     TQueryKey,\n        any,\n        //     TPageParam\n        inferCursorType<TDef['input']>\n      >\n    >,\n  ): TRPCHookResult & UseInfiniteQueryResult<TData, TRPCClientErrorLike<TDef>>;\n\n  // 3rd:\n  <TData = trpcInfiniteData<TDef>>(\n    input: InfiniteInput<TDef['input']>,\n    opts?: makeInfiniteQueryOptions<\n      inferCursorType<TDef['input']>,\n      UseInfiniteQueryOptions<\n        //     TQueryFnData,\n        TDef['output'],\n        //     TError,\n        TRPCClientErrorLike<TDef>,\n        //     TData,\n        TData,\n        //     TQueryKey,\n        any,\n        //     TPageParam\n        inferCursorType<TDef['input']>\n      >\n    >,\n  ): TRPCHookResult & UseInfiniteQueryResult<TData, TRPCClientErrorLike<TDef>>;\n}\n\n// references from react-query\n// declare function useSuspenseInfiniteQuery<\n//   TQueryFnData,\n//   TError = DefaultError,\n//   TData = InfiniteData<TQueryFnData>,\n//   TQueryKey extends QueryKey = QueryKey,\n//   TPageParam = unknown,\n// >(\n//   options: UseSuspenseInfiniteQueryOptions<\n//     TQueryFnData,\n//     TError,\n//     TData,\n//     TQueryFnData,\n//     TQueryKey,\n//     TPageParam\n//   >,\n//   queryClient?: QueryClient,\n// ): UseSuspenseInfiniteQueryResult<TData, TError>;\n\nexport type useTRPCSuspenseInfiniteQuery<TDef extends ResolverDef> = (\n  input: InfiniteInput<TDef['input']>,\n  opts: makeInfiniteQueryOptions<\n    inferCursorType<TDef['input']>,\n    UseSuspenseInfiniteQueryOptions<\n      //     TQueryFnData,\n      TDef['output'],\n      //     TError,\n      TRPCClientErrorLike<TDef>,\n      //     TData,\n      trpcInfiniteData<TDef>,\n      //     TQueryKey,\n      any,\n      //     TPageParam\n      inferCursorType<TDef['input']>\n    >\n  >,\n) => [\n  trpcInfiniteData<TDef>,\n  TRPCHookResult &\n    UseSuspenseInfiniteQueryResult<\n      trpcInfiniteData<TDef>,\n      TRPCClientErrorLike<TDef>\n    >,\n];\n\n/**\n * @internal\n */\nexport type MaybeDecoratedInfiniteQuery<TDef extends ResolverDef> =\n  TDef['input'] extends CursorInput\n    ? {\n        /**\n         * @see https://trpc.io/docs/v11/client/react/useInfiniteQuery\n         */\n        useInfiniteQuery: useTRPCInfiniteQuery<TDef>;\n        /**\n         * @see https://trpc.io/docs/client/react/suspense#usesuspenseinfinitequery\n         */\n        useSuspenseInfiniteQuery: useTRPCSuspenseInfiniteQuery<TDef>;\n\n        usePrefetchInfiniteQuery: (\n          input: Omit<TDef['input'], ReservedInfiniteQueryKeys> | SkipToken,\n          opts: TRPCFetchInfiniteQueryOptions<\n            TDef['input'],\n            TDef['output'],\n            TRPCClientErrorLike<TDef>\n          >,\n        ) => void;\n      }\n    : object;\n\n/**\n * @internal\n */\nexport type DecoratedQueryMethods<TDef extends ResolverDef> = {\n  /**\n   * @see https://trpc.io/docs/v11/client/react/useQuery\n   */\n  useQuery: ProcedureUseQuery<TDef>;\n  usePrefetchQuery: ProcedureUsePrefetchQuery<TDef>;\n  /**\n   * @see https://trpc.io/docs/v11/client/react/suspense#usesuspensequery\n   */\n  useSuspenseQuery: <\n    TQueryFnData extends TDef['output'] = TDef['output'],\n    TData = TQueryFnData,\n  >(\n    input: TDef['input'],\n    opts?: UseTRPCSuspenseQueryOptions<\n      TQueryFnData,\n      TData,\n      TRPCClientErrorLike<TDef>\n    >,\n  ) => [\n    TData,\n    UseSuspenseQueryResult<TData, TRPCClientErrorLike<TDef>> & TRPCHookResult,\n  ];\n};\n\n/**\n * @internal\n */\nexport type DecoratedQuery<TDef extends ResolverDef> =\n  MaybeDecoratedInfiniteQuery<TDef> & DecoratedQueryMethods<TDef>;\n\nexport type DecoratedMutation<TDef extends ResolverDef> = {\n  /**\n   * @see https://trpc.io/docs/v11/client/react/useMutation\n   */\n  useMutation: <TContext = unknown>(\n    opts?: UseTRPCMutationOptions<\n      TDef['input'],\n      TRPCClientErrorLike<TDef>,\n      TDef['output'],\n      TContext\n    >,\n  ) => UseTRPCMutationResult<\n    TDef['output'],\n    TRPCClientErrorLike<TDef>,\n    TDef['input'],\n    TContext\n  >;\n};\n\ninterface ProcedureUseSubscription<TDef extends ResolverDef> {\n  // Without skip token\n  (\n    input: TDef['input'],\n    opts?: UseTRPCSubscriptionOptions<\n      inferAsyncIterableYield<TDef['output']>,\n      TRPCClientErrorLike<TDef>\n    >,\n  ): TRPCSubscriptionResult<\n    inferAsyncIterableYield<TDef['output']>,\n    TRPCClientErrorLike<TDef>\n  >;\n\n  // With skip token\n  (\n    input: TDef['input'] | SkipToken,\n    opts?: Omit<\n      UseTRPCSubscriptionOptions<\n        inferAsyncIterableYield<TDef['output']>,\n        TRPCClientErrorLike<TDef>\n      >,\n      'enabled'\n    >,\n  ): TRPCSubscriptionResult<\n    inferAsyncIterableYield<TDef['output']>,\n    TRPCClientErrorLike<TDef>\n  >;\n}\n/**\n * @internal\n */\nexport type DecorateProcedure<\n  TType extends ProcedureType,\n  TDef extends ResolverDef,\n> = TType extends 'query'\n  ? DecoratedQuery<TDef>\n  : TType extends 'mutation'\n    ? DecoratedMutation<TDef>\n    : TType extends 'subscription'\n      ? {\n          /**\n           * @see https://trpc.io/docs/v11/subscriptions\n           */\n          useSubscription: ProcedureUseSubscription<TDef>;\n        }\n      : never;\n\n/**\n * @internal\n */\nexport type DecorateRouterRecord<\n  TRoot extends AnyRootTypes,\n  TRecord extends RouterRecord,\n> = {\n  [TKey in keyof TRecord]: TRecord[TKey] extends infer $Value\n    ? $Value extends AnyProcedure\n      ? DecorateProcedure<\n          $Value['_def']['type'],\n          {\n            input: inferProcedureInput<$Value>;\n            output: inferTransformedProcedureOutput<TRoot, $Value>;\n            transformer: TRoot['transformer'];\n            errorShape: TRoot['errorShape'];\n          }\n        >\n      : $Value extends RouterRecord\n        ? DecorateRouterRecord<TRoot, $Value>\n        : never\n    : never;\n};\n\n/**\n * @internal\n */\nexport type CreateTRPCReactBase<TRouter extends AnyRouter, TSSRContext> = {\n  /**\n   * @deprecated renamed to `useUtils` and will be removed in a future tRPC version\n   *\n   * @see https://trpc.io/docs/v11/client/react/useUtils\n   */\n  useContext(): CreateReactUtils<TRouter, TSSRContext>;\n  /**\n   * @see https://trpc.io/docs/v11/client/react/useUtils\n   */\n  useUtils(): CreateReactUtils<TRouter, TSSRContext>;\n  Provider: TRPCProvider<TRouter, TSSRContext>;\n  createClient: typeof createTRPCClient<TRouter>;\n  useQueries: TRPCUseQueries<TRouter>;\n  useSuspenseQueries: TRPCUseSuspenseQueries<TRouter>;\n};\n\nexport type CreateTRPCReact<\n  TRouter extends AnyRouter,\n  TSSRContext,\n> = ProtectedIntersection<\n  CreateTRPCReactBase<TRouter, TSSRContext>,\n  DecorateRouterRecord<\n    TRouter['_def']['_config']['$types'],\n    TRouter['_def']['record']\n  >\n>;\n\n/**\n * @internal\n */\nexport function createHooksInternal<\n  TRouter extends AnyRouter,\n  TSSRContext = unknown,\n>(trpc: CreateReactQueryHooks<TRouter, TSSRContext>) {\n  type CreateHooksInternal = CreateTRPCReact<TRouter, TSSRContext>;\n\n  const proxy = createReactDecoration<TRouter, TSSRContext>(\n    trpc,\n  ) as DecorateRouterRecord<\n    TRouter['_def']['_config']['$types'],\n    TRouter['_def']['record']\n  >;\n  return createFlatProxy<CreateHooksInternal>((key) => {\n    if (key === 'useContext' || key === 'useUtils') {\n      return () => {\n        const context = trpc.useUtils();\n        // create a stable reference of the utils context\n        return React.useMemo(() => {\n          return (createReactQueryUtils as any)(context);\n        }, [context]);\n      };\n    }\n\n    if (trpc.hasOwnProperty(key)) {\n      return (trpc as any)[key];\n    }\n\n    return proxy[key];\n  });\n}\n\nexport function createTRPCReact<\n  TRouter extends AnyRouter,\n  TSSRContext = unknown,\n>(\n  opts?: CreateTRPCReactOptions<TRouter>,\n): CreateTRPCReact<TRouter, TSSRContext> {\n  const hooks = createRootHooks<TRouter, TSSRContext>(opts);\n  const proxy = createHooksInternal<TRouter, TSSRContext>(hooks);\n\n  return proxy as any;\n}\n", "import type { AnyRouter } from '@trpc/server/unstable-core-do-not-import';\nimport { createQueryUtilsProxy } from './shared';\nimport type { CreateQueryUtilsOptions } from './utils/createUtilityFunctions';\nimport { createUtilityFunctions } from './utils/createUtilityFunctions';\n\nexport function createTRPCQueryUtils<TRouter extends AnyRouter>(\n  opts: CreateQueryUtilsOptions<TRouter>,\n) {\n  const utils = createUtilityFunctions(opts);\n  return createQueryUtilsProxy<TRouter>(utils);\n}\n"], "names": ["trpc: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><TR<PERSON><PERSON>, TSSRContext>", "opts?: CreateTRPCReactOptions<TRouter>", "opts: Create<PERSON>ueryUtilsOptions<TRouter>"], "mappings": ";;;;;;;;;;;;;;;;;GA4dA,SAAgB,oBAGdA,IAAAA,EAAmD;IAGnD,MAAM,oLAAQ,wBAAA,EACZ,KACD;IAID,OAAO,6LAAA,EAAqC,CAAC,QAAQ;QACnD,IAAI,QAAQ,gBAAgB,QAAQ,WAClC,CAAA,OAAO,MAAM;YACX,MAAM,UAAU,KAAK,QAAA,EAAU;YAE/B,6MAAO,MAAM,IAAA,CAAQ,MAAM;gBACzB,mLAAQ,wBAAA,EAA8B,QAAQ;YAC/C,GAAE;gBAAC,OAAQ;aAAA,CAAC;QACd;QAGH,IAAI,KAAK,cAAA,CAAe,IAAI,CAC1B,CAAA,OAAQ,IAAA,CAAa,IAAA;QAGvB,OAAO,KAAA,CAAM,IAAA;IACd,EAAC;AACH;AAED,SAAgB,gBAIdC,IAAAA,EACuC;IACvC,MAAM,oLAAQ,kBAAA,EAAsC,KAAK;IACzD,MAAM,QAAQ,oBAA0C,MAAM;IAE9D,OAAO;AACR;;;AChgBD,SAAgB,qBACdC,IAAAA,EACA;IACA,MAAM,oLAAQ,yBAAA,EAAuB,KAAK;IAC1C,mLAAO,wBAAA,EAA+B,MAAM;AAC7C", "debugId": null}}, {"offset": {"line": 2492, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/superjson/dist/double-indexed-kv.js", "sourceRoot": "", "sources": ["../src/double-indexed-kv.ts"], "sourcesContent": [], "names": [], "mappings": ";;;AAAM,MAAO,eAAe;IAA5B,aAAA;QACE,IAAA,CAAA,UAAU,GAAG,IAAI,GAAG,EAAQ,CAAC;QAC7B,IAAA,CAAA,UAAU,GAAG,IAAI,GAAG,EAAQ,CAAC;IAmB/B,CAAC;IAjBC,GAAG,CAAC,GAAM,EAAE,KAAQ,EAAA;QAClB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAChC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAClC,CAAC;IAED,QAAQ,CAAC,GAAM,EAAA;QACb,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAClC,CAAC;IAED,UAAU,CAAC,KAAQ,EAAA;QACjB,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACpC,CAAC;IAED,KAAK,GAAA;QACH,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QACxB,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAC1B,CAAC;CACF", "debugId": null}}, {"offset": {"line": 2519, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/superjson/dist/registry.js", "sourceRoot": "", "sources": ["../src/registry.ts"], "sourcesContent": [], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAC;;AAEnD,MAAO,QAAQ;IAGnB,YAA6B,kBAAoC,CAAA;QAApC,IAAA,CAAA,kBAAkB,GAAlB,kBAAkB,CAAkB;QAFzD,IAAA,CAAA,EAAE,GAAG,iKAAI,kBAAe,EAAa,CAAC;IAEsB,CAAC;IAErE,QAAQ,CAAC,KAAQ,EAAE,UAAmB,EAAA;QACpC,IAAI,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;YAC7B,OAAO;SACR;QAED,IAAI,CAAC,UAAU,EAAE;YACf,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;SAC7C;QAED,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;IAED,KAAK,GAAA;QACH,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC;IAClB,CAAC;IAED,aAAa,CAAC,KAAQ,EAAA;QACpB,OAAO,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IACnC,CAAC;IAED,QAAQ,CAAC,UAAkB,EAAA;QACzB,OAAO,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IACtC,CAAC;CACF", "debugId": null}}, {"offset": {"line": 2552, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/superjson/dist/class-registry.js", "sourceRoot": "", "sources": ["../src/class-registry.ts"], "sourcesContent": [], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAC;;AAQnC,MAAO,aAAc,uJAAQ,WAAe;IAChD,aAAA;QACE,KAAK,EAAC,CAAC,CAAC,EAAE,AAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAGb,IAAA,CAAA,mBAAmB,GAAG,IAAI,GAAG,EAAmB,CAAC;IAFzD,CAAC;IAID,QAAQ,CAAC,KAAY,EAAE,OAAkC,EAAA;QACvD,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YAC/B,IAAI,OAAO,CAAC,UAAU,EAAE;gBACtB,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;aACzD;YAED,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;SAC3C,MAAM;YACL,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;SAChC;IACH,CAAC;IAED,eAAe,CAAC,KAAY,EAAA;QAC1B,OAAO,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAC7C,CAAC;CACF", "debugId": null}}, {"offset": {"line": 2580, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/superjson/dist/util.js", "sourceRoot": "", "sources": ["../src/util.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;;;AAAA,SAAS,WAAW,CAAI,MAAyB;IAC/C,IAAI,QAAQ,IAAI,MAAM,EAAE;QACtB,8CAA8C;QAC9C,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;KAC9B;IAED,MAAM,MAAM,GAAQ,EAAE,CAAC;IAEvB,gDAAgD;IAChD,IAAK,MAAM,GAAG,IAAI,MAAM,CAAE;QACxB,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;YAC9B,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;SAC1B;KACF;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAEK,SAAU,IAAI,CAClB,MAAyB,EACzB,SAA4B;IAE5B,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;IACnC,IAAI,MAAM,IAAI,MAAM,EAAE;QACpB,8CAA8C;QAC9C,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KAC/B;IAED,MAAM,cAAc,GAAG,MAAa,CAAC;IAErC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE;QAC9C,MAAM,KAAK,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,SAAS,CAAC,KAAK,CAAC,EAAE;YACpB,OAAO,KAAK,CAAC;SACd;KACF;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAEK,SAAU,OAAO,CACrB,MAAyB,EACzB,GAAgC;IAEhC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,CAAG,CAAD,EAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;AACpE,CAAC;AAEK,SAAU,QAAQ,CAAI,GAAQ,EAAE,KAAQ;IAC5C,OAAO,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AACnC,CAAC;AAEK,SAAU,OAAO,CACrB,MAAW,EACX,SAA4B;IAE5B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE;QACtC,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QACxB,IAAI,SAAS,CAAC,KAAK,CAAC,EAAE;YACpB,OAAO,KAAK,CAAC;SACd;KACF;IAED,OAAO,SAAS,CAAC;AACnB,CAAC", "debugId": null}}, {"offset": {"line": 2634, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/superjson/dist/custom-transformer-registry.js", "sourceRoot": "", "sources": ["../src/custom-transformer-registry.ts"], "sourcesContent": [], "names": [], "mappings": ";;;AACA,OAAO,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;;AAS3B,MAAO,yBAAyB;IAAtC,aAAA;QACU,IAAA,CAAA,WAAW,GAA+C,CAAA,CAAE,CAAC;IAevE,CAAC;IAbC,QAAQ,CAAyB,WAAmC,EAAA;QAClE,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC;IACnD,CAAC;IAED,cAAc,CAAI,CAAI,EAAA;QACpB,qJAAO,OAAA,AAAI,EAAC,IAAI,CAAC,WAAW,GAAE,WAAW,CAAC,EAAE,AAC1C,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC,CACkB,CAAC;IAClD,CAAC;IAED,UAAU,CAAC,IAAY,EAAA;QACrB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;CACF", "debugId": null}}, {"offset": {"line": 2657, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/superjson/dist/is.js", "sourceRoot": "", "sources": ["../src/is.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,MAAM,OAAO,GAAG,CAAC,OAAY,EAAU,CACrC,CADuC,KACjC,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAEhD,MAAM,WAAW,GAAG,CAAC,OAAY,EAAwB,CAC9D,CADgE,MACzD,OAAO,KAAK,WAAW,CAAC;AAE1B,MAAM,MAAM,GAAG,CAAC,OAAY,EAAmB,CAAG,CAAD,MAAQ,KAAK,IAAI,CAAC;AAEnE,MAAM,aAAa,GAAG,CAC3B,OAAY,EACuB,EAAE;IACrC,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,KAAK,IAAI,EAAE,OAAO,KAAK,CAAC;IAClE,IAAI,OAAO,KAAK,MAAM,CAAC,SAAS,EAAE,OAAO,KAAK,CAAC;IAC/C,IAAI,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE,OAAO,IAAI,CAAC;IAEzD,OAAO,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,MAAM,CAAC,SAAS,CAAC;AAC7D,CAAC,CAAC;AAEK,MAAM,aAAa,GAAG,CAAC,OAAY,EAAiB,CACzD,CAD2D,YAC9C,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;AAEvD,MAAM,OAAO,GAAG,CAAC,OAAY,EAAoB,CACtD,CADwD,IACnD,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAElB,MAAM,QAAQ,GAAG,CAAC,OAAY,EAAqB,CACxD,CAD0D,MACnD,OAAO,KAAK,QAAQ,CAAC;AAEvB,MAAM,QAAQ,GAAG,CAAC,OAAY,EAAqB,CACxD,CAD0D,MACnD,OAAO,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AAE1C,MAAM,SAAS,GAAG,CAAC,OAAY,EAAsB,CAC1D,CAD4D,MACrD,OAAO,KAAK,SAAS,CAAC;AAExB,MAAM,QAAQ,GAAG,CAAC,OAAY,EAAqB,CACxD,CAD0D,MACnD,YAAY,MAAM,CAAC;AAErB,MAAM,KAAK,GAAG,CAAC,OAAY,EAA4B,CAC5D,CAD8D,MACvD,YAAY,GAAG,CAAC;AAElB,MAAM,KAAK,GAAG,CAAC,OAAY,EAAuB,CACvD,CADyD,MAClD,YAAY,GAAG,CAAC;AAElB,MAAM,QAAQ,GAAG,CAAC,OAAY,EAAqB,CACxD,CAD0D,MACnD,CAAC,OAAO,CAAC,KAAK,QAAQ,CAAC;AAEzB,MAAM,MAAM,GAAG,CAAC,OAAY,EAAmB,CACpD,CADsD,MAC/C,YAAY,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;AAEhD,MAAM,OAAO,GAAG,CAAC,OAAY,EAAoB,CACtD,CADwD,MACjD,YAAY,KAAK,CAAC;AAEpB,MAAM,UAAU,GAAG,CAAC,OAAY,EAAyB,CAC9D,CADgE,MACzD,OAAO,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;AAEzC,MAAM,WAAW,GAAG,CACzB,OAAY,EACsD,CAClE,CADoE,QAC3D,CAAC,OAAO,CAAC,IAClB,MAAM,CAAC,OAAO,CAAC,IACf,WAAW,CAAC,OAAO,CAAC,IACpB,QAAQ,CAAC,OAAO,CAAC,IACjB,QAAQ,CAAC,OAAO,CAAC,IACjB,QAAQ,CAAC,OAAO,CAAC,CAAC;AAEb,MAAM,QAAQ,GAAG,CAAC,OAAY,EAAqB,CACxD,CAD0D,MACnD,OAAO,KAAK,QAAQ,CAAC;AAEvB,MAAM,UAAU,GAAG,CAAC,OAAY,EAAqB,CAC1D,CAD4D,MACrD,KAAK,QAAQ,IAAI,OAAO,KAAK,CAAC,QAAQ,CAAC;AAezC,MAAM,YAAY,GAAG,CAAC,OAAY,EAAyB,CAChE,CADkE,UACvD,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,YAAY,QAAQ,CAAC,CAAC;AAEzD,MAAM,KAAK,GAAG,CAAC,OAAY,EAAkB,CAAG,CAAD,MAAQ,YAAY,GAAG,CAAC", "debugId": null}}, {"offset": {"line": 2709, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/superjson/dist/pathstringifier.js", "sourceRoot": "", "sources": ["../src/pathstringifier.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;;AAGO,MAAM,SAAS,GAAG,CAAC,GAAW,EAAE,CAAG,CAAD,EAAI,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAE7D,MAAM,aAAa,GAAG,CAAC,IAAU,EAAmB,CACzD,CAD2D,GACvD,CACD,GAAG,CAAC,MAAM,CAAC,CACX,GAAG,CAAC,SAAS,CAAC,CACd,IAAI,CAAC,GAAG,CAAC,CAAC;AAER,MAAM,SAAS,GAAG,CAAC,MAAuB,EAAE,EAAE;IACnD,MAAM,MAAM,GAAa,EAAE,CAAC;IAE5B,IAAI,OAAO,GAAG,EAAE,CAAC;IACjB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE;QACtC,IAAI,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAE5B,MAAM,YAAY,GAAG,IAAI,KAAK,IAAI,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC;QACnE,IAAI,YAAY,EAAE;YAChB,OAAO,IAAI,GAAG,CAAC;YACf,CAAC,EAAE,CAAC;YACJ,SAAS;SACV;QAED,MAAM,cAAc,GAAG,IAAI,KAAK,GAAG,CAAC;QACpC,IAAI,cAAc,EAAE;YAClB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACrB,OAAO,GAAG,EAAE,CAAC;YACb,SAAS;SACV;QAED,OAAO,IAAI,IAAI,CAAC;KACjB;IAED,MAAM,WAAW,GAAG,OAAO,CAAC;IAC5B,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAEzB,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 2743, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/superjson/dist/transformer.js", "sourceRoot": "", "sources": ["../src/transformer.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;;AAAA,OAAO,EACL,QAAQ,EACR,MAAM,EACN,UAAU,EACV,KAAK,EACL,UAAU,EACV,QAAQ,EACR,KAAK,EACL,WAAW,EACX,QAAQ,EACR,OAAO,EACP,OAAO,EACP,YAAY,EAEZ,KAAK,GACN,MAAM,SAAS,CAAC;AACjB,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;;;AA2BpC,SAAS,oBAAoB,CAC3B,YAAsD,EACtD,UAAa,EACb,SAA4C,EAC5C,WAA8C;IAE9C,OAAO;QACL,YAAY;QACZ,UAAU;QACV,SAAS;QACT,WAAW;KACZ,CAAC;AACJ,CAAC;AAED,MAAM,WAAW,GAAG;IAClB,oBAAoB,yIAClB,cAAW,EACX,WAAW,EACX,GAAG,CAAG,CAAD,GAAK,EACV,GAAG,CAAG,CAAD,QAAU,CAChB;IACD,oBAAoB,CAClB,mJAAQ,EACR,QAAQ,GACR,CAAC,CAAC,EAAE,AAAC,CAAC,CAAC,QAAQ,EAAE,GACjB,CAAC,CAAC,EAAE;QACF,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;YACjC,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;SAClB;QAED,OAAO,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;QAE/C,OAAO,CAAQ,CAAC;IAClB,CAAC,CACF;IACD,oBAAoB,yIAClB,SAAM,EACN,MAAM,GACN,CAAC,CAAC,EAAE,AAAC,CAAC,CAAC,WAAW,EAAE,GACpB,CAAC,CAAC,EAAE,AAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CACjB;IAED,oBAAoB,yIAClB,UAAO,EACP,OAAO,EACP,CAAC,CAAC,EAAE,SAAS,EAAE,EAAE;QACf,MAAM,SAAS,GAAQ;YACrB,IAAI,EAAE,CAAC,CAAC,IAAI;YACZ,OAAO,EAAE,CAAC,CAAC,OAAO;SACnB,CAAC;QAEF,SAAS,CAAC,iBAAiB,CAAC,OAAO,EAAC,IAAI,CAAC,EAAE;YACzC,SAAS,CAAC,IAAI,CAAC,GAAI,CAAS,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,OAAO,SAAS,CAAC;IACnB,CAAC,EACD,CAAC,CAAC,EAAE,SAAS,EAAE,EAAE;QACf,MAAM,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QAC/B,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;QAChB,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;QAElB,SAAS,CAAC,iBAAiB,CAAC,OAAO,EAAC,IAAI,CAAC,EAAE;YACxC,CAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,CAAC;IACX,CAAC,CACF;IAED,oBAAoB,CAClB,mJAAQ,EACR,QAAQ,EACR,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,GACX,KAAK,CAAC,EAAE;QACN,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;QACpD,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACtD,OAAO,IAAI,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC,CACF;IAED,oBAAoB,yIAClB,QAAK,EACL,KAAK,EACL,4BAA4B;IAC5B,8CAA8C;IAC9C,CAAC,CAAC,EAAE,CAAC,CAAC;eAAG,CAAC,CAAC,MAAM,EAAE;SAAC,GACpB,CAAC,CAAC,EAAE,AAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAChB;IACD,oBAAoB,yIAClB,QAAK,EACL,KAAK,GACL,CAAC,CAAC,EAAE,AAAC,CAAC;eAAG,CAAC,CAAC,OAAO,EAAE;SAAC,GACrB,CAAC,CAAC,EAAE,AAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAChB;IAED,oBAAoB,CAClB,CAAC,CAAC,EAAe,EAAE,2IAAC,aAAA,AAAU,EAAC,CAAC,CAAC,gJAAI,aAAA,AAAU,EAAC,CAAC,CAAC,EAClD,QAAQ,GACR,CAAC,CAAC,EAAE;QACF,gJAAI,aAAA,AAAU,EAAC,CAAC,CAAC,EAAE;YACjB,OAAO,KAAK,CAAC;SACd;QAED,IAAI,CAAC,GAAG,CAAC,EAAE;YACT,OAAO,UAAU,CAAC;SACnB,MAAM;YACL,OAAO,WAAW,CAAC;SACpB;IACH,CAAC,EACD,MAAM,CACP;IAED,oBAAoB,CAClB,CAAC,CAAC,EAAe,CAAG,CAAD,AAAE,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,EAClD,QAAQ,EACR,GAAG,EAAE;QACH,OAAO,IAAI,CAAC;IACd,CAAC,EACD,MAAM,CACP;IAED,oBAAoB,yIAClB,QAAK,EACL,KAAK,GACL,CAAC,CAAC,EAAE,AAAC,CAAC,CAAC,QAAQ,EAAE,GACjB,CAAC,CAAC,EAAE,AAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAChB;CACF,CAAC;AAEF,SAAS,uBAAuB,CAC9B,YAAsD,EACtD,UAA6C,EAC7C,SAA4C,EAC5C,WAAoD;IAEpD,OAAO;QACL,YAAY;QACZ,UAAU;QACV,SAAS;QACT,WAAW;KACZ,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,GAAG,uBAAuB,CACxC,CAAC,CAAC,EAAE,SAAS,EAAe,EAAE;IAC5B,gJAAI,WAAA,AAAQ,EAAC,CAAC,CAAC,EAAE;QACf,MAAM,YAAY,GAAG,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QACjE,OAAO,YAAY,CAAC;KACrB;IACD,OAAO,KAAK,CAAC;AACf,CAAC,EACD,CAAC,CAAC,EAAE,SAAS,EAAE,EAAE;IACf,MAAM,UAAU,GAAG,SAAS,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;IAC7D,OAAO;QAAC,QAAQ;QAAE,UAAW;KAAC,CAAC;AACjC,CAAC,GACD,CAAC,CAAC,EAAG,AAAD,CAAE,CAAC,WAAW,EAClB,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE;IAClB,MAAM,KAAK,GAAG,SAAS,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtD,IAAI,CAAC,KAAK,EAAE;QACV,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;KACzD;IACD,OAAO,KAAK,CAAC;AACf,CAAC,CACF,CAAC;AAEF,MAAM,iBAAiB,GAAG;IACxB,SAAS;IACT,UAAU;IACV,UAAU;IACV,WAAW;IACX,UAAU;IACV,WAAW;IACX,YAAY;IACZ,YAAY;IACZ,iBAAiB;CAClB,CAAC,MAAM,CAAwC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;IAC5D,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;IACtB,OAAO,GAAG,CAAC;AACb,CAAC,EAAE,CAAA,CAAE,CAAC,CAAC;AAEP,MAAM,cAAc,GAAG,uBAAuB,yIAC5C,eAAY,GACZ,CAAC,CAAC,EAAE,AAAC;QAAC,aAAa;QAAE,CAAC,CAAC,WAAW,CAAC,IAAI;KAAC,GACxC,CAAC,CAAC,EAAG,AAAD,CAAE;WAAG,CAAC;KAAC,EACX,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;IACP,MAAM,IAAI,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAErC,IAAI,CAAC,IAAI,EAAE;QACT,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;KAC9D;IAED,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AACrB,CAAC,CACF,CAAC;AAEI,SAAU,2BAA2B,CACzC,cAAmB,EACnB,SAAoB;IAEpB,IAAI,cAAc,EAAE,WAAW,EAAE;QAC/B,MAAM,YAAY,GAAG,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,aAAa,CAC1D,cAAc,CAAC,WAAW,CAC3B,CAAC;QACF,OAAO,YAAY,CAAC;KACrB;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,SAAS,GAAG,uBAAuB,CACvC,2BAA2B,EAC3B,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE;IACnB,MAAM,UAAU,GAAG,SAAS,CAAC,aAAa,CAAC,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;IAC5E,OAAO;QAAC,OAAO;QAAE,UAAW;KAAC,CAAC;AAChC,CAAC,EACD,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE;IACnB,MAAM,YAAY,GAAG,SAAS,CAAC,aAAa,CAAC,eAAe,CAC1D,KAAK,CAAC,WAAW,CAClB,CAAC;IACF,IAAI,CAAC,YAAY,EAAE;QACjB,OAAO;YAAE,GAAG,KAAK;QAAA,CAAE,CAAC;KACrB;IAED,MAAM,MAAM,GAAQ,CAAA,CAAE,CAAC;IACvB,YAAY,CAAC,OAAO,EAAC,IAAI,CAAC,EAAE;QAC1B,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC;IACH,OAAO,MAAM,CAAC;AAChB,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE;IAClB,MAAM,KAAK,GAAG,SAAS,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAErD,IAAI,CAAC,KAAK,EAAE;QACV,MAAM,IAAI,KAAK,CACb,CAAA,qCAAA,EAAwC,CAAC,CAAC,CAAC,CAAC,CAAA,iFAAA,CAAmF,CAChI,CAAC;KACH;IAED,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;AAC1D,CAAC,CACF,CAAC;AAEF,MAAM,UAAU,GAAG,uBAAuB,CACxC,CAAC,KAAK,EAAE,SAAS,EAAgB,EAAE;IACjC,OAAO,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AACrE,CAAC,EACD,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE;IACnB,MAAM,WAAW,GAAG,SAAS,CAAC,yBAAyB,CAAC,cAAc,CACpE,KAAK,CACL,CAAC;IACH,OAAO;QAAC,QAAQ;QAAE,WAAW,CAAC,IAAI;KAAC,CAAC;AACtC,CAAC,EACD,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE;IACnB,MAAM,WAAW,GAAG,SAAS,CAAC,yBAAyB,CAAC,cAAc,CACpE,KAAK,CACL,CAAC;IACH,OAAO,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AACtC,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE;IAClB,MAAM,WAAW,GAAG,SAAS,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzE,IAAI,CAAC,WAAW,EAAE;QAChB,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;KAC/D;IACD,OAAO,WAAW,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AACpC,CAAC,CACF,CAAC;AAEF,MAAM,cAAc,GAAG;IAAC,SAAS;IAAE,UAAU;IAAE,UAAU;IAAE,cAAc;CAAC,CAAC;AAEpE,MAAM,cAAc,GAAG,CAC5B,KAAU,EACV,SAAoB,EAC8B,EAAE;IACpD,MAAM,uBAAuB,iJAAG,UAAA,AAAO,EAAC,cAAc,EAAE,IAAI,CAAC,EAAE,CAC7D,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC,CACpC,CAAC;IACF,IAAI,uBAAuB,EAAE;QAC3B,OAAO;YACL,KAAK,EAAE,uBAAuB,CAAC,SAAS,CAAC,KAAc,EAAE,SAAS,CAAC;YACnE,IAAI,EAAE,uBAAuB,CAAC,UAAU,CAAC,KAAK,EAAE,SAAS,CAAC;SAC3D,CAAC;KACH;IAED,MAAM,oBAAoB,iJAAG,UAAA,AAAO,EAAC,WAAW,GAAE,IAAI,CAAC,EAAE,AACvD,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC,CACpC,CAAC;IAEF,IAAI,oBAAoB,EAAE;QACxB,OAAO;YACL,KAAK,EAAE,oBAAoB,CAAC,SAAS,CAAC,KAAc,EAAE,SAAS,CAAC;YAChE,IAAI,EAAE,oBAAoB,CAAC,UAAU;SACtC,CAAC;KACH;IAED,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AAEF,MAAM,uBAAuB,GAA0C,CAAA,CAAE,CAAC;AAC1E,WAAW,CAAC,OAAO,EAAC,IAAI,CAAC,EAAE;IACzB,uBAAuB,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;AAClD,CAAC,CAAC,CAAC;AAEI,MAAM,gBAAgB,GAAG,CAC9B,IAAS,EACT,IAAoB,EACpB,SAAoB,EACpB,EAAE;IACF,KAAI,qJAAA,AAAO,EAAC,IAAI,CAAC,EAAE;QACjB,OAAQ,IAAI,CAAC,CAAC,CAAC,EAAE;YACf,KAAK,QAAQ;gBACX,OAAO,UAAU,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;YACvD,KAAK,OAAO;gBACV,OAAO,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;YACtD,KAAK,QAAQ;gBACX,OAAO,UAAU,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;YACvD,KAAK,aAAa;gBAChB,OAAO,cAAc,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;YAC3D;gBACE,MAAM,IAAI,KAAK,CAAC,0BAA0B,GAAG,IAAI,CAAC,CAAC;SACtD;KACF,MAAM;QACL,MAAM,cAAc,GAAG,uBAAuB,CAAC,IAAI,CAAC,CAAC;QACrD,IAAI,CAAC,cAAc,EAAE;YACnB,MAAM,IAAI,KAAK,CAAC,0BAA0B,GAAG,IAAI,CAAC,CAAC;SACpD;QAED,OAAO,cAAc,CAAC,WAAW,CAAC,IAAa,EAAE,SAAS,CAAC,CAAC;KAC7D;AACH,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 2972, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/superjson/dist/accessDeep.js", "sourceRoot": "", "sources": ["../src/accessDeep.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAC/D,OAAO,EAAE,QAAQ,EAAE,MAAM,WAAW,CAAC;;;AAErC,MAAM,SAAS,GAAG,CAAC,KAA+B,EAAE,CAAS,EAAO,EAAE;IACpE,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;IAC3D,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;IAC1B,MAAO,CAAC,GAAG,CAAC,CAAE;QACZ,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,CAAC,EAAE,CAAC;KACL;IAED,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC;AAC3B,CAAC,CAAC;AAEF,SAAS,YAAY,CAAC,IAAyB;IAC7C,kJAAI,WAAA,AAAQ,EAAC,IAAI,EAAE,WAAW,CAAC,EAAE;QAC/B,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;KAC3D;IACD,kJAAI,WAAA,AAAQ,EAAC,IAAI,EAAE,WAAW,CAAC,EAAE;QAC/B,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;KAC3D;IACD,kJAAI,WAAA,AAAQ,EAAC,IAAI,EAAE,aAAa,CAAC,EAAE;QACjC,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;KAC7D;AACH,CAAC;AAEM,MAAM,OAAO,GAAG,CAAC,MAAc,EAAE,IAAyB,EAAU,EAAE;IAC3E,YAAY,CAAC,IAAI,CAAC,CAAC;IAEnB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE;QACpC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACpB,gJAAI,QAAA,AAAK,EAAC,MAAM,CAAC,EAAE;YACjB,MAAM,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC;SAClC,MAAM,gJAAI,QAAA,AAAK,EAAC,MAAM,CAAC,EAAE;YACxB,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC;YACjB,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC;YAEhD,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;YACxC,OAAQ,IAAI,EAAE;gBACZ,KAAK,KAAK;oBACR,MAAM,GAAG,QAAQ,CAAC;oBAClB,MAAM;gBACR,KAAK,OAAO;oBACV,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;oBAC9B,MAAM;aACT;SACF,MAAM;YACL,MAAM,GAAI,MAAc,CAAC,GAAG,CAAC,CAAC;SAC/B;KACF;IAED,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAEK,MAAM,OAAO,GAAG,CACrB,MAAW,EACX,IAAyB,EACzB,MAAuB,EAClB,EAAE;IACP,YAAY,CAAC,IAAI,CAAC,CAAC;IAEnB,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QACrB,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC;KACvB;IAED,IAAI,MAAM,GAAG,MAAM,CAAC;IAEpB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE;QACxC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QAEpB,gJAAI,UAAA,AAAO,EAAC,MAAM,CAAC,EAAE;YACnB,MAAM,KAAK,GAAG,CAAC,GAAG,CAAC;YACnB,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;SACxB,MAAM,gJAAI,gBAAA,AAAa,EAAC,MAAM,CAAC,EAAE;YAChC,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;SACtB,MAAM,gJAAI,QAAA,AAAK,EAAC,MAAM,CAAC,EAAE;YACxB,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC;YACjB,MAAM,GAAG,SAAS,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;SACjC,MAAM,IAAI,oJAAA,AAAK,EAAC,MAAM,CAAC,EAAE;YACxB,MAAM,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;YACpC,IAAI,KAAK,EAAE;gBACT,MAAM;aACP;YAED,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC;YACjB,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC;YAEhD,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;YACxC,OAAQ,IAAI,EAAE;gBACZ,KAAK,KAAK;oBACR,MAAM,GAAG,QAAQ,CAAC;oBAClB,MAAM;gBACR,KAAK,OAAO;oBACV,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;oBAC9B,MAAM;aACT;SACF;KACF;IAED,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAEtC,gJAAI,UAAA,AAAO,EAAC,MAAM,CAAC,EAAE;QACnB,MAAM,CAAC,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;KAC7C,MAAM,KAAI,2JAAA,AAAa,EAAC,MAAM,CAAC,EAAE;QAChC,MAAM,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;KAC3C;IAED,gJAAI,QAAA,AAAK,EAAC,MAAM,CAAC,EAAE;QACjB,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,CAAC;QAC7C,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;QAClC,IAAI,QAAQ,KAAK,QAAQ,EAAE;YACzB,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACxB,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;SACtB;KACF;IAED,gJAAI,QAAA,AAAK,EAAC,MAAM,CAAC,EAAE;QACjB,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACnC,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAExC,MAAM,IAAI,GAAG,CAAC,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC;QAC9C,OAAQ,IAAI,EAAE;YACZ,KAAK,KAAK,CAAC;gBAAC;oBACV,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;oBAChC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;oBAEzC,IAAI,MAAM,KAAK,QAAQ,EAAE;wBACvB,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;qBACzB;oBACD,MAAM;iBACP;YAED,KAAK,OAAO,CAAC;gBAAC;oBACZ,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;oBACnD,MAAM;iBACP;SACF;KACF;IAED,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 3099, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/superjson/dist/plainer.js", "sourceRoot": "", "sources": ["../src/plainer.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;;;AAAA,OAAO,EACL,OAAO,EACP,aAAa,EACb,KAAK,EACL,aAAa,EACb,WAAW,EACX,KAAK,GACN,MAAM,SAAS,CAAC;AACjB,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,EACL,2BAA2B,EAC3B,cAAc,EAEd,gBAAgB,GACjB,MAAM,kBAAkB,CAAC;AAC1B,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AAE9C,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,iBAAiB,CAAC;;;;;;;AASnD,SAAS,QAAQ,CACf,IAAsB,EACtB,MAAsC,EACtC,SAAmB,EAAE;IAErB,IAAI,CAAC,IAAI,EAAE;QACT,OAAO;KACR;IAED,IAAI,6IAAC,UAAA,AAAO,EAAC,IAAI,CAAC,EAAE;sJAClB,UAAO,AAAP,EAAQ,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE,CAC3B,CAD6B,OACrB,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC;mBAAG,MAAM,EAAE;oBAAG,oKAAA,AAAS,EAAC,GAAG,CAAC;aAAC,CAAC,CAC1D,CAAC;QACF,OAAO;KACR;IAED,MAAM,CAAC,SAAS,EAAE,QAAQ,CAAC,GAAG,IAAI,CAAC;IACnC,IAAI,QAAQ,EAAE;YACZ,oJAAA,AAAO,EAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;YAC/B,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,CAAC;mBAAG,MAAM,EAAE;mBAAG,qKAAA,AAAS,EAAC,GAAG,CAAC;aAAC,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;KACJ;IAED,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;AAC5B,CAAC;AAEK,SAAU,qBAAqB,CACnC,KAAU,EACV,WAA0C,EAC1C,SAAoB;IAEpB,QAAQ,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;QACnC,KAAK,OAAG,0JAAA,AAAO,EAAC,KAAK,EAAE,IAAI,GAAE,CAAC,CAAC,EAAE,qJAAC,mBAAA,AAAgB,EAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;IAC1E,CAAC,CAAC,CAAC;IAEH,OAAO,KAAK,CAAC;AACf,CAAC;AAEK,SAAU,mCAAmC,CACjD,KAAU,EACV,WAA2C;IAE3C,SAAS,KAAK,CAAC,cAAwB,EAAE,IAAY;QACnD,MAAM,MAAM,uJAAG,UAAA,AAAO,EAAC,KAAK,2JAAE,YAAA,AAAS,EAAC,IAAI,CAAC,CAAC,CAAC;QAE/C,cAAc,CAAC,GAAG,sJAAC,YAAS,CAAC,CAAC,OAAO,EAAC,mBAAmB,CAAC,EAAE;YAC1D,KAAK,uJAAG,UAAA,AAAO,EAAC,KAAK,EAAE,mBAAmB,EAAE,GAAG,CAAG,CAAD,KAAO,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC;IAED,gJAAI,UAAA,AAAO,EAAC,WAAW,CAAC,EAAE;QACxB,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,WAAW,CAAC;QAClC,IAAI,CAAC,OAAO,EAAC,aAAa,CAAC,EAAE;YAC3B,KAAK,uJAAG,UAAA,AAAO,EAAC,KAAK,2JAAE,YAAA,AAAS,EAAC,aAAa,CAAC,EAAE,GAAG,CAAG,CAAD,IAAM,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,IAAI,KAAK,EAAE;0JACT,UAAO,AAAP,EAAQ,KAAK,EAAE,KAAK,CAAC,CAAC;SACvB;KACF,MAAM;sJACL,UAAA,AAAO,EAAC,WAAW,EAAE,KAAK,CAAC,CAAC;KAC7B;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,MAAM,GAAG,CAAC,MAAW,EAAE,SAAoB,EAAW,EAAE,2IAC5D,gBAAA,AAAa,EAAC,MAAM,CAAC,gJACrB,UAAA,AAAO,EAAC,MAAM,CAAC,+IACf,SAAK,AAAL,EAAM,MAAM,CAAC,gJACb,QAAA,AAAK,EAAC,MAAM,CAAC,yJACb,8BAAA,AAA2B,EAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AAEjD,SAAS,WAAW,CAAC,MAAW,EAAE,IAAW,EAAE,UAA6B;IAC1E,MAAM,WAAW,GAAG,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAE3C,IAAI,WAAW,EAAE;QACf,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACxB,MAAM;QACL,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE;YAAC,IAAI;SAAC,CAAC,CAAC;KAChC;AACH,CAAC;AAYK,SAAU,sCAAsC,CACpD,WAA8B,EAC9B,MAAe;IAEf,MAAM,MAAM,GAA6B,CAAA,CAAE,CAAC;IAC5C,IAAI,iBAAiB,GAAyB,SAAS,CAAC;IAExD,WAAW,CAAC,OAAO,EAAC,KAAK,CAAC,EAAE;QAC1B,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;YACrB,OAAO;SACR;QAED,iEAAiE;QACjE,sEAAsE;QACtE,qGAAqG;QACrG,IAAI,CAAC,MAAM,EAAE;YACX,KAAK,GAAG,KAAK,CACV,GAAG,EAAC,IAAI,CAAC,EAAE,AAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAC7B,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;SACxC;QAED,MAAM,CAAC,kBAAkB,EAAE,GAAG,cAAc,CAAC,GAAG,KAAK,CAAC;QAEtD,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE;YACnC,iBAAiB,GAAG,cAAc,CAAC,GAAG,sJAAC,gBAAa,CAAC,CAAC;SACvD,MAAM;YACL,MAAM,0JAAC,gBAAA,AAAa,EAAC,kBAAkB,CAAC,CAAC,GAAG,cAAc,CAAC,GAAG,sJAC5D,gBAAa,CACd,CAAC;SACH;IACH,CAAC,CAAC,CAAC;IAEH,IAAI,iBAAiB,EAAE;QACrB,gJAAI,gBAAA,AAAa,EAAC,MAAM,CAAC,EAAE;YACzB,OAAO;gBAAC,iBAAiB;aAAC,CAAC;SAC5B,MAAM;YACL,OAAO;gBAAC,iBAAiB;gBAAE,MAAM;aAAC,CAAC;SACpC;KACF,MAAM;QACL,mJAAO,gBAAA,AAAa,EAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC;KACnD;AACH,CAAC;AAEM,MAAM,MAAM,GAAG,CACpB,MAAW,EACX,UAA6B,EAC7B,SAAoB,EACpB,MAAe,EACf,OAAc,EAAE,EAChB,oBAA2B,EAAE,EAC7B,cAAc,IAAI,GAAG,EAAmB,EAChC,EAAE;IACV,MAAM,SAAS,+IAAG,cAAA,AAAW,EAAC,MAAM,CAAC,CAAC;IAEtC,IAAI,CAAC,SAAS,EAAE;QACd,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;QAEtC,MAAM,IAAI,GAAG,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACrC,IAAI,IAAI,EAAE;YACR,wDAAwD;YACxD,OAAO,MAAM,GACT;gBACE,gBAAgB,EAAE,IAAI;aACvB,GACD,IAAI,CAAC;SACV;KACF;IAED,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE;QAC9B,MAAM,WAAW,wJAAG,iBAAA,AAAc,EAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QAEtD,MAAM,MAAM,GAAW,WAAW,GAC9B;YACE,gBAAgB,EAAE,WAAW,CAAC,KAAK;YACnC,WAAW,EAAE;gBAAC,WAAW,CAAC,IAAI;aAAC;SAChC,GACD;YACE,gBAAgB,EAAE,MAAM;SACzB,CAAC;QACN,IAAI,CAAC,SAAS,EAAE;YACd,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;SACjC;QACD,OAAO,MAAM,CAAC;KACf;IAED,QAAI,qJAAA,AAAQ,EAAC,iBAAiB,EAAE,MAAM,CAAC,EAAE;QACvC,8BAA8B;QAC9B,OAAO;YACL,gBAAgB,EAAE,IAAI;SACvB,CAAC;KACH;IAED,MAAM,oBAAoB,IAAG,qKAAc,AAAd,EAAe,MAAM,EAAE,SAAS,CAAC,CAAC;IAC/D,MAAM,WAAW,GAAG,oBAAoB,EAAE,KAAK,IAAI,MAAM,CAAC;IAE1D,MAAM,gBAAgB,IAAQ,qJAAA,AAAO,EAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA,CAAE,CAAC;IAC7D,MAAM,gBAAgB,GAAyC,CAAA,CAAE,CAAC;kJAElE,UAAA,AAAO,EAAC,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;QACpC,IACE,KAAK,KAAK,WAAW,IACrB,KAAK,KAAK,aAAa,IACvB,KAAK,KAAK,WAAW,EACrB;YACA,MAAM,IAAI,KAAK,CACb,CAAA,kBAAA,EAAqB,KAAK,CAAA,wEAAA,CAA0E,CACrG,CAAC;SACH;QAED,MAAM,eAAe,GAAG,MAAM,CAC5B,KAAK,EACL,UAAU,EACV,SAAS,EACT,MAAM,EACN,CAAC;eAAG,IAAI;YAAE,KAAK;SAAC,EAChB,CAAC;eAAG,iBAAiB;YAAE,MAAM;SAAC,EAC9B,WAAW,CACZ,CAAC;QAEF,gBAAgB,CAAC,KAAK,CAAC,GAAG,eAAe,CAAC,gBAAgB,CAAC;QAE3D,KAAI,qJAAA,AAAO,EAAC,eAAe,CAAC,WAAW,CAAC,EAAE;YACxC,gBAAgB,CAAC,KAAK,CAAC,GAAG,eAAe,CAAC,WAAW,CAAC;SACvD,MAAM,gJAAI,gBAAA,AAAa,EAAC,eAAe,CAAC,WAAW,CAAC,EAAE;0JACrD,UAAA,AAAO,EAAC,eAAe,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE;gBACjD,gBAAgB,0JAAC,YAAS,AAAT,EAAU,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;YACxD,CAAC,CAAC,CAAC;SACJ;IACH,CAAC,CAAC,CAAC;IAEH,MAAM,MAAM,+IAAW,gBAAA,AAAa,EAAC,gBAAgB,CAAC,GAClD;QACE,gBAAgB;QAChB,WAAW,EAAE,CAAC,CAAC,oBAAoB,GAC/B;YAAC,oBAAoB,CAAC,IAAI;SAAC,GAC3B,SAAS;KACd,GACD;QACE,gBAAgB;QAChB,WAAW,EAAE,CAAC,CAAC,oBAAoB,GAC/B;YAAC,oBAAoB,CAAC,IAAI;YAAE,gBAAgB;SAAC,GAC7C,gBAAgB;KACrB,CAAC;IACN,IAAI,CAAC,SAAS,EAAE;QACd,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KACjC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 3288, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/superjson/dist/index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;;;;;;;;;AACA,OAAO,EAAE,aAAa,EAAmB,MAAM,qBAAqB,CAAC;AACrE,OAAO,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAC;AACzC,OAAO,EAEL,yBAAyB,GAC1B,MAAM,kCAAkC,CAAC;AAC1C,OAAO,EACL,mCAAmC,EACnC,qBAAqB,EACrB,sCAAsC,EACtC,MAAM,GACP,MAAM,cAAc,CAAC;AACtB,OAAO,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;;;;;;AAEvB,MAAO,SAAS;IAM5B;;OAEG,CACH,YAAY,EACV,MAAM,GAAG,KAAK,EAAA,GAGZ,CAAA,CAAE,CAAA;QA2DG,IAAA,CAAA,aAAa,GAAG,2JAAI,gBAAa,EAAE,CAAC;QAKpC,IAAA,CAAA,cAAc,GAAG,kJAAI,WAAQ,EAAS,CAAC,CAAC,EAAE,AAAC,CAAC,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC;QAKhE,IAAA,CAAA,yBAAyB,GAAG,2KAAI,4BAAyB,EAAE,CAAC;QAW5D,IAAA,CAAA,iBAAiB,GAAa,EAAE,CAAC;QA/ExC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED,SAAS,CAAC,MAAsB,EAAA;QAC9B,MAAM,UAAU,GAAG,IAAI,GAAG,EAAgB,CAAC;QAC3C,MAAM,MAAM,OAAG,sJAAA,AAAM,EAAC,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC7D,MAAM,GAAG,GAAoB;YAC3B,IAAI,EAAE,MAAM,CAAC,gBAAgB;SAC9B,CAAC;QAEF,IAAI,MAAM,CAAC,WAAW,EAAE;YACtB,GAAG,CAAC,IAAI,GAAG;gBACT,GAAG,GAAG,CAAC,IAAI;gBACX,MAAM,EAAE,MAAM,CAAC,WAAW;aAC3B,CAAC;SACH;QAED,MAAM,mBAAmB,oJAAG,yCAAA,AAAsC,EAChE,UAAU,EACV,IAAI,CAAC,MAAM,CACZ,CAAC;QACF,IAAI,mBAAmB,EAAE;YACvB,GAAG,CAAC,IAAI,GAAG;gBACT,GAAG,GAAG,CAAC,IAAI;gBACX,qBAAqB,EAAE,mBAAmB;aAC3C,CAAC;SACH;QAED,OAAO,GAAG,CAAC;IACb,CAAC;IAED,WAAW,CAAc,OAAwB,EAAA;QAC/C,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;QAE/B,IAAI,MAAM,yJAAM,OAAA,AAAI,EAAC,IAAI,CAAQ,CAAC;QAElC,IAAI,IAAI,EAAE,MAAM,EAAE;YAChB,MAAM,oJAAG,wBAAA,AAAqB,EAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;SAC3D;QAED,IAAI,IAAI,EAAE,qBAAqB,EAAE;YAC/B,MAAM,oJAAG,sCAAA,AAAmC,EAC1C,MAAM,EACN,IAAI,CAAC,qBAAqB,CAC3B,CAAC;SACH;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,SAAS,CAAC,MAAsB,EAAA;QAC9B,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;IAChD,CAAC;IAED,KAAK,CAAc,MAAc,EAAA;QAC/B,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IAC9C,CAAC;IAGD,aAAa,CAAC,CAAQ,EAAE,OAAkC,EAAA;QACxD,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAC1C,CAAC;IAGD,cAAc,CAAC,CAAS,EAAE,UAAmB,EAAA;QAC3C,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;IAC9C,CAAC;IAGD,cAAc,CACZ,WAAiD,EACjD,IAAY,EAAA;QAEZ,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC;YACtC,IAAI;YACJ,GAAG,WAAW;SACf,CAAC,CAAC;IACL,CAAC;IAGD,eAAe,CAAC,GAAG,KAAe,EAAA;QAChC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;IACxC,CAAC;;AAEc,UAAA,eAAe,GAAG,IAAI,SAAS,EAAE,CAAC;AAC1C,UAAA,SAAS,GAAG,SAAS,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CACzD,SAAS,CAAC,eAAe,CAC1B,CAAC;AACK,UAAA,WAAW,GAAG,SAAS,CAAC,eAAe,CAAC,WAAW,CAAC,IAAI,CAC7D,SAAS,CAAC,eAAe,CAC1B,CAAC;AACK,UAAA,SAAS,GAAG,SAAS,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CACzD,SAAS,CAAC,eAAe,CAC1B,CAAC;AACK,UAAA,KAAK,GAAG,SAAS,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,CACjD,SAAS,CAAC,eAAe,CAC1B,CAAC;AACK,UAAA,aAAa,GAAG,SAAS,CAAC,eAAe,CAAC,aAAa,CAAC,IAAI,CACjE,SAAS,CAAC,eAAe,CAC1B,CAAC;AACK,UAAA,cAAc,GAAG,SAAS,CAAC,eAAe,CAAC,cAAc,CAAC,IAAI,CACnE,SAAS,CAAC,eAAe,CAC1B,CAAC;AACK,UAAA,cAAc,GAAG,SAAS,CAAC,eAAe,CAAC,cAAc,CAAC,IAAI,CACnE,SAAS,CAAC,eAAe,CAC1B,CAAC;AACK,UAAA,eAAe,GAAG,SAAS,CAAC,eAAe,CAAC,eAAe,CAAC,IAAI,CACrE,SAAS,CAAC,eAAe,CAC1B,CAAC;;AAKG,MAAM,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;AACtC,MAAM,WAAW,GAAG,SAAS,CAAC,WAAW,CAAC;AAE1C,MAAM,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;AACtC,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;AAE9B,MAAM,aAAa,GAAG,SAAS,CAAC,aAAa,CAAC;AAC9C,MAAM,cAAc,GAAG,SAAS,CAAC,cAAc,CAAC;AAChD,MAAM,cAAc,GAAG,SAAS,CAAC,cAAc,CAAC;AAChD,MAAM,eAAe,GAAG,SAAS,CAAC,eAAe,CAAC", "debugId": null}}, {"offset": {"line": 3396, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/is-what/dist/index.js"], "sourcesContent": ["function getType(payload) {\n  return Object.prototype.toString.call(payload).slice(8, -1);\n}\n\nfunction isAnyObject(payload) {\n  return getType(payload) === \"Object\";\n}\n\nfunction isArray(payload) {\n  return getType(payload) === \"Array\";\n}\n\nfunction isBlob(payload) {\n  return getType(payload) === \"Blob\";\n}\n\nfunction isBoolean(payload) {\n  return getType(payload) === \"Boolean\";\n}\n\nfunction isDate(payload) {\n  return getType(payload) === \"Date\" && !isNaN(payload);\n}\n\nfunction isEmptyArray(payload) {\n  return isArray(payload) && payload.length === 0;\n}\n\nfunction isPlainObject(payload) {\n  if (getType(payload) !== \"Object\")\n    return false;\n  const prototype = Object.getPrototypeOf(payload);\n  return !!prototype && prototype.constructor === Object && prototype === Object.prototype;\n}\n\nfunction isEmptyObject(payload) {\n  return isPlainObject(payload) && Object.keys(payload).length === 0;\n}\n\nfunction isEmptyString(payload) {\n  return payload === \"\";\n}\n\nfunction isError(payload) {\n  return getType(payload) === \"Error\" || payload instanceof Error;\n}\n\nfunction isFile(payload) {\n  return getType(payload) === \"File\";\n}\n\nfunction isFullArray(payload) {\n  return isArray(payload) && payload.length > 0;\n}\n\nfunction isFullObject(payload) {\n  return isPlainObject(payload) && Object.keys(payload).length > 0;\n}\n\nfunction isString(payload) {\n  return getType(payload) === \"String\";\n}\n\nfunction isFullString(payload) {\n  return isString(payload) && payload !== \"\";\n}\n\nfunction isFunction(payload) {\n  return typeof payload === \"function\";\n}\n\nfunction isType(payload, type) {\n  if (!(type instanceof Function)) {\n    throw new TypeError(\"Type must be a function\");\n  }\n  if (!Object.prototype.hasOwnProperty.call(type, \"prototype\")) {\n    throw new TypeError(\"Type is not a class\");\n  }\n  const name = type.name;\n  return getType(payload) === name || Boolean(payload && payload.constructor === type);\n}\n\nfunction isInstanceOf(value, classOrClassName) {\n  if (typeof classOrClassName === \"function\") {\n    for (let p = value; p; p = Object.getPrototypeOf(p)) {\n      if (isType(p, classOrClassName)) {\n        return true;\n      }\n    }\n    return false;\n  } else {\n    for (let p = value; p; p = Object.getPrototypeOf(p)) {\n      if (getType(p) === classOrClassName) {\n        return true;\n      }\n    }\n    return false;\n  }\n}\n\nfunction isMap(payload) {\n  return getType(payload) === \"Map\";\n}\n\nfunction isNaNValue(payload) {\n  return getType(payload) === \"Number\" && isNaN(payload);\n}\n\nfunction isNumber(payload) {\n  return getType(payload) === \"Number\" && !isNaN(payload);\n}\n\nfunction isNegativeNumber(payload) {\n  return isNumber(payload) && payload < 0;\n}\n\nfunction isNull(payload) {\n  return getType(payload) === \"Null\";\n}\n\nfunction isOneOf(a, b, c, d, e) {\n  return (value) => a(value) || b(value) || !!c && c(value) || !!d && d(value) || !!e && e(value);\n}\n\nfunction isUndefined(payload) {\n  return getType(payload) === \"Undefined\";\n}\n\nconst isNullOrUndefined = isOneOf(isNull, isUndefined);\n\nfunction isObject(payload) {\n  return isPlainObject(payload);\n}\n\nfunction isObjectLike(payload) {\n  return isAnyObject(payload);\n}\n\nfunction isPositiveNumber(payload) {\n  return isNumber(payload) && payload > 0;\n}\n\nfunction isSymbol(payload) {\n  return getType(payload) === \"Symbol\";\n}\n\nfunction isPrimitive(payload) {\n  return isBoolean(payload) || isNull(payload) || isUndefined(payload) || isNumber(payload) || isString(payload) || isSymbol(payload);\n}\n\nfunction isPromise(payload) {\n  return getType(payload) === \"Promise\";\n}\n\nfunction isRegExp(payload) {\n  return getType(payload) === \"RegExp\";\n}\n\nfunction isSet(payload) {\n  return getType(payload) === \"Set\";\n}\n\nfunction isWeakMap(payload) {\n  return getType(payload) === \"WeakMap\";\n}\n\nfunction isWeakSet(payload) {\n  return getType(payload) === \"WeakSet\";\n}\n\nexport { getType, isAnyObject, isArray, isBlob, isBoolean, isDate, isEmptyArray, isEmptyObject, isEmptyString, isError, isFile, isFullArray, isFullObject, isFullString, isFunction, isInstanceOf, isMap, isNaNValue, isNegativeNumber, isNull, isNullOrUndefined, isNumber, isObject, isObjectLike, isOneOf, isPlainObject, isPositiveNumber, isPrimitive, isPromise, isRegExp, isSet, isString, isSymbol, isType, isUndefined, isWeakMap, isWeakSet };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAAS,QAAQ,OAAO;IACtB,OAAO,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,KAAK,CAAC,GAAG,CAAC;AAC3D;AAEA,SAAS,YAAY,OAAO;IAC1B,OAAO,QAAQ,aAAa;AAC9B;AAEA,SAAS,QAAQ,OAAO;IACtB,OAAO,QAAQ,aAAa;AAC9B;AAEA,SAAS,OAAO,OAAO;IACrB,OAAO,QAAQ,aAAa;AAC9B;AAEA,SAAS,UAAU,OAAO;IACxB,OAAO,QAAQ,aAAa;AAC9B;AAEA,SAAS,OAAO,OAAO;IACrB,OAAO,QAAQ,aAAa,UAAU,CAAC,MAAM;AAC/C;AAEA,SAAS,aAAa,OAAO;IAC3B,OAAO,QAAQ,YAAY,QAAQ,MAAM,KAAK;AAChD;AAEA,SAAS,cAAc,OAAO;IAC5B,IAAI,QAAQ,aAAa,UACvB,OAAO;IACT,MAAM,YAAY,OAAO,cAAc,CAAC;IACxC,OAAO,CAAC,CAAC,aAAa,UAAU,WAAW,KAAK,UAAU,cAAc,OAAO,SAAS;AAC1F;AAEA,SAAS,cAAc,OAAO;IAC5B,OAAO,cAAc,YAAY,OAAO,IAAI,CAAC,SAAS,MAAM,KAAK;AACnE;AAEA,SAAS,cAAc,OAAO;IAC5B,OAAO,YAAY;AACrB;AAEA,SAAS,QAAQ,OAAO;IACtB,OAAO,QAAQ,aAAa,WAAW,mBAAmB;AAC5D;AAEA,SAAS,OAAO,OAAO;IACrB,OAAO,QAAQ,aAAa;AAC9B;AAEA,SAAS,YAAY,OAAO;IAC1B,OAAO,QAAQ,YAAY,QAAQ,MAAM,GAAG;AAC9C;AAEA,SAAS,aAAa,OAAO;IAC3B,OAAO,cAAc,YAAY,OAAO,IAAI,CAAC,SAAS,MAAM,GAAG;AACjE;AAEA,SAAS,SAAS,OAAO;IACvB,OAAO,QAAQ,aAAa;AAC9B;AAEA,SAAS,aAAa,OAAO;IAC3B,OAAO,SAAS,YAAY,YAAY;AAC1C;AAEA,SAAS,WAAW,OAAO;IACzB,OAAO,OAAO,YAAY;AAC5B;AAEA,SAAS,OAAO,OAAO,EAAE,IAAI;IAC3B,IAAI,CAAC,CAAC,gBAAgB,QAAQ,GAAG;QAC/B,MAAM,IAAI,UAAU;IACtB;IACA,IAAI,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,cAAc;QAC5D,MAAM,IAAI,UAAU;IACtB;IACA,MAAM,OAAO,KAAK,IAAI;IACtB,OAAO,QAAQ,aAAa,QAAQ,QAAQ,WAAW,QAAQ,WAAW,KAAK;AACjF;AAEA,SAAS,aAAa,KAAK,EAAE,gBAAgB;IAC3C,IAAI,OAAO,qBAAqB,YAAY;QAC1C,IAAK,IAAI,IAAI,OAAO,GAAG,IAAI,OAAO,cAAc,CAAC,GAAI;YACnD,IAAI,OAAO,GAAG,mBAAmB;gBAC/B,OAAO;YACT;QACF;QACA,OAAO;IACT,OAAO;QACL,IAAK,IAAI,IAAI,OAAO,GAAG,IAAI,OAAO,cAAc,CAAC,GAAI;YACnD,IAAI,QAAQ,OAAO,kBAAkB;gBACnC,OAAO;YACT;QACF;QACA,OAAO;IACT;AACF;AAEA,SAAS,MAAM,OAAO;IACpB,OAAO,QAAQ,aAAa;AAC9B;AAEA,SAAS,WAAW,OAAO;IACzB,OAAO,QAAQ,aAAa,YAAY,MAAM;AAChD;AAEA,SAAS,SAAS,OAAO;IACvB,OAAO,QAAQ,aAAa,YAAY,CAAC,MAAM;AACjD;AAEA,SAAS,iBAAiB,OAAO;IAC/B,OAAO,SAAS,YAAY,UAAU;AACxC;AAEA,SAAS,OAAO,OAAO;IACrB,OAAO,QAAQ,aAAa;AAC9B;AAEA,SAAS,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC5B,OAAO,CAAC,QAAU,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,KAAK,EAAE;AAC3F;AAEA,SAAS,YAAY,OAAO;IAC1B,OAAO,QAAQ,aAAa;AAC9B;AAEA,MAAM,oBAAoB,QAAQ,QAAQ;AAE1C,SAAS,SAAS,OAAO;IACvB,OAAO,cAAc;AACvB;AAEA,SAAS,aAAa,OAAO;IAC3B,OAAO,YAAY;AACrB;AAEA,SAAS,iBAAiB,OAAO;IAC/B,OAAO,SAAS,YAAY,UAAU;AACxC;AAEA,SAAS,SAAS,OAAO;IACvB,OAAO,QAAQ,aAAa;AAC9B;AAEA,SAAS,YAAY,OAAO;IAC1B,OAAO,UAAU,YAAY,OAAO,YAAY,YAAY,YAAY,SAAS,YAAY,SAAS,YAAY,SAAS;AAC7H;AAEA,SAAS,UAAU,OAAO;IACxB,OAAO,QAAQ,aAAa;AAC9B;AAEA,SAAS,SAAS,OAAO;IACvB,OAAO,QAAQ,aAAa;AAC9B;AAEA,SAAS,MAAM,OAAO;IACpB,OAAO,QAAQ,aAAa;AAC9B;AAEA,SAAS,UAAU,OAAO;IACxB,OAAO,QAAQ,aAAa;AAC9B;AAEA,SAAS,UAAU,OAAO;IACxB,OAAO,QAAQ,aAAa;AAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3572, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/copy-anything/dist/index.js"], "sourcesContent": ["import { isArray, isPlainObject } from 'is-what';\n\nfunction assignProp(carry, key, newVal, originalObject, includeNonenumerable) {\n  const propType = {}.propertyIsEnumerable.call(originalObject, key) ? \"enumerable\" : \"nonenumerable\";\n  if (propType === \"enumerable\")\n    carry[key] = newVal;\n  if (includeNonenumerable && propType === \"nonenumerable\") {\n    Object.defineProperty(carry, key, {\n      value: newVal,\n      enumerable: false,\n      writable: true,\n      configurable: true\n    });\n  }\n}\nfunction copy(target, options = {}) {\n  if (isArray(target)) {\n    return target.map((item) => copy(item, options));\n  }\n  if (!isPlainObject(target)) {\n    return target;\n  }\n  const props = Object.getOwnPropertyNames(target);\n  const symbols = Object.getOwnPropertySymbols(target);\n  return [...props, ...symbols].reduce((carry, key) => {\n    if (isArray(options.props) && !options.props.includes(key)) {\n      return carry;\n    }\n    const val = target[key];\n    const newVal = copy(val, options);\n    assignProp(carry, key, newVal, target, options.nonenumerable);\n    return carry;\n  }, {});\n}\n\nexport { copy };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,WAAW,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,cAAc,EAAE,oBAAoB;IAC1E,MAAM,WAAW,CAAA,CAAC,CAAA,EAAE,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,OAAO,eAAe;IACpF,IAAI,aAAa,cACf,KAAK,CAAC,IAAI,GAAG;IACf,IAAI,wBAAwB,aAAa,iBAAiB;QACxD,OAAO,cAAc,CAAC,OAAO,KAAK;YAChC,OAAO;YACP,YAAY;YACZ,UAAU;YACV,cAAc;QAChB;IACF;AACF;AACA,SAAS,KAAK,MAAM,EAAE,UAAU,CAAC,CAAC;IAChC,IAAI,CAAA,GAAA,2IAAA,CAAA,UAAO,AAAD,EAAE,SAAS;QACnB,OAAO,OAAO,GAAG,CAAC,CAAC,OAAS,KAAK,MAAM;IACzC;IACA,IAAI,CAAC,CAAA,GAAA,2IAAA,CAAA,gBAAa,AAAD,EAAE,SAAS;QAC1B,OAAO;IACT;IACA,MAAM,QAAQ,OAAO,mBAAmB,CAAC;IACzC,MAAM,UAAU,OAAO,qBAAqB,CAAC;IAC7C,OAAO;WAAI;WAAU;KAAQ,CAAC,MAAM,CAAC,CAAC,OAAO;QAC3C,IAAI,CAAA,GAAA,2IAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,KAAK,KAAK,CAAC,QAAQ,KAAK,CAAC,QAAQ,CAAC,MAAM;YAC1D,OAAO;QACT;QACA,MAAM,MAAM,MAAM,CAAC,IAAI;QACvB,MAAM,SAAS,KAAK,KAAK;QACzB,WAAW,OAAO,KAAK,QAAQ,QAAQ,QAAQ,aAAa;QAC5D,OAAO;IACT,GAAG,CAAC;AACN", "ignoreList": [0], "debugId": null}}]}