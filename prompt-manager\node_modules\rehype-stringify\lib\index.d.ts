/**
 * Plugin to add support for serializing as HTML.
 *
 * @param {Options | null | undefined} [options]
 *   Configuration (optional).
 * @returns {undefined}
 *   Nothing.
 */
export default function rehypeStringify(options?: Options | null | undefined): undefined;
export default class rehypeStringify {
    /**
     * Plugin to add support for serializing as HTML.
     *
     * @param {Options | null | undefined} [options]
     *   Configuration (optional).
     * @returns {undefined}
     *   Nothing.
     */
    constructor(options?: Options | null | undefined);
    compiler: (tree: Root, file: import("vfile").VFile) => string;
}
import type { Options } from 'hast-util-to-html';
import type { Root } from 'hast';
//# sourceMappingURL=index.d.ts.map