"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/CategorySidebarNew.tsx":
/*!***********************************************!*\
  !*** ./src/components/CategorySidebarNew.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CategorySidebar: () => (/* binding */ CategorySidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ~/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _trpc_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ~/trpc/react */ \"(app-pages-browser)/./src/trpc/react.tsx\");\n/* harmony import */ var _hooks_useStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ~/hooks/useStore */ \"(app-pages-browser)/./src/hooks/useStore.ts\");\n/* __next_internal_client_entry_do_not_use__ CategorySidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction CategorySidebar(param) {\n    let { selectedCategoryId, onCategorySelect, className } = param;\n    _s();\n    const [hoveredCategoryId, setHoveredCategoryId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { openCreateCategoryModal, openEditCategoryModal } = (0,_hooks_useStore__WEBPACK_IMPORTED_MODULE_4__.useUI)();\n    const { data: categories, isLoading } = _trpc_react__WEBPACK_IMPORTED_MODULE_3__.api.category.getAll.useQuery();\n    const handleCategoryClick = (categoryId)=>{\n        onCategorySelect(categoryId);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('h-full flex flex-col p-4', className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"skeleton h-6 w-24 mb-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CategorySidebarNew.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"skeleton h-10 w-full\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CategorySidebarNew.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"skeleton h-10 w-full\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CategorySidebarNew.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"skeleton h-10 w-full\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CategorySidebarNew.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CategorySidebarNew.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CategorySidebarNew.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('h-full flex flex-col', className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-base-300\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold\",\n                    children: \"分类目录\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CategorySidebarNew.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CategorySidebarNew.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>handleCategoryClick(null),\n                            className: \"btn btn-ghost w-full justify-between \".concat(selectedCategoryId === null ? 'btn-active' : ''),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"全部分类\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CategorySidebarNew.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"badge badge-neutral\",\n                                    children: (categories === null || categories === void 0 ? void 0 : categories.reduce((total, cat)=>total + cat._count.prompts, 0)) || 0\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CategorySidebarNew.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CategorySidebarNew.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this),\n                        categories === null || categories === void 0 ? void 0 : categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                onMouseEnter: ()=>setHoveredCategoryId(category.id),\n                                onMouseLeave: ()=>setHoveredCategoryId(null),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleCategoryClick(category.id),\n                                        className: \"btn btn-ghost w-full justify-between \".concat(selectedCategoryId === category.id ? 'btn-active' : ''),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 rounded-full\",\n                                                        style: {\n                                                            backgroundColor: category.color\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CategorySidebarNew.tsx\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: category.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CategorySidebarNew.tsx\",\n                                                        lineNumber: 83,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CategorySidebarNew.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"badge badge-neutral\",\n                                                children: category._count.prompts\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CategorySidebarNew.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CategorySidebarNew.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, this),\n                                    hoveredCategoryId === category.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            openEditCategoryModal(category);\n                                        },\n                                        className: \"btn btn-ghost btn-xs absolute right-2 top-1/2 transform -translate-y-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-3 h-3\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CategorySidebarNew.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CategorySidebarNew.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CategorySidebarNew.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, category.id, true, {\n                                fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CategorySidebarNew.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, this))\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CategorySidebarNew.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CategorySidebarNew.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-base-300\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: openCreateCategoryModal,\n                    className: \"btn btn-primary btn-block\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 4v16m8-8H4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CategorySidebarNew.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CategorySidebarNew.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this),\n                        \"新建分类\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CategorySidebarNew.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CategorySidebarNew.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CategorySidebarNew.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n_s(CategorySidebar, \"DKQN5WTgLw0oHrVJJe2TUE0k5Tk=\", false, function() {\n    return [\n        _hooks_useStore__WEBPACK_IMPORTED_MODULE_4__.useUI\n    ];\n});\n_c = CategorySidebar;\nvar _c;\n$RefreshReg$(_c, \"CategorySidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0NhdGVnb3J5U2lkZWJhck5ldy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRWdDO0FBQ0E7QUFDRTtBQUNNO0FBUWpDLFNBQVNJLGdCQUFnQixLQUlUO1FBSlMsRUFDOUJDLGtCQUFrQixFQUNsQkMsZ0JBQWdCLEVBQ2hCQyxTQUFTLEVBQ1ksR0FKUzs7SUFLOUIsTUFBTSxDQUFDQyxtQkFBbUJDLHFCQUFxQixHQUFHVCwrQ0FBUUEsQ0FBZ0I7SUFFMUUsTUFBTSxFQUFFVSx1QkFBdUIsRUFBRUMscUJBQXFCLEVBQUUsR0FBR1Isc0RBQUtBO0lBQ2hFLE1BQU0sRUFBRVMsTUFBTUMsVUFBVSxFQUFFQyxTQUFTLEVBQUUsR0FBR1osNENBQUdBLENBQUNhLFFBQVEsQ0FBQ0MsTUFBTSxDQUFDQyxRQUFRO0lBRXBFLE1BQU1DLHNCQUFzQixDQUFDQztRQUMzQmIsaUJBQWlCYTtJQUNuQjtJQUVBLElBQUlMLFdBQVc7UUFDYixxQkFDRSw4REFBQ007WUFBSWIsV0FBV04sOENBQUVBLENBQUMsNEJBQTRCTTs7OEJBQzdDLDhEQUFDYTtvQkFBSWIsV0FBVTs7Ozs7OzhCQUNmLDhEQUFDYTtvQkFBSWIsV0FBVTs7c0NBQ2IsOERBQUNhOzRCQUFJYixXQUFVOzs7Ozs7c0NBQ2YsOERBQUNhOzRCQUFJYixXQUFVOzs7Ozs7c0NBQ2YsOERBQUNhOzRCQUFJYixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFJdkI7SUFFQSxxQkFDRSw4REFBQ2E7UUFBSWIsV0FBV04sOENBQUVBLENBQUMsd0JBQXdCTTs7MEJBRXpDLDhEQUFDYTtnQkFBSWIsV0FBVTswQkFDYiw0RUFBQ2M7b0JBQUdkLFdBQVU7OEJBQW9COzs7Ozs7Ozs7OzswQkFJcEMsOERBQUNhO2dCQUFJYixXQUFVOzBCQUNiLDRFQUFDYTtvQkFBSWIsV0FBVTs7c0NBRWIsOERBQUNlOzRCQUNDQyxTQUFTLElBQU1MLG9CQUFvQjs0QkFDbkNYLFdBQVcsd0NBRVYsT0FEQ0YsdUJBQXVCLE9BQU8sZUFBZTs7OENBRy9DLDhEQUFDbUI7OENBQUs7Ozs7Ozs4Q0FDTiw4REFBQ0o7b0NBQUliLFdBQVU7OENBQ1pNLENBQUFBLHVCQUFBQSxpQ0FBQUEsV0FBWVksTUFBTSxDQUFDLENBQUNDLE9BQU9DLE1BQVFELFFBQVFDLElBQUlDLE1BQU0sQ0FBQ0MsT0FBTyxFQUFFLE9BQU07Ozs7Ozs7Ozs7Ozt3QkFLekVoQix1QkFBQUEsaUNBQUFBLFdBQVlpQixHQUFHLENBQUMsQ0FBQ2YseUJBQ2hCLDhEQUFDSztnQ0FFQ2IsV0FBVTtnQ0FDVndCLGNBQWMsSUFBTXRCLHFCQUFxQk0sU0FBU2lCLEVBQUU7Z0NBQ3BEQyxjQUFjLElBQU14QixxQkFBcUI7O2tEQUV6Qyw4REFBQ2E7d0NBQ0NDLFNBQVMsSUFBTUwsb0JBQW9CSCxTQUFTaUIsRUFBRTt3Q0FDOUN6QixXQUFXLHdDQUVWLE9BRENGLHVCQUF1QlUsU0FBU2lCLEVBQUUsR0FBRyxlQUFlOzswREFHdEQsOERBQUNaO2dEQUFJYixXQUFVOztrRUFDYiw4REFBQ2E7d0RBQ0NiLFdBQVU7d0RBQ1YyQixPQUFPOzREQUFFQyxpQkFBaUJwQixTQUFTcUIsS0FBSzt3REFBQzs7Ozs7O2tFQUUzQyw4REFBQ1o7a0VBQU1ULFNBQVNzQixJQUFJOzs7Ozs7Ozs7Ozs7MERBRXRCLDhEQUFDakI7Z0RBQUliLFdBQVU7MERBQ1pRLFNBQVNhLE1BQU0sQ0FBQ0MsT0FBTzs7Ozs7Ozs7Ozs7O29DQUszQnJCLHNCQUFzQk8sU0FBU2lCLEVBQUUsa0JBQ2hDLDhEQUFDVjt3Q0FDQ0MsU0FBUyxDQUFDZTs0Q0FDUkEsRUFBRUMsZUFBZTs0Q0FDakI1QixzQkFBc0JJO3dDQUN4Qjt3Q0FDQVIsV0FBVTtrREFFViw0RUFBQ2lDOzRDQUFJakMsV0FBVTs0Q0FBVWtDLE1BQUs7NENBQU9DLFFBQU87NENBQWVDLFNBQVE7c0RBQ2pFLDRFQUFDQztnREFBS0MsZUFBYztnREFBUUMsZ0JBQWU7Z0RBQVFDLGFBQWE7Z0RBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OytCQWpDdEVqQyxTQUFTaUIsRUFBRTs7Ozs7Ozs7Ozs7Ozs7OzswQkEyQ3hCLDhEQUFDWjtnQkFBSWIsV0FBVTswQkFDYiw0RUFBQ2U7b0JBQ0NDLFNBQVNiO29CQUNUSCxXQUFVOztzQ0FFViw4REFBQ2lDOzRCQUFJakMsV0FBVTs0QkFBVWtDLE1BQUs7NEJBQU9DLFFBQU87NEJBQWVDLFNBQVE7c0NBQ2pFLDRFQUFDQztnQ0FBS0MsZUFBYztnQ0FBUUMsZ0JBQWU7Z0NBQVFDLGFBQWE7Z0NBQUdDLEdBQUU7Ozs7Ozs7Ozs7O3dCQUNqRTs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTWhCO0dBN0dnQjVDOztRQU82Q0Qsa0RBQUtBOzs7S0FQbERDIiwic291cmNlcyI6WyJEOlxcQ3Vyc29yIFByb2plY3RcXHdlYnNpdGVcXEF1Z21lbnQyXFxwcm9tcHQtbWFuYWdlclxcc3JjXFxjb21wb25lbnRzXFxDYXRlZ29yeVNpZGViYXJOZXcudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgY24gfSBmcm9tICd+L2xpYi91dGlscydcbmltcG9ydCB7IGFwaSB9IGZyb20gJ34vdHJwYy9yZWFjdCdcbmltcG9ydCB7IHVzZVVJIH0gZnJvbSAnfi9ob29rcy91c2VTdG9yZSdcblxuaW50ZXJmYWNlIENhdGVnb3J5U2lkZWJhclByb3BzIHtcbiAgc2VsZWN0ZWRDYXRlZ29yeUlkPzogc3RyaW5nIHwgbnVsbFxuICBvbkNhdGVnb3J5U2VsZWN0OiAoY2F0ZWdvcnlJZDogc3RyaW5nIHwgbnVsbCkgPT4gdm9pZFxuICBjbGFzc05hbWU/OiBzdHJpbmdcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIENhdGVnb3J5U2lkZWJhcih7IFxuICBzZWxlY3RlZENhdGVnb3J5SWQsIFxuICBvbkNhdGVnb3J5U2VsZWN0LCBcbiAgY2xhc3NOYW1lIFxufTogQ2F0ZWdvcnlTaWRlYmFyUHJvcHMpIHtcbiAgY29uc3QgW2hvdmVyZWRDYXRlZ29yeUlkLCBzZXRIb3ZlcmVkQ2F0ZWdvcnlJZF0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKVxuICBcbiAgY29uc3QgeyBvcGVuQ3JlYXRlQ2F0ZWdvcnlNb2RhbCwgb3BlbkVkaXRDYXRlZ29yeU1vZGFsIH0gPSB1c2VVSSgpXG4gIGNvbnN0IHsgZGF0YTogY2F0ZWdvcmllcywgaXNMb2FkaW5nIH0gPSBhcGkuY2F0ZWdvcnkuZ2V0QWxsLnVzZVF1ZXJ5KClcblxuICBjb25zdCBoYW5kbGVDYXRlZ29yeUNsaWNrID0gKGNhdGVnb3J5SWQ6IHN0cmluZyB8IG51bGwpID0+IHtcbiAgICBvbkNhdGVnb3J5U2VsZWN0KGNhdGVnb3J5SWQpXG4gIH1cblxuICBpZiAoaXNMb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtjbignaC1mdWxsIGZsZXggZmxleC1jb2wgcC00JywgY2xhc3NOYW1lKX0+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic2tlbGV0b24gaC02IHctMjQgbWItNFwiPjwvZGl2PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic2tlbGV0b24gaC0xMCB3LWZ1bGxcIj48L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNrZWxldG9uIGgtMTAgdy1mdWxsXCI+PC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJza2VsZXRvbiBoLTEwIHctZnVsbFwiPjwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIClcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2NuKCdoLWZ1bGwgZmxleCBmbGV4LWNvbCcsIGNsYXNzTmFtZSl9PlxuICAgICAgey8qIOWktOmDqCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IGJvcmRlci1iIGJvcmRlci1iYXNlLTMwMFwiPlxuICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LWJvbGRcIj7liIbnsbvnm67lvZU8L2gzPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiDliIbnsbvliJfooaggKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBvdmVyZmxvdy15LWF1dG8gcC00XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgey8qIOWFqOmDqOWIhuexu+mAiemhuSAqL31cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVDYXRlZ29yeUNsaWNrKG51bGwpfVxuICAgICAgICAgICAgY2xhc3NOYW1lPXtgYnRuIGJ0bi1naG9zdCB3LWZ1bGwganVzdGlmeS1iZXR3ZWVuICR7XG4gICAgICAgICAgICAgIHNlbGVjdGVkQ2F0ZWdvcnlJZCA9PT0gbnVsbCA/ICdidG4tYWN0aXZlJyA6ICcnXG4gICAgICAgICAgICB9YH1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8c3Bhbj7lhajpg6jliIbnsbs8L3NwYW4+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJhZGdlIGJhZGdlLW5ldXRyYWxcIj5cbiAgICAgICAgICAgICAge2NhdGVnb3JpZXM/LnJlZHVjZSgodG90YWwsIGNhdCkgPT4gdG90YWwgKyBjYXQuX2NvdW50LnByb21wdHMsIDApIHx8IDB9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgIHsvKiDliIbnsbvliJfooaggKi99XG4gICAgICAgICAge2NhdGVnb3JpZXM/Lm1hcCgoY2F0ZWdvcnkpID0+IChcbiAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAga2V5PXtjYXRlZ29yeS5pZH1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicmVsYXRpdmVcIlxuICAgICAgICAgICAgICBvbk1vdXNlRW50ZXI9eygpID0+IHNldEhvdmVyZWRDYXRlZ29yeUlkKGNhdGVnb3J5LmlkKX1cbiAgICAgICAgICAgICAgb25Nb3VzZUxlYXZlPXsoKSA9PiBzZXRIb3ZlcmVkQ2F0ZWdvcnlJZChudWxsKX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUNhdGVnb3J5Q2xpY2soY2F0ZWdvcnkuaWQpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGJ0biBidG4tZ2hvc3Qgdy1mdWxsIGp1c3RpZnktYmV0d2VlbiAke1xuICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRDYXRlZ29yeUlkID09PSBjYXRlZ29yeS5pZCA/ICdidG4tYWN0aXZlJyA6ICcnXG4gICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IFxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTMgaC0zIHJvdW5kZWQtZnVsbFwiXG4gICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IGJhY2tncm91bmRDb2xvcjogY2F0ZWdvcnkuY29sb3IgfX1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8c3Bhbj57Y2F0ZWdvcnkubmFtZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiYWRnZSBiYWRnZS1uZXV0cmFsXCI+XG4gICAgICAgICAgICAgICAgICB7Y2F0ZWdvcnkuX2NvdW50LnByb21wdHN9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgey8qIOe8lui+keaMiemSriAqL31cbiAgICAgICAgICAgICAge2hvdmVyZWRDYXRlZ29yeUlkID09PSBjYXRlZ29yeS5pZCAmJiAoXG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKVxuICAgICAgICAgICAgICAgICAgICBvcGVuRWRpdENhdGVnb3J5TW9kYWwoY2F0ZWdvcnkpXG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYnRuIGJ0bi1naG9zdCBidG4teHMgYWJzb2x1dGUgcmlnaHQtMiB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctMyBoLTNcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTExIDVINmEyIDIgMCAwMC0yIDJ2MTFhMiAyIDAgMDAyIDJoMTFhMiAyIDAgMDAyLTJ2LTVtLTEuNDE0LTkuNDE0YTIgMiAwIDExMi44MjggMi44MjhMMTEuODI4IDE1SDl2LTIuODI4bDguNTg2LTguNTg2elwiIC8+XG4gICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICkpfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7Lyog5bqV6YOo5paw5bu65oyJ6ZKuICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgYm9yZGVyLXQgYm9yZGVyLWJhc2UtMzAwXCI+XG4gICAgICAgIDxidXR0b25cbiAgICAgICAgICBvbkNsaWNrPXtvcGVuQ3JlYXRlQ2F0ZWdvcnlNb2RhbH1cbiAgICAgICAgICBjbGFzc05hbWU9XCJidG4gYnRuLXByaW1hcnkgYnRuLWJsb2NrXCJcbiAgICAgICAgPlxuICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTEyIDR2MTZtOC04SDRcIiAvPlxuICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgIOaWsOW7uuWIhuexu1xuICAgICAgICA8L2J1dHRvbj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJjbiIsImFwaSIsInVzZVVJIiwiQ2F0ZWdvcnlTaWRlYmFyIiwic2VsZWN0ZWRDYXRlZ29yeUlkIiwib25DYXRlZ29yeVNlbGVjdCIsImNsYXNzTmFtZSIsImhvdmVyZWRDYXRlZ29yeUlkIiwic2V0SG92ZXJlZENhdGVnb3J5SWQiLCJvcGVuQ3JlYXRlQ2F0ZWdvcnlNb2RhbCIsIm9wZW5FZGl0Q2F0ZWdvcnlNb2RhbCIsImRhdGEiLCJjYXRlZ29yaWVzIiwiaXNMb2FkaW5nIiwiY2F0ZWdvcnkiLCJnZXRBbGwiLCJ1c2VRdWVyeSIsImhhbmRsZUNhdGVnb3J5Q2xpY2siLCJjYXRlZ29yeUlkIiwiZGl2IiwiaDMiLCJidXR0b24iLCJvbkNsaWNrIiwic3BhbiIsInJlZHVjZSIsInRvdGFsIiwiY2F0IiwiX2NvdW50IiwicHJvbXB0cyIsIm1hcCIsIm9uTW91c2VFbnRlciIsImlkIiwib25Nb3VzZUxlYXZlIiwic3R5bGUiLCJiYWNrZ3JvdW5kQ29sb3IiLCJjb2xvciIsIm5hbWUiLCJlIiwic3RvcFByb3BhZ2F0aW9uIiwic3ZnIiwiZmlsbCIsInN0cm9rZSIsInZpZXdCb3giLCJwYXRoIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwic3Ryb2tlV2lkdGgiLCJkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CategorySidebarNew.tsx\n"));

/***/ })

});