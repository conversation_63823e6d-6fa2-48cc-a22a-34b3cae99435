{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/node_modules/%40uiw/react-textarea-code-editor/esm/style/index.css"], "sourcesContent": ["@media (prefers-color-scheme: dark) {\n  .w-tc-editor {\n    --color-fg-default: #c9d1d9;\n    --color-canvas-subtle: #161b22;\n    --color-prettylights-syntax-comment: #8b949e;\n    --color-prettylights-syntax-entity-tag: #7ee787;\n    --color-prettylights-syntax-entity: #d2a8ff;\n    --color-prettylights-syntax-sublimelinter-gutter-mark: #484f58;\n    --color-prettylights-syntax-constant: #79c0ff;\n    --color-prettylights-syntax-string: #a5d6ff;\n    --color-prettylights-syntax-keyword: #ff7b72;\n    --color-prettylights-syntax-markup-bold: #c9d1d9;\n  }\n}\n@media (prefers-color-scheme: light) {\n  .w-tc-editor {\n    --color-fg-default: #24292f;\n    --color-canvas-subtle: #f6f8fa;\n    --color-prettylights-syntax-comment: #6e7781;\n    --color-prettylights-syntax-entity-tag: #116329;\n    --color-prettylights-syntax-entity: #8250df;\n    --color-prettylights-syntax-sublimelinter-gutter-mark: #8c959f;\n    --color-prettylights-syntax-constant: #0550ae;\n    --color-prettylights-syntax-string: #0a3069;\n    --color-prettylights-syntax-keyword: #cf222e;\n    --color-prettylights-syntax-markup-bold: #24292f;\n  }\n}\n.w-tc-editor[data-color-mode*='dark'],\n[data-color-mode*='dark'] .w-tc-editor,\n[data-color-mode*='dark'] .w-tc-editor-var,\nbody[data-color-mode*='dark'] {\n  --color-fg-default: #c9d1d9;\n  --color-canvas-subtle: #161b22;\n  --color-prettylights-syntax-comment: #8b949e;\n  --color-prettylights-syntax-entity-tag: #7ee787;\n  --color-prettylights-syntax-entity: #d2a8ff;\n  --color-prettylights-syntax-sublimelinter-gutter-mark: #484f58;\n  --color-prettylights-syntax-constant: #79c0ff;\n  --color-prettylights-syntax-string: #a5d6ff;\n  --color-prettylights-syntax-keyword: #ff7b72;\n  --color-prettylights-syntax-markup-bold: #c9d1d9;\n}\n.w-tc-editor[data-color-mode*='light'],\n[data-color-mode*='light'] .w-tc-editor,\n[data-color-mode*='light'] .w-tc-editor-var,\nbody[data-color-mode*='light'] {\n  --color-fg-default: #24292f;\n  --color-canvas-subtle: #f6f8fa;\n  --color-prettylights-syntax-comment: #6e7781;\n  --color-prettylights-syntax-entity-tag: #116329;\n  --color-prettylights-syntax-entity: #8250df;\n  --color-prettylights-syntax-sublimelinter-gutter-mark: #8c959f;\n  --color-prettylights-syntax-constant: #0550ae;\n  --color-prettylights-syntax-string: #0a3069;\n  --color-prettylights-syntax-keyword: #cf222e;\n  --color-prettylights-syntax-markup-bold: #24292f;\n}\n.w-tc-editor {\n  font-family: inherit;\n  font-size: 12px;\n  background-color: var(--color-canvas-subtle);\n  color: var(--color-fg-default);\n}\n.w-tc-editor-text,\n.w-tc-editor-preview {\n  min-height: 16px;\n}\n.w-tc-editor-preview pre {\n  margin: 0;\n  padding: 0;\n  white-space: inherit;\n  font-family: inherit;\n  font-size: inherit;\n}\n.w-tc-editor-preview pre code {\n  font-family: inherit;\n}\n.w-tc-editor code[class*='language-'] .token.cdata,\n.w-tc-editor pre[class*='language-'] .token.cdata,\n.w-tc-editor code[class*='language-'] .token.comment,\n.w-tc-editor pre[class*='language-'] .token.comment,\n.w-tc-editor code[class*='language-'] .token.doctype,\n.w-tc-editor pre[class*='language-'] .token.doctype,\n.w-tc-editor code[class*='language-'] .token.prolog,\n.w-tc-editor pre[class*='language-'] .token.prolog {\n  color: var(--color-prettylights-syntax-comment);\n}\n.w-tc-editor code[class*='language-'] .token.punctuation,\n.w-tc-editor pre[class*='language-'] .token.punctuation {\n  color: var(--color-prettylights-syntax-sublimelinter-gutter-mark);\n}\n.w-tc-editor code[class*='language-'] .namespace,\n.w-tc-editor pre[class*='language-'] .namespace {\n  opacity: 0.7;\n}\n.w-tc-editor code[class*='language-'] .token.boolean,\n.w-tc-editor pre[class*='language-'] .token.boolean,\n.w-tc-editor code[class*='language-'] .token.constant,\n.w-tc-editor pre[class*='language-'] .token.constant,\n.w-tc-editor code[class*='language-'] .token.deleted,\n.w-tc-editor pre[class*='language-'] .token.deleted,\n.w-tc-editor code[class*='language-'] .token.number,\n.w-tc-editor pre[class*='language-'] .token.number,\n.w-tc-editor code[class*='language-'] .token.symbol,\n.w-tc-editor pre[class*='language-'] .token.symbol {\n  color: var(--color-prettylights-syntax-entity-tag);\n}\n.w-tc-editor code[class*='language-'] .token.builtin,\n.w-tc-editor pre[class*='language-'] .token.builtin,\n.w-tc-editor code[class*='language-'] .token.char,\n.w-tc-editor pre[class*='language-'] .token.char,\n.w-tc-editor code[class*='language-'] .token.inserted,\n.w-tc-editor pre[class*='language-'] .token.inserted,\n.w-tc-editor code[class*='language-'] .token.selector,\n.w-tc-editor pre[class*='language-'] .token.selector,\n.w-tc-editor code[class*='language-'] .token.string,\n.w-tc-editor pre[class*='language-'] .token.string {\n  color: var(--color-prettylights-syntax-constant);\n}\n.w-tc-editor code[class*='language-'] .style .token.string,\n.w-tc-editor pre[class*='language-'] .style .token.string,\n.w-tc-editor code[class*='language-'] .token.entity,\n.w-tc-editor pre[class*='language-'] .token.entity,\n.w-tc-editor code[class*='language-'] .token.property,\n.w-tc-editor pre[class*='language-'] .token.property,\n.w-tc-editor code[class*='language-'] .token.operator,\n.w-tc-editor pre[class*='language-'] .token.operator,\n.w-tc-editor code[class*='language-'] .token.url,\n.w-tc-editor pre[class*='language-'] .token.url {\n  color: var(--color-prettylights-syntax-constant);\n}\n.w-tc-editor code[class*='language-'] .token.atrule,\n.w-tc-editor pre[class*='language-'] .token.atrule,\n.w-tc-editor code[class*='language-'] .token.property-access .token.method,\n.w-tc-editor pre[class*='language-'] .token.property-access .token.method,\n.w-tc-editor code[class*='language-'] .token.keyword,\n.w-tc-editor pre[class*='language-'] .token.keyword {\n  color: var(--color-prettylights-syntax-keyword);\n}\n.w-tc-editor code[class*='language-'] .token.function,\n.w-tc-editor pre[class*='language-'] .token.function {\n  color: var(--color-prettylights-syntax-string);\n}\n.w-tc-editor code[class*='language-'] .token.important,\n.w-tc-editor pre[class*='language-'] .token.important,\n.w-tc-editor code[class*='language-'] .token.regex,\n.w-tc-editor pre[class*='language-'] .token.regex,\n.w-tc-editor code[class*='language-'] .token.variable,\n.w-tc-editor pre[class*='language-'] .token.variable {\n  color: var(--color-prettylights-syntax-string-regexp);\n}\n.w-tc-editor code[class*='language-'] .token.bold,\n.w-tc-editor pre[class*='language-'] .token.bold,\n.w-tc-editor code[class*='language-'] .token.important,\n.w-tc-editor pre[class*='language-'] .token.important {\n  color: var(--color-prettylights-syntax-markup-bold);\n}\n.w-tc-editor code[class*='language-'] .token.tag,\n.w-tc-editor pre[class*='language-'] .token.tag {\n  color: var(--color-prettylights-syntax-entity-tag);\n}\n.w-tc-editor code[class*='language-'] .token.attr-value,\n.w-tc-editor pre[class*='language-'] .token.attr-value,\n.w-tc-editor code[class*='language-'] .token.attr-name,\n.w-tc-editor pre[class*='language-'] .token.attr-name {\n  color: var(--color-prettylights-syntax-constant);\n}\n.w-tc-editor code[class*='language-'] .token.selector .class,\n.w-tc-editor pre[class*='language-'] .token.selector .class,\n.w-tc-editor code[class*='language-'] .token.class-name,\n.w-tc-editor pre[class*='language-'] .token.class-name {\n  color: var(--color-prettylights-syntax-entity);\n}\n"], "names": [], "mappings": "AAAA;EACE;;;;;;;;;;;;;;AAaF;EACE;;;;;;;;;;;;;;AAaF;;;;;;;;;;;;;AAeA;;;;;;;;;;;;;AAeA;;;;;;;AAMA;;;;AAIA;;;;;;;;AAOA;;;;AAGA;;;;AAUA;;;;AAIA;;;;AAIA;;;;AAYA;;;;AAwBA;;;;AAQA;;;;AAIA;;;;AAQA;;;;AAMA;;;;AAIA;;;;AAMA", "ignoreList": [0], "debugId": null}}]}