"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/parse-entities";
exports.ids = ["vendor-chunks/parse-entities"];
exports.modules = {

/***/ "(ssr)/./node_modules/parse-entities/lib/index.js":
/*!**************************************************!*\
  !*** ./node_modules/parse-entities/lib/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseEntities: () => (/* binding */ parseEntities)\n/* harmony export */ });\n/* harmony import */ var character_entities_legacy__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! character-entities-legacy */ \"(ssr)/./node_modules/character-entities-legacy/index.js\");\n/* harmony import */ var character_reference_invalid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! character-reference-invalid */ \"(ssr)/./node_modules/character-reference-invalid/index.js\");\n/* harmony import */ var is_decimal__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! is-decimal */ \"(ssr)/./node_modules/is-decimal/index.js\");\n/* harmony import */ var is_hexadecimal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! is-hexadecimal */ \"(ssr)/./node_modules/is-hexadecimal/index.js\");\n/* harmony import */ var is_alphanumerical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! is-alphanumerical */ \"(ssr)/./node_modules/is-alphanumerical/index.js\");\n/* harmony import */ var decode_named_character_reference__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! decode-named-character-reference */ \"(ssr)/./node_modules/decode-named-character-reference/index.js\");\n/**\n * @import {Point} from 'unist'\n * @import {Options} from '../index.js'\n */\n\n\n\n\n\n\n\n\n// Warning messages.\nconst messages = [\n  '',\n  /* 1: Non terminated (named) */\n  'Named character references must be terminated by a semicolon',\n  /* 2: Non terminated (numeric) */\n  'Numeric character references must be terminated by a semicolon',\n  /* 3: Empty (named) */\n  'Named character references cannot be empty',\n  /* 4: Empty (numeric) */\n  'Numeric character references cannot be empty',\n  /* 5: Unknown (named) */\n  'Named character references must be known',\n  /* 6: Disallowed (numeric) */\n  'Numeric character references cannot be disallowed',\n  /* 7: Prohibited (numeric) */\n  'Numeric character references cannot be outside the permissible Unicode range'\n]\n\n/**\n * Parse HTML character references.\n *\n * @param {string} value\n * @param {Readonly<Options> | null | undefined} [options]\n */\nfunction parseEntities(value, options) {\n  const settings = options || {}\n  const additional =\n    typeof settings.additional === 'string'\n      ? settings.additional.charCodeAt(0)\n      : settings.additional\n  /** @type {Array<string>} */\n  const result = []\n  let index = 0\n  let lines = -1\n  let queue = ''\n  /** @type {Point | undefined} */\n  let point\n  /** @type {Array<number>|undefined} */\n  let indent\n\n  if (settings.position) {\n    if ('start' in settings.position || 'indent' in settings.position) {\n      // @ts-expect-error: points don’t have indent.\n      indent = settings.position.indent\n      // @ts-expect-error: points don’t have indent.\n      point = settings.position.start\n    } else {\n      point = settings.position\n    }\n  }\n\n  let line = (point ? point.line : 0) || 1\n  let column = (point ? point.column : 0) || 1\n\n  // Cache the current point.\n  let previous = now()\n  /** @type {number|undefined} */\n  let character\n\n  // Ensure the algorithm walks over the first character (inclusive).\n  index--\n\n  while (++index <= value.length) {\n    // If the previous character was a newline.\n    if (character === 10 /* `\\n` */) {\n      column = (indent ? indent[lines] : 0) || 1\n    }\n\n    character = value.charCodeAt(index)\n\n    if (character === 38 /* `&` */) {\n      const following = value.charCodeAt(index + 1)\n\n      // The behavior depends on the identity of the next character.\n      if (\n        following === 9 /* `\\t` */ ||\n        following === 10 /* `\\n` */ ||\n        following === 12 /* `\\f` */ ||\n        following === 32 /* ` ` */ ||\n        following === 38 /* `&` */ ||\n        following === 60 /* `<` */ ||\n        Number.isNaN(following) ||\n        (additional && following === additional)\n      ) {\n        // Not a character reference.\n        // No characters are consumed, and nothing is returned.\n        // This is not an error, either.\n        queue += String.fromCharCode(character)\n        column++\n        continue\n      }\n\n      const start = index + 1\n      let begin = start\n      let end = start\n      /** @type {string} */\n      let type\n\n      if (following === 35 /* `#` */) {\n        // Numerical reference.\n        end = ++begin\n\n        // The behavior further depends on the next character.\n        const following = value.charCodeAt(end)\n\n        if (following === 88 /* `X` */ || following === 120 /* `x` */) {\n          // ASCII hexadecimal digits.\n          type = 'hexadecimal'\n          end = ++begin\n        } else {\n          // ASCII decimal digits.\n          type = 'decimal'\n        }\n      } else {\n        // Named reference.\n        type = 'named'\n      }\n\n      let characterReferenceCharacters = ''\n      let characterReference = ''\n      let characters = ''\n      // Each type of character reference accepts different characters.\n      // This test is used to detect whether a reference has ended (as the semicolon\n      // is not strictly needed).\n      const test =\n        type === 'named'\n          ? is_alphanumerical__WEBPACK_IMPORTED_MODULE_0__.isAlphanumerical\n          : type === 'decimal'\n            ? is_decimal__WEBPACK_IMPORTED_MODULE_1__.isDecimal\n            : is_hexadecimal__WEBPACK_IMPORTED_MODULE_2__.isHexadecimal\n\n      end--\n\n      while (++end <= value.length) {\n        const following = value.charCodeAt(end)\n\n        if (!test(following)) {\n          break\n        }\n\n        characters += String.fromCharCode(following)\n\n        // Check if we can match a legacy named reference.\n        // If so, we cache that as the last viable named reference.\n        // This ensures we do not need to walk backwards later.\n        if (type === 'named' && character_entities_legacy__WEBPACK_IMPORTED_MODULE_3__.characterEntitiesLegacy.includes(characters)) {\n          characterReferenceCharacters = characters\n          // @ts-expect-error: always able to decode.\n          characterReference = (0,decode_named_character_reference__WEBPACK_IMPORTED_MODULE_4__.decodeNamedCharacterReference)(characters)\n        }\n      }\n\n      let terminated = value.charCodeAt(end) === 59 /* `;` */\n\n      if (terminated) {\n        end++\n\n        const namedReference =\n          type === 'named' ? (0,decode_named_character_reference__WEBPACK_IMPORTED_MODULE_4__.decodeNamedCharacterReference)(characters) : false\n\n        if (namedReference) {\n          characterReferenceCharacters = characters\n          characterReference = namedReference\n        }\n      }\n\n      let diff = 1 + end - start\n      let reference = ''\n\n      if (!terminated && settings.nonTerminated === false) {\n        // Empty.\n      } else if (!characters) {\n        // An empty (possible) reference is valid, unless it’s numeric (thus an\n        // ampersand followed by an octothorp).\n        if (type !== 'named') {\n          warning(4 /* Empty (numeric) */, diff)\n        }\n      } else if (type === 'named') {\n        // An ampersand followed by anything unknown, and not terminated, is\n        // invalid.\n        if (terminated && !characterReference) {\n          warning(5 /* Unknown (named) */, 1)\n        } else {\n          // If there’s something after an named reference which is not known,\n          // cap the reference.\n          if (characterReferenceCharacters !== characters) {\n            end = begin + characterReferenceCharacters.length\n            diff = 1 + end - begin\n            terminated = false\n          }\n\n          // If the reference is not terminated, warn.\n          if (!terminated) {\n            const reason = characterReferenceCharacters\n              ? 1 /* Non terminated (named) */\n              : 3 /* Empty (named) */\n\n            if (settings.attribute) {\n              const following = value.charCodeAt(end)\n\n              if (following === 61 /* `=` */) {\n                warning(reason, diff)\n                characterReference = ''\n              } else if ((0,is_alphanumerical__WEBPACK_IMPORTED_MODULE_0__.isAlphanumerical)(following)) {\n                characterReference = ''\n              } else {\n                warning(reason, diff)\n              }\n            } else {\n              warning(reason, diff)\n            }\n          }\n        }\n\n        reference = characterReference\n      } else {\n        if (!terminated) {\n          // All nonterminated numeric references are not rendered, and emit a\n          // warning.\n          warning(2 /* Non terminated (numeric) */, diff)\n        }\n\n        // When terminated and numerical, parse as either hexadecimal or\n        // decimal.\n        let referenceCode = Number.parseInt(\n          characters,\n          type === 'hexadecimal' ? 16 : 10\n        )\n\n        // Emit a warning when the parsed number is prohibited, and replace with\n        // replacement character.\n        if (prohibited(referenceCode)) {\n          warning(7 /* Prohibited (numeric) */, diff)\n          reference = String.fromCharCode(65533 /* `�` */)\n        } else if (referenceCode in character_reference_invalid__WEBPACK_IMPORTED_MODULE_5__.characterReferenceInvalid) {\n          // Emit a warning when the parsed number is disallowed, and replace by\n          // an alternative.\n          warning(6 /* Disallowed (numeric) */, diff)\n          reference = character_reference_invalid__WEBPACK_IMPORTED_MODULE_5__.characterReferenceInvalid[referenceCode]\n        } else {\n          // Parse the number.\n          let output = ''\n\n          // Emit a warning when the parsed number should not be used.\n          if (disallowed(referenceCode)) {\n            warning(6 /* Disallowed (numeric) */, diff)\n          }\n\n          // Serialize the number.\n          if (referenceCode > 0xffff) {\n            referenceCode -= 0x10000\n            output += String.fromCharCode(\n              (referenceCode >>> (10 & 0x3ff)) | 0xd800\n            )\n            referenceCode = 0xdc00 | (referenceCode & 0x3ff)\n          }\n\n          reference = output + String.fromCharCode(referenceCode)\n        }\n      }\n\n      // Found it!\n      // First eat the queued characters as normal text, then eat a reference.\n      if (reference) {\n        flush()\n\n        previous = now()\n        index = end - 1\n        column += end - start + 1\n        result.push(reference)\n        const next = now()\n        next.offset++\n\n        if (settings.reference) {\n          settings.reference.call(\n            settings.referenceContext || undefined,\n            reference,\n            {start: previous, end: next},\n            value.slice(start - 1, end)\n          )\n        }\n\n        previous = next\n      } else {\n        // If we could not find a reference, queue the checked characters (as\n        // normal characters), and move the pointer to their end.\n        // This is possible because we can be certain neither newlines nor\n        // ampersands are included.\n        characters = value.slice(start - 1, end)\n        queue += characters\n        column += characters.length\n        index = end - 1\n      }\n    } else {\n      // Handle anything other than an ampersand, including newlines and EOF.\n      if (character === 10 /* `\\n` */) {\n        line++\n        lines++\n        column = 0\n      }\n\n      if (Number.isNaN(character)) {\n        flush()\n      } else {\n        queue += String.fromCharCode(character)\n        column++\n      }\n    }\n  }\n\n  // Return the reduced nodes.\n  return result.join('')\n\n  // Get current position.\n  function now() {\n    return {\n      line,\n      column,\n      offset: index + ((point ? point.offset : 0) || 0)\n    }\n  }\n\n  /**\n   * Handle the warning.\n   *\n   * @param {1|2|3|4|5|6|7} code\n   * @param {number} offset\n   */\n  function warning(code, offset) {\n    /** @type {ReturnType<now>} */\n    let position\n\n    if (settings.warning) {\n      position = now()\n      position.column += offset\n      position.offset += offset\n\n      settings.warning.call(\n        settings.warningContext || undefined,\n        messages[code],\n        position,\n        code\n      )\n    }\n  }\n\n  /**\n   * Flush `queue` (normal text).\n   * Macro invoked before each reference and at the end of `value`.\n   * Does nothing when `queue` is empty.\n   */\n  function flush() {\n    if (queue) {\n      result.push(queue)\n\n      if (settings.text) {\n        settings.text.call(settings.textContext || undefined, queue, {\n          start: previous,\n          end: now()\n        })\n      }\n\n      queue = ''\n    }\n  }\n}\n\n/**\n * Check if `character` is outside the permissible unicode range.\n *\n * @param {number} code\n * @returns {boolean}\n */\nfunction prohibited(code) {\n  return (code >= 0xd800 && code <= 0xdfff) || code > 0x10ffff\n}\n\n/**\n * Check if `character` is disallowed.\n *\n * @param {number} code\n * @returns {boolean}\n */\nfunction disallowed(code) {\n  return (\n    (code >= 0x0001 && code <= 0x0008) ||\n    code === 0x000b ||\n    (code >= 0x000d && code <= 0x001f) ||\n    (code >= 0x007f && code <= 0x009f) ||\n    (code >= 0xfdd0 && code <= 0xfdef) ||\n    (code & 0xffff) === 0xffff ||\n    (code & 0xffff) === 0xfffe\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/parse-entities/lib/index.js\n");

/***/ })

};
;