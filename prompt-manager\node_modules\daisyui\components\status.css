/*! 🌼 daisyUI 5.0.45 - MIT License */ @layer utilities{.status{aspect-ratio:1;border-radius:var(--radius-selector);background-color:color-mix(in oklab,var(--color-base-content)20%,transparent);vertical-align:middle;color:#0000004d;background-position:50%;background-repeat:no-repeat;background-image:radial-gradient(circle at 35% 30%,oklch(1 0 0/calc(var(--depth)*.5)),#0000);width:.5rem;height:.5rem;box-shadow:0 2px 3px -1px color-mix(in oklab,currentColor calc(var(--depth)*100%),#0000);display:inline-block;@supports (color:color-mix(in lab, red, red)){&{color:color-mix(in oklab,var(--color-black)30%,transparent)}}}.status-primary{background-color:var(--color-primary);color:var(--color-primary)}.status-secondary{background-color:var(--color-secondary);color:var(--color-secondary)}.status-accent{background-color:var(--color-accent);color:var(--color-accent)}.status-neutral{background-color:var(--color-neutral);color:var(--color-neutral)}.status-info{background-color:var(--color-info);color:var(--color-info)}.status-success{background-color:var(--color-success);color:var(--color-success)}.status-warning{background-color:var(--color-warning);color:var(--color-warning)}.status-error{background-color:var(--color-error);color:var(--color-error)}.status-xs{width:.125rem;height:.125rem}.status-sm{width:.25rem;height:.25rem}.status-md{width:.5rem;height:.5rem}.status-lg{width:.75rem;height:.75rem}.status-xl{width:1rem;height:1rem}@media (width>=640px){.sm\:status{aspect-ratio:1;border-radius:var(--radius-selector);background-color:color-mix(in oklab,var(--color-base-content)20%,transparent);vertical-align:middle;color:#0000004d;background-position:50%;background-repeat:no-repeat;background-image:radial-gradient(circle at 35% 30%,oklch(1 0 0/calc(var(--depth)*.5)),#0000);width:.5rem;height:.5rem;box-shadow:0 2px 3px -1px color-mix(in oklab,currentColor calc(var(--depth)*100%),#0000);display:inline-block;@supports (color:color-mix(in lab, red, red)){&{color:color-mix(in oklab,var(--color-black)30%,transparent)}}}.sm\:status-primary{background-color:var(--color-primary);color:var(--color-primary)}.sm\:status-secondary{background-color:var(--color-secondary);color:var(--color-secondary)}.sm\:status-accent{background-color:var(--color-accent);color:var(--color-accent)}.sm\:status-neutral{background-color:var(--color-neutral);color:var(--color-neutral)}.sm\:status-info{background-color:var(--color-info);color:var(--color-info)}.sm\:status-success{background-color:var(--color-success);color:var(--color-success)}.sm\:status-warning{background-color:var(--color-warning);color:var(--color-warning)}.sm\:status-error{background-color:var(--color-error);color:var(--color-error)}.sm\:status-xs{width:.125rem;height:.125rem}.sm\:status-sm{width:.25rem;height:.25rem}.sm\:status-md{width:.5rem;height:.5rem}.sm\:status-lg{width:.75rem;height:.75rem}.sm\:status-xl{width:1rem;height:1rem}}@media (width>=768px){.md\:status{aspect-ratio:1;border-radius:var(--radius-selector);background-color:color-mix(in oklab,var(--color-base-content)20%,transparent);vertical-align:middle;color:#0000004d;background-position:50%;background-repeat:no-repeat;background-image:radial-gradient(circle at 35% 30%,oklch(1 0 0/calc(var(--depth)*.5)),#0000);width:.5rem;height:.5rem;box-shadow:0 2px 3px -1px color-mix(in oklab,currentColor calc(var(--depth)*100%),#0000);display:inline-block;@supports (color:color-mix(in lab, red, red)){&{color:color-mix(in oklab,var(--color-black)30%,transparent)}}}.md\:status-primary{background-color:var(--color-primary);color:var(--color-primary)}.md\:status-secondary{background-color:var(--color-secondary);color:var(--color-secondary)}.md\:status-accent{background-color:var(--color-accent);color:var(--color-accent)}.md\:status-neutral{background-color:var(--color-neutral);color:var(--color-neutral)}.md\:status-info{background-color:var(--color-info);color:var(--color-info)}.md\:status-success{background-color:var(--color-success);color:var(--color-success)}.md\:status-warning{background-color:var(--color-warning);color:var(--color-warning)}.md\:status-error{background-color:var(--color-error);color:var(--color-error)}.md\:status-xs{width:.125rem;height:.125rem}.md\:status-sm{width:.25rem;height:.25rem}.md\:status-md{width:.5rem;height:.5rem}.md\:status-lg{width:.75rem;height:.75rem}.md\:status-xl{width:1rem;height:1rem}}@media (width>=1024px){.lg\:status{aspect-ratio:1;border-radius:var(--radius-selector);background-color:color-mix(in oklab,var(--color-base-content)20%,transparent);vertical-align:middle;color:#0000004d;background-position:50%;background-repeat:no-repeat;background-image:radial-gradient(circle at 35% 30%,oklch(1 0 0/calc(var(--depth)*.5)),#0000);width:.5rem;height:.5rem;box-shadow:0 2px 3px -1px color-mix(in oklab,currentColor calc(var(--depth)*100%),#0000);display:inline-block;@supports (color:color-mix(in lab, red, red)){&{color:color-mix(in oklab,var(--color-black)30%,transparent)}}}.lg\:status-primary{background-color:var(--color-primary);color:var(--color-primary)}.lg\:status-secondary{background-color:var(--color-secondary);color:var(--color-secondary)}.lg\:status-accent{background-color:var(--color-accent);color:var(--color-accent)}.lg\:status-neutral{background-color:var(--color-neutral);color:var(--color-neutral)}.lg\:status-info{background-color:var(--color-info);color:var(--color-info)}.lg\:status-success{background-color:var(--color-success);color:var(--color-success)}.lg\:status-warning{background-color:var(--color-warning);color:var(--color-warning)}.lg\:status-error{background-color:var(--color-error);color:var(--color-error)}.lg\:status-xs{width:.125rem;height:.125rem}.lg\:status-sm{width:.25rem;height:.25rem}.lg\:status-md{width:.5rem;height:.5rem}.lg\:status-lg{width:.75rem;height:.75rem}.lg\:status-xl{width:1rem;height:1rem}}@media (width>=1280px){.xl\:status{aspect-ratio:1;border-radius:var(--radius-selector);background-color:color-mix(in oklab,var(--color-base-content)20%,transparent);vertical-align:middle;color:#0000004d;background-position:50%;background-repeat:no-repeat;background-image:radial-gradient(circle at 35% 30%,oklch(1 0 0/calc(var(--depth)*.5)),#0000);width:.5rem;height:.5rem;box-shadow:0 2px 3px -1px color-mix(in oklab,currentColor calc(var(--depth)*100%),#0000);display:inline-block;@supports (color:color-mix(in lab, red, red)){&{color:color-mix(in oklab,var(--color-black)30%,transparent)}}}.xl\:status-primary{background-color:var(--color-primary);color:var(--color-primary)}.xl\:status-secondary{background-color:var(--color-secondary);color:var(--color-secondary)}.xl\:status-accent{background-color:var(--color-accent);color:var(--color-accent)}.xl\:status-neutral{background-color:var(--color-neutral);color:var(--color-neutral)}.xl\:status-info{background-color:var(--color-info);color:var(--color-info)}.xl\:status-success{background-color:var(--color-success);color:var(--color-success)}.xl\:status-warning{background-color:var(--color-warning);color:var(--color-warning)}.xl\:status-error{background-color:var(--color-error);color:var(--color-error)}.xl\:status-xs{width:.125rem;height:.125rem}.xl\:status-sm{width:.25rem;height:.25rem}.xl\:status-md{width:.5rem;height:.5rem}.xl\:status-lg{width:.75rem;height:.75rem}.xl\:status-xl{width:1rem;height:1rem}}@media (width>=1536px){.\32 xl\:status{aspect-ratio:1;border-radius:var(--radius-selector);background-color:color-mix(in oklab,var(--color-base-content)20%,transparent);vertical-align:middle;color:#0000004d;background-position:50%;background-repeat:no-repeat;background-image:radial-gradient(circle at 35% 30%,oklch(1 0 0/calc(var(--depth)*.5)),#0000);width:.5rem;height:.5rem;box-shadow:0 2px 3px -1px color-mix(in oklab,currentColor calc(var(--depth)*100%),#0000);display:inline-block;@supports (color:color-mix(in lab, red, red)){&{color:color-mix(in oklab,var(--color-black)30%,transparent)}}}.\32 xl\:status-primary{background-color:var(--color-primary);color:var(--color-primary)}.\32 xl\:status-secondary{background-color:var(--color-secondary);color:var(--color-secondary)}.\32 xl\:status-accent{background-color:var(--color-accent);color:var(--color-accent)}.\32 xl\:status-neutral{background-color:var(--color-neutral);color:var(--color-neutral)}.\32 xl\:status-info{background-color:var(--color-info);color:var(--color-info)}.\32 xl\:status-success{background-color:var(--color-success);color:var(--color-success)}.\32 xl\:status-warning{background-color:var(--color-warning);color:var(--color-warning)}.\32 xl\:status-error{background-color:var(--color-error);color:var(--color-error)}.\32 xl\:status-xs{width:.125rem;height:.125rem}.\32 xl\:status-sm{width:.25rem;height:.25rem}.\32 xl\:status-md{width:.5rem;height:.5rem}.\32 xl\:status-lg{width:.75rem;height:.75rem}.\32 xl\:status-xl{width:1rem;height:1rem}}}