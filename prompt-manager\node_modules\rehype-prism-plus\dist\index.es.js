import{visit as e}from"unist-util-visit";import{toString as r}from"hast-util-to-string";import{filter as t}from"unist-util-filter";import n from"parse-numeric-range";import{refractor as i}from"refractor/lib/common.js";import{refractor as o}from"refractor/lib/all.js";function a(){a=function(e,r){return new t(e,void 0,r)};var e=RegExp.prototype,r=new WeakMap;function t(e,n,i){var o=new RegExp(e,n);return r.set(o,i||r.get(e)),l(o,t.prototype)}function n(e,t){var n=r.get(t);return Object.keys(n).reduce(function(r,t){var i=n[t];if("number"==typeof i)r[t]=e[i];else{for(var o=0;void 0===e[i[o]]&&o+1<i.length;)o++;r[t]=e[i[o]]}return r},Object.create(null))}return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),r&&l(e,r)}(t,RegExp),t.prototype.exec=function(r){var t=e.exec.call(this,r);if(t){t.groups=n(t,this);var i=t.indices;i&&(i.groups=n(i,this))}return t},t.prototype[Symbol.replace]=function(t,i){if("string"==typeof i){var o=r.get(this);return e[Symbol.replace].call(this,t,i.replace(/\$<([^>]+)>/g,function(e,r){var t=o[r];return"$"+(Array.isArray(t)?t.join("$"):t)}))}if("function"==typeof i){var a=this;return e[Symbol.replace].call(this,t,function(){var e=arguments;return"object"!=typeof e[e.length-1]&&(e=[].slice.call(e)).push(n(e,a)),i.apply(this,e)})}return e[Symbol.replace].call(this,t,i)},a.apply(this,arguments)}function l(e,r){return l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,r){return e.__proto__=r,e},l(e,r)}function s(e,r){(null==r||r>e.length)&&(r=e.length);for(var t=0,n=new Array(r);t<r;t++)n[t]=e[t];return n}function u(e,r){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(t)return(t=t.call(e)).next.bind(t);if(Array.isArray(e)||(t=function(e,r){if(e){if("string"==typeof e)return s(e,r);var t=Object.prototype.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?s(e,r):void 0}}(e))||r&&e&&"number"==typeof e.length){t&&(e=t);var n=0;return function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var c=function(i){return function(o){return void 0===o&&(o={}),function(e,r){if(r&&!e.registered(r))throw new Error('The default language "'+r+'" is not registered with refractor.')}(i,o.defaultLanguage),function(r){e(r,"element",l)};function l(e,l,s){var c,p;if(s&&"pre"===s.tagName&&"code"===e.tagName){var f=(null==e||null==(c=e.data)?void 0:c.meta)||(null==e||null==(p=e.properties)?void 0:p.metastring)||"";e.properties.className?"boolean"==typeof e.properties.className?e.properties.className=[]:Array.isArray(e.properties.className)||(e.properties.className=[e.properties.className]):e.properties.className=[];var m,h,d=function(e){for(var r,t=u(e.properties.className);!(r=t()).done;){var n=r.value;if("language-"===n.slice(0,9))return n.slice(9).toLowerCase()}return null}(e);if(!d&&o.defaultLanguage&&e.properties.className.push("language-"+(d=o.defaultLanguage)),e.properties.className.push("code-highlight"),d)try{var g,v;v=null!=(g=d)&&g.includes("diff-")?d.split("-")[1]:d,m=i.highlight(r(e),v),s.properties.className=(s.properties.className||[]).concat("language-"+v)}catch(r){if(!o.ignoreMissing||!/Unknown language/.test(r.message))throw r;m=e}else m=e;m.children=(h=1,function e(r){return r.reduce(function(r,t){if("text"===t.type){var n=t.value,i=(n.match(/\n/g)||"").length;if(0===i)t.position={start:{line:h,column:1},end:{line:h,column:1}},r.push(t);else for(var o,a=n.split("\n"),l=u(a.entries());!(o=l()).done;){var s=o.value,c=s[0],p=s[1];r.push({type:"text",value:c===a.length-1?p:p+"\n",position:{start:{line:h+c,column:1},end:{line:h+c,column:1}}})}return h+=i,r}if(Object.prototype.hasOwnProperty.call(t,"children")){var f=h;return t.children=e(t.children),r.push(t),t.position={start:{line:f,column:1},end:{line:h,column:1}},r}return r.push(t),r},[])})(m.children),m.position=m.children.length>0?{start:{line:m.children[0].position.start.line,column:0},end:{line:m.children[m.children.length-1].position.end.line,column:0}}:{start:{line:0,column:0},end:{line:0,column:0}};for(var y,b=function(e){var r=/{([\d,-]+)}/,t=e.split(",").map(function(e){return e.trim()}).join();if(r.test(t)){var i=r.exec(t)[1],o=n(i);return function(e){return o.includes(e+1)}}return function(){return!1}}(f),w=function(e){var r=/*#__PURE__*/a(/showLineNumbers=(\d+)/i,{lines:1});if(r.test(e)){var t=r.exec(e);return Number(t.groups.lines)}return 1}(f),N=function(e){for(var r=new Array(e),t=0;t<e;t++)r[t]={type:"element",tagName:"span",properties:{className:[]},children:[]};return r}(m.position.end.line),j=["showlinenumbers=false",'showlinenumbers="false"',"showlinenumbers={false}"],x=function(){var e,n,i=y.value,a=i[0],l=i[1];l.properties.className=["code-line"];var s=t(m,function(e){return e.position.start.line<=a+1&&e.position.end.line>=a+1});l.children=s.children,!f.toLowerCase().includes("showLineNumbers".toLowerCase())&&!o.showLineNumbers||j.some(function(e){return f.toLowerCase().includes(e)})||(l.properties.line=[(a+w).toString()],l.properties.className.push("line-number")),b(a)&&l.properties.className.push("highlight-line"),("diff"===d||null!=(e=d)&&e.includes("diff-"))&&"-"===r(l).substring(0,1)?l.properties.className.push("deleted"):("diff"===d||null!=(n=d)&&n.includes("diff-"))&&"+"===r(l).substring(0,1)&&l.properties.className.push("inserted")},O=u(N.entries());!(y=O()).done;)x();N.length>0&&""===r(N[N.length-1]).trim()&&N.pop(),e.children=N}}}},p=c(i),f=c(o);export{f as default,p as rehypePrismCommon,c as rehypePrismGenerator};
//# sourceMappingURL=index.es.js.map
