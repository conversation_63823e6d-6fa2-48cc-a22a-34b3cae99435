{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/env.js"], "sourcesContent": ["import { createEnv } from \"@t3-oss/env-nextjs\";\nimport { z } from \"zod\";\n\nexport const env = createEnv({\n  /**\n   * Specify your server-side environment variables schema here. This way you can ensure the app\n   * isn't built with invalid env vars.\n   */\n  server: {\n    AUTH_SECRET:\n      process.env.NODE_ENV === \"production\"\n        ? z.string()\n        : z.string().optional(),\n    AUTH_DISCORD_ID: z.string().optional(),\n    AUTH_DISCORD_SECRET: z.string().optional(),\n    DATABASE_URL: z.string().url(),\n    NODE_ENV: z\n      .enum([\"development\", \"test\", \"production\"])\n      .default(\"development\"),\n  },\n\n  /**\n   * Specify your client-side environment variables schema here. This way you can ensure the app\n   * isn't built with invalid env vars. To expose them to the client, prefix them with\n   * `NEXT_PUBLIC_`.\n   */\n  client: {\n    // NEXT_PUBLIC_CLIENTVAR: z.string(),\n  },\n\n  /**\n   * You can't destruct `process.env` as a regular object in the Next.js edge runtimes (e.g.\n   * middlewares) or client-side so we need to destruct manually.\n   */\n  runtimeEnv: {\n    AUTH_SECRET: process.env.AUTH_SECRET,\n    AUTH_DISCORD_ID: process.env.AUTH_DISCORD_ID,\n    AUTH_DISCORD_SECRET: process.env.AUTH_DISCORD_SECRET,\n    DATABASE_URL: process.env.DATABASE_URL,\n    NODE_ENV: process.env.NODE_ENV,\n  },\n  /**\n   * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially\n   * useful for Docker builds.\n   */\n  skipValidation: !!process.env.SKIP_ENV_VALIDATION,\n  /**\n   * Makes it so that empty strings are treated as undefined. `SOME_VAR: z.string()` and\n   * `SOME_VAR=''` will throw an error.\n   */\n  emptyStringAsUndefined: true,\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,MAAM,CAAA,GAAA,+JAAA,CAAA,YAAS,AAAD,EAAE;IAC3B;;;GAGC,GACD,QAAQ;QACN,aACE,sCACI,0BACA,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACzB,iBAAiB,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACpC,qBAAqB,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACxC,cAAc,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;QAC5B,UAAU,oKAAA,CAAA,IAAC,CACR,IAAI,CAAC;YAAC;YAAe;YAAQ;SAAa,EAC1C,OAAO,CAAC;IACb;IAEA;;;;GAIC,GACD,QAAQ;IAER;IAEA;;;GAGC,GACD,YAAY;QACV,aAAa,QAAQ,GAAG,CAAC,WAAW;QACpC,iBAAiB,QAAQ,GAAG,CAAC,eAAe;QAC5C,qBAAqB,QAAQ,GAAG,CAAC,mBAAmB;QACpD,cAAc,QAAQ,GAAG,CAAC,YAAY;QACtC,QAAQ;IACV;IACA;;;GAGC,GACD,gBAAgB,CAAC,CAAC,QAAQ,GAAG,CAAC,mBAAmB;IACjD;;;GAGC,GACD,wBAAwB;AAC1B", "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/server/db.ts"], "sourcesContent": ["import { PrismaClient } from \"@prisma/client\";\n\nimport { env } from \"~/env\";\n\nconst createPrismaClient = () =>\n  new PrismaClient({\n    log:\n      env.NODE_ENV === \"development\" ? [\"query\", \"error\", \"warn\"] : [\"error\"],\n  });\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: ReturnType<typeof createPrismaClient> | undefined;\n};\n\nexport const db = globalForPrisma.prisma ?? createPrismaClient();\n\nif (env.NODE_ENV !== \"production\") globalForPrisma.prisma = db;\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;AAEA,MAAM,qBAAqB,IACzB,IAAI,6HAAA,CAAA,eAAY,CAAC;QACf,KACE,4GAAA,CAAA,MAAG,CAAC,QAAQ,KAAK,gBAAgB;YAAC;YAAS;YAAS;SAAO,GAAG;YAAC;SAAQ;IAC3E;AAEF,MAAM,kBAAkB;AAIjB,MAAM,KAAK,gBAAgB,MAAM,IAAI;AAE5C,IAAI,4GAAA,CAAA,MAAG,CAAC,QAAQ,KAAK,cAAc,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/server/auth/config.ts"], "sourcesContent": ["import { PrismaAdapter } from \"@auth/prisma-adapter\";\nimport { type DefaultSession, type NextAuthConfig } from \"next-auth\";\nimport DiscordProvider from \"next-auth/providers/discord\";\n\nimport { db } from \"~/server/db\";\n\n/**\n * Module augmentation for `next-auth` types. Allows us to add custom properties to the `session`\n * object and keep type safety.\n *\n * @see https://next-auth.js.org/getting-started/typescript#module-augmentation\n */\ndeclare module \"next-auth\" {\n  interface Session extends DefaultSession {\n    user: {\n      id: string;\n      // ...other properties\n      // role: UserRole;\n    } & DefaultSession[\"user\"];\n  }\n\n  // interface User {\n  //   // ...other properties\n  //   // role: UserRole;\n  // }\n}\n\n/**\n * Options for NextAuth.js used to configure adapters, providers, callbacks, etc.\n *\n * @see https://next-auth.js.org/configuration/options\n */\nexport const authConfig = {\n  providers: [\n    DiscordProvider,\n    /**\n     * ...add more providers here.\n     *\n     * Most other providers require a bit more work than the Discord provider. For example, the\n     * GitHub provider requires you to add the `refresh_token_expires_in` field to the Account\n     * model. Refer to the NextAuth.js docs for the provider you want to use. Example:\n     *\n     * @see https://next-auth.js.org/providers/github\n     */\n  ],\n  adapter: PrismaAdapter(db),\n  callbacks: {\n    session: ({ session, user }) => ({\n      ...session,\n      user: {\n        ...session.user,\n        id: user.id,\n      },\n    }),\n  },\n} satisfies NextAuthConfig;\n"], "names": [], "mappings": ";;;AAAA;AAEA;AAAA;AAEA;;;;AA4BO,MAAM,aAAa;IACxB,WAAW;QACT,wLAAA,CAAA,UAAe;KAUhB;IACD,SAAS,CAAA,GAAA,sJAAA,CAAA,gBAAa,AAAD,EAAE,qHAAA,CAAA,KAAE;IACzB,WAAW;QACT,SAAS,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,GAAK,CAAC;gBAC/B,GAAG,OAAO;gBACV,MAAM;oBACJ,GAAG,QAAQ,IAAI;oBACf,IAAI,KAAK,EAAE;gBACb;YACF,CAAC;IACH;AACF", "debugId": null}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/server/auth/index.ts"], "sourcesContent": ["import NextAuth from \"next-auth\";\nimport { cache } from \"react\";\n\nimport { authConfig } from \"./config\";\n\nconst { auth: uncachedAuth, handlers, signIn, signOut } = NextAuth(authConfig);\n\nconst auth = cache(uncachedAuth);\n\nexport { auth, handlers, signIn, signOut };\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AACA;AAEA;;;;AAEA,MAAM,EAAE,MAAM,YAAY,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,uJAAA,CAAA,UAAQ,AAAD,EAAE,iIAAA,CAAA,aAAU;AAE7E,MAAM,OAAO,CAAA,GAAA,uMAAA,CAAA,QAAK,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/server/api/trpc.ts"], "sourcesContent": ["/**\n * YOU PROBABLY DON'T NEED TO EDIT THIS FILE, UNLESS:\n * 1. You want to modify request context (see Part 1).\n * 2. You want to create a new middleware or type of procedure (see Part 3).\n *\n * TL;DR - This is where all the tRPC server stuff is created and plugged in. The pieces you will\n * need to use are documented accordingly near the end.\n */\n\nimport { initTRPC, TRPCError } from \"@trpc/server\";\nimport superjson from \"superjson\";\nimport { ZodError } from \"zod\";\n\nimport { auth } from \"~/server/auth\";\nimport { db } from \"~/server/db\";\n\n/**\n * 1. CONTEXT\n *\n * This section defines the \"contexts\" that are available in the backend API.\n *\n * These allow you to access things when processing a request, like the database, the session, etc.\n *\n * This helper generates the \"internals\" for a tRPC context. The API handler and RSC clients each\n * wrap this and provides the required context.\n *\n * @see https://trpc.io/docs/server/context\n */\nexport const createTRPCContext = async (opts: { headers: Headers }) => {\n  const session = await auth();\n\n  return {\n    db,\n    session,\n    ...opts,\n  };\n};\n\n/**\n * 2. INITIALIZATION\n *\n * This is where the tRPC API is initialized, connecting the context and transformer. We also parse\n * ZodErrors so that you get typesafety on the frontend if your procedure fails due to validation\n * errors on the backend.\n */\nconst t = initTRPC.context<typeof createTRPCContext>().create({\n  transformer: superjson,\n  errorFormatter({ shape, error }) {\n    return {\n      ...shape,\n      data: {\n        ...shape.data,\n        zodError:\n          error.cause instanceof ZodError ? error.cause.flatten() : null,\n      },\n    };\n  },\n});\n\n/**\n * Create a server-side caller.\n *\n * @see https://trpc.io/docs/server/server-side-calls\n */\nexport const createCallerFactory = t.createCallerFactory;\n\n/**\n * 3. ROUTER & PROCEDURE (THE IMPORTANT BIT)\n *\n * These are the pieces you use to build your tRPC API. You should import these a lot in the\n * \"/src/server/api/routers\" directory.\n */\n\n/**\n * This is how you create new routers and sub-routers in your tRPC API.\n *\n * @see https://trpc.io/docs/router\n */\nexport const createTRPCRouter = t.router;\n\n/**\n * Middleware for timing procedure execution and adding an artificial delay in development.\n *\n * You can remove this if you don't like it, but it can help catch unwanted waterfalls by simulating\n * network latency that would occur in production but not in local development.\n */\nconst timingMiddleware = t.middleware(async ({ next, path }) => {\n  const start = Date.now();\n\n  if (t._config.isDev) {\n    // artificial delay in dev\n    const waitMs = Math.floor(Math.random() * 400) + 100;\n    await new Promise((resolve) => setTimeout(resolve, waitMs));\n  }\n\n  const result = await next();\n\n  const end = Date.now();\n  console.log(`[TRPC] ${path} took ${end - start}ms to execute`);\n\n  return result;\n});\n\n/**\n * Public (unauthenticated) procedure\n *\n * This is the base piece you use to build new queries and mutations on your tRPC API. It does not\n * guarantee that a user querying is authorized, but you can still access user session data if they\n * are logged in.\n */\nexport const publicProcedure = t.procedure.use(timingMiddleware);\n\n/**\n * Protected (authenticated) procedure\n *\n * If you want a query or mutation to ONLY be accessible to logged in users, use this. It verifies\n * the session is valid and guarantees `ctx.session.user` is not null.\n *\n * @see https://trpc.io/docs/procedures\n */\nexport const protectedProcedure = t.procedure\n  .use(timingMiddleware)\n  .use(({ ctx, next }) => {\n    if (!ctx.session?.user) {\n      throw new TRPCError({ code: \"UNAUTHORIZED\" });\n    }\n    return next({\n      ctx: {\n        // infers the `session` as non-nullable\n        session: { ...ctx.session, user: ctx.session.user },\n      },\n    });\n  });\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;AAED;AAAA;AACA;AACA;AAEA;AACA;;;;;;AAcO,MAAM,oBAAoB,OAAO;IACtC,MAAM,UAAU,MAAM,CAAA,GAAA,gIAAA,CAAA,OAAI,AAAD;IAEzB,OAAO;QACL,IAAA,qHAAA,CAAA,KAAE;QACF;QACA,GAAG,IAAI;IACT;AACF;AAEA;;;;;;CAMC,GACD,MAAM,IAAI,mKAAA,CAAA,WAAQ,CAAC,OAAO,GAA6B,MAAM,CAAC;IAC5D,aAAa,4IAAA,CAAA,UAAS;IACtB,gBAAe,EAAE,KAAK,EAAE,KAAK,EAAE;QAC7B,OAAO;YACL,GAAG,KAAK;YACR,MAAM;gBACJ,GAAG,MAAM,IAAI;gBACb,UACE,MAAM,KAAK,YAAY,uIAAA,CAAA,WAAQ,GAAG,MAAM,KAAK,CAAC,OAAO,KAAK;YAC9D;QACF;IACF;AACF;AAOO,MAAM,sBAAsB,EAAE,mBAAmB;AAcjD,MAAM,mBAAmB,EAAE,MAAM;AAExC;;;;;CAKC,GACD,MAAM,mBAAmB,EAAE,UAAU,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE;IACzD,MAAM,QAAQ,KAAK,GAAG;IAEtB,IAAI,EAAE,OAAO,CAAC,KAAK,EAAE;QACnB,0BAA0B;QAC1B,MAAM,SAAS,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;QACjD,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;IACrD;IAEA,MAAM,SAAS,MAAM;IAErB,MAAM,MAAM,KAAK,GAAG;IACpB,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,MAAM,EAAE,MAAM,MAAM,aAAa,CAAC;IAE7D,OAAO;AACT;AASO,MAAM,kBAAkB,EAAE,SAAS,CAAC,GAAG,CAAC;AAUxC,MAAM,qBAAqB,EAAE,SAAS,CAC1C,GAAG,CAAC,kBACJ,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE;IACjB,IAAI,CAAC,IAAI,OAAO,EAAE,MAAM;QACtB,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;YAAE,MAAM;QAAe;IAC7C;IACA,OAAO,KAAK;QACV,KAAK;YACH,uCAAuC;YACvC,SAAS;gBAAE,GAAG,IAAI,OAAO;gBAAE,MAAM,IAAI,OAAO,CAAC,IAAI;YAAC;QACpD;IACF;AACF", "debugId": null}}, {"offset": {"line": 336, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/server/api/routers/category.ts"], "sourcesContent": ["import { z } from \"zod\";\nimport { createTRPCRouter, protectedProcedure, publicProcedure } from \"~/server/api/trpc\";\n\nexport const categoryRouter = createTRPCRouter({\n  // 获取所有分类\n  getAll: publicProcedure.query(async ({ ctx }) => {\n    return ctx.db.category.findMany({\n      orderBy: { createdAt: \"desc\" },\n      include: {\n        _count: {\n          select: { prompts: true },\n        },\n      },\n    });\n  }),\n\n  // 根据ID获取分类\n  getById: publicProcedure\n    .input(z.object({ id: z.string() }))\n    .query(async ({ ctx, input }) => {\n      return ctx.db.category.findUnique({\n        where: { id: input.id },\n        include: {\n          prompts: {\n            orderBy: { createdAt: \"desc\" },\n            take: 10, // 只返回最新的10个提示词\n          },\n          _count: {\n            select: { prompts: true },\n          },\n        },\n      });\n    }),\n\n  // 创建分类\n  create: protectedProcedure\n    .input(\n      z.object({\n        name: z.string().min(1, \"分类名称不能为空\").max(50, \"分类名称不能超过50个字符\"),\n        description: z.string().optional(),\n        color: z.string().regex(/^#[0-9A-F]{6}$/i, \"颜色格式不正确\").default(\"#3B82F6\"),\n        icon: z.string().optional(),\n      })\n    )\n    .mutation(async ({ ctx, input }) => {\n      return ctx.db.category.create({\n        data: {\n          ...input,\n          createdById: ctx.session.user.id,\n        },\n      });\n    }),\n\n  // 更新分类\n  update: protectedProcedure\n    .input(\n      z.object({\n        id: z.string(),\n        name: z.string().min(1, \"分类名称不能为空\").max(50, \"分类名称不能超过50个字符\").optional(),\n        description: z.string().optional(),\n        color: z.string().regex(/^#[0-9A-F]{6}$/i, \"颜色格式不正确\").optional(),\n        icon: z.string().optional(),\n      })\n    )\n    .mutation(async ({ ctx, input }) => {\n      const { id, ...updateData } = input;\n      \n      // 检查分类是否存在且属于当前用户\n      const category = await ctx.db.category.findUnique({\n        where: { id },\n      });\n\n      if (!category) {\n        throw new Error(\"分类不存在\");\n      }\n\n      if (category.createdById !== ctx.session.user.id) {\n        throw new Error(\"无权限修改此分类\");\n      }\n\n      return ctx.db.category.update({\n        where: { id },\n        data: updateData,\n      });\n    }),\n\n  // 删除分类\n  delete: protectedProcedure\n    .input(z.object({ id: z.string() }))\n    .mutation(async ({ ctx, input }) => {\n      // 检查分类是否存在且属于当前用户\n      const category = await ctx.db.category.findUnique({\n        where: { id: input.id },\n        include: {\n          _count: {\n            select: { prompts: true },\n          },\n        },\n      });\n\n      if (!category) {\n        throw new Error(\"分类不存在\");\n      }\n\n      if (category.createdById !== ctx.session.user.id) {\n        throw new Error(\"无权限删除此分类\");\n      }\n\n      if (category._count.prompts > 0) {\n        throw new Error(\"该分类下还有提示词，无法删除\");\n      }\n\n      return ctx.db.category.delete({\n        where: { id: input.id },\n      });\n    }),\n\n  // 获取分类统计信息\n  getStats: publicProcedure.query(async ({ ctx }) => {\n    const stats = await ctx.db.category.findMany({\n      select: {\n        id: true,\n        name: true,\n        color: true,\n        _count: {\n          select: { prompts: true },\n        },\n      },\n      orderBy: {\n        prompts: {\n          _count: \"desc\",\n        },\n      },\n    });\n\n    return stats;\n  }),\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,iBAAiB,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE;IAC7C,SAAS;IACT,QAAQ,8HAAA,CAAA,kBAAe,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE;QAC1C,OAAO,IAAI,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC9B,SAAS;gBAAE,WAAW;YAAO;YAC7B,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBAAE,SAAS;oBAAK;gBAC1B;YACF;QACF;IACF;IAEA,WAAW;IACX,SAAS,8HAAA,CAAA,kBAAe,CACrB,KAAK,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,IAAI,oKAAA,CAAA,IAAC,CAAC,MAAM;IAAG,IAChC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC1B,OAAO,IAAI,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;YAChC,OAAO;gBAAE,IAAI,MAAM,EAAE;YAAC;YACtB,SAAS;gBACP,SAAS;oBACP,SAAS;wBAAE,WAAW;oBAAO;oBAC7B,MAAM;gBACR;gBACA,QAAQ;oBACN,QAAQ;wBAAE,SAAS;oBAAK;gBAC1B;YACF;QACF;IACF;IAEF,OAAO;IACP,QAAQ,8HAAA,CAAA,qBAAkB,CACvB,KAAK,CACJ,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACP,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,IAAI;QAC5C,aAAa,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAChC,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,mBAAmB,WAAW,OAAO,CAAC;QAC9D,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC3B,IAED,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC7B,OAAO,IAAI,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC5B,MAAM;gBACJ,GAAG,KAAK;gBACR,aAAa,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE;YAClC;QACF;IACF;IAEF,OAAO;IACP,QAAQ,8HAAA,CAAA,qBAAkB,CACvB,KAAK,CACJ,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACP,IAAI,oKAAA,CAAA,IAAC,CAAC,MAAM;QACZ,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,IAAI,iBAAiB,QAAQ;QACrE,aAAa,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAChC,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,mBAAmB,WAAW,QAAQ;QAC9D,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC3B,IAED,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC7B,MAAM,EAAE,EAAE,EAAE,GAAG,YAAY,GAAG;QAE9B,kBAAkB;QAClB,MAAM,WAAW,MAAM,IAAI,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;YAChD,OAAO;gBAAE;YAAG;QACd;QAEA,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,SAAS,WAAW,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE;YAChD,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,IAAI,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC5B,OAAO;gBAAE;YAAG;YACZ,MAAM;QACR;IACF;IAEF,OAAO;IACP,QAAQ,8HAAA,CAAA,qBAAkB,CACvB,KAAK,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,IAAI,oKAAA,CAAA,IAAC,CAAC,MAAM;IAAG,IAChC,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC7B,kBAAkB;QAClB,MAAM,WAAW,MAAM,IAAI,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;YAChD,OAAO;gBAAE,IAAI,MAAM,EAAE;YAAC;YACtB,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBAAE,SAAS;oBAAK;gBAC1B;YACF;QACF;QAEA,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,SAAS,WAAW,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE;YAChD,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,SAAS,MAAM,CAAC,OAAO,GAAG,GAAG;YAC/B,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,IAAI,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC5B,OAAO;gBAAE,IAAI,MAAM,EAAE;YAAC;QACxB;IACF;IAEF,WAAW;IACX,UAAU,8HAAA,CAAA,kBAAe,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE;QAC5C,MAAM,QAAQ,MAAM,IAAI,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC3C,QAAQ;gBACN,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,QAAQ;oBACN,QAAQ;wBAAE,SAAS;oBAAK;gBAC1B;YACF;YACA,SAAS;gBACP,SAAS;oBACP,QAAQ;gBACV;YACF;QACF;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 482, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/server/api/routers/prompt.ts"], "sourcesContent": ["import { z } from \"zod\";\nimport { createTRPCRouter, protectedProcedure, publicProcedure } from \"~/server/api/trpc\";\n\nexport const promptRouter = createTRPCRouter({\n  // 获取所有提示词（支持分页、搜索、筛选）\n  getAll: publicProcedure\n    .input(\n      z.object({\n        page: z.number().min(1).default(1),\n        limit: z.number().min(1).max(100).default(20),\n        search: z.string().optional(),\n        categoryId: z.string().optional(),\n        sortBy: z.enum([\"createdAt\", \"updatedAt\", \"usageCount\", \"title\"]).default(\"createdAt\"),\n        sortOrder: z.enum([\"asc\", \"desc\"]).default(\"desc\"),\n      })\n    )\n    .query(async ({ ctx, input }) => {\n      const { page, limit, search, categoryId, sortBy, sortOrder } = input;\n      const skip = (page - 1) * limit;\n\n      // 构建查询条件\n      const where: any = {\n        isPublic: true,\n      };\n\n      if (search) {\n        where.OR = [\n          { title: { contains: search, mode: \"insensitive\" } },\n          { description: { contains: search, mode: \"insensitive\" } },\n          { content: { contains: search, mode: \"insensitive\" } },\n          { tags: { hasSome: [search] } },\n        ];\n      }\n\n      if (categoryId) {\n        where.categoryId = categoryId;\n      }\n\n      // 获取总数\n      const total = await ctx.db.prompt.count({ where });\n\n      // 获取数据\n      const prompts = await ctx.db.prompt.findMany({\n        where,\n        skip,\n        take: limit,\n        orderBy: { [sortBy]: sortOrder },\n        include: {\n          category: {\n            select: {\n              id: true,\n              name: true,\n              color: true,\n              icon: true,\n            },\n          },\n          createdBy: {\n            select: {\n              id: true,\n              name: true,\n              image: true,\n            },\n          },\n        },\n      });\n\n      return {\n        prompts,\n        pagination: {\n          page,\n          limit,\n          total,\n          totalPages: Math.ceil(total / limit),\n        },\n      };\n    }),\n\n  // 根据ID获取提示词详情\n  getById: publicProcedure\n    .input(z.object({ id: z.string() }))\n    .query(async ({ ctx, input }) => {\n      return ctx.db.prompt.findUnique({\n        where: { id: input.id },\n        include: {\n          category: {\n            select: {\n              id: true,\n              name: true,\n              color: true,\n              icon: true,\n            },\n          },\n          createdBy: {\n            select: {\n              id: true,\n              name: true,\n              image: true,\n            },\n          },\n        },\n      });\n    }),\n\n  // 创建提示词\n  create: protectedProcedure\n    .input(\n      z.object({\n        title: z.string().min(1, \"标题不能为空\").max(100, \"标题不能超过100个字符\"),\n        content: z.string().min(1, \"内容不能为空\"),\n        description: z.string().max(200, \"描述不能超过200个字符\").optional(),\n        tags: z.array(z.string()).max(10, \"标签不能超过10个\").default([]),\n        categoryId: z.string().optional(),\n        isPublic: z.boolean().default(true),\n      })\n    )\n    .mutation(async ({ ctx, input }) => {\n      return ctx.db.prompt.create({\n        data: {\n          ...input,\n          createdById: ctx.session.user.id,\n        },\n        include: {\n          category: {\n            select: {\n              id: true,\n              name: true,\n              color: true,\n              icon: true,\n            },\n          },\n        },\n      });\n    }),\n\n  // 更新提示词\n  update: protectedProcedure\n    .input(\n      z.object({\n        id: z.string(),\n        title: z.string().min(1, \"标题不能为空\").max(100, \"标题不能超过100个字符\").optional(),\n        content: z.string().min(1, \"内容不能为空\").optional(),\n        description: z.string().max(200, \"描述不能超过200个字符\").optional(),\n        tags: z.array(z.string()).max(10, \"标签不能超过10个\").optional(),\n        categoryId: z.string().optional(),\n        isPublic: z.boolean().optional(),\n      })\n    )\n    .mutation(async ({ ctx, input }) => {\n      const { id, ...updateData } = input;\n      \n      // 检查提示词是否存在且属于当前用户\n      const prompt = await ctx.db.prompt.findUnique({\n        where: { id },\n      });\n\n      if (!prompt) {\n        throw new Error(\"提示词不存在\");\n      }\n\n      if (prompt.createdById !== ctx.session.user.id) {\n        throw new Error(\"无权限修改此提示词\");\n      }\n\n      return ctx.db.prompt.update({\n        where: { id },\n        data: updateData,\n        include: {\n          category: {\n            select: {\n              id: true,\n              name: true,\n              color: true,\n              icon: true,\n            },\n          },\n        },\n      });\n    }),\n\n  // 删除提示词\n  delete: protectedProcedure\n    .input(z.object({ id: z.string() }))\n    .mutation(async ({ ctx, input }) => {\n      // 检查提示词是否存在且属于当前用户\n      const prompt = await ctx.db.prompt.findUnique({\n        where: { id: input.id },\n      });\n\n      if (!prompt) {\n        throw new Error(\"提示词不存在\");\n      }\n\n      if (prompt.createdById !== ctx.session.user.id) {\n        throw new Error(\"无权限删除此提示词\");\n      }\n\n      return ctx.db.prompt.delete({\n        where: { id: input.id },\n      });\n    }),\n\n  // 复制提示词（增加使用次数）\n  copy: publicProcedure\n    .input(z.object({ id: z.string() }))\n    .mutation(async ({ ctx, input }) => {\n      // 更新使用次数\n      const prompt = await ctx.db.prompt.update({\n        where: { id: input.id },\n        data: {\n          usageCount: {\n            increment: 1,\n          },\n        },\n      });\n\n      // 如果用户已登录，记录使用历史\n      if (ctx.session?.user?.id) {\n        await ctx.db.promptUsage.create({\n          data: {\n            promptId: input.id,\n            userId: ctx.session.user.id,\n          },\n        });\n      }\n\n      return prompt;\n    }),\n\n  // 获取我的提示词\n  getMine: protectedProcedure\n    .input(\n      z.object({\n        page: z.number().min(1).default(1),\n        limit: z.number().min(1).max(100).default(20),\n        search: z.string().optional(),\n        categoryId: z.string().optional(),\n      })\n    )\n    .query(async ({ ctx, input }) => {\n      const { page, limit, search, categoryId } = input;\n      const skip = (page - 1) * limit;\n\n      const where: any = {\n        createdById: ctx.session.user.id,\n      };\n\n      if (search) {\n        where.OR = [\n          { title: { contains: search, mode: \"insensitive\" } },\n          { description: { contains: search, mode: \"insensitive\" } },\n          { content: { contains: search, mode: \"insensitive\" } },\n        ];\n      }\n\n      if (categoryId) {\n        where.categoryId = categoryId;\n      }\n\n      const total = await ctx.db.prompt.count({ where });\n\n      const prompts = await ctx.db.prompt.findMany({\n        where,\n        skip,\n        take: limit,\n        orderBy: { updatedAt: \"desc\" },\n        include: {\n          category: {\n            select: {\n              id: true,\n              name: true,\n              color: true,\n              icon: true,\n            },\n          },\n        },\n      });\n\n      return {\n        prompts,\n        pagination: {\n          page,\n          limit,\n          total,\n          totalPages: Math.ceil(total / limit),\n        },\n      };\n    }),\n\n  // 获取热门提示词\n  getPopular: publicProcedure\n    .input(z.object({ limit: z.number().min(1).max(50).default(10) }))\n    .query(async ({ ctx, input }) => {\n      return ctx.db.prompt.findMany({\n        where: { isPublic: true },\n        take: input.limit,\n        orderBy: { usageCount: \"desc\" },\n        include: {\n          category: {\n            select: {\n              id: true,\n              name: true,\n              color: true,\n              icon: true,\n            },\n          },\n          createdBy: {\n            select: {\n              id: true,\n              name: true,\n              image: true,\n            },\n          },\n        },\n      });\n    }),\n\n  // 获取最新提示词\n  getLatest: publicProcedure\n    .input(z.object({ limit: z.number().min(1).max(50).default(10) }))\n    .query(async ({ ctx, input }) => {\n      return ctx.db.prompt.findMany({\n        where: { isPublic: true },\n        take: input.limit,\n        orderBy: { createdAt: \"desc\" },\n        include: {\n          category: {\n            select: {\n              id: true,\n              name: true,\n              color: true,\n              icon: true,\n            },\n          },\n          createdBy: {\n            select: {\n              id: true,\n              name: true,\n              image: true,\n            },\n          },\n        },\n      });\n    }),\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,eAAe,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE;IAC3C,sBAAsB;IACtB,QAAQ,8HAAA,CAAA,kBAAe,CACpB,KAAK,CACJ,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACP,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;QAChC,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,OAAO,CAAC;QAC1C,QAAQ,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC3B,YAAY,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC/B,QAAQ,oKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAa;YAAa;YAAc;SAAQ,EAAE,OAAO,CAAC;QAC1E,WAAW,oKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAO;SAAO,EAAE,OAAO,CAAC;IAC7C,IAED,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC1B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG;QAC/D,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,SAAS;QACT,MAAM,QAAa;YACjB,UAAU;QACZ;QAEA,IAAI,QAAQ;YACV,MAAM,EAAE,GAAG;gBACT;oBAAE,OAAO;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;gBACnD;oBAAE,aAAa;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;gBACzD;oBAAE,SAAS;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;gBACrD;oBAAE,MAAM;wBAAE,SAAS;4BAAC;yBAAO;oBAAC;gBAAE;aAC/B;QACH;QAEA,IAAI,YAAY;YACd,MAAM,UAAU,GAAG;QACrB;QAEA,OAAO;QACP,MAAM,QAAQ,MAAM,IAAI,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC;YAAE;QAAM;QAEhD,OAAO;QACP,MAAM,UAAU,MAAM,IAAI,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC;YAC3C;YACA;YACA,MAAM;YACN,SAAS;gBAAE,CAAC,OAAO,EAAE;YAAU;YAC/B,SAAS;gBACP,UAAU;oBACR,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,MAAM;oBACR;gBACF;gBACA,WAAW;oBACT,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,OAAO;oBACT;gBACF;YACF;QACF;QAEA,OAAO;YACL;YACA,YAAY;gBACV;gBACA;gBACA;gBACA,YAAY,KAAK,IAAI,CAAC,QAAQ;YAChC;QACF;IACF;IAEF,cAAc;IACd,SAAS,8HAAA,CAAA,kBAAe,CACrB,KAAK,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,IAAI,oKAAA,CAAA,IAAC,CAAC,MAAM;IAAG,IAChC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC1B,OAAO,IAAI,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC;YAC9B,OAAO;gBAAE,IAAI,MAAM,EAAE;YAAC;YACtB,SAAS;gBACP,UAAU;oBACR,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,MAAM;oBACR;gBACF;gBACA,WAAW;oBACT,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,OAAO;oBACT;gBACF;YACF;QACF;IACF;IAEF,QAAQ;IACR,QAAQ,8HAAA,CAAA,qBAAkB,CACvB,KAAK,CACJ,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACP,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,KAAK;QAC5C,SAAS,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAC3B,aAAa,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,gBAAgB,QAAQ;QACzD,MAAM,oKAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,IAAI,GAAG,CAAC,IAAI,aAAa,OAAO,CAAC,EAAE;QACzD,YAAY,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC/B,UAAU,oKAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAChC,IAED,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC7B,OAAO,IAAI,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC;YAC1B,MAAM;gBACJ,GAAG,KAAK;gBACR,aAAa,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE;YAClC;YACA,SAAS;gBACP,UAAU;oBACR,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,MAAM;oBACR;gBACF;YACF;QACF;IACF;IAEF,QAAQ;IACR,QAAQ,8HAAA,CAAA,qBAAkB,CACvB,KAAK,CACJ,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACP,IAAI,oKAAA,CAAA,IAAC,CAAC,MAAM;QACZ,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,KAAK,gBAAgB,QAAQ;QACpE,SAAS,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,UAAU,QAAQ;QAC7C,aAAa,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,gBAAgB,QAAQ;QACzD,MAAM,oKAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,IAAI,GAAG,CAAC,IAAI,aAAa,QAAQ;QACvD,YAAY,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC/B,UAAU,oKAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;IAChC,IAED,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC7B,MAAM,EAAE,EAAE,EAAE,GAAG,YAAY,GAAG;QAE9B,mBAAmB;QACnB,MAAM,SAAS,MAAM,IAAI,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC;YAC5C,OAAO;gBAAE;YAAG;QACd;QAEA,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,OAAO,WAAW,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE;YAC9C,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,IAAI,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC;YAC1B,OAAO;gBAAE;YAAG;YACZ,MAAM;YACN,SAAS;gBACP,UAAU;oBACR,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,MAAM;oBACR;gBACF;YACF;QACF;IACF;IAEF,QAAQ;IACR,QAAQ,8HAAA,CAAA,qBAAkB,CACvB,KAAK,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,IAAI,oKAAA,CAAA,IAAC,CAAC,MAAM;IAAG,IAChC,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC7B,mBAAmB;QACnB,MAAM,SAAS,MAAM,IAAI,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC;YAC5C,OAAO;gBAAE,IAAI,MAAM,EAAE;YAAC;QACxB;QAEA,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,OAAO,WAAW,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE;YAC9C,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,IAAI,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC;YAC1B,OAAO;gBAAE,IAAI,MAAM,EAAE;YAAC;QACxB;IACF;IAEF,gBAAgB;IAChB,MAAM,8HAAA,CAAA,kBAAe,CAClB,KAAK,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,IAAI,oKAAA,CAAA,IAAC,CAAC,MAAM;IAAG,IAChC,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC7B,SAAS;QACT,MAAM,SAAS,MAAM,IAAI,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC;YACxC,OAAO;gBAAE,IAAI,MAAM,EAAE;YAAC;YACtB,MAAM;gBACJ,YAAY;oBACV,WAAW;gBACb;YACF;QACF;QAEA,iBAAiB;QACjB,IAAI,IAAI,OAAO,EAAE,MAAM,IAAI;YACzB,MAAM,IAAI,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC;gBAC9B,MAAM;oBACJ,UAAU,MAAM,EAAE;oBAClB,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE;gBAC7B;YACF;QACF;QAEA,OAAO;IACT;IAEF,UAAU;IACV,SAAS,8HAAA,CAAA,qBAAkB,CACxB,KAAK,CACJ,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACP,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;QAChC,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,OAAO,CAAC;QAC1C,QAAQ,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC3B,YAAY,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACjC,IAED,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC1B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG;QAC5C,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,MAAM,QAAa;YACjB,aAAa,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE;QAClC;QAEA,IAAI,QAAQ;YACV,MAAM,EAAE,GAAG;gBACT;oBAAE,OAAO;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;gBACnD;oBAAE,aAAa;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;gBACzD;oBAAE,SAAS;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;aACtD;QACH;QAEA,IAAI,YAAY;YACd,MAAM,UAAU,GAAG;QACrB;QAEA,MAAM,QAAQ,MAAM,IAAI,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC;YAAE;QAAM;QAEhD,MAAM,UAAU,MAAM,IAAI,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC;YAC3C;YACA;YACA,MAAM;YACN,SAAS;gBAAE,WAAW;YAAO;YAC7B,SAAS;gBACP,UAAU;oBACR,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,MAAM;oBACR;gBACF;YACF;QACF;QAEA,OAAO;YACL;YACA,YAAY;gBACV;gBACA;gBACA;gBACA,YAAY,KAAK,IAAI,CAAC,QAAQ;YAChC;QACF;IACF;IAEF,UAAU;IACV,YAAY,8HAAA,CAAA,kBAAe,CACxB,KAAK,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,OAAO,CAAC;IAAI,IAC9D,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC1B,OAAO,IAAI,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC;YAC5B,OAAO;gBAAE,UAAU;YAAK;YACxB,MAAM,MAAM,KAAK;YACjB,SAAS;gBAAE,YAAY;YAAO;YAC9B,SAAS;gBACP,UAAU;oBACR,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,MAAM;oBACR;gBACF;gBACA,WAAW;oBACT,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,OAAO;oBACT;gBACF;YACF;QACF;IACF;IAEF,UAAU;IACV,WAAW,8HAAA,CAAA,kBAAe,CACvB,KAAK,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,OAAO,CAAC;IAAI,IAC9D,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC1B,OAAO,IAAI,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC;YAC5B,OAAO;gBAAE,UAAU;YAAK;YACxB,MAAM,MAAM,KAAK;YACjB,SAAS;gBAAE,WAAW;YAAO;YAC7B,SAAS;gBACP,UAAU;oBACR,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,MAAM;oBACR;gBACF;gBACA,WAAW;oBACT,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,OAAO;oBACT;gBACF;YACF;QACF;IACF;AACJ", "debugId": null}}, {"offset": {"line": 861, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/server/api/routers/stats.ts"], "sourcesContent": ["import { z } from \"zod\";\nimport { createTRPCRouter, protectedProcedure, publicProcedure } from \"~/server/api/trpc\";\n\nexport const statsRouter = createTRPCRouter({\n  // 获取总体统计信息\n  getOverview: publicProcedure.query(async ({ ctx }) => {\n    const [\n      totalPrompts,\n      totalCategories,\n      totalUsers,\n      totalUsages,\n    ] = await Promise.all([\n      ctx.db.prompt.count({ where: { isPublic: true } }),\n      ctx.db.category.count(),\n      ctx.db.user.count(),\n      ctx.db.promptUsage.count(),\n    ]);\n\n    return {\n      totalPrompts,\n      totalCategories,\n      totalUsers,\n      totalUsages,\n    };\n  }),\n\n  // 获取分类统计\n  getCategoryStats: publicProcedure.query(async ({ ctx }) => {\n    const categoryStats = await ctx.db.category.findMany({\n      select: {\n        id: true,\n        name: true,\n        color: true,\n        icon: true,\n        _count: {\n          select: { prompts: true },\n        },\n      },\n      orderBy: {\n        prompts: {\n          _count: \"desc\",\n        },\n      },\n    });\n\n    return categoryStats.map(category => ({\n      id: category.id,\n      name: category.name,\n      color: category.color,\n      icon: category.icon,\n      promptCount: category._count.prompts,\n    }));\n  }),\n\n  // 获取使用趋势（最近30天）\n  getUsageTrend: publicProcedure.query(async ({ ctx }) => {\n    const thirtyDaysAgo = new Date();\n    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n\n    const usageData = await ctx.db.promptUsage.findMany({\n      where: {\n        createdAt: {\n          gte: thirtyDaysAgo,\n        },\n      },\n      select: {\n        createdAt: true,\n      },\n      orderBy: {\n        createdAt: \"asc\",\n      },\n    });\n\n    // 按日期分组统计\n    const dailyUsage = new Map<string, number>();\n    \n    // 初始化最近30天的数据\n    for (let i = 29; i >= 0; i--) {\n      const date = new Date();\n      date.setDate(date.getDate() - i);\n      const dateStr = date.toISOString().split('T')[0];\n      dailyUsage.set(dateStr!, 0);\n    }\n\n    // 统计实际使用数据\n    usageData.forEach(usage => {\n      const dateStr = usage.createdAt.toISOString().split('T')[0];\n      if (dateStr) {\n        dailyUsage.set(dateStr, (dailyUsage.get(dateStr) || 0) + 1);\n      }\n    });\n\n    return Array.from(dailyUsage.entries()).map(([date, count]) => ({\n      date,\n      count,\n    }));\n  }),\n\n  // 获取热门标签\n  getPopularTags: publicProcedure\n    .input(z.object({ limit: z.number().min(1).max(50).default(20) }))\n    .query(async ({ ctx, input }) => {\n      const prompts = await ctx.db.prompt.findMany({\n        where: { isPublic: true },\n        select: { tags: true },\n      });\n\n      // 统计标签使用频率\n      const tagCount = new Map<string, number>();\n      prompts.forEach(prompt => {\n        prompt.tags.forEach(tag => {\n          tagCount.set(tag, (tagCount.get(tag) || 0) + 1);\n        });\n      });\n\n      // 排序并返回前N个\n      return Array.from(tagCount.entries())\n        .sort((a, b) => b[1] - a[1])\n        .slice(0, input.limit)\n        .map(([tag, count]) => ({ tag, count }));\n    }),\n\n  // 获取用户个人统计（需要登录）\n  getMyStats: protectedProcedure.query(async ({ ctx }) => {\n    const userId = ctx.session.user.id;\n\n    const [\n      myPrompts,\n      myCategories,\n      myUsages,\n      myTotalUsageCount,\n    ] = await Promise.all([\n      ctx.db.prompt.count({ where: { createdById: userId } }),\n      ctx.db.category.count({ where: { createdById: userId } }),\n      ctx.db.promptUsage.count({ where: { userId } }),\n      ctx.db.prompt.aggregate({\n        where: { createdById: userId },\n        _sum: { usageCount: true },\n      }),\n    ]);\n\n    // 获取我最常用的分类\n    const myTopCategories = await ctx.db.category.findMany({\n      where: { createdById: userId },\n      select: {\n        id: true,\n        name: true,\n        color: true,\n        _count: {\n          select: { prompts: true },\n        },\n      },\n      orderBy: {\n        prompts: {\n          _count: \"desc\",\n        },\n      },\n      take: 5,\n    });\n\n    // 获取我最受欢迎的提示词\n    const myTopPrompts = await ctx.db.prompt.findMany({\n      where: { createdById: userId },\n      select: {\n        id: true,\n        title: true,\n        usageCount: true,\n        category: {\n          select: {\n            name: true,\n            color: true,\n          },\n        },\n      },\n      orderBy: { usageCount: \"desc\" },\n      take: 5,\n    });\n\n    return {\n      myPrompts,\n      myCategories,\n      myUsages,\n      myTotalUsageCount: myTotalUsageCount._sum.usageCount || 0,\n      myTopCategories: myTopCategories.map(cat => ({\n        id: cat.id,\n        name: cat.name,\n        color: cat.color,\n        promptCount: cat._count.prompts,\n      })),\n      myTopPrompts,\n    };\n  }),\n\n  // 获取最近使用的提示词（需要登录）\n  getRecentUsage: protectedProcedure\n    .input(z.object({ limit: z.number().min(1).max(50).default(10) }))\n    .query(async ({ ctx, input }) => {\n      const recentUsages = await ctx.db.promptUsage.findMany({\n        where: { userId: ctx.session.user.id },\n        take: input.limit,\n        orderBy: { createdAt: \"desc\" },\n        include: {\n          prompt: {\n            select: {\n              id: true,\n              title: true,\n              description: true,\n              category: {\n                select: {\n                  name: true,\n                  color: true,\n                  icon: true,\n                },\n              },\n            },\n          },\n        },\n      });\n\n      return recentUsages.map(usage => ({\n        id: usage.id,\n        usedAt: usage.createdAt,\n        prompt: usage.prompt,\n      }));\n    }),\n\n  // 获取使用排行榜\n  getUsageLeaderboard: publicProcedure\n    .input(z.object({ limit: z.number().min(1).max(50).default(10) }))\n    .query(async ({ ctx, input }) => {\n      return ctx.db.prompt.findMany({\n        where: { isPublic: true },\n        take: input.limit,\n        orderBy: { usageCount: \"desc\" },\n        select: {\n          id: true,\n          title: true,\n          description: true,\n          usageCount: true,\n          category: {\n            select: {\n              name: true,\n              color: true,\n              icon: true,\n            },\n          },\n          createdBy: {\n            select: {\n              name: true,\n              image: true,\n            },\n          },\n        },\n      });\n    }),\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,cAAc,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE;IAC1C,WAAW;IACX,aAAa,8HAAA,CAAA,kBAAe,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE;QAC/C,MAAM,CACJ,cACA,iBACA,YACA,YACD,GAAG,MAAM,QAAQ,GAAG,CAAC;YACpB,IAAI,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC;gBAAE,OAAO;oBAAE,UAAU;gBAAK;YAAE;YAChD,IAAI,EAAE,CAAC,QAAQ,CAAC,KAAK;YACrB,IAAI,EAAE,CAAC,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,CAAC,WAAW,CAAC,KAAK;SACzB;QAED,OAAO;YACL;YACA;YACA;YACA;QACF;IACF;IAEA,SAAS;IACT,kBAAkB,8HAAA,CAAA,kBAAe,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE;QACpD,MAAM,gBAAgB,MAAM,IAAI,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACnD,QAAQ;gBACN,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,QAAQ;oBACN,QAAQ;wBAAE,SAAS;oBAAK;gBAC1B;YACF;YACA,SAAS;gBACP,SAAS;oBACP,QAAQ;gBACV;YACF;QACF;QAEA,OAAO,cAAc,GAAG,CAAC,CAAA,WAAY,CAAC;gBACpC,IAAI,SAAS,EAAE;gBACf,MAAM,SAAS,IAAI;gBACnB,OAAO,SAAS,KAAK;gBACrB,MAAM,SAAS,IAAI;gBACnB,aAAa,SAAS,MAAM,CAAC,OAAO;YACtC,CAAC;IACH;IAEA,gBAAgB;IAChB,eAAe,8HAAA,CAAA,kBAAe,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE;QACjD,MAAM,gBAAgB,IAAI;QAC1B,cAAc,OAAO,CAAC,cAAc,OAAO,KAAK;QAEhD,MAAM,YAAY,MAAM,IAAI,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC;YAClD,OAAO;gBACL,WAAW;oBACT,KAAK;gBACP;YACF;YACA,QAAQ;gBACN,WAAW;YACb;YACA,SAAS;gBACP,WAAW;YACb;QACF;QAEA,UAAU;QACV,MAAM,aAAa,IAAI;QAEvB,cAAc;QACd,IAAK,IAAI,IAAI,IAAI,KAAK,GAAG,IAAK;YAC5B,MAAM,OAAO,IAAI;YACjB,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK;YAC9B,MAAM,UAAU,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAChD,WAAW,GAAG,CAAC,SAAU;QAC3B;QAEA,WAAW;QACX,UAAU,OAAO,CAAC,CAAA;YAChB,MAAM,UAAU,MAAM,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC3D,IAAI,SAAS;gBACX,WAAW,GAAG,CAAC,SAAS,CAAC,WAAW,GAAG,CAAC,YAAY,CAAC,IAAI;YAC3D;QACF;QAEA,OAAO,MAAM,IAAI,CAAC,WAAW,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,MAAM,MAAM,GAAK,CAAC;gBAC9D;gBACA;YACF,CAAC;IACH;IAEA,SAAS;IACT,gBAAgB,8HAAA,CAAA,kBAAe,CAC5B,KAAK,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,OAAO,CAAC;IAAI,IAC9D,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC1B,MAAM,UAAU,MAAM,IAAI,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC;YAC3C,OAAO;gBAAE,UAAU;YAAK;YACxB,QAAQ;gBAAE,MAAM;YAAK;QACvB;QAEA,WAAW;QACX,MAAM,WAAW,IAAI;QACrB,QAAQ,OAAO,CAAC,CAAA;YACd,OAAO,IAAI,CAAC,OAAO,CAAC,CAAA;gBAClB,SAAS,GAAG,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,QAAQ,CAAC,IAAI;YAC/C;QACF;QAEA,WAAW;QACX,OAAO,MAAM,IAAI,CAAC,SAAS,OAAO,IAC/B,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,EAC1B,KAAK,CAAC,GAAG,MAAM,KAAK,EACpB,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,GAAK,CAAC;gBAAE;gBAAK;YAAM,CAAC;IAC1C;IAEF,iBAAiB;IACjB,YAAY,8HAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE;QACjD,MAAM,SAAS,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE;QAElC,MAAM,CACJ,WACA,cACA,UACA,kBACD,GAAG,MAAM,QAAQ,GAAG,CAAC;YACpB,IAAI,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC;gBAAE,OAAO;oBAAE,aAAa;gBAAO;YAAE;YACrD,IAAI,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC;gBAAE,OAAO;oBAAE,aAAa;gBAAO;YAAE;YACvD,IAAI,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC;gBAAE,OAAO;oBAAE;gBAAO;YAAE;YAC7C,IAAI,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC;gBACtB,OAAO;oBAAE,aAAa;gBAAO;gBAC7B,MAAM;oBAAE,YAAY;gBAAK;YAC3B;SACD;QAED,YAAY;QACZ,MAAM,kBAAkB,MAAM,IAAI,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACrD,OAAO;gBAAE,aAAa;YAAO;YAC7B,QAAQ;gBACN,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,QAAQ;oBACN,QAAQ;wBAAE,SAAS;oBAAK;gBAC1B;YACF;YACA,SAAS;gBACP,SAAS;oBACP,QAAQ;gBACV;YACF;YACA,MAAM;QACR;QAEA,cAAc;QACd,MAAM,eAAe,MAAM,IAAI,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC;YAChD,OAAO;gBAAE,aAAa;YAAO;YAC7B,QAAQ;gBACN,IAAI;gBACJ,OAAO;gBACP,YAAY;gBACZ,UAAU;oBACR,QAAQ;wBACN,MAAM;wBACN,OAAO;oBACT;gBACF;YACF;YACA,SAAS;gBAAE,YAAY;YAAO;YAC9B,MAAM;QACR;QAEA,OAAO;YACL;YACA;YACA;YACA,mBAAmB,kBAAkB,IAAI,CAAC,UAAU,IAAI;YACxD,iBAAiB,gBAAgB,GAAG,CAAC,CAAA,MAAO,CAAC;oBAC3C,IAAI,IAAI,EAAE;oBACV,MAAM,IAAI,IAAI;oBACd,OAAO,IAAI,KAAK;oBAChB,aAAa,IAAI,MAAM,CAAC,OAAO;gBACjC,CAAC;YACD;QACF;IACF;IAEA,mBAAmB;IACnB,gBAAgB,8HAAA,CAAA,qBAAkB,CAC/B,KAAK,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,OAAO,CAAC;IAAI,IAC9D,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC1B,MAAM,eAAe,MAAM,IAAI,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC;YACrD,OAAO;gBAAE,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE;YAAC;YACrC,MAAM,MAAM,KAAK;YACjB,SAAS;gBAAE,WAAW;YAAO;YAC7B,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,aAAa;wBACb,UAAU;4BACR,QAAQ;gCACN,MAAM;gCACN,OAAO;gCACP,MAAM;4BACR;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAO,aAAa,GAAG,CAAC,CAAA,QAAS,CAAC;gBAChC,IAAI,MAAM,EAAE;gBACZ,QAAQ,MAAM,SAAS;gBACvB,QAAQ,MAAM,MAAM;YACtB,CAAC;IACH;IAEF,UAAU;IACV,qBAAqB,8HAAA,CAAA,kBAAe,CACjC,KAAK,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,OAAO,CAAC;IAAI,IAC9D,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC1B,OAAO,IAAI,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC;YAC5B,OAAO;gBAAE,UAAU;YAAK;YACxB,MAAM,MAAM,KAAK;YACjB,SAAS;gBAAE,YAAY;YAAO;YAC9B,QAAQ;gBACN,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,YAAY;gBACZ,UAAU;oBACR,QAAQ;wBACN,MAAM;wBACN,OAAO;wBACP,MAAM;oBACR;gBACF;gBACA,WAAW;oBACT,QAAQ;wBACN,MAAM;wBACN,OAAO;oBACT;gBACF;YACF;QACF;IACF;AACJ", "debugId": null}}, {"offset": {"line": 1137, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/server/api/root.ts"], "sourcesContent": ["import { categoryRouter } from \"~/server/api/routers/category\";\nimport { promptRouter } from \"~/server/api/routers/prompt\";\nimport { statsRouter } from \"~/server/api/routers/stats\";\nimport { createCallerFactory, createTRPCRouter } from \"~/server/api/trpc\";\n\n/**\n * This is the primary router for your server.\n *\n * All routers added in /api/routers should be manually added here.\n */\nexport const appRouter = createTRPCRouter({\n  category: categoryRouter,\n  prompt: promptRouter,\n  stats: statsRouter,\n});\n\n// export type definition of API\nexport type AppRouter = typeof appRouter;\n\n/**\n * Create a server-side caller for the tRPC API.\n * @example\n * const trpc = createCaller(createContext);\n * const res = await trpc.post.all();\n *       ^? Post[]\n */\nexport const createCaller = createCallerFactory(appRouter);\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAOO,MAAM,YAAY,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE;IACxC,UAAU,6IAAA,CAAA,iBAAc;IACxB,QAAQ,2IAAA,CAAA,eAAY;IACpB,OAAO,0IAAA,CAAA,cAAW;AACpB;AAYO,MAAM,eAAe,CAAA,GAAA,8HAAA,CAAA,sBAAmB,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 1159, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/app/api/trpc/%5Btrpc%5D/route.ts"], "sourcesContent": ["import { fetchRe<PERSON><PERSON><PERSON><PERSON> } from \"@trpc/server/adapters/fetch\";\nimport { type NextRequest } from \"next/server\";\n\nimport { env } from \"~/env\";\nimport { appRouter } from \"~/server/api/root\";\nimport { createTRPCContext } from \"~/server/api/trpc\";\n\n/**\n * This wraps the `createTRPCContext` helper and provides the required context for the tRPC API when\n * handling a HTTP request (e.g. when you make requests from Client Components).\n */\nconst createContext = async (req: NextRequest) => {\n  return createTRPCContext({\n    headers: req.headers,\n  });\n};\n\nconst handler = (req: NextRequest) =>\n  fetchRequestHandler({\n    endpoint: \"/api/trpc\",\n    req,\n    router: appRouter,\n    createContext: () => createContext(req),\n    onError:\n      env.NODE_ENV === \"development\"\n        ? ({ path, error }) => {\n            console.error(\n              `❌ tRPC failed on ${path ?? \"<no-path>\"}: ${error.message}`,\n            );\n          }\n        : undefined,\n  });\n\nexport { handler as GET, handler as <PERSON><PERSON><PERSON> };\n"], "names": [], "mappings": ";;;;AAAA;AAGA;AACA;AACA;;;;;AAEA;;;CAGC,GACD,MAAM,gBAAgB,OAAO;IAC3B,OAAO,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD,EAAE;QACvB,SAAS,IAAI,OAAO;IACtB;AACF;AAEA,MAAM,UAAU,CAAC,MACf,CAAA,GAAA,yKAAA,CAAA,sBAAmB,AAAD,EAAE;QAClB,UAAU;QACV;QACA,QAAQ,8HAAA,CAAA,YAAS;QACjB,eAAe,IAAM,cAAc;QACnC,SACE,4GAAA,CAAA,MAAG,CAAC,QAAQ,KAAK,gBACb,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE;YACd,QAAQ,KAAK,CACX,CAAC,iBAAiB,EAAE,QAAQ,YAAY,EAAE,EAAE,MAAM,OAAO,EAAE;QAE/D,IACA;IACR", "debugId": null}}]}