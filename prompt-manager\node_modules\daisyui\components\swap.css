/*! 🌼 daisyUI 5.0.45 - MIT License */ @layer utilities{.swap{cursor:pointer;vertical-align:middle;-webkit-user-select:none;user-select:none;place-content:center;display:inline-grid;position:relative;& input{appearance:none;border:none}&>*{grid-row-start:1;grid-column-start:1;transition-property:transform,rotate,opacity;transition-duration:.2s;transition-timing-function:cubic-bezier(0,0,.2,1)}& .swap-on,& .swap-indeterminate,& input:indeterminate~.swap-on{opacity:0}& input:is(:checked,:indeterminate){&~.swap-off{opacity:0}}& input:checked~.swap-on,& input:indeterminate~.swap-indeterminate{opacity:1;backface-visibility:visible}}.swap-active{& .swap-off{opacity:0}& .swap-on{opacity:1}}.swap-rotate{& .swap-on,& input:indeterminate~.swap-on{rotate:45deg}& input:is(:checked,:indeterminate)~.swap-on,&.swap-active .swap-on{rotate:none}& input:is(:checked,:indeterminate)~.swap-off,&.swap-active .swap-off{rotate:-45deg}}.swap-flip{transform-style:preserve-3d;perspective:20rem;& .swap-on,& .swap-indeterminate,& input:indeterminate~.swap-on{backface-visibility:hidden;transform:rotateY(180deg)}& input:is(:checked,:indeterminate)~.swap-on,&.swap-active .swap-on{transform:rotateY(0)}& input:is(:checked,:indeterminate)~.swap-off,&.swap-active .swap-off{backface-visibility:hidden;opacity:1;transform:rotateY(-180deg)}}}