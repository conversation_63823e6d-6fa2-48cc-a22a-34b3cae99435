import _extends from "@babel/runtime/helpers/extends";
import _objectWithoutPropertiesLoose from "@babel/runtime/helpers/objectWithoutPropertiesLoose";
var _excluded = ["rehypePlugins"];
import React from 'react';
import Editor from "./Editor.js";
import rehypePrism from 'rehype-prism-plus';
import { jsx as _jsx } from "react/jsx-runtime";
export * from "./Editor.js";
export default /*#__PURE__*/React.forwardRef((props, ref) => {
  var {
      rehypePlugins = [[rehypePrism, {
        ignoreMissing: true
      }]]
    } = props,
    reset = _objectWithoutPropertiesLoose(props, _excluded);
  return /*#__PURE__*/_jsx(Editor, _extends({}, reset, {
    rehypePlugins: rehypePlugins,
    ref: ref
  }));
});