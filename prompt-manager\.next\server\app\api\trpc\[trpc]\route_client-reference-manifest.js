globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/api/trpc/[trpc]/route"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/trpc/react.tsx":{"*":{"id":"(ssr)/./src/trpc/react.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/HydrationBoundary.js":{"*":{"id":"(ssr)/./node_modules/@tanstack/react-query/build/modern/HydrationBoundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/IsRestoringProvider.js":{"*":{"id":"(ssr)/./node_modules/@tanstack/react-query/build/modern/IsRestoringProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js":{"*":{"id":"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js":{"*":{"id":"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useInfiniteQuery.js":{"*":{"id":"(ssr)/./node_modules/@tanstack/react-query/build/modern/useInfiniteQuery.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useIsFetching.js":{"*":{"id":"(ssr)/./node_modules/@tanstack/react-query/build/modern/useIsFetching.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js":{"*":{"id":"(ssr)/./node_modules/@tanstack/react-query/build/modern/useMutation.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutationState.js":{"*":{"id":"(ssr)/./node_modules/@tanstack/react-query/build/modern/useMutationState.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQueries.js":{"*":{"id":"(ssr)/./node_modules/@tanstack/react-query/build/modern/useQueries.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js":{"*":{"id":"(ssr)/./node_modules/@tanstack/react-query/build/modern/useQuery.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useSuspenseInfiniteQuery.js":{"*":{"id":"(ssr)/./node_modules/@tanstack/react-query/build/modern/useSuspenseInfiniteQuery.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useSuspenseQueries.js":{"*":{"id":"(ssr)/./node_modules/@tanstack/react-query/build/modern/useSuspenseQueries.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useSuspenseQuery.js":{"*":{"id":"(ssr)/./node_modules/@tanstack/react-query/build/modern/useSuspenseQuery.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/_components/HomePage.tsx":{"*":{"id":"(ssr)/./src/app/_components/HomePage.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"*":{"id":"(ssr)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js":{"*":{"id":"(ssr)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\react-hot-toast\\dist\\index.mjs":{"id":"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\src\\styles\\globals.css":{"id":"(app-pages-browser)/./src/styles/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-geist-sans\"}],\"variableName\":\"geist\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-geist-sans\"}],\"variableName\":\"geist\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\src\\trpc\\react.tsx":{"id":"(app-pages-browser)/./src/trpc/react.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\@tanstack\\react-query\\build\\modern\\HydrationBoundary.js":{"id":"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/HydrationBoundary.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\@tanstack\\react-query\\build\\modern\\IsRestoringProvider.js":{"id":"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/IsRestoringProvider.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\@tanstack\\react-query\\build\\modern\\QueryClientProvider.js":{"id":"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\@tanstack\\react-query\\build\\modern\\QueryErrorResetBoundary.js":{"id":"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\@tanstack\\react-query\\build\\modern\\useInfiniteQuery.js":{"id":"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useInfiniteQuery.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\@tanstack\\react-query\\build\\modern\\useIsFetching.js":{"id":"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useIsFetching.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\@tanstack\\react-query\\build\\modern\\useMutation.js":{"id":"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\@tanstack\\react-query\\build\\modern\\useMutationState.js":{"id":"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutationState.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\@tanstack\\react-query\\build\\modern\\useQueries.js":{"id":"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQueries.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\@tanstack\\react-query\\build\\modern\\useQuery.js":{"id":"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\@tanstack\\react-query\\build\\modern\\useSuspenseInfiniteQuery.js":{"id":"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useSuspenseInfiniteQuery.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\@tanstack\\react-query\\build\\modern\\useSuspenseQueries.js":{"id":"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useSuspenseQueries.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\@tanstack\\react-query\\build\\modern\\useSuspenseQuery.js":{"id":"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useSuspenseQuery.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\src\\app\\_components\\HomePage.tsx":{"id":"(app-pages-browser)/./src/app/_components/HomePage.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\next\\dist\\client\\components\\builtin\\global-error.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\next\\dist\\esm\\client\\components\\builtin\\global-error.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\next\\dist\\lib\\metadata\\generate\\icon-mark.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\next\\dist\\esm\\lib\\metadata\\generate\\icon-mark.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\next\\dist\\next-devtools\\userspace\\app\\segment-explorer-node.js":{"id":"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\node_modules\\next\\dist\\esm\\next-devtools\\userspace\\app\\segment-explorer-node.js":{"id":"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false}},"entryCSSFiles":{"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\src\\":[],"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\src\\app\\page":[{"inlined":false,"path":"static/css/app/page.css"}],"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\src\\app\\api\\trpc\\[trpc]\\route":[]},"rscModuleMapping":{"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/styles/globals.css":{"*":{"id":"(rsc)/./src/styles/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/trpc/react.tsx":{"*":{"id":"(rsc)/./src/trpc/react.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/HydrationBoundary.js":{"*":{"id":"(rsc)/./node_modules/@tanstack/react-query/build/modern/HydrationBoundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/IsRestoringProvider.js":{"*":{"id":"(rsc)/./node_modules/@tanstack/react-query/build/modern/IsRestoringProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js":{"*":{"id":"(rsc)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js":{"*":{"id":"(rsc)/./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useInfiniteQuery.js":{"*":{"id":"(rsc)/./node_modules/@tanstack/react-query/build/modern/useInfiniteQuery.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useIsFetching.js":{"*":{"id":"(rsc)/./node_modules/@tanstack/react-query/build/modern/useIsFetching.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js":{"*":{"id":"(rsc)/./node_modules/@tanstack/react-query/build/modern/useMutation.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutationState.js":{"*":{"id":"(rsc)/./node_modules/@tanstack/react-query/build/modern/useMutationState.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQueries.js":{"*":{"id":"(rsc)/./node_modules/@tanstack/react-query/build/modern/useQueries.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js":{"*":{"id":"(rsc)/./node_modules/@tanstack/react-query/build/modern/useQuery.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useSuspenseInfiniteQuery.js":{"*":{"id":"(rsc)/./node_modules/@tanstack/react-query/build/modern/useSuspenseInfiniteQuery.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useSuspenseQueries.js":{"*":{"id":"(rsc)/./node_modules/@tanstack/react-query/build/modern/useSuspenseQueries.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useSuspenseQuery.js":{"*":{"id":"(rsc)/./node_modules/@tanstack/react-query/build/modern/useSuspenseQuery.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/_components/HomePage.tsx":{"*":{"id":"(rsc)/./src/app/_components/HomePage.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"*":{"id":"(rsc)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js":{"*":{"id":"(rsc)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}