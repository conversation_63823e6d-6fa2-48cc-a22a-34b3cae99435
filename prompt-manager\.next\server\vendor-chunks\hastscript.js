"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hastscript";
exports.ids = ["vendor-chunks/hastscript"];
exports.modules = {

/***/ "(ssr)/./node_modules/hastscript/lib/create-h.js":
/*!*************************************************!*\
  !*** ./node_modules/hastscript/lib/create-h.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createH: () => (/* binding */ createH)\n/* harmony export */ });\n/* harmony import */ var comma_separated_tokens__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! comma-separated-tokens */ \"(ssr)/./node_modules/comma-separated-tokens/index.js\");\n/* harmony import */ var hast_util_parse_selector__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-parse-selector */ \"(ssr)/./node_modules/hast-util-parse-selector/lib/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/lib/find.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/lib/normalize.js\");\n/* harmony import */ var space_separated_tokens__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! space-separated-tokens */ \"(ssr)/./node_modules/space-separated-tokens/index.js\");\n/**\n * @import {Element, Nodes, RootContent, Root} from 'hast'\n * @import {Info, Schema} from 'property-information'\n */\n\n/**\n * @typedef {Array<Nodes | PrimitiveChild>} ArrayChildNested\n *   List of children (deep).\n */\n\n/**\n * @typedef {Array<ArrayChildNested | Nodes | PrimitiveChild>} ArrayChild\n *   List of children.\n */\n\n/**\n * @typedef {Array<number | string>} ArrayValue\n *   List of property values for space- or comma separated values (such as `className`).\n */\n\n/**\n * @typedef {ArrayChild | Nodes | PrimitiveChild} Child\n *   Acceptable child value.\n */\n\n/**\n * @typedef {number | string | null | undefined} PrimitiveChild\n *   Primitive children, either ignored (nullish), or turned into text nodes.\n */\n\n/**\n * @typedef {boolean | number | string | null | undefined} PrimitiveValue\n *   Primitive property value.\n */\n\n/**\n * @typedef {Record<string, PropertyValue | Style>} Properties\n *   Acceptable value for element properties.\n */\n\n/**\n * @typedef {ArrayValue | PrimitiveValue} PropertyValue\n *   Primitive value or list value.\n */\n\n/**\n * @typedef {Element | Root} Result\n *   Result from a `h` (or `s`) call.\n */\n\n/**\n * @typedef {number | string} StyleValue\n *   Value for a CSS style field.\n */\n\n/**\n * @typedef {Record<string, StyleValue>} Style\n *   Supported value of a `style` prop.\n */\n\n\n\n\n\n\n/**\n * @param {Schema} schema\n *   Schema to use.\n * @param {string} defaultTagName\n *   Default tag name.\n * @param {ReadonlyArray<string> | undefined} [caseSensitive]\n *   Case-sensitive tag names (default: `undefined`).\n * @returns\n *   `h`.\n */\nfunction createH(schema, defaultTagName, caseSensitive) {\n  const adjust = caseSensitive ? createAdjustMap(caseSensitive) : undefined\n\n  /**\n   * Hyperscript compatible DSL for creating virtual hast trees.\n   *\n   * @overload\n   * @param {null | undefined} [selector]\n   * @param {...Child} children\n   * @returns {Root}\n   *\n   * @overload\n   * @param {string} selector\n   * @param {Properties} properties\n   * @param {...Child} children\n   * @returns {Element}\n   *\n   * @overload\n   * @param {string} selector\n   * @param {...Child} children\n   * @returns {Element}\n   *\n   * @param {string | null | undefined} [selector]\n   *   Selector.\n   * @param {Child | Properties | null | undefined} [properties]\n   *   Properties (or first child) (default: `undefined`).\n   * @param {...Child} children\n   *   Children.\n   * @returns {Result}\n   *   Result.\n   */\n  function h(selector, properties, ...children) {\n    /** @type {Result} */\n    let node\n\n    if (selector === null || selector === undefined) {\n      node = {type: 'root', children: []}\n      // Properties are not supported for roots.\n      const child = /** @type {Child} */ (properties)\n      children.unshift(child)\n    } else {\n      node = (0,hast_util_parse_selector__WEBPACK_IMPORTED_MODULE_0__.parseSelector)(selector, defaultTagName)\n      // Normalize the name.\n      const lower = node.tagName.toLowerCase()\n      const adjusted = adjust ? adjust.get(lower) : undefined\n      node.tagName = adjusted || lower\n\n      // Handle properties.\n      if (isChild(properties)) {\n        children.unshift(properties)\n      } else {\n        for (const [key, value] of Object.entries(properties)) {\n          addProperty(schema, node.properties, key, value)\n        }\n      }\n    }\n\n    // Handle children.\n    for (const child of children) {\n      addChild(node.children, child)\n    }\n\n    if (node.type === 'element' && node.tagName === 'template') {\n      node.content = {type: 'root', children: node.children}\n      node.children = []\n    }\n\n    return node\n  }\n\n  return h\n}\n\n/**\n * Check if something is properties or a child.\n *\n * @param {Child | Properties} value\n *   Value to check.\n * @returns {value is Child}\n *   Whether `value` is definitely a child.\n */\nfunction isChild(value) {\n  // Never properties if not an object.\n  if (value === null || typeof value !== 'object' || Array.isArray(value)) {\n    return true\n  }\n\n  // Never node without `type`; that’s the main discriminator.\n  if (typeof value.type !== 'string') return false\n\n  // Slower check: never property value if object or array with\n  // non-number/strings.\n  const record = /** @type {Record<string, unknown>} */ (value)\n  const keys = Object.keys(value)\n\n  for (const key of keys) {\n    const value = record[key]\n\n    if (value && typeof value === 'object') {\n      if (!Array.isArray(value)) return true\n\n      const list = /** @type {ReadonlyArray<unknown>} */ (value)\n\n      for (const item of list) {\n        if (typeof item !== 'number' && typeof item !== 'string') {\n          return true\n        }\n      }\n    }\n  }\n\n  // Also see empty `children` as a node.\n  if ('children' in value && Array.isArray(value.children)) {\n    return true\n  }\n\n  // Default to properties, someone can always pass an empty object,\n  // put `data: {}` in a node,\n  // or wrap it in an array.\n  return false\n}\n\n/**\n * @param {Schema} schema\n *   Schema.\n * @param {Properties} properties\n *   Properties object.\n * @param {string} key\n *   Property name.\n * @param {PropertyValue | Style} value\n *   Property value.\n * @returns {undefined}\n *   Nothing.\n */\nfunction addProperty(schema, properties, key, value) {\n  const info = (0,property_information__WEBPACK_IMPORTED_MODULE_1__.find)(schema, key)\n  /** @type {PropertyValue} */\n  let result\n\n  // Ignore nullish and NaN values.\n  if (value === null || value === undefined) return\n\n  if (typeof value === 'number') {\n    // Ignore NaN.\n    if (Number.isNaN(value)) return\n\n    result = value\n  }\n  // Booleans.\n  else if (typeof value === 'boolean') {\n    result = value\n  }\n  // Handle list values.\n  else if (typeof value === 'string') {\n    if (info.spaceSeparated) {\n      result = (0,space_separated_tokens__WEBPACK_IMPORTED_MODULE_2__.parse)(value)\n    } else if (info.commaSeparated) {\n      result = (0,comma_separated_tokens__WEBPACK_IMPORTED_MODULE_3__.parse)(value)\n    } else if (info.commaOrSpaceSeparated) {\n      result = (0,space_separated_tokens__WEBPACK_IMPORTED_MODULE_2__.parse)((0,comma_separated_tokens__WEBPACK_IMPORTED_MODULE_3__.parse)(value).join(' '))\n    } else {\n      result = parsePrimitive(info, info.property, value)\n    }\n  } else if (Array.isArray(value)) {\n    result = [...value]\n  } else {\n    result = info.property === 'style' ? style(value) : String(value)\n  }\n\n  if (Array.isArray(result)) {\n    /** @type {Array<number | string>} */\n    const finalResult = []\n\n    for (const item of result) {\n      // Assume no booleans in array.\n      finalResult.push(\n        /** @type {number | string} */ (\n          parsePrimitive(info, info.property, item)\n        )\n      )\n    }\n\n    result = finalResult\n  }\n\n  // Class names (which can be added both on the `selector` and here).\n  if (info.property === 'className' && Array.isArray(properties.className)) {\n    // Assume no booleans in `className`.\n    result = properties.className.concat(\n      /** @type {Array<number | string> | number | string} */ (result)\n    )\n  }\n\n  properties[info.property] = result\n}\n\n/**\n * @param {Array<RootContent>} nodes\n *   Children.\n * @param {Child} value\n *   Child.\n * @returns {undefined}\n *   Nothing.\n */\nfunction addChild(nodes, value) {\n  if (value === null || value === undefined) {\n    // Empty.\n  } else if (typeof value === 'number' || typeof value === 'string') {\n    nodes.push({type: 'text', value: String(value)})\n  } else if (Array.isArray(value)) {\n    for (const child of value) {\n      addChild(nodes, child)\n    }\n  } else if (typeof value === 'object' && 'type' in value) {\n    if (value.type === 'root') {\n      addChild(nodes, value.children)\n    } else {\n      nodes.push(value)\n    }\n  } else {\n    throw new Error('Expected node, nodes, or string, got `' + value + '`')\n  }\n}\n\n/**\n * Parse a single primitives.\n *\n * @param {Info} info\n *   Property information.\n * @param {string} name\n *   Property name.\n * @param {PrimitiveValue} value\n *   Property value.\n * @returns {PrimitiveValue}\n *   Property value.\n */\nfunction parsePrimitive(info, name, value) {\n  if (typeof value === 'string') {\n    if (info.number && value && !Number.isNaN(Number(value))) {\n      return Number(value)\n    }\n\n    if (\n      (info.boolean || info.overloadedBoolean) &&\n      (value === '' || (0,property_information__WEBPACK_IMPORTED_MODULE_4__.normalize)(value) === (0,property_information__WEBPACK_IMPORTED_MODULE_4__.normalize)(name))\n    ) {\n      return true\n    }\n  }\n\n  return value\n}\n\n/**\n * Serialize a `style` object as a string.\n *\n * @param {Style} styles\n *   Style object.\n * @returns {string}\n *   CSS string.\n */\nfunction style(styles) {\n  /** @type {Array<string>} */\n  const result = []\n\n  for (const [key, value] of Object.entries(styles)) {\n    result.push([key, value].join(': '))\n  }\n\n  return result.join('; ')\n}\n\n/**\n * Create a map to adjust casing.\n *\n * @param {ReadonlyArray<string>} values\n *   List of properly cased keys.\n * @returns {Map<string, string>}\n *   Map of lowercase keys to uppercase keys.\n */\nfunction createAdjustMap(values) {\n  /** @type {Map<string, string>} */\n  const result = new Map()\n\n  for (const value of values) {\n    result.set(value.toLowerCase(), value)\n  }\n\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/lib/create-h.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/lib/index.js":
/*!**********************************************!*\
  !*** ./node_modules/hastscript/lib/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   h: () => (/* binding */ h),\n/* harmony export */   s: () => (/* binding */ s)\n/* harmony export */ });\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/index.js\");\n/* harmony import */ var _create_h_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./create-h.js */ \"(ssr)/./node_modules/hastscript/lib/create-h.js\");\n/* harmony import */ var _svg_case_sensitive_tag_names_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./svg-case-sensitive-tag-names.js */ \"(ssr)/./node_modules/hastscript/lib/svg-case-sensitive-tag-names.js\");\n// Register the JSX namespace on `h`.\n/**\n * @typedef {import('./jsx-classic.js').Element} h.JSX.Element\n * @typedef {import('./jsx-classic.js').ElementChildrenAttribute} h.JSX.ElementChildrenAttribute\n * @typedef {import('./jsx-classic.js').IntrinsicAttributes} h.JSX.IntrinsicAttributes\n * @typedef {import('./jsx-classic.js').IntrinsicElements} h.JSX.IntrinsicElements\n */\n\n// Register the JSX namespace on `s`.\n/**\n * @typedef {import('./jsx-classic.js').Element} s.JSX.Element\n * @typedef {import('./jsx-classic.js').ElementChildrenAttribute} s.JSX.ElementChildrenAttribute\n * @typedef {import('./jsx-classic.js').IntrinsicAttributes} s.JSX.IntrinsicAttributes\n * @typedef {import('./jsx-classic.js').IntrinsicElements} s.JSX.IntrinsicElements\n */\n\n\n\n\n\n// Note: this explicit type is needed, otherwise TS creates broken types.\n/** @type {ReturnType<createH>} */\nconst h = (0,_create_h_js__WEBPACK_IMPORTED_MODULE_0__.createH)(property_information__WEBPACK_IMPORTED_MODULE_1__.html, 'div')\n\n// Note: this explicit type is needed, otherwise TS creates broken types.\n/** @type {ReturnType<createH>} */\nconst s = (0,_create_h_js__WEBPACK_IMPORTED_MODULE_0__.createH)(property_information__WEBPACK_IMPORTED_MODULE_1__.svg, 'g', _svg_case_sensitive_tag_names_js__WEBPACK_IMPORTED_MODULE_2__.svgCaseSensitiveTagNames)\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0EsYUFBYSxvQ0FBb0M7QUFDakQsYUFBYSxxREFBcUQ7QUFDbEUsYUFBYSxnREFBZ0Q7QUFDN0QsYUFBYSw4Q0FBOEM7QUFDM0Q7O0FBRUE7QUFDQTtBQUNBLGFBQWEsb0NBQW9DO0FBQ2pELGFBQWEscURBQXFEO0FBQ2xFLGFBQWEsZ0RBQWdEO0FBQzdELGFBQWEsOENBQThDO0FBQzNEOztBQUU4QztBQUNUO0FBQ3FDOztBQUUxRTtBQUNBLFdBQVcscUJBQXFCO0FBQ3pCLFVBQVUscURBQU8sQ0FBQyxzREFBSTs7QUFFN0I7QUFDQSxXQUFXLHFCQUFxQjtBQUN6QixVQUFVLHFEQUFPLENBQUMscURBQUcsT0FBTyxzRkFBd0IiLCJzb3VyY2VzIjpbIkQ6XFxDdXJzb3IgUHJvamVjdFxcd2Vic2l0ZVxcQXVnbWVudDJcXHByb21wdC1tYW5hZ2VyXFxub2RlX21vZHVsZXNcXGhhc3RzY3JpcHRcXGxpYlxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gUmVnaXN0ZXIgdGhlIEpTWCBuYW1lc3BhY2Ugb24gYGhgLlxuLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuL2pzeC1jbGFzc2ljLmpzJykuRWxlbWVudH0gaC5KU1guRWxlbWVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi9qc3gtY2xhc3NpYy5qcycpLkVsZW1lbnRDaGlsZHJlbkF0dHJpYnV0ZX0gaC5KU1guRWxlbWVudENoaWxkcmVuQXR0cmlidXRlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuL2pzeC1jbGFzc2ljLmpzJykuSW50cmluc2ljQXR0cmlidXRlc30gaC5KU1guSW50cmluc2ljQXR0cmlidXRlc1xuICogQHR5cGVkZWYge2ltcG9ydCgnLi9qc3gtY2xhc3NpYy5qcycpLkludHJpbnNpY0VsZW1lbnRzfSBoLkpTWC5JbnRyaW5zaWNFbGVtZW50c1xuICovXG5cbi8vIFJlZ2lzdGVyIHRoZSBKU1ggbmFtZXNwYWNlIG9uIGBzYC5cbi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi9qc3gtY2xhc3NpYy5qcycpLkVsZW1lbnR9IHMuSlNYLkVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4vanN4LWNsYXNzaWMuanMnKS5FbGVtZW50Q2hpbGRyZW5BdHRyaWJ1dGV9IHMuSlNYLkVsZW1lbnRDaGlsZHJlbkF0dHJpYnV0ZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi9qc3gtY2xhc3NpYy5qcycpLkludHJpbnNpY0F0dHJpYnV0ZXN9IHMuSlNYLkludHJpbnNpY0F0dHJpYnV0ZXNcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4vanN4LWNsYXNzaWMuanMnKS5JbnRyaW5zaWNFbGVtZW50c30gcy5KU1guSW50cmluc2ljRWxlbWVudHNcbiAqL1xuXG5pbXBvcnQge2h0bWwsIHN2Z30gZnJvbSAncHJvcGVydHktaW5mb3JtYXRpb24nXG5pbXBvcnQge2NyZWF0ZUh9IGZyb20gJy4vY3JlYXRlLWguanMnXG5pbXBvcnQge3N2Z0Nhc2VTZW5zaXRpdmVUYWdOYW1lc30gZnJvbSAnLi9zdmctY2FzZS1zZW5zaXRpdmUtdGFnLW5hbWVzLmpzJ1xuXG4vLyBOb3RlOiB0aGlzIGV4cGxpY2l0IHR5cGUgaXMgbmVlZGVkLCBvdGhlcndpc2UgVFMgY3JlYXRlcyBicm9rZW4gdHlwZXMuXG4vKiogQHR5cGUge1JldHVyblR5cGU8Y3JlYXRlSD59ICovXG5leHBvcnQgY29uc3QgaCA9IGNyZWF0ZUgoaHRtbCwgJ2RpdicpXG5cbi8vIE5vdGU6IHRoaXMgZXhwbGljaXQgdHlwZSBpcyBuZWVkZWQsIG90aGVyd2lzZSBUUyBjcmVhdGVzIGJyb2tlbiB0eXBlcy5cbi8qKiBAdHlwZSB7UmV0dXJuVHlwZTxjcmVhdGVIPn0gKi9cbmV4cG9ydCBjb25zdCBzID0gY3JlYXRlSChzdmcsICdnJywgc3ZnQ2FzZVNlbnNpdGl2ZVRhZ05hbWVzKVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/lib/svg-case-sensitive-tag-names.js":
/*!*********************************************************************!*\
  !*** ./node_modules/hastscript/lib/svg-case-sensitive-tag-names.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   svgCaseSensitiveTagNames: () => (/* binding */ svgCaseSensitiveTagNames)\n/* harmony export */ });\n/**\n * List of case-sensitive SVG tag names.\n *\n * @type {ReadonlyArray<string>}\n */\nconst svgCaseSensitiveTagNames = [\n  'altGlyph',\n  'altGlyphDef',\n  'altGlyphItem',\n  'animateColor',\n  'animateMotion',\n  'animateTransform',\n  'clipPath',\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feDistantLight',\n  'feDropShadow',\n  'feFlood',\n  'feFuncA',\n  'feFuncB',\n  'feFuncG',\n  'feFuncR',\n  'feGaussianBlur',\n  'feImage',\n  'feMerge',\n  'feMergeNode',\n  'feMorphology',\n  'feOffset',\n  'fePointLight',\n  'feSpecularLighting',\n  'feSpotLight',\n  'feTile',\n  'feTurbulence',\n  'foreignObject',\n  'glyphRef',\n  'linearGradient',\n  'radialGradient',\n  'solidColor',\n  'textArea',\n  'textPath'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9saWIvc3ZnLWNhc2Utc2Vuc2l0aXZlLXRhZy1uYW1lcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXEN1cnNvciBQcm9qZWN0XFx3ZWJzaXRlXFxBdWdtZW50MlxccHJvbXB0LW1hbmFnZXJcXG5vZGVfbW9kdWxlc1xcaGFzdHNjcmlwdFxcbGliXFxzdmctY2FzZS1zZW5zaXRpdmUtdGFnLW5hbWVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogTGlzdCBvZiBjYXNlLXNlbnNpdGl2ZSBTVkcgdGFnIG5hbWVzLlxuICpcbiAqIEB0eXBlIHtSZWFkb25seUFycmF5PHN0cmluZz59XG4gKi9cbmV4cG9ydCBjb25zdCBzdmdDYXNlU2Vuc2l0aXZlVGFnTmFtZXMgPSBbXG4gICdhbHRHbHlwaCcsXG4gICdhbHRHbHlwaERlZicsXG4gICdhbHRHbHlwaEl0ZW0nLFxuICAnYW5pbWF0ZUNvbG9yJyxcbiAgJ2FuaW1hdGVNb3Rpb24nLFxuICAnYW5pbWF0ZVRyYW5zZm9ybScsXG4gICdjbGlwUGF0aCcsXG4gICdmZUJsZW5kJyxcbiAgJ2ZlQ29sb3JNYXRyaXgnLFxuICAnZmVDb21wb25lbnRUcmFuc2ZlcicsXG4gICdmZUNvbXBvc2l0ZScsXG4gICdmZUNvbnZvbHZlTWF0cml4JyxcbiAgJ2ZlRGlmZnVzZUxpZ2h0aW5nJyxcbiAgJ2ZlRGlzcGxhY2VtZW50TWFwJyxcbiAgJ2ZlRGlzdGFudExpZ2h0JyxcbiAgJ2ZlRHJvcFNoYWRvdycsXG4gICdmZUZsb29kJyxcbiAgJ2ZlRnVuY0EnLFxuICAnZmVGdW5jQicsXG4gICdmZUZ1bmNHJyxcbiAgJ2ZlRnVuY1InLFxuICAnZmVHYXVzc2lhbkJsdXInLFxuICAnZmVJbWFnZScsXG4gICdmZU1lcmdlJyxcbiAgJ2ZlTWVyZ2VOb2RlJyxcbiAgJ2ZlTW9ycGhvbG9neScsXG4gICdmZU9mZnNldCcsXG4gICdmZVBvaW50TGlnaHQnLFxuICAnZmVTcGVjdWxhckxpZ2h0aW5nJyxcbiAgJ2ZlU3BvdExpZ2h0JyxcbiAgJ2ZlVGlsZScsXG4gICdmZVR1cmJ1bGVuY2UnLFxuICAnZm9yZWlnbk9iamVjdCcsXG4gICdnbHlwaFJlZicsXG4gICdsaW5lYXJHcmFkaWVudCcsXG4gICdyYWRpYWxHcmFkaWVudCcsXG4gICdzb2xpZENvbG9yJyxcbiAgJ3RleHRBcmVhJyxcbiAgJ3RleHRQYXRoJ1xuXVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/lib/svg-case-sensitive-tag-names.js\n");

/***/ })

};
;