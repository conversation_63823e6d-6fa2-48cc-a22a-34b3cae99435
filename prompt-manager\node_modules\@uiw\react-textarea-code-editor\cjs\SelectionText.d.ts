export declare class SelectionText {
    elm: HTMLTextAreaElement;
    start: number;
    end: number;
    value: string;
    constructor(elm: HTMLTextAreaElement);
    position(start?: number, end?: number): this;
    insertText(text: string): this;
    getSelectedValue(start?: number, end?: number): string;
    getLineStartNumber(): number;
    /** Indent on new lines */
    getIndentText(): string;
    lineStarInstert(text: string): this;
    lineStarRemove(text: string): void;
    /** Notify any possible listeners of the change */
    notifyChange(): void;
}
