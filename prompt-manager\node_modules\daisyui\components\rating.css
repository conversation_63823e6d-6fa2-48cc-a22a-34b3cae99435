/*! 🌼 daisyUI 5.0.45 - MIT License */ @layer utilities{.rating{vertical-align:middle;display:inline-flex;position:relative;& input{appearance:none;border:none}& :where(*){background-color:var(--color-base-content);opacity:.2;border-radius:0;width:1.5rem;height:1.5rem;animation:.25s ease-out rating;&:is(input){cursor:pointer}}& .rating-hidden{background-color:#0000;width:.5rem}& input[type=radio]:checked{background-image:none}& *{&:checked,&[aria-checked=true],&[aria-current=true],&:has(~:checked,~[aria-checked=true],~[aria-current=true]){opacity:1}&:focus-visible{transition:scale .2s ease-out;scale:1.1}}& :active:focus{animation:none;scale:1.1}&.rating-xs :where(:not(.rating-hidden)){width:1rem;height:1rem}&.rating-sm :where(:not(.rating-hidden)){width:1.25rem;height:1.25rem}&.rating-md :where(:not(.rating-hidden)){width:1.5rem;height:1.5rem}&.rating-lg :where(:not(.rating-hidden)){width:1.75rem;height:1.75rem}&.rating-xl :where(:not(.rating-hidden)){width:2rem;height:2rem}}.rating-half{& :where(:not(.rating-hidden)){width:.75rem}}.rating-half{&.rating-xs :not(.rating-hidden){width:.5rem}&.rating-sm :not(.rating-hidden){width:.625rem}&.rating-md :not(.rating-hidden){width:.75rem}&.rating-lg :not(.rating-hidden){width:.875rem}&.rating-xl :not(.rating-hidden){width:1rem}}@keyframes rating{0%,40%{filter:brightness(1.05)contrast(1.05);scale:1.1}}@media (width>=640px){.sm\:rating{vertical-align:middle;display:inline-flex;position:relative;& input{appearance:none;border:none}& :where(*){background-color:var(--color-base-content);opacity:.2;border-radius:0;width:1.5rem;height:1.5rem;animation:.25s ease-out rating;&:is(input){cursor:pointer}}& .rating-hidden{background-color:#0000;width:.5rem}& input[type=radio]:checked{background-image:none}& *{&:checked,&[aria-checked=true],&[aria-current=true],&:has(~:checked,~[aria-checked=true],~[aria-current=true]){opacity:1}&:focus-visible{transition:scale .2s ease-out;scale:1.1}}& :active:focus{animation:none;scale:1.1}&.rating-xs :where(:not(.rating-hidden)){width:1rem;height:1rem}&.rating-sm :where(:not(.rating-hidden)){width:1.25rem;height:1.25rem}&.rating-md :where(:not(.rating-hidden)){width:1.5rem;height:1.5rem}&.rating-lg :where(:not(.rating-hidden)){width:1.75rem;height:1.75rem}&.rating-xl :where(:not(.rating-hidden)){width:2rem;height:2rem}}.sm\:rating-half{& :where(:not(.rating-hidden)){width:.75rem}}.sm\:rating-half{&.rating-xs :not(.rating-hidden){width:.5rem}&.rating-sm :not(.rating-hidden){width:.625rem}&.rating-md :not(.rating-hidden){width:.75rem}&.rating-lg :not(.rating-hidden){width:.875rem}&.rating-xl :not(.rating-hidden){width:1rem}}}@media (width>=768px){.md\:rating{vertical-align:middle;display:inline-flex;position:relative;& input{appearance:none;border:none}& :where(*){background-color:var(--color-base-content);opacity:.2;border-radius:0;width:1.5rem;height:1.5rem;animation:.25s ease-out rating;&:is(input){cursor:pointer}}& .rating-hidden{background-color:#0000;width:.5rem}& input[type=radio]:checked{background-image:none}& *{&:checked,&[aria-checked=true],&[aria-current=true],&:has(~:checked,~[aria-checked=true],~[aria-current=true]){opacity:1}&:focus-visible{transition:scale .2s ease-out;scale:1.1}}& :active:focus{animation:none;scale:1.1}&.rating-xs :where(:not(.rating-hidden)){width:1rem;height:1rem}&.rating-sm :where(:not(.rating-hidden)){width:1.25rem;height:1.25rem}&.rating-md :where(:not(.rating-hidden)){width:1.5rem;height:1.5rem}&.rating-lg :where(:not(.rating-hidden)){width:1.75rem;height:1.75rem}&.rating-xl :where(:not(.rating-hidden)){width:2rem;height:2rem}}.md\:rating-half{& :where(:not(.rating-hidden)){width:.75rem}}.md\:rating-half{&.rating-xs :not(.rating-hidden){width:.5rem}&.rating-sm :not(.rating-hidden){width:.625rem}&.rating-md :not(.rating-hidden){width:.75rem}&.rating-lg :not(.rating-hidden){width:.875rem}&.rating-xl :not(.rating-hidden){width:1rem}}}@media (width>=1024px){.lg\:rating{vertical-align:middle;display:inline-flex;position:relative;& input{appearance:none;border:none}& :where(*){background-color:var(--color-base-content);opacity:.2;border-radius:0;width:1.5rem;height:1.5rem;animation:.25s ease-out rating;&:is(input){cursor:pointer}}& .rating-hidden{background-color:#0000;width:.5rem}& input[type=radio]:checked{background-image:none}& *{&:checked,&[aria-checked=true],&[aria-current=true],&:has(~:checked,~[aria-checked=true],~[aria-current=true]){opacity:1}&:focus-visible{transition:scale .2s ease-out;scale:1.1}}& :active:focus{animation:none;scale:1.1}&.rating-xs :where(:not(.rating-hidden)){width:1rem;height:1rem}&.rating-sm :where(:not(.rating-hidden)){width:1.25rem;height:1.25rem}&.rating-md :where(:not(.rating-hidden)){width:1.5rem;height:1.5rem}&.rating-lg :where(:not(.rating-hidden)){width:1.75rem;height:1.75rem}&.rating-xl :where(:not(.rating-hidden)){width:2rem;height:2rem}}.lg\:rating-half{& :where(:not(.rating-hidden)){width:.75rem}}.lg\:rating-half{&.rating-xs :not(.rating-hidden){width:.5rem}&.rating-sm :not(.rating-hidden){width:.625rem}&.rating-md :not(.rating-hidden){width:.75rem}&.rating-lg :not(.rating-hidden){width:.875rem}&.rating-xl :not(.rating-hidden){width:1rem}}}@media (width>=1280px){.xl\:rating{vertical-align:middle;display:inline-flex;position:relative;& input{appearance:none;border:none}& :where(*){background-color:var(--color-base-content);opacity:.2;border-radius:0;width:1.5rem;height:1.5rem;animation:.25s ease-out rating;&:is(input){cursor:pointer}}& .rating-hidden{background-color:#0000;width:.5rem}& input[type=radio]:checked{background-image:none}& *{&:checked,&[aria-checked=true],&[aria-current=true],&:has(~:checked,~[aria-checked=true],~[aria-current=true]){opacity:1}&:focus-visible{transition:scale .2s ease-out;scale:1.1}}& :active:focus{animation:none;scale:1.1}&.rating-xs :where(:not(.rating-hidden)){width:1rem;height:1rem}&.rating-sm :where(:not(.rating-hidden)){width:1.25rem;height:1.25rem}&.rating-md :where(:not(.rating-hidden)){width:1.5rem;height:1.5rem}&.rating-lg :where(:not(.rating-hidden)){width:1.75rem;height:1.75rem}&.rating-xl :where(:not(.rating-hidden)){width:2rem;height:2rem}}.xl\:rating-half{& :where(:not(.rating-hidden)){width:.75rem}}.xl\:rating-half{&.rating-xs :not(.rating-hidden){width:.5rem}&.rating-sm :not(.rating-hidden){width:.625rem}&.rating-md :not(.rating-hidden){width:.75rem}&.rating-lg :not(.rating-hidden){width:.875rem}&.rating-xl :not(.rating-hidden){width:1rem}}}@media (width>=1536px){.\32 xl\:rating{vertical-align:middle;display:inline-flex;position:relative;& input{appearance:none;border:none}& :where(*){background-color:var(--color-base-content);opacity:.2;border-radius:0;width:1.5rem;height:1.5rem;animation:.25s ease-out rating;&:is(input){cursor:pointer}}& .rating-hidden{background-color:#0000;width:.5rem}& input[type=radio]:checked{background-image:none}& *{&:checked,&[aria-checked=true],&[aria-current=true],&:has(~:checked,~[aria-checked=true],~[aria-current=true]){opacity:1}&:focus-visible{transition:scale .2s ease-out;scale:1.1}}& :active:focus{animation:none;scale:1.1}&.rating-xs :where(:not(.rating-hidden)){width:1rem;height:1rem}&.rating-sm :where(:not(.rating-hidden)){width:1.25rem;height:1.25rem}&.rating-md :where(:not(.rating-hidden)){width:1.5rem;height:1.5rem}&.rating-lg :where(:not(.rating-hidden)){width:1.75rem;height:1.75rem}&.rating-xl :where(:not(.rating-hidden)){width:2rem;height:2rem}}.\32 xl\:rating-half{& :where(:not(.rating-hidden)){width:.75rem}}.\32 xl\:rating-half{&.rating-xs :not(.rating-hidden){width:.5rem}&.rating-sm :not(.rating-hidden){width:.625rem}&.rating-md :not(.rating-hidden){width:.75rem}&.rating-lg :not(.rating-hidden){width:.875rem}&.rating-xl :not(.rating-hidden){width:1rem}}}}