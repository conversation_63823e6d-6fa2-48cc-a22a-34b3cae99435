"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/EditCategoryModal.tsx":
/*!**********************************************!*\
  !*** ./src/components/EditCategoryModal.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EditCategoryModal: () => (/* binding */ EditCategoryModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_ui_Modal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ~/components/ui/Modal */ \"(app-pages-browser)/./src/components/ui/Modal.tsx\");\n/* harmony import */ var _components_CategoryForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ~/components/CategoryForm */ \"(app-pages-browser)/./src/components/CategoryForm.tsx\");\n/* harmony import */ var _hooks_useStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ~/hooks/useStore */ \"(app-pages-browser)/./src/hooks/useStore.ts\");\n/* harmony import */ var _trpc_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ~/trpc/react */ \"(app-pages-browser)/./src/trpc/react.tsx\");\n/* __next_internal_client_entry_do_not_use__ EditCategoryModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction EditCategoryModal() {\n    _s();\n    const { modals, currentEditingCategory, closeEditCategory } = (0,_hooks_useStore__WEBPACK_IMPORTED_MODULE_4__.useModals)();\n    // 更新分类的mutation\n    const updateCategoryMutation = _trpc_react__WEBPACK_IMPORTED_MODULE_5__.api.category.update.useMutation({\n        onSuccess: {\n            \"EditCategoryModal.useMutation[updateCategoryMutation]\": ()=>{\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.toast.success('分类更新成功！');\n                closeEditCategory();\n                // 刷新分类列表\n                void utils.category.getAll.invalidate();\n                void utils.category.getById.invalidate();\n                void utils.stats.getCategoryStats.invalidate();\n            }\n        }[\"EditCategoryModal.useMutation[updateCategoryMutation]\"],\n        onError: {\n            \"EditCategoryModal.useMutation[updateCategoryMutation]\": (error)=>{\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.toast.error(error.message || '更新失败，请重试');\n            }\n        }[\"EditCategoryModal.useMutation[updateCategoryMutation]\"]\n    });\n    // 获取utils用于刷新数据\n    const utils = _trpc_react__WEBPACK_IMPORTED_MODULE_5__.api.useUtils();\n    const handleSubmit = async (data)=>{\n        if (!currentEditingCategory) return;\n        try {\n            await updateCategoryMutation.mutateAsync({\n                id: currentEditingCategory.id,\n                name: data.name,\n                description: data.description || undefined,\n                color: data.color,\n                icon: data.icon || undefined\n            });\n        } catch (error) {\n        // 错误已在onError中处理\n        }\n    };\n    if (!currentEditingCategory) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Modal__WEBPACK_IMPORTED_MODULE_2__.Modal, {\n        open: modals.editCategory,\n        onClose: closeEditCategory,\n        size: \"lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Modal__WEBPACK_IMPORTED_MODULE_2__.ModalHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Modal__WEBPACK_IMPORTED_MODULE_2__.ModalTitle, {\n                        children: \"编辑分类\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\EditCategoryModal.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Modal__WEBPACK_IMPORTED_MODULE_2__.ModalCloseButton, {\n                        onClose: closeEditCategory\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\EditCategoryModal.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\EditCategoryModal.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Modal__WEBPACK_IMPORTED_MODULE_2__.ModalContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CategoryForm__WEBPACK_IMPORTED_MODULE_3__.CategoryForm, {\n                    category: currentEditingCategory,\n                    onSubmit: handleSubmit,\n                    onCancel: closeEditCategory,\n                    isLoading: updateCategoryMutation.isPending\n                }, void 0, false, {\n                    fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\EditCategoryModal.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\EditCategoryModal.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\EditCategoryModal.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n_s(EditCategoryModal, \"G2Kzhu3PXzqoLAjsA8qwVZtQYME=\", false, function() {\n    return [\n        _hooks_useStore__WEBPACK_IMPORTED_MODULE_4__.useModals,\n        _trpc_react__WEBPACK_IMPORTED_MODULE_5__.api.useUtils\n    ];\n});\n_c = EditCategoryModal;\nvar _c;\n$RefreshReg$(_c, \"EditCategoryModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/EditCategoryModal.tsx\n"));

/***/ })

});