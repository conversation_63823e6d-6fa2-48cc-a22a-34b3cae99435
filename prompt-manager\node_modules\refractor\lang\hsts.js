// @ts-nocheck
/**
 * @import {Syntax} from '../core.js'
 */
hsts.displayName = 'hsts'
hsts.aliases = []

/** @type {Syntax} */
export default function hsts(Prism) {
  /**
   * Original by <PERSON>.
   *
   * Reference: https://scotthelme.co.uk/hsts-cheat-sheet/
   */

  Prism.languages.hsts = {
    directive: {
      pattern: /\b(?:includeSubDomains|max-age|preload)(?=[\s;=]|$)/i,
      alias: 'property'
    },
    operator: /=/,
    punctuation: /;/
  }
}
