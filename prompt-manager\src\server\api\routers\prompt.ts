import { z } from "zod";
import { createTRPCRouter, protectedProcedure, publicProcedure } from "~/server/api/trpc";

export const promptRouter = createTRPCRouter({
  // 获取所有提示词（支持分页、搜索、筛选）
  getAll: publicProcedure
    .input(
      z.object({
        page: z.number().min(1).default(1),
        limit: z.number().min(1).max(100).default(20),
        search: z.string().optional(),
        categoryId: z.string().optional(),
        sortBy: z.enum(["createdAt", "updatedAt", "usageCount", "title"]).default("createdAt"),
        sortOrder: z.enum(["asc", "desc"]).default("desc"),
      })
    )
    .query(async ({ ctx, input }) => {
      const { page, limit, search, categoryId, sortBy, sortOrder } = input;
      const skip = (page - 1) * limit;

      // 构建查询条件
      const where: any = {
        isPublic: true,
      };

      if (search) {
        where.OR = [
          { title: { contains: search, mode: "insensitive" } },
          { description: { contains: search, mode: "insensitive" } },
          { content: { contains: search, mode: "insensitive" } },
          { tags: { hasSome: [search] } },
        ];
      }

      if (categoryId) {
        where.categoryId = categoryId;
      }

      // 获取总数
      const total = await ctx.db.prompt.count({ where });

      // 获取数据
      const prompts = await ctx.db.prompt.findMany({
        where,
        skip,
        take: limit,
        orderBy: { [sortBy]: sortOrder },
        include: {
          category: {
            select: {
              id: true,
              name: true,
              color: true,
              icon: true,
            },
          },
          createdBy: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
        },
      });

      return {
        prompts,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    }),

  // 根据ID获取提示词详情
  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      return ctx.db.prompt.findUnique({
        where: { id: input.id },
        include: {
          category: {
            select: {
              id: true,
              name: true,
              color: true,
              icon: true,
            },
          },
          createdBy: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
        },
      });
    }),

  // 创建提示词
  create: protectedProcedure
    .input(
      z.object({
        title: z.string().min(1, "标题不能为空").max(100, "标题不能超过100个字符"),
        content: z.string().min(1, "内容不能为空"),
        description: z.string().max(200, "描述不能超过200个字符").optional(),
        tags: z.array(z.string()).max(10, "标签不能超过10个").default([]),
        categoryId: z.string().optional(),
        isPublic: z.boolean().default(true),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.db.prompt.create({
        data: {
          ...input,
          createdById: ctx.session.user.id,
        },
        include: {
          category: {
            select: {
              id: true,
              name: true,
              color: true,
              icon: true,
            },
          },
        },
      });
    }),

  // 更新提示词
  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        title: z.string().min(1, "标题不能为空").max(100, "标题不能超过100个字符").optional(),
        content: z.string().min(1, "内容不能为空").optional(),
        description: z.string().max(200, "描述不能超过200个字符").optional(),
        tags: z.array(z.string()).max(10, "标签不能超过10个").optional(),
        categoryId: z.string().optional(),
        isPublic: z.boolean().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { id, ...updateData } = input;
      
      // 检查提示词是否存在且属于当前用户
      const prompt = await ctx.db.prompt.findUnique({
        where: { id },
      });

      if (!prompt) {
        throw new Error("提示词不存在");
      }

      if (prompt.createdById !== ctx.session.user.id) {
        throw new Error("无权限修改此提示词");
      }

      return ctx.db.prompt.update({
        where: { id },
        data: updateData,
        include: {
          category: {
            select: {
              id: true,
              name: true,
              color: true,
              icon: true,
            },
          },
        },
      });
    }),

  // 删除提示词
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // 检查提示词是否存在且属于当前用户
      const prompt = await ctx.db.prompt.findUnique({
        where: { id: input.id },
      });

      if (!prompt) {
        throw new Error("提示词不存在");
      }

      if (prompt.createdById !== ctx.session.user.id) {
        throw new Error("无权限删除此提示词");
      }

      return ctx.db.prompt.delete({
        where: { id: input.id },
      });
    }),

  // 复制提示词（增加使用次数）
  copy: publicProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // 更新使用次数
      const prompt = await ctx.db.prompt.update({
        where: { id: input.id },
        data: {
          usageCount: {
            increment: 1,
          },
        },
      });

      // 如果用户已登录，记录使用历史
      if (ctx.session?.user?.id) {
        await ctx.db.promptUsage.create({
          data: {
            promptId: input.id,
            userId: ctx.session.user.id,
          },
        });
      }

      return prompt;
    }),

  // 获取我的提示词
  getMine: protectedProcedure
    .input(
      z.object({
        page: z.number().min(1).default(1),
        limit: z.number().min(1).max(100).default(20),
        search: z.string().optional(),
        categoryId: z.string().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      const { page, limit, search, categoryId } = input;
      const skip = (page - 1) * limit;

      const where: any = {
        createdById: ctx.session.user.id,
      };

      if (search) {
        where.OR = [
          { title: { contains: search, mode: "insensitive" } },
          { description: { contains: search, mode: "insensitive" } },
          { content: { contains: search, mode: "insensitive" } },
        ];
      }

      if (categoryId) {
        where.categoryId = categoryId;
      }

      const total = await ctx.db.prompt.count({ where });

      const prompts = await ctx.db.prompt.findMany({
        where,
        skip,
        take: limit,
        orderBy: { updatedAt: "desc" },
        include: {
          category: {
            select: {
              id: true,
              name: true,
              color: true,
              icon: true,
            },
          },
        },
      });

      return {
        prompts,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    }),

  // 获取热门提示词
  getPopular: publicProcedure
    .input(z.object({ limit: z.number().min(1).max(50).default(10) }))
    .query(async ({ ctx, input }) => {
      return ctx.db.prompt.findMany({
        where: { isPublic: true },
        take: input.limit,
        orderBy: { usageCount: "desc" },
        include: {
          category: {
            select: {
              id: true,
              name: true,
              color: true,
              icon: true,
            },
          },
          createdBy: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
        },
      });
    }),

  // 获取最新提示词
  getLatest: publicProcedure
    .input(z.object({ limit: z.number().min(1).max(50).default(10) }))
    .query(async ({ ctx, input }) => {
      return ctx.db.prompt.findMany({
        where: { isPublic: true },
        take: input.limit,
        orderBy: { createdAt: "desc" },
        include: {
          category: {
            select: {
              id: true,
              name: true,
              color: true,
              icon: true,
            },
          },
          createdBy: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
        },
      });
    }),
});
