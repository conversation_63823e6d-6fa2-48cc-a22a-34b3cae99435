:root:has(input.theme-controller[value=bumblebee]:checked),[data-theme="bumblebee"] {
color-scheme: light;
--color-base-100: oklch(100% 0 0);
--color-base-200: oklch(97% 0 0);
--color-base-300: oklch(92% 0 0);
--color-base-content: oklch(20% 0 0);
--color-primary: oklch(85% 0.199 91.936);
--color-primary-content: oklch(42% 0.095 57.708);
--color-secondary: oklch(75% 0.183 55.934);
--color-secondary-content: oklch(40% 0.123 38.172);
--color-accent: oklch(0% 0 0);
--color-accent-content: oklch(100% 0 0);
--color-neutral: oklch(37% 0.01 67.558);
--color-neutral-content: oklch(92% 0.003 48.717);
--color-info: oklch(74% 0.16 232.661);
--color-info-content: oklch(39% 0.09 240.876);
--color-success: oklch(76% 0.177 163.223);
--color-success-content: oklch(37% 0.077 168.94);
--color-warning: oklch(82% 0.189 84.429);
--color-warning-content: oklch(41% 0.112 45.904);
--color-error: oklch(70% 0.191 22.216);
--color-error-content: oklch(39% 0.141 25.723);
--radius-selector: 1rem;
--radius-field: 0.5rem;
--radius-box: 1rem;
--size-selector: 0.25rem;
--size-field: 0.25rem;
--border: 1px;
--depth: 1;
--noise: 0;
}
