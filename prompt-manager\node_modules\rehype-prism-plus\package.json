{"name": "rehype-prism-plus", "version": "2.0.0", "description": "rehype plugin to highlight code blocks in HTML with Prism (via refractor) with line highlighting and line numbers", "source": "index.js", "files": ["dist"], "main": "./dist/index.es.js", "module": "./dist/index.es.js", "types": "./dist/index.d.ts", "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.es.js"}, "./common": {"types": "./dist/common.d.ts", "default": "./dist/common.es.js"}, "./all": {"types": "./dist/all.d.ts", "default": "./dist/all.es.js"}, "./generator": {"types": "./dist/generator.d.ts", "default": "./dist/generator.es.js"}}, "typesVersions": {"*": {".": ["./dist/index"], "common": ["./dist/common"], "all": ["./dist/all"], "generator": ["./dist/generator"]}}, "scripts": {"build": "tsc -b && microbundle src/index.js src/common.js src/all.js src/generator.js --format esm", "tsc": "tsc --watch", "lint": "eslint .", "prettier": "prettier --write '*.js'", "test": "uvu"}, "repository": {"type": "git", "url": "git+https://github.com/timlrx/rehype-prism-plus.git"}, "keywords": ["rehype", "rehype-plugin", "syntax-highlighting", "prism", "mdx", "jsx"], "author": "<PERSON> <<EMAIL>> (https://timlrx.com)", "license": "MIT", "bugs": {"url": "https://github.com/timlrx/rehype-prism-plus/issues"}, "homepage": "https://github.com/timlrx/rehype-prism-plus#readme", "dependencies": {"hast-util-to-string": "^3.0.0", "parse-numeric-range": "^1.3.0", "refractor": "^4.8.0", "rehype-parse": "^9.0.0", "unist-util-filter": "^5.0.0", "unist-util-visit": "^5.0.0"}, "devDependencies": {"dedent": "^0.7.0", "eslint": "^8.43.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-node": "^11.1.0", "husky": "^8.0.0", "lint-staged": "^11.1.2", "microbundle": "^0.15.1", "prettier": "^2.8.8", "rehype": "^13.0.1", "remark": "^15.0.1", "remark-rehype": "^11.0.0", "typescript": "5.1.3", "unified": "^11.0.4", "uvu": "^0.5.1"}, "prettier": {"printWidth": 100, "tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": true, "semi": false, "trailingComma": "es5"}, "lint-staged": {"*.+(js|jsx|ts|tsx)": ["eslint --fix"], "*.+(js|jsx|ts|tsx|json|css|md|mdx)": ["prettier --write"]}}