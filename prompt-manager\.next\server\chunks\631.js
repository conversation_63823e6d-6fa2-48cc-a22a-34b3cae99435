exports.id=631,exports.ids=[631],exports.modules={81:(a,b,c)=>{"use strict";function d(a){return{id:"discord",name:"Discord",type:"oauth",authorization:"https://discord.com/api/oauth2/authorize?scope=identify+email",token:"https://discord.com/api/oauth2/token",userinfo:"https://discord.com/api/users/@me",profile(a){if(null===a.avatar){let b="0"===a.discriminator?Number(BigInt(a.id)>>BigInt(22))%6:parseInt(a.discriminator)%5;a.image_url=`https://cdn.discordapp.com/embed/avatars/${b}.png`}else{let b=a.avatar.startsWith("a_")?"gif":"png";a.image_url=`https://cdn.discordapp.com/avatars/${a.id}/${a.avatar}.${b}`}return{id:a.id,name:a.global_name??a.username,email:a.email,image:a.image_url}},style:{bg:"#5865F2",text:"#fff"},options:a}}c.d(b,{A:()=>d})},163:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unstable_rethrow",{enumerable:!0,get:function(){return d}});let d=c(1042).unstable_rethrow;("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},397:(a,b,c)=>{var d;(()=>{var e={226:function(e,f){!function(g,h){"use strict";var i="function",j="undefined",k="object",l="string",m="major",n="model",o="name",p="type",q="vendor",r="version",s="architecture",t="console",u="mobile",v="tablet",w="smarttv",x="wearable",y="embedded",z="Amazon",A="Apple",B="ASUS",C="BlackBerry",D="Browser",E="Chrome",F="Firefox",G="Google",H="Huawei",I="Microsoft",J="Motorola",K="Opera",L="Samsung",M="Sharp",N="Sony",O="Xiaomi",P="Zebra",Q="Facebook",R="Chromium OS",S="Mac OS",T=function(a,b){var c={};for(var d in a)b[d]&&b[d].length%2==0?c[d]=b[d].concat(a[d]):c[d]=a[d];return c},U=function(a){for(var b={},c=0;c<a.length;c++)b[a[c].toUpperCase()]=a[c];return b},V=function(a,b){return typeof a===l&&-1!==W(b).indexOf(W(a))},W=function(a){return a.toLowerCase()},X=function(a,b){if(typeof a===l)return a=a.replace(/^\s\s*/,""),typeof b===j?a:a.substring(0,350)},Y=function(a,b){for(var c,d,e,f,g,j,l=0;l<b.length&&!g;){var m=b[l],n=b[l+1];for(c=d=0;c<m.length&&!g&&m[c];)if(g=m[c++].exec(a))for(e=0;e<n.length;e++)j=g[++d],typeof(f=n[e])===k&&f.length>0?2===f.length?typeof f[1]==i?this[f[0]]=f[1].call(this,j):this[f[0]]=f[1]:3===f.length?typeof f[1]!==i||f[1].exec&&f[1].test?this[f[0]]=j?j.replace(f[1],f[2]):void 0:this[f[0]]=j?f[1].call(this,j,f[2]):void 0:4===f.length&&(this[f[0]]=j?f[3].call(this,j.replace(f[1],f[2])):h):this[f]=j||h;l+=2}},Z=function(a,b){for(var c in b)if(typeof b[c]===k&&b[c].length>0){for(var d=0;d<b[c].length;d++)if(V(b[c][d],a))return"?"===c?h:c}else if(V(b[c],a))return"?"===c?h:c;return a},$={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},_={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[r,[o,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[r,[o,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[o,r],[/opios[\/ ]+([\w\.]+)/i],[r,[o,K+" Mini"]],[/\bopr\/([\w\.]+)/i],[r,[o,K]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[o,r],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[r,[o,"UC"+D]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[r,[o,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[r,[o,"WeChat"]],[/konqueror\/([\w\.]+)/i],[r,[o,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[r,[o,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[r,[o,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[o,/(.+)/,"$1 Secure "+D],r],[/\bfocus\/([\w\.]+)/i],[r,[o,F+" Focus"]],[/\bopt\/([\w\.]+)/i],[r,[o,K+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[r,[o,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[r,[o,"Dolphin"]],[/coast\/([\w\.]+)/i],[r,[o,K+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[r,[o,"MIUI "+D]],[/fxios\/([-\w\.]+)/i],[r,[o,F]],[/\bqihu|(qi?ho?o?|360)browser/i],[[o,"360 "+D]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[o,/(.+)/,"$1 "+D],r],[/(comodo_dragon)\/([\w\.]+)/i],[[o,/_/g," "],r],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[o,r],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[o],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[o,Q],r],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[o,r],[/\bgsa\/([\w\.]+) .*safari\//i],[r,[o,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[r,[o,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[r,[o,E+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[o,E+" WebView"],r],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[r,[o,"Android "+D]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[o,r],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[r,[o,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[r,o],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[o,[r,Z,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[o,r],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[o,"Netscape"],r],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[r,[o,F+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[o,r],[/(cobalt)\/([\w\.]+)/i],[o,[r,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[s,"amd64"]],[/(ia32(?=;))/i],[[s,W]],[/((?:i[346]|x)86)[;\)]/i],[[s,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[s,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[s,"armhf"]],[/windows (ce|mobile); ppc;/i],[[s,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[s,/ower/,"",W]],[/(sun4\w)[;\)]/i],[[s,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[s,W]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[n,[q,L],[p,v]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[n,[q,L],[p,u]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[n,[q,A],[p,u]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[n,[q,A],[p,v]],[/(macintosh);/i],[n,[q,A]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[n,[q,M],[p,u]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[n,[q,H],[p,v]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[n,[q,H],[p,u]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[n,/_/g," "],[q,O],[p,u]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[n,/_/g," "],[q,O],[p,v]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[n,[q,"OPPO"],[p,u]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[n,[q,"Vivo"],[p,u]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[n,[q,"Realme"],[p,u]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[n,[q,J],[p,u]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[n,[q,J],[p,v]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[n,[q,"LG"],[p,v]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[n,[q,"LG"],[p,u]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[n,[q,"Lenovo"],[p,v]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[n,/_/g," "],[q,"Nokia"],[p,u]],[/(pixel c)\b/i],[n,[q,G],[p,v]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[n,[q,G],[p,u]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[n,[q,N],[p,u]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[n,"Xperia Tablet"],[q,N],[p,v]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[n,[q,"OnePlus"],[p,u]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[n,[q,z],[p,v]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[n,/(.+)/g,"Fire Phone $1"],[q,z],[p,u]],[/(playbook);[-\w\),; ]+(rim)/i],[n,q,[p,v]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[n,[q,C],[p,u]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[n,[q,B],[p,v]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[n,[q,B],[p,u]],[/(nexus 9)/i],[n,[q,"HTC"],[p,v]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[q,[n,/_/g," "],[p,u]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[n,[q,"Acer"],[p,v]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[n,[q,"Meizu"],[p,u]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[q,n,[p,u]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[q,n,[p,v]],[/(surface duo)/i],[n,[q,I],[p,v]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[n,[q,"Fairphone"],[p,u]],[/(u304aa)/i],[n,[q,"AT&T"],[p,u]],[/\bsie-(\w*)/i],[n,[q,"Siemens"],[p,u]],[/\b(rct\w+) b/i],[n,[q,"RCA"],[p,v]],[/\b(venue[\d ]{2,7}) b/i],[n,[q,"Dell"],[p,v]],[/\b(q(?:mv|ta)\w+) b/i],[n,[q,"Verizon"],[p,v]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[n,[q,"Barnes & Noble"],[p,v]],[/\b(tm\d{3}\w+) b/i],[n,[q,"NuVision"],[p,v]],[/\b(k88) b/i],[n,[q,"ZTE"],[p,v]],[/\b(nx\d{3}j) b/i],[n,[q,"ZTE"],[p,u]],[/\b(gen\d{3}) b.+49h/i],[n,[q,"Swiss"],[p,u]],[/\b(zur\d{3}) b/i],[n,[q,"Swiss"],[p,v]],[/\b((zeki)?tb.*\b) b/i],[n,[q,"Zeki"],[p,v]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[q,"Dragon Touch"],n,[p,v]],[/\b(ns-?\w{0,9}) b/i],[n,[q,"Insignia"],[p,v]],[/\b((nxa|next)-?\w{0,9}) b/i],[n,[q,"NextBook"],[p,v]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[q,"Voice"],n,[p,u]],[/\b(lvtel\-)?(v1[12]) b/i],[[q,"LvTel"],n,[p,u]],[/\b(ph-1) /i],[n,[q,"Essential"],[p,u]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[n,[q,"Envizen"],[p,v]],[/\b(trio[-\w\. ]+) b/i],[n,[q,"MachSpeed"],[p,v]],[/\btu_(1491) b/i],[n,[q,"Rotor"],[p,v]],[/(shield[\w ]+) b/i],[n,[q,"Nvidia"],[p,v]],[/(sprint) (\w+)/i],[q,n,[p,u]],[/(kin\.[onetw]{3})/i],[[n,/\./g," "],[q,I],[p,u]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[n,[q,P],[p,v]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[n,[q,P],[p,u]],[/smart-tv.+(samsung)/i],[q,[p,w]],[/hbbtv.+maple;(\d+)/i],[[n,/^/,"SmartTV"],[q,L],[p,w]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[q,"LG"],[p,w]],[/(apple) ?tv/i],[q,[n,A+" TV"],[p,w]],[/crkey/i],[[n,E+"cast"],[q,G],[p,w]],[/droid.+aft(\w)( bui|\))/i],[n,[q,z],[p,w]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[n,[q,M],[p,w]],[/(bravia[\w ]+)( bui|\))/i],[n,[q,N],[p,w]],[/(mitv-\w{5}) bui/i],[n,[q,O],[p,w]],[/Hbbtv.*(technisat) (.*);/i],[q,n,[p,w]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[q,X],[n,X],[p,w]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[p,w]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[q,n,[p,t]],[/droid.+; (shield) bui/i],[n,[q,"Nvidia"],[p,t]],[/(playstation [345portablevi]+)/i],[n,[q,N],[p,t]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[n,[q,I],[p,t]],[/((pebble))app/i],[q,n,[p,x]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[n,[q,A],[p,x]],[/droid.+; (glass) \d/i],[n,[q,G],[p,x]],[/droid.+; (wt63?0{2,3})\)/i],[n,[q,P],[p,x]],[/(quest( 2| pro)?)/i],[n,[q,Q],[p,x]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[q,[p,y]],[/(aeobc)\b/i],[n,[q,z],[p,y]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[n,[p,u]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[n,[p,v]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[p,v]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[p,u]],[/(android[-\w\. ]{0,9});.+buil/i],[n,[q,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[r,[o,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[r,[o,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[o,r],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[r,o]],os:[[/microsoft (windows) (vista|xp)/i],[o,r],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[o,[r,Z,$]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[o,"Windows"],[r,Z,$]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[r,/_/g,"."],[o,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[o,S],[r,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[r,o],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[o,r],[/\(bb(10);/i],[r,[o,C]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[r,[o,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[r,[o,F+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[r,[o,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[r,[o,"watchOS"]],[/crkey\/([\d\.]+)/i],[r,[o,E+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[o,R],r],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[o,r],[/(sunos) ?([\w\.\d]*)/i],[[o,"Solaris"],r],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[o,r]]},aa=function(a,b){if(typeof a===k&&(b=a,a=h),!(this instanceof aa))return new aa(a,b).getResult();var c=typeof g!==j&&g.navigator?g.navigator:h,d=a||(c&&c.userAgent?c.userAgent:""),e=c&&c.userAgentData?c.userAgentData:h,f=b?T(_,b):_,t=c&&c.userAgent==d;return this.getBrowser=function(){var a,b={};return b[o]=h,b[r]=h,Y.call(b,d,f.browser),b[m]=typeof(a=b[r])===l?a.replace(/[^\d\.]/g,"").split(".")[0]:h,t&&c&&c.brave&&typeof c.brave.isBrave==i&&(b[o]="Brave"),b},this.getCPU=function(){var a={};return a[s]=h,Y.call(a,d,f.cpu),a},this.getDevice=function(){var a={};return a[q]=h,a[n]=h,a[p]=h,Y.call(a,d,f.device),t&&!a[p]&&e&&e.mobile&&(a[p]=u),t&&"Macintosh"==a[n]&&c&&typeof c.standalone!==j&&c.maxTouchPoints&&c.maxTouchPoints>2&&(a[n]="iPad",a[p]=v),a},this.getEngine=function(){var a={};return a[o]=h,a[r]=h,Y.call(a,d,f.engine),a},this.getOS=function(){var a={};return a[o]=h,a[r]=h,Y.call(a,d,f.os),t&&!a[o]&&e&&"Unknown"!=e.platform&&(a[o]=e.platform.replace(/chrome os/i,R).replace(/macos/i,S)),a},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return d},this.setUA=function(a){return d=typeof a===l&&a.length>350?X(a,350):a,this},this.setUA(d),this};aa.VERSION="1.0.35",aa.BROWSER=U([o,r,m]),aa.CPU=U([s]),aa.DEVICE=U([n,q,p,t,u,w,v,x,y]),aa.ENGINE=aa.OS=U([o,r]),typeof f!==j?(e.exports&&(f=e.exports=aa),f.UAParser=aa):c.amdO?void 0===(d=(function(){return aa}).call(b,c,b,a))||(a.exports=d):typeof g!==j&&(g.UAParser=aa);var ab=typeof g!==j&&(g.jQuery||g.Zepto);if(ab&&!ab.ua){var ac=new aa;ab.ua=ac.getResult(),ab.ua.get=function(){return ac.getUA()},ab.ua.set=function(a){ac.setUA(a);var b=ac.getResult();for(var c in b)ab.ua[c]=b[c]}}}("object"==typeof window?window:this)}},f={};function g(a){var b=f[a];if(void 0!==b)return b.exports;var c=f[a]={exports:{}},d=!0;try{e[a].call(c.exports,c,c.exports,g),d=!1}finally{d&&delete f[a]}return c.exports}g.ab=__dirname+"/",a.exports=g(226)})()},899:(a,b,c)=>{"use strict";function d(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unauthorized",{enumerable:!0,get:function(){return d}}),c(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},1042:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unstable_rethrow",{enumerable:!0,get:function(){return function a(b){if((0,g.isNextRouterError)(b)||(0,f.isBailoutToCSRError)(b)||(0,i.isDynamicServerError)(b)||(0,h.isDynamicPostpone)(b)||(0,e.isPostpone)(b)||(0,d.isHangingPromiseRejectionError)(b))throw b;b instanceof Error&&"cause"in b&&a(b.cause)}}});let d=c(8388),e=c(2637),f=c(1846),g=c(1162),h=c(4971),i=c(8479);("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},1243:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"URLPattern",{enumerable:!0,get:function(){return c}});let c="undefined"==typeof URLPattern?void 0:URLPattern},2079:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unstable_rootParams",{enumerable:!0,get:function(){return k}});let d=c(1617),e=c(4971),f=c(9294),g=c(3033),h=c(8388),i=c(2609),j=new WeakMap;async function k(){let a=f.workAsyncStorage.getStore();if(!a)throw Object.defineProperty(new d.InvariantError("Missing workStore in unstable_rootParams"),"__NEXT_ERROR_CODE",{value:"E615",enumerable:!1,configurable:!0});let b=g.workUnitAsyncStorage.getStore();if(!b)throw Object.defineProperty(Error(`Route ${a.route} used \`unstable_rootParams()\` in Pages Router. This API is only available within App Router.`),"__NEXT_ERROR_CODE",{value:"E641",enumerable:!1,configurable:!0});switch(b.type){case"unstable-cache":case"cache":throw Object.defineProperty(Error(`Route ${a.route} used \`unstable_rootParams()\` inside \`"use cache"\` or \`unstable_cache\`. Support for this API inside cache scopes is planned for a future version of Next.js.`),"__NEXT_ERROR_CODE",{value:"E642",enumerable:!1,configurable:!0});case"prerender":case"prerender-client":case"prerender-ppr":case"prerender-legacy":return function(a,b,c){let f=b.fallbackRouteParams;if(f){let n=!1;for(let b in a)if(f.has(b)){n=!0;break}if(n)switch(c.type){case"prerender":let o=j.get(a);if(o)return o;let p=(0,h.makeHangingPromise)(c.renderSignal,"`unstable_rootParams`");return j.set(a,p),p;case"prerender-client":let q="`unstable_rootParams`";throw Object.defineProperty(new d.InvariantError(`${q} must not be used within a client component. Next.js should be preventing ${q} from being included in client components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E693",enumerable:!1,configurable:!0});default:var g=a,k=f,l=b,m=c;let r=j.get(g);if(r)return r;let s={...g},t=Promise.resolve(s);return j.set(g,t),Object.keys(g).forEach(a=>{i.wellKnownProperties.has(a)||(k.has(a)?Object.defineProperty(s,a,{get(){let b=(0,i.describeStringPropertyAccess)("unstable_rootParams",a);"prerender-ppr"===m.type?(0,e.postponeWithTracking)(l.route,b,m.dynamicTracking):(0,e.throwToInterruptStaticGeneration)(b,l,m)},enumerable:!0}):t[a]=g[a])}),t}}return Promise.resolve(a)}(b.rootParams,a,b);default:return Promise.resolve(b.rootParams)}}},2174:(a,b)=>{"use strict";function c(){throw Object.defineProperty(Error('ImageResponse moved from "next/server" to "next/og" since Next.js 14, please import from "next/og" instead'),"__NEXT_ERROR_CODE",{value:"E183",enumerable:!1,configurable:!0})}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"ImageResponse",{enumerable:!0,get:function(){return c}})},2289:(a,b,c)=>{"use strict";let d;c.d(b,{YO:()=>ay,zM:()=>ax,k5:()=>aA,ai:()=>aw,Ik:()=>az,Yj:()=>av});var e,f,g=c(5282),h=c(8269);let i=(a,b)=>{let c;switch(a.code){case g.eq.invalid_type:c=a.received===h.Zp.undefined?"Required":`Expected ${a.expected}, received ${a.received}`;break;case g.eq.invalid_literal:c=`Invalid literal value, expected ${JSON.stringify(a.expected,h.ZS.jsonStringifyReplacer)}`;break;case g.eq.unrecognized_keys:c=`Unrecognized key(s) in object: ${h.ZS.joinValues(a.keys,", ")}`;break;case g.eq.invalid_union:c="Invalid input";break;case g.eq.invalid_union_discriminator:c=`Invalid discriminator value. Expected ${h.ZS.joinValues(a.options)}`;break;case g.eq.invalid_enum_value:c=`Invalid enum value. Expected ${h.ZS.joinValues(a.options)}, received '${a.received}'`;break;case g.eq.invalid_arguments:c="Invalid function arguments";break;case g.eq.invalid_return_type:c="Invalid function return type";break;case g.eq.invalid_date:c="Invalid date";break;case g.eq.invalid_string:"object"==typeof a.validation?"includes"in a.validation?(c=`Invalid input: must include "${a.validation.includes}"`,"number"==typeof a.validation.position&&(c=`${c} at one or more positions greater than or equal to ${a.validation.position}`)):"startsWith"in a.validation?c=`Invalid input: must start with "${a.validation.startsWith}"`:"endsWith"in a.validation?c=`Invalid input: must end with "${a.validation.endsWith}"`:h.ZS.assertNever(a.validation):c="regex"!==a.validation?`Invalid ${a.validation}`:"Invalid";break;case g.eq.too_small:c="array"===a.type?`Array must contain ${a.exact?"exactly":a.inclusive?"at least":"more than"} ${a.minimum} element(s)`:"string"===a.type?`String must contain ${a.exact?"exactly":a.inclusive?"at least":"over"} ${a.minimum} character(s)`:"number"===a.type||"bigint"===a.type?`Number must be ${a.exact?"exactly equal to ":a.inclusive?"greater than or equal to ":"greater than "}${a.minimum}`:"date"===a.type?`Date must be ${a.exact?"exactly equal to ":a.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(a.minimum))}`:"Invalid input";break;case g.eq.too_big:c="array"===a.type?`Array must contain ${a.exact?"exactly":a.inclusive?"at most":"less than"} ${a.maximum} element(s)`:"string"===a.type?`String must contain ${a.exact?"exactly":a.inclusive?"at most":"under"} ${a.maximum} character(s)`:"number"===a.type?`Number must be ${a.exact?"exactly":a.inclusive?"less than or equal to":"less than"} ${a.maximum}`:"bigint"===a.type?`BigInt must be ${a.exact?"exactly":a.inclusive?"less than or equal to":"less than"} ${a.maximum}`:"date"===a.type?`Date must be ${a.exact?"exactly":a.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(a.maximum))}`:"Invalid input";break;case g.eq.custom:c="Invalid input";break;case g.eq.invalid_intersection_types:c="Intersection results could not be merged";break;case g.eq.not_multiple_of:c=`Number must be a multiple of ${a.multipleOf}`;break;case g.eq.not_finite:c="Number must be finite";break;default:c=b.defaultError,h.ZS.assertNever(a)}return{message:c}};!function(a){a.errToObj=a=>"string"==typeof a?{message:a}:a||{},a.toString=a=>"string"==typeof a?a:a?.message}(e||(e={}));let j=a=>{let{data:b,path:c,errorMaps:d,issueData:e}=a,f=[...c,...e.path||[]],g={...e,path:f};if(void 0!==e.message)return{...e,path:f,message:e.message};let h="";for(let a of d.filter(a=>!!a).slice().reverse())h=a(g,{data:b,defaultError:h}).message;return{...e,path:f,message:h}};function k(a,b){let c=j({issueData:b,data:a.data,path:a.path,errorMaps:[a.common.contextualErrorMap,a.schemaErrorMap,i,void 0].filter(a=>!!a)});a.common.issues.push(c)}class l{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(a,b){let c=[];for(let d of b){if("aborted"===d.status)return m;"dirty"===d.status&&a.dirty(),c.push(d.value)}return{status:a.value,value:c}}static async mergeObjectAsync(a,b){let c=[];for(let a of b){let b=await a.key,d=await a.value;c.push({key:b,value:d})}return l.mergeObjectSync(a,c)}static mergeObjectSync(a,b){let c={};for(let d of b){let{key:b,value:e}=d;if("aborted"===b.status||"aborted"===e.status)return m;"dirty"===b.status&&a.dirty(),"dirty"===e.status&&a.dirty(),"__proto__"!==b.value&&(void 0!==e.value||d.alwaysSet)&&(c[b.value]=e.value)}return{status:a.value,value:c}}}let m=Object.freeze({status:"aborted"}),n=a=>({status:"dirty",value:a}),o=a=>({status:"valid",value:a}),p=a=>"undefined"!=typeof Promise&&a instanceof Promise;class q{constructor(a,b,c,d){this._cachedPath=[],this.parent=a,this.data=b,this._path=c,this._key=d}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}let r=(a,b)=>{if("valid"===b.status)return{success:!0,data:b.value};if(!a.common.issues.length)throw Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;let b=new g.G(a.common.issues);return this._error=b,this._error}}};function s(a){if(!a)return{};let{errorMap:b,invalid_type_error:c,required_error:d,description:e}=a;if(b&&(c||d))throw Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');return b?{errorMap:b,description:e}:{errorMap:(b,e)=>{let{message:f}=a;return"invalid_enum_value"===b.code?{message:f??e.defaultError}:void 0===e.data?{message:f??d??e.defaultError}:"invalid_type"!==b.code?{message:e.defaultError}:{message:f??c??e.defaultError}},description:e}}class t{get description(){return this._def.description}_getType(a){return(0,h.CR)(a.data)}_getOrReturnCtx(a,b){return b||{common:a.parent.common,data:a.data,parsedType:(0,h.CR)(a.data),schemaErrorMap:this._def.errorMap,path:a.path,parent:a.parent}}_processInputParams(a){return{status:new l,ctx:{common:a.parent.common,data:a.data,parsedType:(0,h.CR)(a.data),schemaErrorMap:this._def.errorMap,path:a.path,parent:a.parent}}}_parseSync(a){let b=this._parse(a);if(p(b))throw Error("Synchronous parse encountered promise.");return b}_parseAsync(a){return Promise.resolve(this._parse(a))}parse(a,b){let c=this.safeParse(a,b);if(c.success)return c.data;throw c.error}safeParse(a,b){let c={common:{issues:[],async:b?.async??!1,contextualErrorMap:b?.errorMap},path:b?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:a,parsedType:(0,h.CR)(a)},d=this._parseSync({data:a,path:c.path,parent:c});return r(c,d)}"~validate"(a){let b={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:a,parsedType:(0,h.CR)(a)};if(!this["~standard"].async)try{let c=this._parseSync({data:a,path:[],parent:b});return"valid"===c.status?{value:c.value}:{issues:b.common.issues}}catch(a){a?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),b.common={issues:[],async:!0}}return this._parseAsync({data:a,path:[],parent:b}).then(a=>"valid"===a.status?{value:a.value}:{issues:b.common.issues})}async parseAsync(a,b){let c=await this.safeParseAsync(a,b);if(c.success)return c.data;throw c.error}async safeParseAsync(a,b){let c={common:{issues:[],contextualErrorMap:b?.errorMap,async:!0},path:b?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:a,parsedType:(0,h.CR)(a)},d=this._parse({data:a,path:c.path,parent:c});return r(c,await (p(d)?d:Promise.resolve(d)))}refine(a,b){return this._refinement((c,d)=>{let e=a(c),f=()=>d.addIssue({code:g.eq.custom,..."string"==typeof b||void 0===b?{message:b}:"function"==typeof b?b(c):b});return"undefined"!=typeof Promise&&e instanceof Promise?e.then(a=>!!a||(f(),!1)):!!e||(f(),!1)})}refinement(a,b){return this._refinement((c,d)=>!!a(c)||(d.addIssue("function"==typeof b?b(c,d):b),!1))}_refinement(a){return new am({schema:this,typeName:f.ZodEffects,effect:{type:"refinement",refinement:a}})}superRefine(a){return this._refinement(a)}constructor(a){this.spa=this.safeParseAsync,this._def=a,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:a=>this["~validate"](a)}}optional(){return an.create(this,this._def)}nullable(){return ao.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return X.create(this)}promise(){return al.create(this,this._def)}or(a){return Z.create([this,a],this._def)}and(a){return aa.create(this,a,this._def)}transform(a){return new am({...s(this._def),schema:this,typeName:f.ZodEffects,effect:{type:"transform",transform:a}})}default(a){return new ap({...s(this._def),innerType:this,defaultValue:"function"==typeof a?a:()=>a,typeName:f.ZodDefault})}brand(){return new as({typeName:f.ZodBranded,type:this,...s(this._def)})}catch(a){return new aq({...s(this._def),innerType:this,catchValue:"function"==typeof a?a:()=>a,typeName:f.ZodCatch})}describe(a){return new this.constructor({...this._def,description:a})}pipe(a){return at.create(this,a)}readonly(){return au.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}let u=/^c[^\s-]{8,}$/i,v=/^[0-9a-z]+$/,w=/^[0-9A-HJKMNP-TV-Z]{26}$/i,x=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,y=/^[a-z0-9_-]{21}$/i,z=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,A=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,B=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,C=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,D=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,E=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,F=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,G=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,H=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,I="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",J=RegExp(`^${I}$`);function K(a){let b="[0-5]\\d";a.precision?b=`${b}\\.\\d{${a.precision}}`:null==a.precision&&(b=`${b}(\\.\\d+)?`);let c=a.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${b})${c}`}class L extends t{_parse(a){var b,c,e,f;let i;if(this._def.coerce&&(a.data=String(a.data)),this._getType(a)!==h.Zp.string){let b=this._getOrReturnCtx(a);return k(b,{code:g.eq.invalid_type,expected:h.Zp.string,received:b.parsedType}),m}let j=new l;for(let l of this._def.checks)if("min"===l.kind)a.data.length<l.value&&(k(i=this._getOrReturnCtx(a,i),{code:g.eq.too_small,minimum:l.value,type:"string",inclusive:!0,exact:!1,message:l.message}),j.dirty());else if("max"===l.kind)a.data.length>l.value&&(k(i=this._getOrReturnCtx(a,i),{code:g.eq.too_big,maximum:l.value,type:"string",inclusive:!0,exact:!1,message:l.message}),j.dirty());else if("length"===l.kind){let b=a.data.length>l.value,c=a.data.length<l.value;(b||c)&&(i=this._getOrReturnCtx(a,i),b?k(i,{code:g.eq.too_big,maximum:l.value,type:"string",inclusive:!0,exact:!0,message:l.message}):c&&k(i,{code:g.eq.too_small,minimum:l.value,type:"string",inclusive:!0,exact:!0,message:l.message}),j.dirty())}else if("email"===l.kind)B.test(a.data)||(k(i=this._getOrReturnCtx(a,i),{validation:"email",code:g.eq.invalid_string,message:l.message}),j.dirty());else if("emoji"===l.kind)d||(d=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),d.test(a.data)||(k(i=this._getOrReturnCtx(a,i),{validation:"emoji",code:g.eq.invalid_string,message:l.message}),j.dirty());else if("uuid"===l.kind)x.test(a.data)||(k(i=this._getOrReturnCtx(a,i),{validation:"uuid",code:g.eq.invalid_string,message:l.message}),j.dirty());else if("nanoid"===l.kind)y.test(a.data)||(k(i=this._getOrReturnCtx(a,i),{validation:"nanoid",code:g.eq.invalid_string,message:l.message}),j.dirty());else if("cuid"===l.kind)u.test(a.data)||(k(i=this._getOrReturnCtx(a,i),{validation:"cuid",code:g.eq.invalid_string,message:l.message}),j.dirty());else if("cuid2"===l.kind)v.test(a.data)||(k(i=this._getOrReturnCtx(a,i),{validation:"cuid2",code:g.eq.invalid_string,message:l.message}),j.dirty());else if("ulid"===l.kind)w.test(a.data)||(k(i=this._getOrReturnCtx(a,i),{validation:"ulid",code:g.eq.invalid_string,message:l.message}),j.dirty());else if("url"===l.kind)try{new URL(a.data)}catch{k(i=this._getOrReturnCtx(a,i),{validation:"url",code:g.eq.invalid_string,message:l.message}),j.dirty()}else"regex"===l.kind?(l.regex.lastIndex=0,l.regex.test(a.data)||(k(i=this._getOrReturnCtx(a,i),{validation:"regex",code:g.eq.invalid_string,message:l.message}),j.dirty())):"trim"===l.kind?a.data=a.data.trim():"includes"===l.kind?a.data.includes(l.value,l.position)||(k(i=this._getOrReturnCtx(a,i),{code:g.eq.invalid_string,validation:{includes:l.value,position:l.position},message:l.message}),j.dirty()):"toLowerCase"===l.kind?a.data=a.data.toLowerCase():"toUpperCase"===l.kind?a.data=a.data.toUpperCase():"startsWith"===l.kind?a.data.startsWith(l.value)||(k(i=this._getOrReturnCtx(a,i),{code:g.eq.invalid_string,validation:{startsWith:l.value},message:l.message}),j.dirty()):"endsWith"===l.kind?a.data.endsWith(l.value)||(k(i=this._getOrReturnCtx(a,i),{code:g.eq.invalid_string,validation:{endsWith:l.value},message:l.message}),j.dirty()):"datetime"===l.kind?(function(a){let b=`${I}T${K(a)}`,c=[];return c.push(a.local?"Z?":"Z"),a.offset&&c.push("([+-]\\d{2}:?\\d{2})"),b=`${b}(${c.join("|")})`,RegExp(`^${b}$`)})(l).test(a.data)||(k(i=this._getOrReturnCtx(a,i),{code:g.eq.invalid_string,validation:"datetime",message:l.message}),j.dirty()):"date"===l.kind?J.test(a.data)||(k(i=this._getOrReturnCtx(a,i),{code:g.eq.invalid_string,validation:"date",message:l.message}),j.dirty()):"time"===l.kind?RegExp(`^${K(l)}$`).test(a.data)||(k(i=this._getOrReturnCtx(a,i),{code:g.eq.invalid_string,validation:"time",message:l.message}),j.dirty()):"duration"===l.kind?A.test(a.data)||(k(i=this._getOrReturnCtx(a,i),{validation:"duration",code:g.eq.invalid_string,message:l.message}),j.dirty()):"ip"===l.kind?(b=a.data,!(("v4"===(c=l.version)||!c)&&C.test(b)||("v6"===c||!c)&&E.test(b))&&1&&(k(i=this._getOrReturnCtx(a,i),{validation:"ip",code:g.eq.invalid_string,message:l.message}),j.dirty())):"jwt"===l.kind?!function(a,b){if(!z.test(a))return!1;try{let[c]=a.split(".");if(!c)return!1;let d=c.replace(/-/g,"+").replace(/_/g,"/").padEnd(c.length+(4-c.length%4)%4,"="),e=JSON.parse(atob(d));if("object"!=typeof e||null===e||"typ"in e&&e?.typ!=="JWT"||!e.alg||b&&e.alg!==b)return!1;return!0}catch{return!1}}(a.data,l.alg)&&(k(i=this._getOrReturnCtx(a,i),{validation:"jwt",code:g.eq.invalid_string,message:l.message}),j.dirty()):"cidr"===l.kind?(e=a.data,!(("v4"===(f=l.version)||!f)&&D.test(e)||("v6"===f||!f)&&F.test(e))&&1&&(k(i=this._getOrReturnCtx(a,i),{validation:"cidr",code:g.eq.invalid_string,message:l.message}),j.dirty())):"base64"===l.kind?G.test(a.data)||(k(i=this._getOrReturnCtx(a,i),{validation:"base64",code:g.eq.invalid_string,message:l.message}),j.dirty()):"base64url"===l.kind?H.test(a.data)||(k(i=this._getOrReturnCtx(a,i),{validation:"base64url",code:g.eq.invalid_string,message:l.message}),j.dirty()):h.ZS.assertNever(l);return{status:j.value,value:a.data}}_regex(a,b,c){return this.refinement(b=>a.test(b),{validation:b,code:g.eq.invalid_string,...e.errToObj(c)})}_addCheck(a){return new L({...this._def,checks:[...this._def.checks,a]})}email(a){return this._addCheck({kind:"email",...e.errToObj(a)})}url(a){return this._addCheck({kind:"url",...e.errToObj(a)})}emoji(a){return this._addCheck({kind:"emoji",...e.errToObj(a)})}uuid(a){return this._addCheck({kind:"uuid",...e.errToObj(a)})}nanoid(a){return this._addCheck({kind:"nanoid",...e.errToObj(a)})}cuid(a){return this._addCheck({kind:"cuid",...e.errToObj(a)})}cuid2(a){return this._addCheck({kind:"cuid2",...e.errToObj(a)})}ulid(a){return this._addCheck({kind:"ulid",...e.errToObj(a)})}base64(a){return this._addCheck({kind:"base64",...e.errToObj(a)})}base64url(a){return this._addCheck({kind:"base64url",...e.errToObj(a)})}jwt(a){return this._addCheck({kind:"jwt",...e.errToObj(a)})}ip(a){return this._addCheck({kind:"ip",...e.errToObj(a)})}cidr(a){return this._addCheck({kind:"cidr",...e.errToObj(a)})}datetime(a){return"string"==typeof a?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:a}):this._addCheck({kind:"datetime",precision:void 0===a?.precision?null:a?.precision,offset:a?.offset??!1,local:a?.local??!1,...e.errToObj(a?.message)})}date(a){return this._addCheck({kind:"date",message:a})}time(a){return"string"==typeof a?this._addCheck({kind:"time",precision:null,message:a}):this._addCheck({kind:"time",precision:void 0===a?.precision?null:a?.precision,...e.errToObj(a?.message)})}duration(a){return this._addCheck({kind:"duration",...e.errToObj(a)})}regex(a,b){return this._addCheck({kind:"regex",regex:a,...e.errToObj(b)})}includes(a,b){return this._addCheck({kind:"includes",value:a,position:b?.position,...e.errToObj(b?.message)})}startsWith(a,b){return this._addCheck({kind:"startsWith",value:a,...e.errToObj(b)})}endsWith(a,b){return this._addCheck({kind:"endsWith",value:a,...e.errToObj(b)})}min(a,b){return this._addCheck({kind:"min",value:a,...e.errToObj(b)})}max(a,b){return this._addCheck({kind:"max",value:a,...e.errToObj(b)})}length(a,b){return this._addCheck({kind:"length",value:a,...e.errToObj(b)})}nonempty(a){return this.min(1,e.errToObj(a))}trim(){return new L({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new L({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new L({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(a=>"datetime"===a.kind)}get isDate(){return!!this._def.checks.find(a=>"date"===a.kind)}get isTime(){return!!this._def.checks.find(a=>"time"===a.kind)}get isDuration(){return!!this._def.checks.find(a=>"duration"===a.kind)}get isEmail(){return!!this._def.checks.find(a=>"email"===a.kind)}get isURL(){return!!this._def.checks.find(a=>"url"===a.kind)}get isEmoji(){return!!this._def.checks.find(a=>"emoji"===a.kind)}get isUUID(){return!!this._def.checks.find(a=>"uuid"===a.kind)}get isNANOID(){return!!this._def.checks.find(a=>"nanoid"===a.kind)}get isCUID(){return!!this._def.checks.find(a=>"cuid"===a.kind)}get isCUID2(){return!!this._def.checks.find(a=>"cuid2"===a.kind)}get isULID(){return!!this._def.checks.find(a=>"ulid"===a.kind)}get isIP(){return!!this._def.checks.find(a=>"ip"===a.kind)}get isCIDR(){return!!this._def.checks.find(a=>"cidr"===a.kind)}get isBase64(){return!!this._def.checks.find(a=>"base64"===a.kind)}get isBase64url(){return!!this._def.checks.find(a=>"base64url"===a.kind)}get minLength(){let a=null;for(let b of this._def.checks)"min"===b.kind&&(null===a||b.value>a)&&(a=b.value);return a}get maxLength(){let a=null;for(let b of this._def.checks)"max"===b.kind&&(null===a||b.value<a)&&(a=b.value);return a}}L.create=a=>new L({checks:[],typeName:f.ZodString,coerce:a?.coerce??!1,...s(a)});class M extends t{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(a){let b;if(this._def.coerce&&(a.data=Number(a.data)),this._getType(a)!==h.Zp.number){let b=this._getOrReturnCtx(a);return k(b,{code:g.eq.invalid_type,expected:h.Zp.number,received:b.parsedType}),m}let c=new l;for(let d of this._def.checks)"int"===d.kind?h.ZS.isInteger(a.data)||(k(b=this._getOrReturnCtx(a,b),{code:g.eq.invalid_type,expected:"integer",received:"float",message:d.message}),c.dirty()):"min"===d.kind?(d.inclusive?a.data<d.value:a.data<=d.value)&&(k(b=this._getOrReturnCtx(a,b),{code:g.eq.too_small,minimum:d.value,type:"number",inclusive:d.inclusive,exact:!1,message:d.message}),c.dirty()):"max"===d.kind?(d.inclusive?a.data>d.value:a.data>=d.value)&&(k(b=this._getOrReturnCtx(a,b),{code:g.eq.too_big,maximum:d.value,type:"number",inclusive:d.inclusive,exact:!1,message:d.message}),c.dirty()):"multipleOf"===d.kind?0!==function(a,b){let c=(a.toString().split(".")[1]||"").length,d=(b.toString().split(".")[1]||"").length,e=c>d?c:d;return Number.parseInt(a.toFixed(e).replace(".",""))%Number.parseInt(b.toFixed(e).replace(".",""))/10**e}(a.data,d.value)&&(k(b=this._getOrReturnCtx(a,b),{code:g.eq.not_multiple_of,multipleOf:d.value,message:d.message}),c.dirty()):"finite"===d.kind?Number.isFinite(a.data)||(k(b=this._getOrReturnCtx(a,b),{code:g.eq.not_finite,message:d.message}),c.dirty()):h.ZS.assertNever(d);return{status:c.value,value:a.data}}gte(a,b){return this.setLimit("min",a,!0,e.toString(b))}gt(a,b){return this.setLimit("min",a,!1,e.toString(b))}lte(a,b){return this.setLimit("max",a,!0,e.toString(b))}lt(a,b){return this.setLimit("max",a,!1,e.toString(b))}setLimit(a,b,c,d){return new M({...this._def,checks:[...this._def.checks,{kind:a,value:b,inclusive:c,message:e.toString(d)}]})}_addCheck(a){return new M({...this._def,checks:[...this._def.checks,a]})}int(a){return this._addCheck({kind:"int",message:e.toString(a)})}positive(a){return this._addCheck({kind:"min",value:0,inclusive:!1,message:e.toString(a)})}negative(a){return this._addCheck({kind:"max",value:0,inclusive:!1,message:e.toString(a)})}nonpositive(a){return this._addCheck({kind:"max",value:0,inclusive:!0,message:e.toString(a)})}nonnegative(a){return this._addCheck({kind:"min",value:0,inclusive:!0,message:e.toString(a)})}multipleOf(a,b){return this._addCheck({kind:"multipleOf",value:a,message:e.toString(b)})}finite(a){return this._addCheck({kind:"finite",message:e.toString(a)})}safe(a){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:e.toString(a)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:e.toString(a)})}get minValue(){let a=null;for(let b of this._def.checks)"min"===b.kind&&(null===a||b.value>a)&&(a=b.value);return a}get maxValue(){let a=null;for(let b of this._def.checks)"max"===b.kind&&(null===a||b.value<a)&&(a=b.value);return a}get isInt(){return!!this._def.checks.find(a=>"int"===a.kind||"multipleOf"===a.kind&&h.ZS.isInteger(a.value))}get isFinite(){let a=null,b=null;for(let c of this._def.checks)if("finite"===c.kind||"int"===c.kind||"multipleOf"===c.kind)return!0;else"min"===c.kind?(null===b||c.value>b)&&(b=c.value):"max"===c.kind&&(null===a||c.value<a)&&(a=c.value);return Number.isFinite(b)&&Number.isFinite(a)}}M.create=a=>new M({checks:[],typeName:f.ZodNumber,coerce:a?.coerce||!1,...s(a)});class N extends t{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(a){let b;if(this._def.coerce)try{a.data=BigInt(a.data)}catch{return this._getInvalidInput(a)}if(this._getType(a)!==h.Zp.bigint)return this._getInvalidInput(a);let c=new l;for(let d of this._def.checks)"min"===d.kind?(d.inclusive?a.data<d.value:a.data<=d.value)&&(k(b=this._getOrReturnCtx(a,b),{code:g.eq.too_small,type:"bigint",minimum:d.value,inclusive:d.inclusive,message:d.message}),c.dirty()):"max"===d.kind?(d.inclusive?a.data>d.value:a.data>=d.value)&&(k(b=this._getOrReturnCtx(a,b),{code:g.eq.too_big,type:"bigint",maximum:d.value,inclusive:d.inclusive,message:d.message}),c.dirty()):"multipleOf"===d.kind?a.data%d.value!==BigInt(0)&&(k(b=this._getOrReturnCtx(a,b),{code:g.eq.not_multiple_of,multipleOf:d.value,message:d.message}),c.dirty()):h.ZS.assertNever(d);return{status:c.value,value:a.data}}_getInvalidInput(a){let b=this._getOrReturnCtx(a);return k(b,{code:g.eq.invalid_type,expected:h.Zp.bigint,received:b.parsedType}),m}gte(a,b){return this.setLimit("min",a,!0,e.toString(b))}gt(a,b){return this.setLimit("min",a,!1,e.toString(b))}lte(a,b){return this.setLimit("max",a,!0,e.toString(b))}lt(a,b){return this.setLimit("max",a,!1,e.toString(b))}setLimit(a,b,c,d){return new N({...this._def,checks:[...this._def.checks,{kind:a,value:b,inclusive:c,message:e.toString(d)}]})}_addCheck(a){return new N({...this._def,checks:[...this._def.checks,a]})}positive(a){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:e.toString(a)})}negative(a){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:e.toString(a)})}nonpositive(a){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:e.toString(a)})}nonnegative(a){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:e.toString(a)})}multipleOf(a,b){return this._addCheck({kind:"multipleOf",value:a,message:e.toString(b)})}get minValue(){let a=null;for(let b of this._def.checks)"min"===b.kind&&(null===a||b.value>a)&&(a=b.value);return a}get maxValue(){let a=null;for(let b of this._def.checks)"max"===b.kind&&(null===a||b.value<a)&&(a=b.value);return a}}N.create=a=>new N({checks:[],typeName:f.ZodBigInt,coerce:a?.coerce??!1,...s(a)});class O extends t{_parse(a){if(this._def.coerce&&(a.data=!!a.data),this._getType(a)!==h.Zp.boolean){let b=this._getOrReturnCtx(a);return k(b,{code:g.eq.invalid_type,expected:h.Zp.boolean,received:b.parsedType}),m}return o(a.data)}}O.create=a=>new O({typeName:f.ZodBoolean,coerce:a?.coerce||!1,...s(a)});class P extends t{_parse(a){let b;if(this._def.coerce&&(a.data=new Date(a.data)),this._getType(a)!==h.Zp.date){let b=this._getOrReturnCtx(a);return k(b,{code:g.eq.invalid_type,expected:h.Zp.date,received:b.parsedType}),m}if(Number.isNaN(a.data.getTime()))return k(this._getOrReturnCtx(a),{code:g.eq.invalid_date}),m;let c=new l;for(let d of this._def.checks)"min"===d.kind?a.data.getTime()<d.value&&(k(b=this._getOrReturnCtx(a,b),{code:g.eq.too_small,message:d.message,inclusive:!0,exact:!1,minimum:d.value,type:"date"}),c.dirty()):"max"===d.kind?a.data.getTime()>d.value&&(k(b=this._getOrReturnCtx(a,b),{code:g.eq.too_big,message:d.message,inclusive:!0,exact:!1,maximum:d.value,type:"date"}),c.dirty()):h.ZS.assertNever(d);return{status:c.value,value:new Date(a.data.getTime())}}_addCheck(a){return new P({...this._def,checks:[...this._def.checks,a]})}min(a,b){return this._addCheck({kind:"min",value:a.getTime(),message:e.toString(b)})}max(a,b){return this._addCheck({kind:"max",value:a.getTime(),message:e.toString(b)})}get minDate(){let a=null;for(let b of this._def.checks)"min"===b.kind&&(null===a||b.value>a)&&(a=b.value);return null!=a?new Date(a):null}get maxDate(){let a=null;for(let b of this._def.checks)"max"===b.kind&&(null===a||b.value<a)&&(a=b.value);return null!=a?new Date(a):null}}P.create=a=>new P({checks:[],coerce:a?.coerce||!1,typeName:f.ZodDate,...s(a)});class Q extends t{_parse(a){if(this._getType(a)!==h.Zp.symbol){let b=this._getOrReturnCtx(a);return k(b,{code:g.eq.invalid_type,expected:h.Zp.symbol,received:b.parsedType}),m}return o(a.data)}}Q.create=a=>new Q({typeName:f.ZodSymbol,...s(a)});class R extends t{_parse(a){if(this._getType(a)!==h.Zp.undefined){let b=this._getOrReturnCtx(a);return k(b,{code:g.eq.invalid_type,expected:h.Zp.undefined,received:b.parsedType}),m}return o(a.data)}}R.create=a=>new R({typeName:f.ZodUndefined,...s(a)});class S extends t{_parse(a){if(this._getType(a)!==h.Zp.null){let b=this._getOrReturnCtx(a);return k(b,{code:g.eq.invalid_type,expected:h.Zp.null,received:b.parsedType}),m}return o(a.data)}}S.create=a=>new S({typeName:f.ZodNull,...s(a)});class T extends t{constructor(){super(...arguments),this._any=!0}_parse(a){return o(a.data)}}T.create=a=>new T({typeName:f.ZodAny,...s(a)});class U extends t{constructor(){super(...arguments),this._unknown=!0}_parse(a){return o(a.data)}}U.create=a=>new U({typeName:f.ZodUnknown,...s(a)});class V extends t{_parse(a){let b=this._getOrReturnCtx(a);return k(b,{code:g.eq.invalid_type,expected:h.Zp.never,received:b.parsedType}),m}}V.create=a=>new V({typeName:f.ZodNever,...s(a)});class W extends t{_parse(a){if(this._getType(a)!==h.Zp.undefined){let b=this._getOrReturnCtx(a);return k(b,{code:g.eq.invalid_type,expected:h.Zp.void,received:b.parsedType}),m}return o(a.data)}}W.create=a=>new W({typeName:f.ZodVoid,...s(a)});class X extends t{_parse(a){let{ctx:b,status:c}=this._processInputParams(a),d=this._def;if(b.parsedType!==h.Zp.array)return k(b,{code:g.eq.invalid_type,expected:h.Zp.array,received:b.parsedType}),m;if(null!==d.exactLength){let a=b.data.length>d.exactLength.value,e=b.data.length<d.exactLength.value;(a||e)&&(k(b,{code:a?g.eq.too_big:g.eq.too_small,minimum:e?d.exactLength.value:void 0,maximum:a?d.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:d.exactLength.message}),c.dirty())}if(null!==d.minLength&&b.data.length<d.minLength.value&&(k(b,{code:g.eq.too_small,minimum:d.minLength.value,type:"array",inclusive:!0,exact:!1,message:d.minLength.message}),c.dirty()),null!==d.maxLength&&b.data.length>d.maxLength.value&&(k(b,{code:g.eq.too_big,maximum:d.maxLength.value,type:"array",inclusive:!0,exact:!1,message:d.maxLength.message}),c.dirty()),b.common.async)return Promise.all([...b.data].map((a,c)=>d.type._parseAsync(new q(b,a,b.path,c)))).then(a=>l.mergeArray(c,a));let e=[...b.data].map((a,c)=>d.type._parseSync(new q(b,a,b.path,c)));return l.mergeArray(c,e)}get element(){return this._def.type}min(a,b){return new X({...this._def,minLength:{value:a,message:e.toString(b)}})}max(a,b){return new X({...this._def,maxLength:{value:a,message:e.toString(b)}})}length(a,b){return new X({...this._def,exactLength:{value:a,message:e.toString(b)}})}nonempty(a){return this.min(1,a)}}X.create=(a,b)=>new X({type:a,minLength:null,maxLength:null,exactLength:null,typeName:f.ZodArray,...s(b)});class Y extends t{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;let a=this._def.shape(),b=h.ZS.objectKeys(a);return this._cached={shape:a,keys:b},this._cached}_parse(a){if(this._getType(a)!==h.Zp.object){let b=this._getOrReturnCtx(a);return k(b,{code:g.eq.invalid_type,expected:h.Zp.object,received:b.parsedType}),m}let{status:b,ctx:c}=this._processInputParams(a),{shape:d,keys:e}=this._getCached(),f=[];if(!(this._def.catchall instanceof V&&"strip"===this._def.unknownKeys))for(let a in c.data)e.includes(a)||f.push(a);let i=[];for(let a of e){let b=d[a],e=c.data[a];i.push({key:{status:"valid",value:a},value:b._parse(new q(c,e,c.path,a)),alwaysSet:a in c.data})}if(this._def.catchall instanceof V){let a=this._def.unknownKeys;if("passthrough"===a)for(let a of f)i.push({key:{status:"valid",value:a},value:{status:"valid",value:c.data[a]}});else if("strict"===a)f.length>0&&(k(c,{code:g.eq.unrecognized_keys,keys:f}),b.dirty());else if("strip"===a);else throw Error("Internal ZodObject error: invalid unknownKeys value.")}else{let a=this._def.catchall;for(let b of f){let d=c.data[b];i.push({key:{status:"valid",value:b},value:a._parse(new q(c,d,c.path,b)),alwaysSet:b in c.data})}}return c.common.async?Promise.resolve().then(async()=>{let a=[];for(let b of i){let c=await b.key,d=await b.value;a.push({key:c,value:d,alwaysSet:b.alwaysSet})}return a}).then(a=>l.mergeObjectSync(b,a)):l.mergeObjectSync(b,i)}get shape(){return this._def.shape()}strict(a){return e.errToObj,new Y({...this._def,unknownKeys:"strict",...void 0!==a?{errorMap:(b,c)=>{let d=this._def.errorMap?.(b,c).message??c.defaultError;return"unrecognized_keys"===b.code?{message:e.errToObj(a).message??d}:{message:d}}}:{}})}strip(){return new Y({...this._def,unknownKeys:"strip"})}passthrough(){return new Y({...this._def,unknownKeys:"passthrough"})}extend(a){return new Y({...this._def,shape:()=>({...this._def.shape(),...a})})}merge(a){return new Y({unknownKeys:a._def.unknownKeys,catchall:a._def.catchall,shape:()=>({...this._def.shape(),...a._def.shape()}),typeName:f.ZodObject})}setKey(a,b){return this.augment({[a]:b})}catchall(a){return new Y({...this._def,catchall:a})}pick(a){let b={};for(let c of h.ZS.objectKeys(a))a[c]&&this.shape[c]&&(b[c]=this.shape[c]);return new Y({...this._def,shape:()=>b})}omit(a){let b={};for(let c of h.ZS.objectKeys(this.shape))a[c]||(b[c]=this.shape[c]);return new Y({...this._def,shape:()=>b})}deepPartial(){return function a(b){if(b instanceof Y){let c={};for(let d in b.shape){let e=b.shape[d];c[d]=an.create(a(e))}return new Y({...b._def,shape:()=>c})}if(b instanceof X)return new X({...b._def,type:a(b.element)});if(b instanceof an)return an.create(a(b.unwrap()));if(b instanceof ao)return ao.create(a(b.unwrap()));if(b instanceof ab)return ab.create(b.items.map(b=>a(b)));else return b}(this)}partial(a){let b={};for(let c of h.ZS.objectKeys(this.shape)){let d=this.shape[c];a&&!a[c]?b[c]=d:b[c]=d.optional()}return new Y({...this._def,shape:()=>b})}required(a){let b={};for(let c of h.ZS.objectKeys(this.shape))if(a&&!a[c])b[c]=this.shape[c];else{let a=this.shape[c];for(;a instanceof an;)a=a._def.innerType;b[c]=a}return new Y({...this._def,shape:()=>b})}keyof(){return ai(h.ZS.objectKeys(this.shape))}}Y.create=(a,b)=>new Y({shape:()=>a,unknownKeys:"strip",catchall:V.create(),typeName:f.ZodObject,...s(b)}),Y.strictCreate=(a,b)=>new Y({shape:()=>a,unknownKeys:"strict",catchall:V.create(),typeName:f.ZodObject,...s(b)}),Y.lazycreate=(a,b)=>new Y({shape:a,unknownKeys:"strip",catchall:V.create(),typeName:f.ZodObject,...s(b)});class Z extends t{_parse(a){let{ctx:b}=this._processInputParams(a),c=this._def.options;if(b.common.async)return Promise.all(c.map(async a=>{let c={...b,common:{...b.common,issues:[]},parent:null};return{result:await a._parseAsync({data:b.data,path:b.path,parent:c}),ctx:c}})).then(function(a){for(let b of a)if("valid"===b.result.status)return b.result;for(let c of a)if("dirty"===c.result.status)return b.common.issues.push(...c.ctx.common.issues),c.result;let c=a.map(a=>new g.G(a.ctx.common.issues));return k(b,{code:g.eq.invalid_union,unionErrors:c}),m});{let a,d=[];for(let e of c){let c={...b,common:{...b.common,issues:[]},parent:null},f=e._parseSync({data:b.data,path:b.path,parent:c});if("valid"===f.status)return f;"dirty"!==f.status||a||(a={result:f,ctx:c}),c.common.issues.length&&d.push(c.common.issues)}if(a)return b.common.issues.push(...a.ctx.common.issues),a.result;let e=d.map(a=>new g.G(a));return k(b,{code:g.eq.invalid_union,unionErrors:e}),m}}get options(){return this._def.options}}Z.create=(a,b)=>new Z({options:a,typeName:f.ZodUnion,...s(b)});let $=a=>{if(a instanceof ag)return $(a.schema);if(a instanceof am)return $(a.innerType());if(a instanceof ah)return[a.value];if(a instanceof aj)return a.options;if(a instanceof ak)return h.ZS.objectValues(a.enum);else if(a instanceof ap)return $(a._def.innerType);else if(a instanceof R)return[void 0];else if(a instanceof S)return[null];else if(a instanceof an)return[void 0,...$(a.unwrap())];else if(a instanceof ao)return[null,...$(a.unwrap())];else if(a instanceof as)return $(a.unwrap());else if(a instanceof au)return $(a.unwrap());else if(a instanceof aq)return $(a._def.innerType);else return[]};class _ extends t{_parse(a){let{ctx:b}=this._processInputParams(a);if(b.parsedType!==h.Zp.object)return k(b,{code:g.eq.invalid_type,expected:h.Zp.object,received:b.parsedType}),m;let c=this.discriminator,d=b.data[c],e=this.optionsMap.get(d);return e?b.common.async?e._parseAsync({data:b.data,path:b.path,parent:b}):e._parseSync({data:b.data,path:b.path,parent:b}):(k(b,{code:g.eq.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[c]}),m)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(a,b,c){let d=new Map;for(let c of b){let b=$(c.shape[a]);if(!b.length)throw Error(`A discriminator value for key \`${a}\` could not be extracted from all schema options`);for(let e of b){if(d.has(e))throw Error(`Discriminator property ${String(a)} has duplicate value ${String(e)}`);d.set(e,c)}}return new _({typeName:f.ZodDiscriminatedUnion,discriminator:a,options:b,optionsMap:d,...s(c)})}}class aa extends t{_parse(a){let{status:b,ctx:c}=this._processInputParams(a),d=(a,d)=>{if("aborted"===a.status||"aborted"===d.status)return m;let e=function a(b,c){let d=(0,h.CR)(b),e=(0,h.CR)(c);if(b===c)return{valid:!0,data:b};if(d===h.Zp.object&&e===h.Zp.object){let d=h.ZS.objectKeys(c),e=h.ZS.objectKeys(b).filter(a=>-1!==d.indexOf(a)),f={...b,...c};for(let d of e){let e=a(b[d],c[d]);if(!e.valid)return{valid:!1};f[d]=e.data}return{valid:!0,data:f}}if(d===h.Zp.array&&e===h.Zp.array){if(b.length!==c.length)return{valid:!1};let d=[];for(let e=0;e<b.length;e++){let f=a(b[e],c[e]);if(!f.valid)return{valid:!1};d.push(f.data)}return{valid:!0,data:d}}if(d===h.Zp.date&&e===h.Zp.date&&+b==+c)return{valid:!0,data:b};return{valid:!1}}(a.value,d.value);return e.valid?(("dirty"===a.status||"dirty"===d.status)&&b.dirty(),{status:b.value,value:e.data}):(k(c,{code:g.eq.invalid_intersection_types}),m)};return c.common.async?Promise.all([this._def.left._parseAsync({data:c.data,path:c.path,parent:c}),this._def.right._parseAsync({data:c.data,path:c.path,parent:c})]).then(([a,b])=>d(a,b)):d(this._def.left._parseSync({data:c.data,path:c.path,parent:c}),this._def.right._parseSync({data:c.data,path:c.path,parent:c}))}}aa.create=(a,b,c)=>new aa({left:a,right:b,typeName:f.ZodIntersection,...s(c)});class ab extends t{_parse(a){let{status:b,ctx:c}=this._processInputParams(a);if(c.parsedType!==h.Zp.array)return k(c,{code:g.eq.invalid_type,expected:h.Zp.array,received:c.parsedType}),m;if(c.data.length<this._def.items.length)return k(c,{code:g.eq.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),m;!this._def.rest&&c.data.length>this._def.items.length&&(k(c,{code:g.eq.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),b.dirty());let d=[...c.data].map((a,b)=>{let d=this._def.items[b]||this._def.rest;return d?d._parse(new q(c,a,c.path,b)):null}).filter(a=>!!a);return c.common.async?Promise.all(d).then(a=>l.mergeArray(b,a)):l.mergeArray(b,d)}get items(){return this._def.items}rest(a){return new ab({...this._def,rest:a})}}ab.create=(a,b)=>{if(!Array.isArray(a))throw Error("You must pass an array of schemas to z.tuple([ ... ])");return new ab({items:a,typeName:f.ZodTuple,rest:null,...s(b)})};class ac extends t{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(a){let{status:b,ctx:c}=this._processInputParams(a);if(c.parsedType!==h.Zp.object)return k(c,{code:g.eq.invalid_type,expected:h.Zp.object,received:c.parsedType}),m;let d=[],e=this._def.keyType,f=this._def.valueType;for(let a in c.data)d.push({key:e._parse(new q(c,a,c.path,a)),value:f._parse(new q(c,c.data[a],c.path,a)),alwaysSet:a in c.data});return c.common.async?l.mergeObjectAsync(b,d):l.mergeObjectSync(b,d)}get element(){return this._def.valueType}static create(a,b,c){return new ac(b instanceof t?{keyType:a,valueType:b,typeName:f.ZodRecord,...s(c)}:{keyType:L.create(),valueType:a,typeName:f.ZodRecord,...s(b)})}}class ad extends t{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(a){let{status:b,ctx:c}=this._processInputParams(a);if(c.parsedType!==h.Zp.map)return k(c,{code:g.eq.invalid_type,expected:h.Zp.map,received:c.parsedType}),m;let d=this._def.keyType,e=this._def.valueType,f=[...c.data.entries()].map(([a,b],f)=>({key:d._parse(new q(c,a,c.path,[f,"key"])),value:e._parse(new q(c,b,c.path,[f,"value"]))}));if(c.common.async){let a=new Map;return Promise.resolve().then(async()=>{for(let c of f){let d=await c.key,e=await c.value;if("aborted"===d.status||"aborted"===e.status)return m;("dirty"===d.status||"dirty"===e.status)&&b.dirty(),a.set(d.value,e.value)}return{status:b.value,value:a}})}{let a=new Map;for(let c of f){let d=c.key,e=c.value;if("aborted"===d.status||"aborted"===e.status)return m;("dirty"===d.status||"dirty"===e.status)&&b.dirty(),a.set(d.value,e.value)}return{status:b.value,value:a}}}}ad.create=(a,b,c)=>new ad({valueType:b,keyType:a,typeName:f.ZodMap,...s(c)});class ae extends t{_parse(a){let{status:b,ctx:c}=this._processInputParams(a);if(c.parsedType!==h.Zp.set)return k(c,{code:g.eq.invalid_type,expected:h.Zp.set,received:c.parsedType}),m;let d=this._def;null!==d.minSize&&c.data.size<d.minSize.value&&(k(c,{code:g.eq.too_small,minimum:d.minSize.value,type:"set",inclusive:!0,exact:!1,message:d.minSize.message}),b.dirty()),null!==d.maxSize&&c.data.size>d.maxSize.value&&(k(c,{code:g.eq.too_big,maximum:d.maxSize.value,type:"set",inclusive:!0,exact:!1,message:d.maxSize.message}),b.dirty());let e=this._def.valueType;function f(a){let c=new Set;for(let d of a){if("aborted"===d.status)return m;"dirty"===d.status&&b.dirty(),c.add(d.value)}return{status:b.value,value:c}}let i=[...c.data.values()].map((a,b)=>e._parse(new q(c,a,c.path,b)));return c.common.async?Promise.all(i).then(a=>f(a)):f(i)}min(a,b){return new ae({...this._def,minSize:{value:a,message:e.toString(b)}})}max(a,b){return new ae({...this._def,maxSize:{value:a,message:e.toString(b)}})}size(a,b){return this.min(a,b).max(a,b)}nonempty(a){return this.min(1,a)}}ae.create=(a,b)=>new ae({valueType:a,minSize:null,maxSize:null,typeName:f.ZodSet,...s(b)});class af extends t{constructor(){super(...arguments),this.validate=this.implement}_parse(a){let{ctx:b}=this._processInputParams(a);if(b.parsedType!==h.Zp.function)return k(b,{code:g.eq.invalid_type,expected:h.Zp.function,received:b.parsedType}),m;function c(a,c){return j({data:a,path:b.path,errorMaps:[b.common.contextualErrorMap,b.schemaErrorMap,i,i].filter(a=>!!a),issueData:{code:g.eq.invalid_arguments,argumentsError:c}})}function d(a,c){return j({data:a,path:b.path,errorMaps:[b.common.contextualErrorMap,b.schemaErrorMap,i,i].filter(a=>!!a),issueData:{code:g.eq.invalid_return_type,returnTypeError:c}})}let e={errorMap:b.common.contextualErrorMap},f=b.data;if(this._def.returns instanceof al){let a=this;return o(async function(...b){let h=new g.G([]),i=await a._def.args.parseAsync(b,e).catch(a=>{throw h.addIssue(c(b,a)),h}),j=await Reflect.apply(f,this,i);return await a._def.returns._def.type.parseAsync(j,e).catch(a=>{throw h.addIssue(d(j,a)),h})})}{let a=this;return o(function(...b){let h=a._def.args.safeParse(b,e);if(!h.success)throw new g.G([c(b,h.error)]);let i=Reflect.apply(f,this,h.data),j=a._def.returns.safeParse(i,e);if(!j.success)throw new g.G([d(i,j.error)]);return j.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...a){return new af({...this._def,args:ab.create(a).rest(U.create())})}returns(a){return new af({...this._def,returns:a})}implement(a){return this.parse(a)}strictImplement(a){return this.parse(a)}static create(a,b,c){return new af({args:a||ab.create([]).rest(U.create()),returns:b||U.create(),typeName:f.ZodFunction,...s(c)})}}class ag extends t{get schema(){return this._def.getter()}_parse(a){let{ctx:b}=this._processInputParams(a);return this._def.getter()._parse({data:b.data,path:b.path,parent:b})}}ag.create=(a,b)=>new ag({getter:a,typeName:f.ZodLazy,...s(b)});class ah extends t{_parse(a){if(a.data!==this._def.value){let b=this._getOrReturnCtx(a);return k(b,{received:b.data,code:g.eq.invalid_literal,expected:this._def.value}),m}return{status:"valid",value:a.data}}get value(){return this._def.value}}function ai(a,b){return new aj({values:a,typeName:f.ZodEnum,...s(b)})}ah.create=(a,b)=>new ah({value:a,typeName:f.ZodLiteral,...s(b)});class aj extends t{_parse(a){if("string"!=typeof a.data){let b=this._getOrReturnCtx(a),c=this._def.values;return k(b,{expected:h.ZS.joinValues(c),received:b.parsedType,code:g.eq.invalid_type}),m}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(a.data)){let b=this._getOrReturnCtx(a),c=this._def.values;return k(b,{received:b.data,code:g.eq.invalid_enum_value,options:c}),m}return o(a.data)}get options(){return this._def.values}get enum(){let a={};for(let b of this._def.values)a[b]=b;return a}get Values(){let a={};for(let b of this._def.values)a[b]=b;return a}get Enum(){let a={};for(let b of this._def.values)a[b]=b;return a}extract(a,b=this._def){return aj.create(a,{...this._def,...b})}exclude(a,b=this._def){return aj.create(this.options.filter(b=>!a.includes(b)),{...this._def,...b})}}aj.create=ai;class ak extends t{_parse(a){let b=h.ZS.getValidEnumValues(this._def.values),c=this._getOrReturnCtx(a);if(c.parsedType!==h.Zp.string&&c.parsedType!==h.Zp.number){let a=h.ZS.objectValues(b);return k(c,{expected:h.ZS.joinValues(a),received:c.parsedType,code:g.eq.invalid_type}),m}if(this._cache||(this._cache=new Set(h.ZS.getValidEnumValues(this._def.values))),!this._cache.has(a.data)){let a=h.ZS.objectValues(b);return k(c,{received:c.data,code:g.eq.invalid_enum_value,options:a}),m}return o(a.data)}get enum(){return this._def.values}}ak.create=(a,b)=>new ak({values:a,typeName:f.ZodNativeEnum,...s(b)});class al extends t{unwrap(){return this._def.type}_parse(a){let{ctx:b}=this._processInputParams(a);return b.parsedType!==h.Zp.promise&&!1===b.common.async?(k(b,{code:g.eq.invalid_type,expected:h.Zp.promise,received:b.parsedType}),m):o((b.parsedType===h.Zp.promise?b.data:Promise.resolve(b.data)).then(a=>this._def.type.parseAsync(a,{path:b.path,errorMap:b.common.contextualErrorMap})))}}al.create=(a,b)=>new al({type:a,typeName:f.ZodPromise,...s(b)});class am extends t{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===f.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(a){let{status:b,ctx:c}=this._processInputParams(a),d=this._def.effect||null,e={addIssue:a=>{k(c,a),a.fatal?b.abort():b.dirty()},get path(){return c.path}};if(e.addIssue=e.addIssue.bind(e),"preprocess"===d.type){let a=d.transform(c.data,e);if(c.common.async)return Promise.resolve(a).then(async a=>{if("aborted"===b.value)return m;let d=await this._def.schema._parseAsync({data:a,path:c.path,parent:c});return"aborted"===d.status?m:"dirty"===d.status||"dirty"===b.value?n(d.value):d});{if("aborted"===b.value)return m;let d=this._def.schema._parseSync({data:a,path:c.path,parent:c});return"aborted"===d.status?m:"dirty"===d.status||"dirty"===b.value?n(d.value):d}}if("refinement"===d.type){let a=a=>{let b=d.refinement(a,e);if(c.common.async)return Promise.resolve(b);if(b instanceof Promise)throw Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return a};if(!1!==c.common.async)return this._def.schema._parseAsync({data:c.data,path:c.path,parent:c}).then(c=>"aborted"===c.status?m:("dirty"===c.status&&b.dirty(),a(c.value).then(()=>({status:b.value,value:c.value}))));{let d=this._def.schema._parseSync({data:c.data,path:c.path,parent:c});return"aborted"===d.status?m:("dirty"===d.status&&b.dirty(),a(d.value),{status:b.value,value:d.value})}}if("transform"===d.type)if(!1!==c.common.async)return this._def.schema._parseAsync({data:c.data,path:c.path,parent:c}).then(a=>"valid"!==a.status?m:Promise.resolve(d.transform(a.value,e)).then(a=>({status:b.value,value:a})));else{let a=this._def.schema._parseSync({data:c.data,path:c.path,parent:c});if("valid"!==a.status)return m;let f=d.transform(a.value,e);if(f instanceof Promise)throw Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:b.value,value:f}}h.ZS.assertNever(d)}}am.create=(a,b,c)=>new am({schema:a,typeName:f.ZodEffects,effect:b,...s(c)}),am.createWithPreprocess=(a,b,c)=>new am({schema:b,effect:{type:"preprocess",transform:a},typeName:f.ZodEffects,...s(c)});class an extends t{_parse(a){return this._getType(a)===h.Zp.undefined?o(void 0):this._def.innerType._parse(a)}unwrap(){return this._def.innerType}}an.create=(a,b)=>new an({innerType:a,typeName:f.ZodOptional,...s(b)});class ao extends t{_parse(a){return this._getType(a)===h.Zp.null?o(null):this._def.innerType._parse(a)}unwrap(){return this._def.innerType}}ao.create=(a,b)=>new ao({innerType:a,typeName:f.ZodNullable,...s(b)});class ap extends t{_parse(a){let{ctx:b}=this._processInputParams(a),c=b.data;return b.parsedType===h.Zp.undefined&&(c=this._def.defaultValue()),this._def.innerType._parse({data:c,path:b.path,parent:b})}removeDefault(){return this._def.innerType}}ap.create=(a,b)=>new ap({innerType:a,typeName:f.ZodDefault,defaultValue:"function"==typeof b.default?b.default:()=>b.default,...s(b)});class aq extends t{_parse(a){let{ctx:b}=this._processInputParams(a),c={...b,common:{...b.common,issues:[]}},d=this._def.innerType._parse({data:c.data,path:c.path,parent:{...c}});return p(d)?d.then(a=>({status:"valid",value:"valid"===a.status?a.value:this._def.catchValue({get error(){return new g.G(c.common.issues)},input:c.data})})):{status:"valid",value:"valid"===d.status?d.value:this._def.catchValue({get error(){return new g.G(c.common.issues)},input:c.data})}}removeCatch(){return this._def.innerType}}aq.create=(a,b)=>new aq({innerType:a,typeName:f.ZodCatch,catchValue:"function"==typeof b.catch?b.catch:()=>b.catch,...s(b)});class ar extends t{_parse(a){if(this._getType(a)!==h.Zp.nan){let b=this._getOrReturnCtx(a);return k(b,{code:g.eq.invalid_type,expected:h.Zp.nan,received:b.parsedType}),m}return{status:"valid",value:a.data}}}ar.create=a=>new ar({typeName:f.ZodNaN,...s(a)}),Symbol("zod_brand");class as extends t{_parse(a){let{ctx:b}=this._processInputParams(a),c=b.data;return this._def.type._parse({data:c,path:b.path,parent:b})}unwrap(){return this._def.type}}class at extends t{_parse(a){let{status:b,ctx:c}=this._processInputParams(a);if(c.common.async)return(async()=>{let a=await this._def.in._parseAsync({data:c.data,path:c.path,parent:c});return"aborted"===a.status?m:"dirty"===a.status?(b.dirty(),n(a.value)):this._def.out._parseAsync({data:a.value,path:c.path,parent:c})})();{let a=this._def.in._parseSync({data:c.data,path:c.path,parent:c});return"aborted"===a.status?m:"dirty"===a.status?(b.dirty(),{status:"dirty",value:a.value}):this._def.out._parseSync({data:a.value,path:c.path,parent:c})}}static create(a,b){return new at({in:a,out:b,typeName:f.ZodPipeline})}}class au extends t{_parse(a){let b=this._def.innerType._parse(a),c=a=>("valid"===a.status&&(a.value=Object.freeze(a.value)),a);return p(b)?b.then(a=>c(a)):c(b)}unwrap(){return this._def.innerType}}au.create=(a,b)=>new au({innerType:a,typeName:f.ZodReadonly,...s(b)}),Y.lazycreate,function(a){a.ZodString="ZodString",a.ZodNumber="ZodNumber",a.ZodNaN="ZodNaN",a.ZodBigInt="ZodBigInt",a.ZodBoolean="ZodBoolean",a.ZodDate="ZodDate",a.ZodSymbol="ZodSymbol",a.ZodUndefined="ZodUndefined",a.ZodNull="ZodNull",a.ZodAny="ZodAny",a.ZodUnknown="ZodUnknown",a.ZodNever="ZodNever",a.ZodVoid="ZodVoid",a.ZodArray="ZodArray",a.ZodObject="ZodObject",a.ZodUnion="ZodUnion",a.ZodDiscriminatedUnion="ZodDiscriminatedUnion",a.ZodIntersection="ZodIntersection",a.ZodTuple="ZodTuple",a.ZodRecord="ZodRecord",a.ZodMap="ZodMap",a.ZodSet="ZodSet",a.ZodFunction="ZodFunction",a.ZodLazy="ZodLazy",a.ZodLiteral="ZodLiteral",a.ZodEnum="ZodEnum",a.ZodEffects="ZodEffects",a.ZodNativeEnum="ZodNativeEnum",a.ZodOptional="ZodOptional",a.ZodNullable="ZodNullable",a.ZodDefault="ZodDefault",a.ZodCatch="ZodCatch",a.ZodPromise="ZodPromise",a.ZodBranded="ZodBranded",a.ZodPipeline="ZodPipeline",a.ZodReadonly="ZodReadonly"}(f||(f={}));let av=L.create,aw=M.create;ar.create,N.create;let ax=O.create;P.create,Q.create,R.create,S.create,T.create,U.create,V.create,W.create;let ay=X.create,az=Y.create;Y.strictCreate,Z.create,_.create,aa.create,ab.create,ac.create,ad.create,ae.create,af.create,ag.create,ah.create;let aA=aj.create;ak.create,al.create,am.create,an.create,ao.create,am.createWithPreprocess,at.create},2765:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"notFound",{enumerable:!0,get:function(){return e}});let d=""+c(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function e(){let a=Object.defineProperty(Error(d),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw a.digest=d,a}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},2871:(a,b,c)=>{"use strict";function d(a){let b="object"==typeof a.client?a.client:{},c="object"==typeof a.server?a.server:{},d=a.shared,e=a.runtimeEnv?a.runtimeEnv:{...process.env,...a.experimental__runtimeEnv};return function(a){let b=a.runtimeEnvStrict??a.runtimeEnv??process.env;if(a.emptyStringAsUndefined)for(let[a,c]of Object.entries(b))""===c&&delete b[a];if(a.skipValidation)return b;let c="object"==typeof a.client?a.client:{},d="object"==typeof a.server?a.server:{},e="object"==typeof a.shared?a.shared:{},f=a.isServer??("undefined"==typeof window||"Deno"in window),g=function(a,b){let c={},d=[];for(let e in a){let f=a[e],g=b[e],h=f["~standard"].validate(g);if(h instanceof Promise)throw Error(`Validation must be synchronous, but ${e} returned a Promise.`);if(h.issues){d.push(...h.issues.map(a=>({...a,path:[e,...a.path??[]]})));continue}c[e]=h.value}return d.length?{issues:d}:{value:c}}(f?{...d,...e,...c}:{...c,...e},b),h=a.onValidationError??(a=>{throw console.error("❌ Invalid environment variables:",a),Error("Invalid environment variables")}),i=a.onInvalidAccess??(()=>{throw Error("❌ Attempted to access a server-side environment variable on the client")});if(g.issues)return h(g.issues);let j=(a.extends??[]).reduce((a,b)=>Object.assign(a,b),{});return new Proxy(Object.assign(g.value,j),{get(b,c){if("string"==typeof c&&"__esModule"!==c&&"$$typeof"!==c)return f||a.clientPrefix&&(c.startsWith(a.clientPrefix)||c in e)?Reflect.get(b,c):i(c)}})}({...a,shared:d,client:b,server:c,clientPrefix:"NEXT_PUBLIC_",runtimeEnv:e})}c.d(b,{w:()=>d})},2944:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"connection",{enumerable:!0,get:function(){return j}});let d=c(9294),e=c(3033),f=c(4971),g=c(23),h=c(8388),i=c(8719);function j(){let a=d.workAsyncStorage.getStore(),b=e.workUnitAsyncStorage.getStore();if(a){if(b&&"after"===b.phase&&!(0,i.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${a.route} used "connection" inside "after(...)". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but "after(...)" executes after the request, so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E186",enumerable:!1,configurable:!0});if(a.forceStatic)return Promise.resolve(void 0);if(b){if("cache"===b.type)throw Object.defineProperty(Error(`Route ${a.route} used "connection" inside "use cache". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E111",enumerable:!1,configurable:!0});else if("unstable-cache"===b.type)throw Object.defineProperty(Error(`Route ${a.route} used "connection" inside a function cached with "unstable_cache(...)". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E1",enumerable:!1,configurable:!0})}if(a.dynamicShouldError)throw Object.defineProperty(new g.StaticGenBailoutError(`Route ${a.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`connection\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E562",enumerable:!1,configurable:!0});if(b)if("prerender"===b.type||"prerender-client"===b.type)return(0,h.makeHangingPromise)(b.renderSignal,"`connection()`");else"prerender-ppr"===b.type?(0,f.postponeWithTracking)(a.route,"connection",b.dynamicTracking):"prerender-legacy"===b.type&&(0,f.throwToInterruptStaticGeneration)("connection",a,b);(0,f.trackDynamicDataInDynamicRender)(a,b)}return Promise.resolve(void 0)}},3182:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{isBot:function(){return e},userAgent:function(){return g},userAgentFromString:function(){return f}});let d=function(a){return a&&a.__esModule?a:{default:a}}(c(397));function e(a){return/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Google-InspectionTool|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(a)}function f(a){return{...(0,d.default)(a),isBot:void 0!==a&&e(a)}}function g({headers:a}){return f(a.get("user-agent")||void 0)}},3381:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),function(a,b){Object.keys(a).forEach(function(c){"default"===c||Object.prototype.hasOwnProperty.call(b,c)||Object.defineProperty(b,c,{enumerable:!0,get:function(){return a[c]}})})}(c(4871),b)},3426:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"NextResponse",{enumerable:!0,get:function(){return l}});let d=c(3158),e=c(6608),f=c(7912),g=c(3763),h=c(3158),i=Symbol("internal response"),j=new Set([301,302,303,307,308]);function k(a,b){var c;if(null==a||null==(c=a.request)?void 0:c.headers){if(!(a.request.headers instanceof Headers))throw Object.defineProperty(Error("request.headers must be an instance of Headers"),"__NEXT_ERROR_CODE",{value:"E119",enumerable:!1,configurable:!0});let c=[];for(let[d,e]of a.request.headers)b.set("x-middleware-request-"+d,e),c.push(d);b.set("x-middleware-override-headers",c.join(","))}}class l extends Response{constructor(a,b={}){super(a,b);let c=this.headers,j=new Proxy(new h.ResponseCookies(c),{get(a,e,f){switch(e){case"delete":case"set":return(...f)=>{let g=Reflect.apply(a[e],a,f),i=new Headers(c);return g instanceof h.ResponseCookies&&c.set("x-middleware-set-cookie",g.getAll().map(a=>(0,d.stringifyCookie)(a)).join(",")),k(b,i),g};default:return g.ReflectAdapter.get(a,e,f)}}});this[i]={cookies:j,url:b.url?new e.NextURL(b.url,{headers:(0,f.toNodeOutgoingHttpHeaders)(c),nextConfig:b.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[i].cookies}static json(a,b){let c=Response.json(a,b);return new l(c.body,c)}static redirect(a,b){let c="number"==typeof b?b:(null==b?void 0:b.status)??307;if(!j.has(c))throw Object.defineProperty(RangeError('Failed to execute "redirect" on "response": Invalid status code'),"__NEXT_ERROR_CODE",{value:"E529",enumerable:!1,configurable:!0});let d="object"==typeof b?b:{},e=new Headers(null==d?void 0:d.headers);return e.set("Location",(0,f.validateURL)(a)),new l(null,{...d,headers:e,status:c})}static rewrite(a,b){let c=new Headers(null==b?void 0:b.headers);return c.set("x-middleware-rewrite",(0,f.validateURL)(a)),k(b,c),new l(null,{...b,headers:c})}static next(a){let b=new Headers(null==a?void 0:a.headers);return b.set("x-middleware-next","1"),k(a,b),new l(null,{...a,headers:b})}}},3643:(a,b,c)=>{"use strict";let d,e,f,g,h;c.d(b,{Ay:()=>fx});var i=function(a,b,c,d,e){if("m"===d)throw TypeError("Private method is not writable");if("a"===d&&!e)throw TypeError("Private accessor was defined without a setter");if("function"==typeof b?a!==b||!e:!b.has(a))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===d?e.call(a,c):e?e.value=c:b.set(a,c),c},j=function(a,b,c,d){if("a"===c&&!d)throw TypeError("Private accessor was defined without a getter");if("function"==typeof b?a!==b||!d:!b.has(a))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===c?d:"a"===c?d.call(a):d?d.value:b.get(a)};function k(a){let b=a?"__Secure-":"";return{sessionToken:{name:`${b}authjs.session-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a}},callbackUrl:{name:`${b}authjs.callback-url`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a}},csrfToken:{name:`${a?"__Host-":""}authjs.csrf-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a}},pkceCodeVerifier:{name:`${b}authjs.pkce.code_verifier`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a,maxAge:900}},state:{name:`${b}authjs.state`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a,maxAge:900}},nonce:{name:`${b}authjs.nonce`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a}},webauthnChallenge:{name:`${b}authjs.challenge`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a,maxAge:900}}}}class l{constructor(a,b,c){if(cc.add(this),cd.set(this,{}),ce.set(this,void 0),cf.set(this,void 0),i(this,cf,c,"f"),i(this,ce,a,"f"),!b)return;let{name:d}=a;for(let[a,c]of Object.entries(b))a.startsWith(d)&&c&&(j(this,cd,"f")[a]=c)}get value(){return Object.keys(j(this,cd,"f")).sort((a,b)=>parseInt(a.split(".").pop()||"0")-parseInt(b.split(".").pop()||"0")).map(a=>j(this,cd,"f")[a]).join("")}chunk(a,b){let c=j(this,cc,"m",ch).call(this);for(let d of j(this,cc,"m",cg).call(this,{name:j(this,ce,"f").name,value:a,options:{...j(this,ce,"f").options,...b}}))c[d.name]=d;return Object.values(c)}clean(){return Object.values(j(this,cc,"m",ch).call(this))}}cd=new WeakMap,ce=new WeakMap,cf=new WeakMap,cc=new WeakSet,cg=function(a){let b=Math.ceil(a.value.length/3936);if(1===b)return j(this,cd,"f")[a.name]=a.value,[a];let c=[];for(let d=0;d<b;d++){let b=`${a.name}.${d}`,e=a.value.substr(3936*d,3936);c.push({...a,name:b,value:e}),j(this,cd,"f")[b]=e}return j(this,cf,"f").debug("CHUNKING_SESSION_COOKIE",{message:"Session cookie exceeds allowed 4096 bytes.",emptyCookieSize:160,valueSize:a.value.length,chunks:c.map(a=>a.value.length+160)}),c},ch=function(){let a={};for(let b in j(this,cd,"f"))delete j(this,cd,"f")?.[b],a[b]={name:b,value:"",options:{...j(this,ce,"f").options,maxAge:0}};return a};class m extends Error{constructor(a,b){a instanceof Error?super(void 0,{cause:{err:a,...a.cause,...b}}):"string"==typeof a?(b instanceof Error&&(b={err:b,...b.cause}),super(a,b)):super(void 0,a),this.name=this.constructor.name,this.type=this.constructor.type??"AuthError",this.kind=this.constructor.kind??"error",Error.captureStackTrace?.(this,this.constructor);let c=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${c}`}}class n extends m{}n.kind="signIn";class o extends m{}o.type="AdapterError";class p extends m{}p.type="AccessDenied";class q extends m{}q.type="CallbackRouteError";class r extends m{}r.type="ErrorPageLoop";class s extends m{}s.type="EventError";class t extends m{}t.type="InvalidCallbackUrl";class u extends n{constructor(){super(...arguments),this.code="credentials"}}u.type="CredentialsSignin";class v extends m{}v.type="InvalidEndpoints";class w extends m{}w.type="InvalidCheck";class x extends m{}x.type="JWTSessionError";class y extends m{}y.type="MissingAdapter";class z extends m{}z.type="MissingAdapterMethods";class A extends m{}A.type="MissingAuthorize";class B extends m{}B.type="MissingSecret";class C extends n{}C.type="OAuthAccountNotLinked";class D extends n{}D.type="OAuthCallbackError";class E extends m{}E.type="OAuthProfileParseError";class F extends m{}F.type="SessionTokenError";class G extends n{}G.type="OAuthSignInError";class H extends n{}H.type="EmailSignInError";class I extends m{}I.type="SignOutError";class J extends m{}J.type="UnknownAction";class K extends m{}K.type="UnsupportedStrategy";class L extends m{}L.type="InvalidProvider";class M extends m{}M.type="UntrustedHost";class N extends m{}N.type="Verification";class O extends n{}O.type="MissingCSRF";let P=new Set(["CredentialsSignin","OAuthAccountNotLinked","OAuthCallbackError","AccessDenied","Verification","MissingCSRF","AccountNotLinked","WebAuthnVerificationError"]);class Q extends m{}Q.type="DuplicateConditionalUI";class R extends m{}R.type="MissingWebAuthnAutocomplete";class S extends m{}S.type="WebAuthnVerificationError";class T extends n{}T.type="AccountNotLinked";class U extends m{}U.type="ExperimentalFeatureNotEnabled";let V=!1;function W(a,b){try{return/^https?:/.test(new URL(a,a.startsWith("/")?b:void 0).protocol)}catch{return!1}}let X=!1,Y=!1,Z=!1,$=["createVerificationToken","useVerificationToken","getUserByEmail"],_=["createUser","getUser","getUserByEmail","getUserByAccount","updateUser","linkAccount","createSession","getSessionAndUser","updateSession","deleteSession"],aa=["createUser","getUser","linkAccount","getAccount","getAuthenticator","createAuthenticator","listAuthenticatorsByUserId","updateAuthenticatorCounter"];var ab=c(5511);"function"!=typeof ab.hkdf||process.versions.electron||(d=async(...a)=>new Promise((b,c)=>{ab.hkdf(...a,(a,d)=>{a?c(a):b(new Uint8Array(d))})}));let ac=async(a,b,c,e,f)=>(d||((a,b,c,d,e)=>{let f=parseInt(a.substr(3),10)>>3||20,g=(0,ab.createHmac)(a,c.byteLength?c:new Uint8Array(f)).update(b).digest(),h=Math.ceil(e/f),i=new Uint8Array(f*h+d.byteLength+1),j=0,k=0;for(let b=1;b<=h;b++)i.set(d,k),i[k+d.byteLength]=b,i.set((0,ab.createHmac)(a,g).update(i.subarray(j,k+d.byteLength+1)).digest(),k),j=k,k+=f;return i.slice(0,e)}))(a,b,c,e,f);function ad(a,b){if("string"==typeof a)return new TextEncoder().encode(a);if(!(a instanceof Uint8Array))throw TypeError(`"${b}"" must be an instance of Uint8Array or a string`);return a}async function ae(a,b,c,d,e){return ac(function(a){switch(a){case"sha256":case"sha384":case"sha512":case"sha1":return a;default:throw TypeError('unsupported "digest" value')}}(a),function(a){let b=ad(a,"ikm");if(!b.byteLength)throw TypeError('"ikm" must be at least one byte in length');return b}(b),ad(c,"salt"),function(a){let b=ad(a,"info");if(b.byteLength>1024)throw TypeError('"info" must not contain more than 1024 bytes');return b}(d),function(a,b){if("number"!=typeof a||!Number.isInteger(a)||a<1)throw TypeError('"keylen" must be a positive integer');if(a>255*(parseInt(b.substr(3),10)>>3||20))throw TypeError('"keylen" too large');return a}(e,a))}var af=c(7598);let ag=(a,b)=>(0,af.createHash)(a).update(b).digest();var ah=c(4573);let ai=new TextEncoder,aj=new TextDecoder;function ak(...a){let b=new Uint8Array(a.reduce((a,{length:b})=>a+b,0)),c=0;for(let d of a)b.set(d,c),c+=d.length;return b}function al(a,b){return ak(ai.encode(a),new Uint8Array([0]),b)}function am(a,b,c){if(b<0||b>=0x100000000)throw RangeError(`value must be >= 0 and <= ${0x100000000-1}. Received ${b}`);a.set([b>>>24,b>>>16,b>>>8,255&b],c)}function an(a){let b=new Uint8Array(4);return am(b,a),b}function ao(a){return ak(an(a.length),a)}async function ap(a,b,c){let d=Math.ceil((b>>3)/32),e=new Uint8Array(32*d);for(let b=0;b<d;b++){let d=new Uint8Array(4+a.length+c.length);d.set(an(b+1)),d.set(a,4),d.set(c,4+a.length),e.set(await ag("sha256",d),32*b)}return e.slice(0,b>>3)}let aq=a=>ah.Buffer.from(a).toString("base64url"),ar=a=>new Uint8Array(ah.Buffer.from(function(a){let b=a;return b instanceof Uint8Array&&(b=aj.decode(b)),b}(a),"base64url"));class as extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(a,b){super(a,b),this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}class at extends as{static code="ERR_JWT_CLAIM_VALIDATION_FAILED";code="ERR_JWT_CLAIM_VALIDATION_FAILED";claim;reason;payload;constructor(a,b,c="unspecified",d="unspecified"){super(a,{cause:{claim:c,reason:d,payload:b}}),this.claim=c,this.reason=d,this.payload=b}}class au extends as{static code="ERR_JWT_EXPIRED";code="ERR_JWT_EXPIRED";claim;reason;payload;constructor(a,b,c="unspecified",d="unspecified"){super(a,{cause:{claim:c,reason:d,payload:b}}),this.claim=c,this.reason=d,this.payload=b}}class av extends as{static code="ERR_JOSE_ALG_NOT_ALLOWED";code="ERR_JOSE_ALG_NOT_ALLOWED"}class aw extends as{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class ax extends as{static code="ERR_JWE_DECRYPTION_FAILED";code="ERR_JWE_DECRYPTION_FAILED";constructor(a="decryption operation failed",b){super(a,b)}}class ay extends as{static code="ERR_JWE_INVALID";code="ERR_JWE_INVALID"}class az extends as{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class aA extends as{static code="ERR_JWK_INVALID";code="ERR_JWK_INVALID"}class aB extends as{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(a="multiple matching keys found in the JSON Web Key Set",b){super(a,b)}}function aC(a){if("object"!=typeof a||null===a||"[object Object]"!==Object.prototype.toString.call(a))return!1;if(null===Object.getPrototypeOf(a))return!0;let b=a;for(;null!==Object.getPrototypeOf(b);)b=Object.getPrototypeOf(b);return Object.getPrototypeOf(a)===b}let aD=(a,b)=>{if("string"!=typeof a||!a)throw new aA(`${b} missing or invalid`)};async function aE(a,b){let c;if(!aC(a))throw TypeError("JWK must be an object");if("sha256"!==(b??="sha256")&&"sha384"!==b&&"sha512"!==b)throw TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"');switch(a.kty){case"EC":aD(a.crv,'"crv" (Curve) Parameter'),aD(a.x,'"x" (X Coordinate) Parameter'),aD(a.y,'"y" (Y Coordinate) Parameter'),c={crv:a.crv,kty:a.kty,x:a.x,y:a.y};break;case"OKP":aD(a.crv,'"crv" (Subtype of Key Pair) Parameter'),aD(a.x,'"x" (Public Key) Parameter'),c={crv:a.crv,kty:a.kty,x:a.x};break;case"RSA":aD(a.e,'"e" (Exponent) Parameter'),aD(a.n,'"n" (Modulus) Parameter'),c={e:a.e,kty:a.kty,n:a.n};break;case"oct":aD(a.k,'"k" (Key Value) Parameter'),c={k:a.k,kty:a.kty};break;default:throw new aw('"kty" (Key Type) Parameter missing or unsupported')}let d=ai.encode(JSON.stringify(c));return aq(await ag(b,d))}let aF=Symbol();function aG(a){switch(a){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new aw(`Unsupported JWE Algorithm: ${a}`)}}let aH=(a,b)=>{if(b.length<<3!==aG(a))throw new ay("Invalid Initialization Vector length")};var aI=c(7975);let aJ=a=>aI.types.isKeyObject(a),aK=(a,b)=>{let c;switch(a){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":c=parseInt(a.slice(-3),10);break;case"A128GCM":case"A192GCM":case"A256GCM":c=parseInt(a.slice(1,4),10);break;default:throw new aw(`Content Encryption Algorithm ${a} is not supported either by JOSE or your javascript runtime`)}if(b instanceof Uint8Array){let a=b.byteLength<<3;if(a!==c)throw new ay(`Invalid Content Encryption Key length. Expected ${c} bits, got ${a} bits`);return}if(aJ(b)&&"secret"===b.type){let a=b.symmetricKeySize<<3;if(a!==c)throw new ay(`Invalid Content Encryption Key length. Expected ${c} bits, got ${a} bits`);return}throw TypeError("Invalid Content Encryption Key type")};function aL(a,b,c,d,e,f){let g=ak(a,b,c,function(a){let b=Math.floor(a/0x100000000),c=new Uint8Array(8);return am(c,b,0),am(c,a%0x100000000,4),c}(a.length<<3)),h=(0,af.createHmac)(`sha${d}`,e);return h.update(g),h.digest().slice(0,f>>3)}let aM=af.webcrypto,aN=a=>aI.types.isCryptoKey(a);function aO(a,b="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${b} must be ${a}`)}function aP(a,b){return a.name===b}function aQ(a,b,...c){switch(b){case"A128GCM":case"A192GCM":case"A256GCM":{if(!aP(a.algorithm,"AES-GCM"))throw aO("AES-GCM");let c=parseInt(b.slice(1,4),10);if(a.algorithm.length!==c)throw aO(c,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!aP(a.algorithm,"AES-KW"))throw aO("AES-KW");let c=parseInt(b.slice(1,4),10);if(a.algorithm.length!==c)throw aO(c,"algorithm.length");break}case"ECDH":switch(a.algorithm.name){case"ECDH":case"X25519":case"X448":break;default:throw aO("ECDH, X25519, or X448")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!aP(a.algorithm,"PBKDF2"))throw aO("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!aP(a.algorithm,"RSA-OAEP"))throw aO("RSA-OAEP");let c=parseInt(b.slice(9),10)||1;if(parseInt(a.algorithm.hash.name.slice(4),10)!==c)throw aO(`SHA-${c}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}if(c.length&&!c.some(b=>a.usages.includes(b))){let a="CryptoKey does not support this operation, its usages must include ";if(c.length>2){let b=c.pop();a+=`one of ${c.join(", ")}, or ${b}.`}else 2===c.length?a+=`one of ${c[0]} or ${c[1]}.`:a+=`${c[0]}.`;throw TypeError(a)}}function aR(a,b,...c){if((c=c.filter(Boolean)).length>2){let b=c.pop();a+=`one of type ${c.join(", ")}, or ${b}.`}else 2===c.length?a+=`one of type ${c[0]} or ${c[1]}.`:a+=`of type ${c[0]}.`;return null==b?a+=` Received ${b}`:"function"==typeof b&&b.name?a+=` Received function ${b.name}`:"object"==typeof b&&null!=b&&b.constructor?.name&&(a+=` Received an instance of ${b.constructor.name}`),a}let aS=(a,...b)=>aR("Key must be ",a,...b);function aT(a,b,...c){return aR(`Key for the ${a} algorithm must be `,b,...c)}let aU=a=>(e||=new Set((0,af.getCiphers)())).has(a),aV=a=>aJ(a)||aN(a),aW=["KeyObject"];(globalThis.CryptoKey||aM?.CryptoKey)&&aW.push("CryptoKey");let aX=(a,b,c,d,e)=>{let f;if(aN(c))aQ(c,a,"encrypt"),f=af.KeyObject.from(c);else if(c instanceof Uint8Array||aJ(c))f=c;else throw TypeError(aS(c,...aW,"Uint8Array"));if(aK(a,f),d)aH(a,d);else d=(0,af.randomFillSync)(new Uint8Array(aG(a)>>3));switch(a){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return function(a,b,c,d,e){let f=parseInt(a.slice(1,4),10);aJ(c)&&(c=c.export());let g=c.subarray(f>>3),h=c.subarray(0,f>>3),i=`aes-${f}-cbc`;if(!aU(i))throw new aw(`alg ${a} is not supported by your javascript runtime`);let j=(0,af.createCipheriv)(i,g,d),k=ak(j.update(b),j.final()),l=aL(e,d,k,parseInt(a.slice(-3),10),h,f);return{ciphertext:k,tag:l,iv:d}}(a,b,f,d,e);case"A128GCM":case"A192GCM":case"A256GCM":return function(a,b,c,d,e){let f=parseInt(a.slice(1,4),10),g=`aes-${f}-gcm`;if(!aU(g))throw new aw(`alg ${a} is not supported by your javascript runtime`);let h=(0,af.createCipheriv)(g,c,d,{authTagLength:16});e.byteLength&&h.setAAD(e,{plaintextLength:b.length});let i=h.update(b);return h.final(),{ciphertext:i,tag:h.getAuthTag(),iv:d}}(a,b,f,d,e);default:throw new aw("Unsupported JWE Content Encryption Algorithm")}};function aY(a,b){if(a.symmetricKeySize<<3!==parseInt(b.slice(1,4),10))throw TypeError(`Invalid key size for alg: ${b}`)}function aZ(a,b,c){if(aJ(a))return a;if(a instanceof Uint8Array)return(0,af.createSecretKey)(a);if(aN(a))return aQ(a,b,c),af.KeyObject.from(a);throw TypeError(aS(a,...aW,"Uint8Array"))}let a$=(a,b,c)=>{let d=parseInt(a.slice(1,4),10),e=`aes${d}-wrap`;if(!aU(e))throw new aw(`alg ${a} is not supported either by JOSE or your javascript runtime`);let f=aZ(b,a,"wrapKey");aY(f,a);let g=(0,af.createCipheriv)(e,f,ah.Buffer.alloc(8,166));return ak(g.update(c),g.final())},a_=(a,b,c)=>{let d=parseInt(a.slice(1,4),10),e=`aes${d}-wrap`;if(!aU(e))throw new aw(`alg ${a} is not supported either by JOSE or your javascript runtime`);let f=aZ(b,a,"unwrapKey");aY(f,a);let g=(0,af.createDecipheriv)(e,f,ah.Buffer.alloc(8,166));return ak(g.update(c),g.final())};function a0(a){return aC(a)&&"string"==typeof a.kty}new WeakMap;let a1=(a,b)=>{let c;if(aN(a))c=af.KeyObject.from(a);else if(aJ(a))c=a;else if(a0(a))return a.crv;else throw TypeError(aS(a,...aW));if("secret"===c.type)throw TypeError('only "private" or "public" type keys can be used for this operation');switch(c.asymmetricKeyType){case"ed25519":case"ed448":return`Ed${c.asymmetricKeyType.slice(2)}`;case"x25519":case"x448":return`X${c.asymmetricKeyType.slice(1)}`;case"ec":{let a=c.asymmetricKeyDetails.namedCurve;if(b)return a;switch(a){case"prime256v1":return"P-256";case"secp384r1":return"P-384";case"secp521r1":return"P-521";case"secp256k1":return"secp256k1";default:throw new aw("Unsupported key curve for this operation")}}default:throw TypeError("Invalid asymmetric key type for this operation")}},a2=(0,aI.promisify)(af.generateKeyPair);async function a3(a,b,c,d,e=new Uint8Array(0),f=new Uint8Array(0)){let g,h;if(aN(a))aQ(a,"ECDH"),g=af.KeyObject.from(a);else if(aJ(a))g=a;else throw TypeError(aS(a,...aW));if(aN(b))aQ(b,"ECDH","deriveBits"),h=af.KeyObject.from(b);else if(aJ(b))h=b;else throw TypeError(aS(b,...aW));let i=ak(ao(ai.encode(c)),ao(e),ao(f),an(d));return ap((0,af.diffieHellman)({privateKey:h,publicKey:g}),d,i)}async function a4(a){let b;if(aN(a))b=af.KeyObject.from(a);else if(aJ(a))b=a;else throw TypeError(aS(a,...aW));switch(b.asymmetricKeyType){case"x25519":return a2("x25519");case"x448":return a2("x448");case"ec":return a2("ec",{namedCurve:a1(b)});default:throw new aw("Invalid or unsupported EPK")}}let a5=a=>["P-256","P-384","P-521","X25519","X448"].includes(a1(a));function a6(a){if(!(a instanceof Uint8Array)||a.length<8)throw new ay("PBES2 Salt Input must be 8 or more octets")}let a7=(0,aI.promisify)(af.pbkdf2);function a8(a,b){if(aJ(a))return a.export();if(a instanceof Uint8Array)return a;if(aN(a))return aQ(a,b,"deriveBits","deriveKey"),af.KeyObject.from(a).export();throw TypeError(aS(a,...aW,"Uint8Array"))}let a9=async(a,b,c,d=2048,e=(0,af.randomFillSync)(new Uint8Array(16)))=>{a6(e);let f=al(a,e),g=parseInt(a.slice(13,16),10)>>3,h=a8(b,a),i=await a7(h,f,d,g,`sha${a.slice(8,11)}`);return{encryptedKey:await a$(a.slice(-6),i,c),p2c:d,p2s:aq(e)}},ba=async(a,b,c,d,e)=>{a6(e);let f=al(a,e),g=parseInt(a.slice(13,16),10)>>3,h=a8(b,a),i=await a7(h,f,d,g,`sha${a.slice(8,11)}`);return a_(a.slice(-6),i,c)},bb=(a,b)=>{if("rsa"!==a.asymmetricKeyType)throw TypeError("Invalid key for this operation, its asymmetricKeyType must be rsa");((a,b)=>{let c;try{c=a instanceof af.KeyObject?a.asymmetricKeyDetails?.modulusLength:Buffer.from(a.n,"base64url").byteLength<<3}catch{}if("number"!=typeof c||c<2048)throw TypeError(`${b} requires key modulusLength to be 2048 bits or larger`)})(a,b)},bc=(0,aI.deprecate)(()=>af.constants.RSA_PKCS1_PADDING,'The RSA1_5 "alg" (JWE Algorithm) is deprecated and will be removed in the next major revision.'),bd=a=>{switch(a){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return af.constants.RSA_PKCS1_OAEP_PADDING;case"RSA1_5":return bc();default:return}},be=a=>{switch(a){case"RSA-OAEP":return"sha1";case"RSA-OAEP-256":return"sha256";case"RSA-OAEP-384":return"sha384";case"RSA-OAEP-512":return"sha512";default:return}};function bf(a,b,...c){if(aJ(a))return a;if(aN(a))return aQ(a,b,...c),af.KeyObject.from(a);throw TypeError(aS(a,...aW))}let bg={};function bh(a){switch(a){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new aw(`Unsupported JWE Algorithm: ${a}`)}}let bi=a=>(0,af.randomFillSync)(new Uint8Array(bh(a)>>3));async function bj(a){let b;if(aN(a)){if(!a.extractable)throw TypeError("CryptoKey is not extractable");b=af.KeyObject.from(a)}else if(aJ(a))b=a;else if(a instanceof Uint8Array)return{kty:"oct",k:aq(a)};else throw TypeError(aS(a,...aW,"Uint8Array"));if("secret"!==b.type&&!["rsa","ec","ed25519","x25519","ed448","x448"].includes(b.asymmetricKeyType))throw new aw("Unsupported key asymmetricKeyType");return b.export({format:"jwk"})}let bk=a=>a?.[Symbol.toStringTag],bl=(a,b,c)=>{if(void 0!==b.use&&"sig"!==b.use)throw TypeError("Invalid key for this operation, when present its use must be sig");if(void 0!==b.key_ops&&b.key_ops.includes?.(c)!==!0)throw TypeError(`Invalid key for this operation, when present its key_ops must include ${c}`);if(void 0!==b.alg&&b.alg!==a)throw TypeError(`Invalid key for this operation, when present its alg must be ${a}`);return!0};function bm(a,b,c,d){b.startsWith("HS")||"dir"===b||b.startsWith("PBES2")||/^A\d{3}(?:GCM)?KW$/.test(b)?((a,b,c,d)=>{if(!(b instanceof Uint8Array)){if(d&&a0(b)){var e;if(a0(e=b)&&"oct"===e.kty&&"string"==typeof e.k&&bl(a,b,c))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!aV(b))throw TypeError(aT(a,b,...aW,"Uint8Array",d?"JSON Web Key":null));if("secret"!==b.type)throw TypeError(`${bk(b)} instances for symmetric algorithms must be of type "secret"`)}})(b,c,d,a):((a,b,c,d)=>{var e,f;if(d&&a0(b))switch(c){case"sign":if("oct"!==(e=b).kty&&"string"==typeof e.d&&bl(a,b,c))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"verify":if("oct"!==(f=b).kty&&void 0===f.d&&bl(a,b,c))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!aV(b))throw TypeError(aT(a,b,...aW,d?"JSON Web Key":null));if("secret"===b.type)throw TypeError(`${bk(b)} instances for asymmetric algorithms must not be of type "secret"`);if("sign"===c&&"public"===b.type)throw TypeError(`${bk(b)} instances for asymmetric algorithm signing must be of type "private"`);if("decrypt"===c&&"public"===b.type)throw TypeError(`${bk(b)} instances for asymmetric algorithm decryption must be of type "private"`);if(b.algorithm&&"verify"===c&&"private"===b.type)throw TypeError(`${bk(b)} instances for asymmetric algorithm verifying must be of type "public"`);if(b.algorithm&&"encrypt"===c&&"private"===b.type)throw TypeError(`${bk(b)} instances for asymmetric algorithm encryption must be of type "public"`)})(b,c,d,a)}let bn=bm.bind(void 0,!1);bm.bind(void 0,!0);let bo=af.timingSafeEqual,bp=(a,b,c,d,e,f)=>{let g;if(aN(b))aQ(b,a,"decrypt"),g=af.KeyObject.from(b);else if(b instanceof Uint8Array||aJ(b))g=b;else throw TypeError(aS(b,...aW,"Uint8Array"));if(!d)throw new ay("JWE Initialization Vector missing");if(!e)throw new ay("JWE Authentication Tag missing");switch(aK(a,g),aH(a,d),a){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return function(a,b,c,d,e,f){let g,h,i=parseInt(a.slice(1,4),10);aJ(b)&&(b=b.export());let j=b.subarray(i>>3),k=b.subarray(0,i>>3),l=parseInt(a.slice(-3),10),m=`aes-${i}-cbc`;if(!aU(m))throw new aw(`alg ${a} is not supported by your javascript runtime`);let n=aL(f,d,c,l,k,i);try{g=bo(e,n)}catch{}if(!g)throw new ax;try{let a=(0,af.createDecipheriv)(m,j,d);h=ak(a.update(c),a.final())}catch{}if(!h)throw new ax;return h}(a,g,c,d,e,f);case"A128GCM":case"A192GCM":case"A256GCM":return function(a,b,c,d,e,f){let g=parseInt(a.slice(1,4),10),h=`aes-${g}-gcm`;if(!aU(h))throw new aw(`alg ${a} is not supported by your javascript runtime`);try{let a=(0,af.createDecipheriv)(h,b,d,{authTagLength:16});a.setAuthTag(e),f.byteLength&&a.setAAD(f,{plaintextLength:c.length});let g=a.update(c);return a.final(),g}catch{throw new ax}}(a,g,c,d,e,f);default:throw new aw("Unsupported JWE Content Encryption Algorithm")}};async function bq(a,b,c,d){let e=a.slice(0,7),f=await aX(e,c,b,d,new Uint8Array(0));return{encryptedKey:f.ciphertext,iv:aq(f.iv),tag:aq(f.tag)}}async function br(a,b,c,d,e){return bp(a.slice(0,7),b,c,d,e,new Uint8Array(0))}async function bs(a,b,c,d,e={}){let f,g,h;switch(bn(a,c,"encrypt"),c=await bg.normalizePublicKey?.(c,a)||c,a){case"dir":h=c;break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{if(!a5(c))throw new aw("ECDH with the provided key is not allowed or not supported by your javascript runtime");let{apu:i,apv:j}=e,{epk:k}=e;k||=(await a4(c)).privateKey;let{x:l,y:m,crv:n,kty:o}=await bj(k),p=await a3(c,k,"ECDH-ES"===a?b:a,"ECDH-ES"===a?bh(b):parseInt(a.slice(-5,-2),10),i,j);if(g={epk:{x:l,crv:n,kty:o}},"EC"===o&&(g.epk.y=m),i&&(g.apu=aq(i)),j&&(g.apv=aq(j)),"ECDH-ES"===a){h=p;break}h=d||bi(b);let q=a.slice(-6);f=await a$(q,p,h);break}case"RSA1_5":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":h=d||bi(b),f=await ((a,b,c)=>{let d=bd(a),e=be(a),f=bf(b,a,"wrapKey","encrypt");return bb(f,a),(0,af.publicEncrypt)({key:f,oaepHash:e,padding:d},c)})(a,c,h);break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{h=d||bi(b);let{p2c:i,p2s:j}=e;({encryptedKey:f,...g}=await a9(a,c,h,i,j));break}case"A128KW":case"A192KW":case"A256KW":h=d||bi(b),f=await a$(a,c,h);break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{h=d||bi(b);let{iv:i}=e;({encryptedKey:f,...g}=await bq(a,c,h,i));break}default:throw new aw('Invalid or unsupported "alg" (JWE Algorithm) header value')}return{cek:h,encryptedKey:f,parameters:g}}let bt=(...a)=>{let b,c=a.filter(Boolean);if(0===c.length||1===c.length)return!0;for(let a of c){let c=Object.keys(a);if(!b||0===b.size){b=new Set(c);continue}for(let a of c){if(b.has(a))return!1;b.add(a)}}return!0},bu=function(a,b,c,d,e){let f;if(void 0!==e.crit&&d?.crit===void 0)throw new a('"crit" (Critical) Header Parameter MUST be integrity protected');if(!d||void 0===d.crit)return new Set;if(!Array.isArray(d.crit)||0===d.crit.length||d.crit.some(a=>"string"!=typeof a||0===a.length))throw new a('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let g of(f=void 0!==c?new Map([...Object.entries(c),...b.entries()]):b,d.crit)){if(!f.has(g))throw new aw(`Extension Header Parameter "${g}" is not recognized`);if(void 0===e[g])throw new a(`Extension Header Parameter "${g}" is missing`);if(f.get(g)&&void 0===d[g])throw new a(`Extension Header Parameter "${g}" MUST be integrity protected`)}return new Set(d.crit)};class bv{_plaintext;_protectedHeader;_sharedUnprotectedHeader;_unprotectedHeader;_aad;_cek;_iv;_keyManagementParameters;constructor(a){if(!(a instanceof Uint8Array))throw TypeError("plaintext must be an instance of Uint8Array");this._plaintext=a}setKeyManagementParameters(a){if(this._keyManagementParameters)throw TypeError("setKeyManagementParameters can only be called once");return this._keyManagementParameters=a,this}setProtectedHeader(a){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=a,this}setSharedUnprotectedHeader(a){if(this._sharedUnprotectedHeader)throw TypeError("setSharedUnprotectedHeader can only be called once");return this._sharedUnprotectedHeader=a,this}setUnprotectedHeader(a){if(this._unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this._unprotectedHeader=a,this}setAdditionalAuthenticatedData(a){return this._aad=a,this}setContentEncryptionKey(a){if(this._cek)throw TypeError("setContentEncryptionKey can only be called once");return this._cek=a,this}setInitializationVector(a){if(this._iv)throw TypeError("setInitializationVector can only be called once");return this._iv=a,this}async encrypt(a,b){let c,d,e,f,g;if(!this._protectedHeader&&!this._unprotectedHeader&&!this._sharedUnprotectedHeader)throw new ay("either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()");if(!bt(this._protectedHeader,this._unprotectedHeader,this._sharedUnprotectedHeader))throw new ay("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let h={...this._protectedHeader,...this._unprotectedHeader,...this._sharedUnprotectedHeader};if(bu(ay,new Map,b?.crit,this._protectedHeader,h),void 0!==h.zip)throw new aw('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:i,enc:j}=h;if("string"!=typeof i||!i)throw new ay('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("string"!=typeof j||!j)throw new ay('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if(this._cek&&("dir"===i||"ECDH-ES"===i))throw TypeError(`setContentEncryptionKey cannot be called with JWE "alg" (Algorithm) Header ${i}`);{let e;({cek:d,encryptedKey:c,parameters:e}=await bs(i,j,a,this._cek,this._keyManagementParameters)),e&&(b&&aF in b?this._unprotectedHeader?this._unprotectedHeader={...this._unprotectedHeader,...e}:this.setUnprotectedHeader(e):this._protectedHeader?this._protectedHeader={...this._protectedHeader,...e}:this.setProtectedHeader(e))}f=this._protectedHeader?ai.encode(aq(JSON.stringify(this._protectedHeader))):ai.encode(""),this._aad?(g=aq(this._aad),e=ak(f,ai.encode("."),ai.encode(g))):e=f;let{ciphertext:k,tag:l,iv:m}=await aX(j,this._plaintext,d,this._iv,e),n={ciphertext:aq(k)};return m&&(n.iv=aq(m)),l&&(n.tag=aq(l)),c&&(n.encrypted_key=aq(c)),g&&(n.aad=g),this._protectedHeader&&(n.protected=aj.decode(f)),this._sharedUnprotectedHeader&&(n.unprotected=this._sharedUnprotectedHeader),this._unprotectedHeader&&(n.header=this._unprotectedHeader),n}}class bw{_flattened;constructor(a){this._flattened=new bv(a)}setContentEncryptionKey(a){return this._flattened.setContentEncryptionKey(a),this}setInitializationVector(a){return this._flattened.setInitializationVector(a),this}setProtectedHeader(a){return this._flattened.setProtectedHeader(a),this}setKeyManagementParameters(a){return this._flattened.setKeyManagementParameters(a),this}async encrypt(a,b){let c=await this._flattened.encrypt(a,b);return[c.protected,c.encrypted_key,c.iv,c.ciphertext,c.tag].join(".")}}let bx=a=>Math.floor(a.getTime()/1e3),by=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,bz=a=>{let b,c=by.exec(a);if(!c||c[4]&&c[1])throw TypeError("Invalid time period format");let d=parseFloat(c[2]);switch(c[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":b=Math.round(d);break;case"minute":case"minutes":case"min":case"mins":case"m":b=Math.round(60*d);break;case"hour":case"hours":case"hr":case"hrs":case"h":b=Math.round(3600*d);break;case"day":case"days":case"d":b=Math.round(86400*d);break;case"week":case"weeks":case"w":b=Math.round(604800*d);break;default:b=Math.round(0x1e187e0*d)}return"-"===c[1]||"ago"===c[4]?-b:b};function bA(a,b){if(!Number.isFinite(b))throw TypeError(`Invalid ${a} input`);return b}class bB{_payload;constructor(a={}){if(!aC(a))throw TypeError("JWT Claims Set MUST be an object");this._payload=a}setIssuer(a){return this._payload={...this._payload,iss:a},this}setSubject(a){return this._payload={...this._payload,sub:a},this}setAudience(a){return this._payload={...this._payload,aud:a},this}setJti(a){return this._payload={...this._payload,jti:a},this}setNotBefore(a){return"number"==typeof a?this._payload={...this._payload,nbf:bA("setNotBefore",a)}:a instanceof Date?this._payload={...this._payload,nbf:bA("setNotBefore",bx(a))}:this._payload={...this._payload,nbf:bx(new Date)+bz(a)},this}setExpirationTime(a){return"number"==typeof a?this._payload={...this._payload,exp:bA("setExpirationTime",a)}:a instanceof Date?this._payload={...this._payload,exp:bA("setExpirationTime",bx(a))}:this._payload={...this._payload,exp:bx(new Date)+bz(a)},this}setIssuedAt(a){return void 0===a?this._payload={...this._payload,iat:bx(new Date)}:a instanceof Date?this._payload={...this._payload,iat:bA("setIssuedAt",bx(a))}:"string"==typeof a?this._payload={...this._payload,iat:bA("setIssuedAt",bx(new Date)+bz(a))}:this._payload={...this._payload,iat:bA("setIssuedAt",a)},this}}class bC extends bB{_cek;_iv;_keyManagementParameters;_protectedHeader;_replicateIssuerAsHeader;_replicateSubjectAsHeader;_replicateAudienceAsHeader;setProtectedHeader(a){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=a,this}setKeyManagementParameters(a){if(this._keyManagementParameters)throw TypeError("setKeyManagementParameters can only be called once");return this._keyManagementParameters=a,this}setContentEncryptionKey(a){if(this._cek)throw TypeError("setContentEncryptionKey can only be called once");return this._cek=a,this}setInitializationVector(a){if(this._iv)throw TypeError("setInitializationVector can only be called once");return this._iv=a,this}replicateIssuerAsHeader(){return this._replicateIssuerAsHeader=!0,this}replicateSubjectAsHeader(){return this._replicateSubjectAsHeader=!0,this}replicateAudienceAsHeader(){return this._replicateAudienceAsHeader=!0,this}async encrypt(a,b){let c=new bw(ai.encode(JSON.stringify(this._payload)));return this._replicateIssuerAsHeader&&(this._protectedHeader={...this._protectedHeader,iss:this._payload.iss}),this._replicateSubjectAsHeader&&(this._protectedHeader={...this._protectedHeader,sub:this._payload.sub}),this._replicateAudienceAsHeader&&(this._protectedHeader={...this._protectedHeader,aud:this._payload.aud}),c.setProtectedHeader(this._protectedHeader),this._iv&&c.setInitializationVector(this._iv),this._cek&&c.setContentEncryptionKey(this._cek),this._keyManagementParameters&&c.setKeyManagementParameters(this._keyManagementParameters),c.encrypt(a,b)}}async function bD(a,b){if(!aC(a))throw TypeError("JWK must be an object");switch(b||=a.alg,a.kty){case"oct":if("string"!=typeof a.k||!a.k)throw TypeError('missing "k" (Key Value) Parameter value');return ar(a.k);case"RSA":if("oth"in a&&void 0!==a.oth)throw new aw('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":var c;return(c={...a,alg:b}).d?(0,af.createPrivateKey)({format:"jwk",key:c}):(0,af.createPublicKey)({format:"jwk",key:c});default:throw new aw('Unsupported "kty" (Key Type) Parameter value')}}async function bE(a,b,c,d,e){switch(bn(a,b,"decrypt"),b=await bg.normalizePrivateKey?.(b,a)||b,a){case"dir":if(void 0!==c)throw new ay("Encountered unexpected JWE Encrypted Key");return b;case"ECDH-ES":if(void 0!==c)throw new ay("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let e,f;if(!aC(d.epk))throw new ay('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(!a5(b))throw new aw("ECDH with the provided key is not allowed or not supported by your javascript runtime");let g=await bD(d.epk,a);if(void 0!==d.apu){if("string"!=typeof d.apu)throw new ay('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{e=ar(d.apu)}catch{throw new ay("Failed to base64url decode the apu")}}if(void 0!==d.apv){if("string"!=typeof d.apv)throw new ay('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{f=ar(d.apv)}catch{throw new ay("Failed to base64url decode the apv")}}let h=await a3(g,b,"ECDH-ES"===a?d.enc:a,"ECDH-ES"===a?bh(d.enc):parseInt(a.slice(-5,-2),10),e,f);if("ECDH-ES"===a)return h;if(void 0===c)throw new ay("JWE Encrypted Key missing");return a_(a.slice(-6),h,c)}case"RSA1_5":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===c)throw new ay("JWE Encrypted Key missing");var f=b;let g=bd(a),h=be(a),i=bf(f,a,"unwrapKey","decrypt");return bb(i,a),(0,af.privateDecrypt)({key:i,oaepHash:h,padding:g},c);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let f;if(void 0===c)throw new ay("JWE Encrypted Key missing");if("number"!=typeof d.p2c)throw new ay('JOSE Header "p2c" (PBES2 Count) missing or invalid');let g=e?.maxPBES2Count||1e4;if(d.p2c>g)throw new ay('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof d.p2s)throw new ay('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{f=ar(d.p2s)}catch{throw new ay("Failed to base64url decode the p2s")}return ba(a,b,c,d.p2c,f)}case"A128KW":case"A192KW":case"A256KW":if(void 0===c)throw new ay("JWE Encrypted Key missing");return a_(a,b,c);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let e,f;if(void 0===c)throw new ay("JWE Encrypted Key missing");if("string"!=typeof d.iv)throw new ay('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof d.tag)throw new ay('JOSE Header "tag" (Authentication Tag) missing or invalid');try{e=ar(d.iv)}catch{throw new ay("Failed to base64url decode the iv")}try{f=ar(d.tag)}catch{throw new ay("Failed to base64url decode the tag")}return br(a,b,c,e,f)}default:throw new aw('Invalid or unsupported "alg" (JWE Algorithm) header value')}}let bF=(a,b)=>{if(void 0!==b&&(!Array.isArray(b)||b.some(a=>"string"!=typeof a)))throw TypeError(`"${a}" option must be an array of strings`);if(b)return new Set(b)};async function bG(a,b,c){let d,e,f,g,h,i,j;if(!aC(a))throw new ay("Flattened JWE must be an object");if(void 0===a.protected&&void 0===a.header&&void 0===a.unprotected)throw new ay("JOSE Header missing");if(void 0!==a.iv&&"string"!=typeof a.iv)throw new ay("JWE Initialization Vector incorrect type");if("string"!=typeof a.ciphertext)throw new ay("JWE Ciphertext missing or incorrect type");if(void 0!==a.tag&&"string"!=typeof a.tag)throw new ay("JWE Authentication Tag incorrect type");if(void 0!==a.protected&&"string"!=typeof a.protected)throw new ay("JWE Protected Header incorrect type");if(void 0!==a.encrypted_key&&"string"!=typeof a.encrypted_key)throw new ay("JWE Encrypted Key incorrect type");if(void 0!==a.aad&&"string"!=typeof a.aad)throw new ay("JWE AAD incorrect type");if(void 0!==a.header&&!aC(a.header))throw new ay("JWE Shared Unprotected Header incorrect type");if(void 0!==a.unprotected&&!aC(a.unprotected))throw new ay("JWE Per-Recipient Unprotected Header incorrect type");if(a.protected)try{let b=ar(a.protected);d=JSON.parse(aj.decode(b))}catch{throw new ay("JWE Protected Header is invalid")}if(!bt(d,a.header,a.unprotected))throw new ay("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let k={...d,...a.header,...a.unprotected};if(bu(ay,new Map,c?.crit,d,k),void 0!==k.zip)throw new aw('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:l,enc:m}=k;if("string"!=typeof l||!l)throw new ay("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof m||!m)throw new ay("missing JWE Encryption Algorithm (enc) in JWE Header");let n=c&&bF("keyManagementAlgorithms",c.keyManagementAlgorithms),o=c&&bF("contentEncryptionAlgorithms",c.contentEncryptionAlgorithms);if(n&&!n.has(l)||!n&&l.startsWith("PBES2"))throw new av('"alg" (Algorithm) Header Parameter value not allowed');if(o&&!o.has(m))throw new av('"enc" (Encryption Algorithm) Header Parameter value not allowed');if(void 0!==a.encrypted_key)try{e=ar(a.encrypted_key)}catch{throw new ay("Failed to base64url decode the encrypted_key")}let p=!1;"function"==typeof b&&(b=await b(d,a),p=!0);try{f=await bE(l,b,e,k,c)}catch(a){if(a instanceof TypeError||a instanceof ay||a instanceof aw)throw a;f=bi(m)}if(void 0!==a.iv)try{g=ar(a.iv)}catch{throw new ay("Failed to base64url decode the iv")}if(void 0!==a.tag)try{h=ar(a.tag)}catch{throw new ay("Failed to base64url decode the tag")}let q=ai.encode(a.protected??"");i=void 0!==a.aad?ak(q,ai.encode("."),ai.encode(a.aad)):q;try{j=ar(a.ciphertext)}catch{throw new ay("Failed to base64url decode the ciphertext")}let r={plaintext:await bp(m,f,j,g,h,i)};if(void 0!==a.protected&&(r.protectedHeader=d),void 0!==a.aad)try{r.additionalAuthenticatedData=ar(a.aad)}catch{throw new ay("Failed to base64url decode the aad")}return(void 0!==a.unprotected&&(r.sharedUnprotectedHeader=a.unprotected),void 0!==a.header&&(r.unprotectedHeader=a.header),p)?{...r,key:b}:r}async function bH(a,b,c){if(a instanceof Uint8Array&&(a=aj.decode(a)),"string"!=typeof a)throw new ay("Compact JWE must be a string or Uint8Array");let{0:d,1:e,2:f,3:g,4:h,length:i}=a.split(".");if(5!==i)throw new ay("Invalid Compact JWE");let j=await bG({ciphertext:g,iv:f||void 0,protected:d,tag:h||void 0,encrypted_key:e||void 0},b,c),k={plaintext:j.plaintext,protectedHeader:j.protectedHeader};return"function"==typeof b?{...k,key:j.key}:k}let bI=a=>a.toLowerCase().replace(/^application\//,"");async function bJ(a,b,c){let d=await bH(a,b,c),e=((a,b,c={})=>{let d,e,f,g;try{d=JSON.parse(aj.decode(b))}catch{}if(!aC(d))throw new az("JWT Claims Set must be a top-level JSON object");let{typ:h}=c;if(h&&("string"!=typeof a.typ||bI(a.typ)!==bI(h)))throw new at('unexpected "typ" JWT header value',d,"typ","check_failed");let{requiredClaims:i=[],issuer:j,subject:k,audience:l,maxTokenAge:m}=c,n=[...i];for(let a of(void 0!==m&&n.push("iat"),void 0!==l&&n.push("aud"),void 0!==k&&n.push("sub"),void 0!==j&&n.push("iss"),new Set(n.reverse())))if(!(a in d))throw new at(`missing required "${a}" claim`,d,a,"missing");if(j&&!(Array.isArray(j)?j:[j]).includes(d.iss))throw new at('unexpected "iss" claim value',d,"iss","check_failed");if(k&&d.sub!==k)throw new at('unexpected "sub" claim value',d,"sub","check_failed");if(l&&(f=d.aud,g="string"==typeof l?[l]:l,"string"==typeof f?!g.includes(f):!(!!Array.isArray(f)&&g.some(Set.prototype.has.bind(new Set(f))))))throw new at('unexpected "aud" claim value',d,"aud","check_failed");switch(typeof c.clockTolerance){case"string":e=bz(c.clockTolerance);break;case"number":e=c.clockTolerance;break;case"undefined":e=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:o}=c,p=bx(o||new Date);if((void 0!==d.iat||m)&&"number"!=typeof d.iat)throw new at('"iat" claim must be a number',d,"iat","invalid");if(void 0!==d.nbf){if("number"!=typeof d.nbf)throw new at('"nbf" claim must be a number',d,"nbf","invalid");if(d.nbf>p+e)throw new at('"nbf" claim timestamp check failed',d,"nbf","check_failed")}if(void 0!==d.exp){if("number"!=typeof d.exp)throw new at('"exp" claim must be a number',d,"exp","invalid");if(d.exp<=p-e)throw new au('"exp" claim timestamp check failed',d,"exp","check_failed")}if(m){let a=p-d.iat;if(a-e>("number"==typeof m?m:bz(m)))throw new au('"iat" claim timestamp check failed (too far in the past)',d,"iat","check_failed");if(a<0-e)throw new at('"iat" claim timestamp check failed (it should be in the past)',d,"iat","check_failed")}return d})(d.protectedHeader,d.plaintext,c),{protectedHeader:f}=d;if(void 0!==f.iss&&f.iss!==e.iss)throw new at('replicated "iss" claim header parameter mismatch',e,"iss","mismatch");if(void 0!==f.sub&&f.sub!==e.sub)throw new at('replicated "sub" claim header parameter mismatch',e,"sub","mismatch");if(void 0!==f.aud&&JSON.stringify(f.aud)!==JSON.stringify(e.aud))throw new at('replicated "aud" claim header parameter mismatch',e,"aud","mismatch");let g={payload:e,protectedHeader:f};return"function"==typeof b?{...g,key:d.key}:g}var bK=c(5538);let bL="A256CBC-HS512";async function bM(a){let{token:b={},secret:c,maxAge:d=2592e3,salt:e}=a,f=Array.isArray(c)?c:[c],g=await bO(bL,f[0],e),h=await aE({kty:"oct",k:aq(g)},`sha${g.byteLength<<3}`);return await new bC(b).setProtectedHeader({alg:"dir",enc:bL,kid:h}).setIssuedAt().setExpirationTime((Date.now()/1e3|0)+d).setJti(crypto.randomUUID()).encrypt(g)}async function bN(a){let{token:b,secret:c,salt:d}=a,e=Array.isArray(c)?c:[c];if(!b)return null;let{payload:f}=await bJ(b,async({kid:a,enc:b})=>{for(let c of e){let e=await bO(b,c,d);if(void 0===a||a===await aE({kty:"oct",k:aq(e)},`sha${e.byteLength<<3}`))return e}throw Error("no matching decryption secret")},{clockTolerance:15,keyManagementAlgorithms:["dir"],contentEncryptionAlgorithms:[bL,"A256GCM"]});return f}async function bO(a,b,c){let d;switch(a){case"A256CBC-HS512":d=64;break;case"A256GCM":d=32;break;default:throw Error("Unsupported JWT Content Encryption Algorithm")}return await ae("sha256",b,c,`Auth.js Generated Encryption Key (${c})`,d)}async function bP({options:a,paramValue:b,cookieValue:c}){let{url:d,callbacks:e}=a,f=d.origin;return b?f=await e.redirect({url:b,baseUrl:d.origin}):c&&(f=await e.redirect({url:c,baseUrl:d.origin})),{callbackUrl:f,callbackUrlCookie:f!==c?f:void 0}}let bQ="\x1b[31m",bR="\x1b[0m",bS={error(a){let b=a instanceof m?a.type:a.name;if(console.error(`${bQ}[auth][error]${bR} ${b}: ${a.message}`),a.cause&&"object"==typeof a.cause&&"err"in a.cause&&a.cause.err instanceof Error){let{err:b,...c}=a.cause;console.error(`${bQ}[auth][cause]${bR}:`,b.stack),c&&console.error(`${bQ}[auth][details]${bR}:`,JSON.stringify(c,null,2))}else a.stack&&console.error(a.stack.replace(/.*/,"").substring(1))},warn(a){let b=`https://warnings.authjs.dev#${a}`;console.warn(`\x1b[33m[auth][warn][${a}]${bR}`,`Read more: ${b}`)},debug(a,b){console.log(`\x1b[90m[auth][debug]:${bR} ${a}`,JSON.stringify(b,null,2))}};function bT(a){let b={...bS};return a.debug||(b.debug=()=>{}),a.logger?.error&&(b.error=a.logger.error),a.logger?.warn&&(b.warn=a.logger.warn),a.logger?.debug&&(b.debug=a.logger.debug),a.logger??(a.logger=b),b}let bU=["providers","session","csrf","signin","signout","callback","verify-request","error","webauthn-options"];async function bV(a){if(!("body"in a)||!a.body||"POST"!==a.method)return;let b=a.headers.get("content-type");return b?.includes("application/json")?await a.json():b?.includes("application/x-www-form-urlencoded")?Object.fromEntries(new URLSearchParams(await a.text())):void 0}async function bW(a,b){try{if("GET"!==a.method&&"POST"!==a.method)throw new J("Only GET and POST requests are supported");b.basePath??(b.basePath="/auth");let c=new URL(a.url),{action:d,providerId:e}=function(a,b){let c=a.match(RegExp(`^${b}(.+)`));if(null===c)throw new J(`Cannot parse action at ${a}`);let d=c.at(-1).replace(/^\//,"").split("/").filter(Boolean);if(1!==d.length&&2!==d.length)throw new J(`Cannot parse action at ${a}`);let[e,f]=d;if(!bU.includes(e)||f&&!["signin","callback","webauthn-options"].includes(e))throw new J(`Cannot parse action at ${a}`);return{action:e,providerId:f}}(c.pathname,b.basePath);return{url:c,action:d,providerId:e,method:a.method,headers:Object.fromEntries(a.headers),body:a.body?await bV(a):void 0,cookies:(0,bK.q)(a.headers.get("cookie")??"")??{},error:c.searchParams.get("error")??void 0,query:Object.fromEntries(c.searchParams)}}catch(d){let c=bT(b);c.error(d),c.debug("request",a)}}function bX(a){let b=new Headers(a.headers);a.cookies?.forEach(a=>{let{name:c,value:d,options:e}=a,f=(0,bK.l)(c,d,e);b.has("Set-Cookie")?b.append("Set-Cookie",f):b.set("Set-Cookie",f)});let c=a.body;"application/json"===b.get("content-type")?c=JSON.stringify(a.body):"application/x-www-form-urlencoded"===b.get("content-type")&&(c=new URLSearchParams(a.body).toString());let d=new Response(c,{headers:b,status:a.redirect?302:a.status??200});return a.redirect&&d.headers.set("Location",a.redirect),d}async function bY(a){let b=new TextEncoder().encode(a);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",b))).map(a=>a.toString(16).padStart(2,"0")).join("").toString()}function bZ(a){return Array.from(crypto.getRandomValues(new Uint8Array(a))).reduce((a,b)=>a+("0"+b.toString(16)).slice(-2),"")}async function b$({options:a,cookieValue:b,isPost:c,bodyValue:d}){if(b){let[e,f]=b.split("|");if(f===await bY(`${e}${a.secret}`))return{csrfTokenVerified:c&&e===d,csrfToken:e}}let e=bZ(32),f=await bY(`${e}${a.secret}`);return{cookie:`${e}|${f}`,csrfToken:e}}function b_(a,b){if(!b)throw new O(`CSRF token was missing during an action ${a}`)}function b0(a){return null!==a&&"object"==typeof a}function b1(a,...b){if(!b.length)return a;let c=b.shift();if(b0(a)&&b0(c))for(let b in c)b0(c[b])?(b0(a[b])||(a[b]=Array.isArray(c[b])?[]:{}),b1(a[b],c[b])):void 0!==c[b]&&(a[b]=c[b]);return b1(a,...b)}let b2=Symbol("skip-csrf-check"),b3=Symbol("return-type-raw"),b4=Symbol("custom-fetch"),b5=Symbol("conform-internal"),b6=a=>b8({id:a.sub??a.id??crypto.randomUUID(),name:a.name??a.nickname??a.preferred_username,email:a.email,image:a.picture}),b7=a=>b8({access_token:a.access_token,id_token:a.id_token,refresh_token:a.refresh_token,expires_at:a.expires_at,scope:a.scope,token_type:a.token_type,session_state:a.session_state});function b8(a){let b={};for(let[c,d]of Object.entries(a))void 0!==d&&(b[c]=d);return b}function b9(a,b){if(!a&&b)return;if("string"==typeof a)return{url:new URL(a)};let c=new URL(a?.url??"https://authjs.dev");if(a?.params!=null)for(let[b,d]of Object.entries(a.params))"claims"===b&&(d=JSON.stringify(d)),c.searchParams.set(b,String(d));return{url:c,request:a?.request,conform:a?.conform,...a?.clientPrivateKey?{clientPrivateKey:a?.clientPrivateKey}:null}}let ca={signIn:()=>!0,redirect:({url:a,baseUrl:b})=>a.startsWith("/")?`${b}${a}`:new URL(a).origin===b?a:b,session:({session:a})=>({user:{name:a.user?.name,email:a.user?.email,image:a.user?.image},expires:a.expires?.toISOString?.()??a.expires}),jwt:({token:a})=>a};async function cb({authOptions:a,providerId:b,action:c,url:d,cookies:e,callbackUrl:f,csrfToken:g,csrfDisabled:h,isPost:i}){var j,l;let m=bT(a),{providers:n,provider:p}=function(a){let{providerId:b,config:c}=a,d=new URL(c.basePath??"/auth",a.url.origin),e=c.providers.map(a=>{let b="function"==typeof a?a():a,{options:e,...f}=b,g=e?.id??f.id,h=b1(f,e,{signinUrl:`${d}/signin/${g}`,callbackUrl:`${d}/callback/${g}`});if("oauth"===b.type||"oidc"===b.type){h.redirectProxyUrl??(h.redirectProxyUrl=e?.redirectProxyUrl??c.redirectProxyUrl);let a=function(a){a.issuer&&(a.wellKnown??(a.wellKnown=`${a.issuer}/.well-known/openid-configuration`));let b=b9(a.authorization,a.issuer);b&&!b.url?.searchParams.has("scope")&&b.url.searchParams.set("scope","openid profile email");let c=b9(a.token,a.issuer),d=b9(a.userinfo,a.issuer),e=a.checks??["pkce"];return a.redirectProxyUrl&&(e.includes("state")||e.push("state"),a.redirectProxyUrl=`${a.redirectProxyUrl}/callback/${a.id}`),{...a,authorization:b,token:c,checks:e,userinfo:d,profile:a.profile??b6,account:a.account??b7}}(h);return a.authorization?.url.searchParams.get("response_mode")==="form_post"&&delete a.redirectProxyUrl,a[b4]??(a[b4]=e?.[b4]),a}return h});return{providers:e,provider:e.find(({id:a})=>a===b)}}({url:d,providerId:b,config:a}),q=!1;if((p?.type==="oauth"||p?.type==="oidc")&&p.redirectProxyUrl)try{q=new URL(p.redirectProxyUrl).origin===d.origin}catch{throw TypeError(`redirectProxyUrl must be a valid URL. Received: ${p.redirectProxyUrl}`)}let r={debug:!1,pages:{},theme:{colorScheme:"auto",logo:"",brandColor:"",buttonText:""},...a,url:d,action:c,provider:p,cookies:b1(k(a.useSecureCookies??"https:"===d.protocol),a.cookies),providers:n,session:{strategy:a.adapter?"database":"jwt",maxAge:2592e3,updateAge:86400,generateSessionToken:()=>crypto.randomUUID(),...a.session},jwt:{secret:a.secret,maxAge:a.session?.maxAge??2592e3,encode:bM,decode:bN,...a.jwt},events:(j=a.events??{},l=m,Object.keys(j).reduce((a,b)=>(a[b]=async(...a)=>{try{let c=j[b];return await c(...a)}catch(a){l.error(new s(a))}},a),{})),adapter:function(a,b){if(a)return Object.keys(a).reduce((c,d)=>(c[d]=async(...c)=>{try{b.debug(`adapter_${d}`,{args:c});let e=a[d];return await e(...c)}catch(c){let a=new o(c);throw b.error(a),a}},c),{})}(a.adapter,m),callbacks:{...ca,...a.callbacks},logger:m,callbackUrl:d.origin,isOnRedirectProxy:q,experimental:{...a.experimental}},t=[];if(h)r.csrfTokenVerified=!0;else{let{csrfToken:a,cookie:b,csrfTokenVerified:c}=await b$({options:r,cookieValue:e?.[r.cookies.csrfToken.name],isPost:i,bodyValue:g});r.csrfToken=a,r.csrfTokenVerified=c,b&&t.push({name:r.cookies.csrfToken.name,value:b,options:r.cookies.csrfToken.options})}let{callbackUrl:u,callbackUrlCookie:v}=await bP({options:r,cookieValue:e?.[r.cookies.callbackUrl.name],paramValue:f});return r.callbackUrl=u,v&&t.push({name:r.cookies.callbackUrl.name,value:v,options:r.cookies.callbackUrl.options}),{options:r,cookies:t}}var cc,cd,ce,cf,cg,ch,ci,cj,ck,cl,cm,cn={},co=[],cp=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function cq(a,b){for(var c in b)a[c]=b[c];return a}function cr(a){var b=a.parentNode;b&&b.removeChild(a)}function cs(a,b,c,d,e){var f={type:a,props:b,key:c,ref:d,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:null==e?++ck:e};return null==e&&null!=cj.vnode&&cj.vnode(f),f}function ct(a){return a.children}function cu(a,b){this.props=a,this.context=b}function cv(a,b){if(null==b)return a.__?cv(a.__,a.__.__k.indexOf(a)+1):null;for(var c;b<a.__k.length;b++)if(null!=(c=a.__k[b])&&null!=c.__e)return c.__e;return"function"==typeof a.type?cv(a):null}function cw(a){(!a.__d&&(a.__d=!0)&&cl.push(a)&&!cx.__r++||cm!==cj.debounceRendering)&&((cm=cj.debounceRendering)||setTimeout)(cx)}function cx(){for(var a;cx.__r=cl.length;)a=cl.sort(function(a,b){return a.__v.__b-b.__v.__b}),cl=[],a.some(function(a){var b,c,d,e,f;a.__d&&(e=(d=a.__v).__e,(f=a.__P)&&(b=[],(c=cq({},d)).__v=d.__v+1,cE(f,d,c,a.__n,void 0!==f.ownerSVGElement,null!=d.__h?[e]:null,b,null==e?cv(d):e,d.__h),function(a,b){cj.__c&&cj.__c(b,a),a.some(function(b){try{a=b.__h,b.__h=[],a.some(function(a){a.call(b)})}catch(a){cj.__e(a,b.__v)}})}(b,d),d.__e!=e&&function a(b){var c,d;if(null!=(b=b.__)&&null!=b.__c){for(b.__e=b.__c.base=null,c=0;c<b.__k.length;c++)if(null!=(d=b.__k[c])&&null!=d.__e){b.__e=b.__c.base=d.__e;break}return a(b)}}(d)))})}function cy(a,b,c,d,e,f,g,h,i,j){var k,l,m,n,o,p,q,r=d&&d.__k||co,s=r.length;for(c.__k=[],k=0;k<b.length;k++)if(null!=(n=c.__k[k]=null==(n=b[k])||"boolean"==typeof n?null:"string"==typeof n||"number"==typeof n||"bigint"==typeof n?cs(null,n,null,null,n):Array.isArray(n)?cs(ct,{children:n},null,null,null):n.__b>0?cs(n.type,n.props,n.key,n.ref?n.ref:null,n.__v):n)){if(n.__=c,n.__b=c.__b+1,null===(m=r[k])||m&&n.key==m.key&&n.type===m.type)r[k]=void 0;else for(l=0;l<s;l++){if((m=r[l])&&n.key==m.key&&n.type===m.type){r[l]=void 0;break}m=null}cE(a,n,m=m||cn,e,f,g,h,i,j),o=n.__e,(l=n.ref)&&m.ref!=l&&(q||(q=[]),m.ref&&q.push(m.ref,null,n),q.push(l,n.__c||o,n)),null!=o?(null==p&&(p=o),"function"==typeof n.type&&n.__k===m.__k?n.__d=i=function a(b,c,d){for(var e,f=b.__k,g=0;f&&g<f.length;g++)(e=f[g])&&(e.__=b,c="function"==typeof e.type?a(e,c,d):cz(d,e,e,f,e.__e,c));return c}(n,i,a):i=cz(a,n,m,r,o,i),"function"==typeof c.type&&(c.__d=i)):i&&m.__e==i&&i.parentNode!=a&&(i=cv(m))}for(c.__e=p,k=s;k--;)null!=r[k]&&function a(b,c,d){var e,f;if(cj.unmount&&cj.unmount(b),(e=b.ref)&&(e.current&&e.current!==b.__e||cF(e,null,c)),null!=(e=b.__c)){if(e.componentWillUnmount)try{e.componentWillUnmount()}catch(a){cj.__e(a,c)}e.base=e.__P=null,b.__c=void 0}if(e=b.__k)for(f=0;f<e.length;f++)e[f]&&a(e[f],c,d||"function"!=typeof b.type);d||null==b.__e||cr(b.__e),b.__=b.__e=b.__d=void 0}(r[k],r[k]);if(q)for(k=0;k<q.length;k++)cF(q[k],q[++k],q[++k])}function cz(a,b,c,d,e,f){var g,h,i;if(void 0!==b.__d)g=b.__d,b.__d=void 0;else if(null==c||e!=f||null==e.parentNode)a:if(null==f||f.parentNode!==a)a.appendChild(e),g=null;else{for(h=f,i=0;(h=h.nextSibling)&&i<d.length;i+=1)if(h==e)break a;a.insertBefore(e,f),g=f}return void 0!==g?g:e.nextSibling}function cA(a,b,c){"-"===b[0]?a.setProperty(b,c):a[b]=null==c?"":"number"!=typeof c||cp.test(b)?c:c+"px"}function cB(a,b,c,d,e){var f;a:if("style"===b)if("string"==typeof c)a.style.cssText=c;else{if("string"==typeof d&&(a.style.cssText=d=""),d)for(b in d)c&&b in c||cA(a.style,b,"");if(c)for(b in c)d&&c[b]===d[b]||cA(a.style,b,c[b])}else if("o"===b[0]&&"n"===b[1])f=b!==(b=b.replace(/Capture$/,"")),b=b.toLowerCase()in a?b.toLowerCase().slice(2):b.slice(2),a.l||(a.l={}),a.l[b+f]=c,c?d||a.addEventListener(b,f?cD:cC,f):a.removeEventListener(b,f?cD:cC,f);else if("dangerouslySetInnerHTML"!==b){if(e)b=b.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("href"!==b&&"list"!==b&&"form"!==b&&"tabIndex"!==b&&"download"!==b&&b in a)try{a[b]=null==c?"":c;break a}catch(a){}"function"==typeof c||(null==c||!1===c&&-1==b.indexOf("-")?a.removeAttribute(b):a.setAttribute(b,c))}}function cC(a){this.l[a.type+!1](cj.event?cj.event(a):a)}function cD(a){this.l[a.type+!0](cj.event?cj.event(a):a)}function cE(a,b,c,d,e,f,g,h,i){var j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y=b.type;if(void 0!==b.constructor)return null;null!=c.__h&&(i=c.__h,h=b.__e=c.__e,b.__h=null,f=[h]),(j=cj.__b)&&j(b);try{a:if("function"==typeof y){if(q=b.props,r=(j=y.contextType)&&d[j.__c],s=j?r?r.props.value:j.__:d,c.__c?p=(k=b.__c=c.__c).__=k.__E:("prototype"in y&&y.prototype.render?b.__c=k=new y(q,s):(b.__c=k=new cu(q,s),k.constructor=y,k.render=cG),r&&r.sub(k),k.props=q,k.state||(k.state={}),k.context=s,k.__n=d,l=k.__d=!0,k.__h=[],k._sb=[]),null==k.__s&&(k.__s=k.state),null!=y.getDerivedStateFromProps&&(k.__s==k.state&&(k.__s=cq({},k.__s)),cq(k.__s,y.getDerivedStateFromProps(q,k.__s))),m=k.props,n=k.state,l)null==y.getDerivedStateFromProps&&null!=k.componentWillMount&&k.componentWillMount(),null!=k.componentDidMount&&k.__h.push(k.componentDidMount);else{if(null==y.getDerivedStateFromProps&&q!==m&&null!=k.componentWillReceiveProps&&k.componentWillReceiveProps(q,s),!k.__e&&null!=k.shouldComponentUpdate&&!1===k.shouldComponentUpdate(q,k.__s,s)||b.__v===c.__v){for(k.props=q,k.state=k.__s,b.__v!==c.__v&&(k.__d=!1),k.__v=b,b.__e=c.__e,b.__k=c.__k,b.__k.forEach(function(a){a&&(a.__=b)}),t=0;t<k._sb.length;t++)k.__h.push(k._sb[t]);k._sb=[],k.__h.length&&g.push(k);break a}null!=k.componentWillUpdate&&k.componentWillUpdate(q,k.__s,s),null!=k.componentDidUpdate&&k.__h.push(function(){k.componentDidUpdate(m,n,o)})}if(k.context=s,k.props=q,k.__v=b,k.__P=a,u=cj.__r,v=0,"prototype"in y&&y.prototype.render){for(k.state=k.__s,k.__d=!1,u&&u(b),j=k.render(k.props,k.state,k.context),w=0;w<k._sb.length;w++)k.__h.push(k._sb[w]);k._sb=[]}else do k.__d=!1,u&&u(b),j=k.render(k.props,k.state,k.context),k.state=k.__s;while(k.__d&&++v<25);k.state=k.__s,null!=k.getChildContext&&(d=cq(cq({},d),k.getChildContext())),l||null==k.getSnapshotBeforeUpdate||(o=k.getSnapshotBeforeUpdate(m,n)),x=null!=j&&j.type===ct&&null==j.key?j.props.children:j,cy(a,Array.isArray(x)?x:[x],b,c,d,e,f,g,h,i),k.base=b.__e,b.__h=null,k.__h.length&&g.push(k),p&&(k.__E=k.__=null),k.__e=!1}else null==f&&b.__v===c.__v?(b.__k=c.__k,b.__e=c.__e):b.__e=function(a,b,c,d,e,f,g,h){var i,j,k,l=c.props,m=b.props,n=b.type,o=0;if("svg"===n&&(e=!0),null!=f){for(;o<f.length;o++)if((i=f[o])&&"setAttribute"in i==!!n&&(n?i.localName===n:3===i.nodeType)){a=i,f[o]=null;break}}if(null==a){if(null===n)return document.createTextNode(m);a=e?document.createElementNS("http://www.w3.org/2000/svg",n):document.createElement(n,m.is&&m),f=null,h=!1}if(null===n)l===m||h&&a.data===m||(a.data=m);else{if(f=f&&ci.call(a.childNodes),j=(l=c.props||cn).dangerouslySetInnerHTML,k=m.dangerouslySetInnerHTML,!h){if(null!=f)for(l={},o=0;o<a.attributes.length;o++)l[a.attributes[o].name]=a.attributes[o].value;(k||j)&&(k&&(j&&k.__html==j.__html||k.__html===a.innerHTML)||(a.innerHTML=k&&k.__html||""))}if(function(a,b,c,d,e){var f;for(f in c)"children"===f||"key"===f||f in b||cB(a,f,null,c[f],d);for(f in b)e&&"function"!=typeof b[f]||"children"===f||"key"===f||"value"===f||"checked"===f||c[f]===b[f]||cB(a,f,b[f],c[f],d)}(a,m,l,e,h),k)b.__k=[];else if(cy(a,Array.isArray(o=b.props.children)?o:[o],b,c,d,e&&"foreignObject"!==n,f,g,f?f[0]:c.__k&&cv(c,0),h),null!=f)for(o=f.length;o--;)null!=f[o]&&cr(f[o]);h||("value"in m&&void 0!==(o=m.value)&&(o!==a.value||"progress"===n&&!o||"option"===n&&o!==l.value)&&cB(a,"value",o,l.value,!1),"checked"in m&&void 0!==(o=m.checked)&&o!==a.checked&&cB(a,"checked",o,l.checked,!1))}return a}(c.__e,b,c,d,e,f,g,i);(j=cj.diffed)&&j(b)}catch(a){b.__v=null,(i||null!=f)&&(b.__e=h,b.__h=!!i,f[f.indexOf(h)]=null),cj.__e(a,b,c)}}function cF(a,b,c){try{"function"==typeof a?a(b):a.current=b}catch(a){cj.__e(a,c)}}function cG(a,b,c){return this.constructor(a,c)}ci=co.slice,cj={__e:function(a,b,c,d){for(var e,f,g;b=b.__;)if((e=b.__c)&&!e.__)try{if((f=e.constructor)&&null!=f.getDerivedStateFromError&&(e.setState(f.getDerivedStateFromError(a)),g=e.__d),null!=e.componentDidCatch&&(e.componentDidCatch(a,d||{}),g=e.__d),g)return e.__E=e}catch(b){a=b}throw a}},ck=0,cu.prototype.setState=function(a,b){var c;c=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=cq({},this.state),"function"==typeof a&&(a=a(cq({},c),this.props)),a&&cq(c,a),null!=a&&this.__v&&(b&&this._sb.push(b),cw(this))},cu.prototype.forceUpdate=function(a){this.__v&&(this.__e=!0,a&&this.__h.push(a),cw(this))},cu.prototype.render=ct,cl=[],cx.__r=0;var cH=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|^--/i,cI=/^(area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)$/,cJ=/[\s\n\\/='"\0<>]/,cK=/^xlink:?./,cL=/["&<]/;function cM(a){if(!1===cL.test(a+=""))return a;for(var b=0,c=0,d="",e="";c<a.length;c++){switch(a.charCodeAt(c)){case 34:e="&quot;";break;case 38:e="&amp;";break;case 60:e="&lt;";break;default:continue}c!==b&&(d+=a.slice(b,c)),d+=e,b=c+1}return c!==b&&(d+=a.slice(b,c)),d}var cN=function(a,b){return String(a).replace(/(\n+)/g,"$1"+(b||"	"))},cO=function(a,b,c){return String(a).length>(b||40)||!c&&-1!==String(a).indexOf("\n")||-1!==String(a).indexOf("<")},cP={},cQ=/([A-Z])/g;function cR(a){var b="";for(var c in a){var d=a[c];null!=d&&""!==d&&(b&&(b+=" "),b+="-"==c[0]?c:cP[c]||(cP[c]=c.replace(cQ,"-$1").toLowerCase()),b="number"==typeof d&&!1===cH.test(c)?b+": "+d+"px;":b+": "+d+";")}return b||void 0}function cS(a,b){return Array.isArray(b)?b.reduce(cS,a):null!=b&&!1!==b&&a.push(b),a}function cT(){this.__d=!0}function cU(a,b){return{__v:a,context:b,props:a.props,setState:cT,forceUpdate:cT,__d:!0,__h:[]}}function cV(a,b){var c=a.contextType,d=c&&b[c.__c];return null!=c?d?d.props.value:c.__:b}var cW=[],cX={shallow:!0};cZ.render=cZ;var cY=[];function cZ(a,b,c){b=b||{};var d,e=cj.__s;return cj.__s=!0,d=c&&(c.pretty||c.voidElements||c.sortAttributes||c.shallow||c.allAttributes||c.xml||c.attributeHook)?function a(b,c,d,e,f,g){if(null==b||"boolean"==typeof b)return"";if("object"!=typeof b)return cM(b);var h=d.pretty,i=h&&"string"==typeof h?h:"	";if(Array.isArray(b)){for(var j="",k=0;k<b.length;k++)h&&k>0&&(j+="\n"),j+=a(b[k],c,d,e,f,g);return j}var l,m=b.type,n=b.props,o=!1;if("function"==typeof m){if(o=!0,!d.shallow||!e&&!1!==d.renderRootComponent){if(m===ct){var p=[];return cS(p,b.props.children),a(p,c,d,!1!==d.shallowHighOrder,f,g)}var q,r=b.__c=cU(b,c);cj.__b&&cj.__b(b);var s=cj.__r;if(m.prototype&&"function"==typeof m.prototype.render){var t=cV(m,c);(r=b.__c=new m(n,t)).__v=b,r._dirty=r.__d=!0,r.props=n,null==r.state&&(r.state={}),null==r._nextState&&null==r.__s&&(r._nextState=r.__s=r.state),r.context=t,m.getDerivedStateFromProps?r.state=Object.assign({},r.state,m.getDerivedStateFromProps(r.props,r.state)):r.componentWillMount&&(r.componentWillMount(),r.state=r._nextState!==r.state?r._nextState:r.__s!==r.state?r.__s:r.state),s&&s(b),q=r.render(r.props,r.state,r.context)}else for(var u=cV(m,c),v=0;r.__d&&v++<25;)r.__d=!1,s&&s(b),q=m.call(b.__c,n,u);return r.getChildContext&&(c=Object.assign({},c,r.getChildContext())),cj.diffed&&cj.diffed(b),a(q,c,d,!1!==d.shallowHighOrder,f,g)}m=(l=m).displayName||l!==Function&&l.name||function(a){var b=(Function.prototype.toString.call(a).match(/^\s*function\s+([^( ]+)/)||"")[1];if(!b){for(var c=-1,d=cW.length;d--;)if(cW[d]===a){c=d;break}c<0&&(c=cW.push(a)-1),b="UnnamedComponent"+c}return b}(l)}var w,x,y="<"+m;if(n){var z=Object.keys(n);d&&!0===d.sortAttributes&&z.sort();for(var A=0;A<z.length;A++){var B=z[A],C=n[B];if("children"!==B){if(!cJ.test(B)&&(d&&d.allAttributes||"key"!==B&&"ref"!==B&&"__self"!==B&&"__source"!==B)){if("defaultValue"===B)B="value";else if("defaultChecked"===B)B="checked";else if("defaultSelected"===B)B="selected";else if("className"===B){if(void 0!==n.class)continue;B="class"}else f&&cK.test(B)&&(B=B.toLowerCase().replace(/^xlink:?/,"xlink:"));if("htmlFor"===B){if(n.for)continue;B="for"}"style"===B&&C&&"object"==typeof C&&(C=cR(C)),"a"===B[0]&&"r"===B[1]&&"boolean"==typeof C&&(C=String(C));var D=d.attributeHook&&d.attributeHook(B,C,c,d,o);if(D||""===D)y+=D;else if("dangerouslySetInnerHTML"===B)x=C&&C.__html;else if("textarea"===m&&"value"===B)w=C;else if((C||0===C||""===C)&&"function"!=typeof C){if(!(!0!==C&&""!==C||(C=B,d&&d.xml))){y=y+" "+B;continue}if("value"===B){if("select"===m){g=C;continue}"option"===m&&g==C&&void 0===n.selected&&(y+=" selected")}y=y+" "+B+'="'+cM(C)+'"'}}}else w=C}}if(h){var E=y.replace(/\n\s*/," ");E===y||~E.indexOf("\n")?h&&~y.indexOf("\n")&&(y+="\n"):y=E}if(y+=">",cJ.test(m))throw Error(m+" is not a valid HTML tag name in "+y);var F,G=cI.test(m)||d.voidElements&&d.voidElements.test(m),H=[];if(x)h&&cO(x)&&(x="\n"+i+cN(x,i)),y+=x;else if(null!=w&&cS(F=[],w).length){for(var I=h&&~y.indexOf("\n"),J=!1,K=0;K<F.length;K++){var L=F[K];if(null!=L&&!1!==L){var M=a(L,c,d,!0,"svg"===m||"foreignObject"!==m&&f,g);if(h&&!I&&cO(M)&&(I=!0),M)if(h){var N=M.length>0&&"<"!=M[0];J&&N?H[H.length-1]+=M:H.push(M),J=N}else H.push(M)}}if(h&&I)for(var O=H.length;O--;)H[O]="\n"+i+cN(H[O],i)}if(H.length||x)y+=H.join("");else if(d&&d.xml)return y.substring(0,y.length-1)+" />";return!G||F||x?(h&&~y.indexOf("\n")&&(y+="\n"),y=y+"</"+m+">"):y=y.replace(/>$/," />"),y}(a,b,c):function a(b,c,d,e){if(null==b||!0===b||!1===b||""===b)return"";if("object"!=typeof b)return cM(b);if(c$(b)){for(var f="",g=0;g<b.length;g++)f+=a(b[g],c,d,e);return f}cj.__b&&cj.__b(b);var h=b.type,i=b.props;if("function"==typeof h){if(h===ct)return a(b.props.children,c,d,e);var j,k,l,m,n,o=h.prototype&&"function"==typeof h.prototype.render?(j=c,l=cV(k=b.type,j),m=new k(b.props,l),b.__c=m,m.__v=b,m.__d=!0,m.props=b.props,null==m.state&&(m.state={}),null==m.__s&&(m.__s=m.state),m.context=l,k.getDerivedStateFromProps?m.state=c_({},m.state,k.getDerivedStateFromProps(m.props,m.state)):m.componentWillMount&&(m.componentWillMount(),m.state=m.__s!==m.state?m.__s:m.state),(n=cj.__r)&&n(b),m.render(m.props,m.state,m.context)):function(a,b){var c,d=cU(a,b),e=cV(a.type,b);a.__c=d;for(var f=cj.__r,g=0;d.__d&&g++<25;)d.__d=!1,f&&f(a),c=a.type.call(d,a.props,e);return c}(b,c),p=b.__c;p.getChildContext&&(c=c_({},c,p.getChildContext()));var q=a(o,c,d,e);return cj.diffed&&cj.diffed(b),q}var r,s,t="<";if(t+=h,i)for(var u in r=i.children,i){var v,w,x,y=i[u];if(!("key"===u||"ref"===u||"__self"===u||"__source"===u||"children"===u||"className"===u&&"class"in i||"htmlFor"===u&&"for"in i||cJ.test(u))){if(w=u="className"===(v=u)?"class":"htmlFor"===v?"for":"defaultValue"===v?"value":"defaultChecked"===v?"checked":"defaultSelected"===v?"selected":d&&cK.test(v)?v.toLowerCase().replace(/^xlink:?/,"xlink:"):v,x=y,y="style"===w&&null!=x&&"object"==typeof x?cR(x):"a"===w[0]&&"r"===w[1]&&"boolean"==typeof x?String(x):x,"dangerouslySetInnerHTML"===u)s=y&&y.__html;else if("textarea"===h&&"value"===u)r=y;else if((y||0===y||""===y)&&"function"!=typeof y){if(!0===y||""===y){y=u,t=t+" "+u;continue}if("value"===u){if("select"===h){e=y;continue}"option"!==h||e!=y||"selected"in i||(t+=" selected")}t=t+" "+u+'="'+cM(y)+'"'}}}var z=t;if(t+=">",cJ.test(h))throw Error(h+" is not a valid HTML tag name in "+t);var A="",B=!1;if(s)A+=s,B=!0;else if("string"==typeof r)A+=cM(r),B=!0;else if(c$(r))for(var C=0;C<r.length;C++){var D=r[C];if(null!=D&&!1!==D){var E=a(D,c,"svg"===h||"foreignObject"!==h&&d,e);E&&(A+=E,B=!0)}}else if(null!=r&&!1!==r&&!0!==r){var F=a(r,c,"svg"===h||"foreignObject"!==h&&d,e);F&&(A+=F,B=!0)}if(cj.diffed&&cj.diffed(b),B)t+=A;else if(cI.test(h))return z+" />";return t+"</"+h+">"}(a,b,!1,void 0),cj.__c&&cj.__c(a,cY),cj.__s=e,cY.length=0,d}var c$=Array.isArray,c_=Object.assign;cZ.shallowRender=function(a,b){return cZ(a,b,cX)};var c0=0;function c1(a,b,c,d,e){var f,g,h={};for(g in b)"ref"==g?f=b[g]:h[g]=b[g];var i={type:a,props:h,key:c,ref:f,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:--c0,__source:e,__self:d};if("function"==typeof a&&(f=a.defaultProps))for(g in f)void 0===h[g]&&(h[g]=f[g]);return cj.vnode&&cj.vnode(i),i}async function c2(a,b){let c=window.SimpleWebAuthnBrowser;async function d(c){let d=new URL(`${a}/webauthn-options/${b}`);c&&d.searchParams.append("action",c),f().forEach(a=>{d.searchParams.append(a.name,a.value)});let e=await fetch(d);return e.ok?e.json():void console.error("Failed to fetch options",e)}function e(){let a=`#${b}-form`,c=document.querySelector(a);if(!c)throw Error(`Form '${a}' not found`);return c}function f(){return Array.from(e().querySelectorAll("input[data-form-field]"))}async function g(a,b){let c=e();if(a){let b=document.createElement("input");b.type="hidden",b.name="action",b.value=a,c.appendChild(b)}if(b){let a=document.createElement("input");a.type="hidden",a.name="data",a.value=JSON.stringify(b),c.appendChild(a)}return c.submit()}async function h(a,b){let d=await c.startAuthentication(a,b);return await g("authenticate",d)}async function i(a){f().forEach(a=>{if(a.required&&!a.value)throw Error(`Missing required field: ${a.name}`)});let b=await c.startRegistration(a);return await g("register",b)}async function j(){if(!c.browserSupportsWebAuthnAutofill())return;let a=await d("authenticate");if(!a)return void console.error("Failed to fetch option for autofill authentication");try{await h(a.options,!0)}catch(a){console.error(a)}}(async function(){let a=e();if(!c.browserSupportsWebAuthn()){a.style.display="none";return}a&&a.addEventListener("submit",async a=>{a.preventDefault();let b=await d(void 0);if(!b)return void console.error("Failed to fetch options for form submission");if("authenticate"===b.action)try{await h(b.options,!1)}catch(a){console.error(a)}else if("register"===b.action)try{await i(b.options)}catch(a){console.error(a)}})})(),j()}let c3={default:"Unable to sign in.",Signin:"Try signing in with a different account.",OAuthSignin:"Try signing in with a different account.",OAuthCallbackError:"Try signing in with a different account.",OAuthCreateAccount:"Try signing in with a different account.",EmailCreateAccount:"Try signing in with a different account.",Callback:"Try signing in with a different account.",OAuthAccountNotLinked:"To confirm your identity, sign in with the same account you used originally.",EmailSignin:"The e-mail could not be sent.",CredentialsSignin:"Sign in failed. Check the details you provided are correct.",SessionRequired:"Please sign in to access this page."},c4=`:root {
  --border-width: 1px;
  --border-radius: 0.5rem;
  --color-error: #c94b4b;
  --color-info: #157efb;
  --color-info-hover: #0f6ddb;
  --color-info-text: #fff;
}

.__next-auth-theme-auto,
.__next-auth-theme-light {
  --color-background: #ececec;
  --color-background-hover: rgba(236, 236, 236, 0.8);
  --color-background-card: #fff;
  --color-text: #000;
  --color-primary: #444;
  --color-control-border: #bbb;
  --color-button-active-background: #f9f9f9;
  --color-button-active-border: #aaa;
  --color-separator: #ccc;
}

.__next-auth-theme-dark {
  --color-background: #161b22;
  --color-background-hover: rgba(22, 27, 34, 0.8);
  --color-background-card: #0d1117;
  --color-text: #fff;
  --color-primary: #ccc;
  --color-control-border: #555;
  --color-button-active-background: #060606;
  --color-button-active-border: #666;
  --color-separator: #444;
}

@media (prefers-color-scheme: dark) {
  .__next-auth-theme-auto {
    --color-background: #161b22;
    --color-background-hover: rgba(22, 27, 34, 0.8);
    --color-background-card: #0d1117;
    --color-text: #fff;
    --color-primary: #ccc;
    --color-control-border: #555;
    --color-button-active-background: #060606;
    --color-button-active-border: #666;
    --color-separator: #444;
  }

  button,
  a.button {
    color: var(--provider-dark-color, var(--color-primary)) !important;
    background-color: var(
      --provider-dark-bg,
      var(--color-background)
    ) !important;
  }

    :is(button,a.button):hover {
      background-color: var(
        --provider-dark-bg-hover,
        var(--color-background-hover)
      ) !important;
    }

    :is(button,a.button) span {
      color: var(--provider-dark-bg) !important;
    }
}

html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
  margin: 0;
  padding: 0;
}

body {
  background-color: var(--color-background);
  margin: 0;
  padding: 0;
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
}

h1 {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  font-weight: 400;
  color: var(--color-text);
}

p {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  color: var(--color-text);
}

form {
  margin: 0;
  padding: 0;
}

label {
  font-weight: 500;
  text-align: left;
  margin-bottom: 0.25rem;
  display: block;
  color: var(--color-text);
}

input[type] {
  box-sizing: border-box;
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  border: var(--border-width) solid var(--color-control-border);
  background: var(--color-background-card);
  font-size: 1rem;
  border-radius: var(--border-radius);
  color: var(--color-text);
}

p {
  font-size: 1.1rem;
  line-height: 2rem;
}

a.button {
  text-decoration: none;
  line-height: 1rem;
}

a.button:link,
  a.button:visited {
    background-color: var(--color-background);
    color: var(--color-primary);
  }

button,
a.button {
  padding: 0.75rem 1rem;
  color: var(--provider-color, var(--color-primary));
  background-color: var(--provider-bg, var(--color-background));
  border: 1px solid #00000031;
  font-size: 0.9rem;
  height: 50px;
  border-radius: var(--border-radius);
  transition: background-color 250ms ease-in-out;
  font-weight: 300;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:is(button,a.button):hover {
    background-color: var(--provider-bg-hover, var(--color-background-hover));
    cursor: pointer;
  }

:is(button,a.button):active {
    cursor: pointer;
  }

:is(button,a.button) span {
    color: #fff;
  }

#submitButton {
  color: var(--button-text-color, var(--color-info-text));
  background-color: var(--brand-color, var(--color-info));
  width: 100%;
}

#submitButton:hover {
    background-color: var(
      --button-hover-bg,
      var(--color-info-hover)
    ) !important;
  }

a.site {
  color: var(--color-primary);
  text-decoration: none;
  font-size: 1rem;
  line-height: 2rem;
}

a.site:hover {
    text-decoration: underline;
  }

.page {
  position: absolute;
  width: 100%;
  height: 100%;
  display: grid;
  place-items: center;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.page > div {
    text-align: center;
  }

.error a.button {
    padding-left: 2rem;
    padding-right: 2rem;
    margin-top: 0.5rem;
  }

.error .message {
    margin-bottom: 1.5rem;
  }

.signin input[type="text"] {
    margin-left: auto;
    margin-right: auto;
    display: block;
  }

.signin hr {
    display: block;
    border: 0;
    border-top: 1px solid var(--color-separator);
    margin: 2rem auto 1rem auto;
    overflow: visible;
  }

.signin hr::before {
      content: "or";
      background: var(--color-background-card);
      color: #888;
      padding: 0 0.4rem;
      position: relative;
      top: -0.7rem;
    }

.signin .error {
    background: #f5f5f5;
    font-weight: 500;
    border-radius: 0.3rem;
    background: var(--color-error);
  }

.signin .error p {
      text-align: left;
      padding: 0.5rem 1rem;
      font-size: 0.9rem;
      line-height: 1.2rem;
      color: var(--color-info-text);
    }

.signin > div,
  .signin form {
    display: block;
  }

.signin > div input[type], .signin form input[type] {
      margin-bottom: 0.5rem;
    }

.signin > div button, .signin form button {
      width: 100%;
    }

.signin .provider + .provider {
    margin-top: 1rem;
  }

.logo {
  display: inline-block;
  max-width: 150px;
  margin: 1.25rem 0;
  max-height: 70px;
}

.card {
  background-color: var(--color-background-card);
  border-radius: 1rem;
  padding: 1.25rem 2rem;
}

.card .header {
    color: var(--color-primary);
  }

.card input[type]::-moz-placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type]::placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type] {
    background: color-mix(in srgb, var(--color-background-card) 95%, black);
  }

.section-header {
  color: var(--color-text);
}

@media screen and (min-width: 450px) {
  .card {
    margin: 2rem 0;
    width: 368px;
  }
}

@media screen and (max-width: 450px) {
  .card {
    margin: 1rem 0;
    width: 343px;
  }
}
`;function c5({html:a,title:b,status:c,cookies:d,theme:e,headTags:f}){return{cookies:d,status:c,headers:{"Content-Type":"text/html"},body:`<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><style>${c4}</style><title>${b}</title>${f??""}</head><body class="__next-auth-theme-${e?.colorScheme??"auto"}"><div class="page">${cZ(a)}</div></body></html>`}}function c6(a){let{url:b,theme:c,query:d,cookies:e,pages:f,providers:g}=a;return{csrf:(a,b,c)=>a?(b.logger.warn("csrf-disabled"),c.push({name:b.cookies.csrfToken.name,value:"",options:{...b.cookies.csrfToken.options,maxAge:0}}),{status:404,cookies:c}):{headers:{"Content-Type":"application/json"},body:{csrfToken:b.csrfToken},cookies:c},providers:a=>({headers:{"Content-Type":"application/json"},body:a.reduce((a,{id:b,name:c,type:d,signinUrl:e,callbackUrl:f})=>(a[b]={id:b,name:c,type:d,signinUrl:e,callbackUrl:f},a),{})}),signin(b,h){if(b)throw new J("Unsupported action");if(f?.signIn){let b=`${f.signIn}${f.signIn.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:a.callbackUrl??"/"})}`;return h&&(b=`${b}&${new URLSearchParams({error:h})}`),{redirect:b,cookies:e}}let i=g?.find(a=>"webauthn"===a.type&&a.enableConditionalUI&&!!a.simpleWebAuthnBrowserVersion),j="";if(i){let{simpleWebAuthnBrowserVersion:a}=i;j=`<script src="https://unpkg.com/@simplewebauthn/browser@${a}/dist/bundle/index.umd.min.js" crossorigin="anonymous"></script>`}return c5({cookies:e,theme:c,html:function(a){let{csrfToken:b,providers:c=[],callbackUrl:d,theme:e,email:f,error:g}=a;"undefined"!=typeof document&&e?.brandColor&&document.documentElement.style.setProperty("--brand-color",e.brandColor),"undefined"!=typeof document&&e?.buttonText&&document.documentElement.style.setProperty("--button-text-color",e.buttonText);let h=g&&(c3[g]??c3.default),i=c.find(a=>"webauthn"===a.type&&a.enableConditionalUI)?.id;return c1("div",{className:"signin",children:[e?.brandColor&&c1("style",{dangerouslySetInnerHTML:{__html:`:root {--brand-color: ${e.brandColor}}`}}),e?.buttonText&&c1("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${e.buttonText}
        }
      `}}),c1("div",{className:"card",children:[h&&c1("div",{className:"error",children:c1("p",{children:h})}),e?.logo&&c1("img",{src:e.logo,alt:"Logo",className:"logo"}),c.map((a,e)=>{let g,h,i;("oauth"===a.type||"oidc"===a.type)&&({bg:g="#fff",brandColor:h,logo:i=`https://authjs.dev/img/providers/${a.id}.svg`}=a.style??{});let j=h??g??"#fff";return c1("div",{className:"provider",children:["oauth"===a.type||"oidc"===a.type?c1("form",{action:a.signinUrl,method:"POST",children:[c1("input",{type:"hidden",name:"csrfToken",value:b}),d&&c1("input",{type:"hidden",name:"callbackUrl",value:d}),c1("button",{type:"submit",className:"button",style:{"--provider-bg":"#fff","--provider-bg-hover":`color-mix(in srgb, ${j} 30%, #fff)`,"--provider-dark-bg":"#161b22","--provider-dark-bg-hover":`color-mix(in srgb, ${j} 30%, #000)`},tabIndex:0,children:[c1("span",{style:{filter:"invert(1) grayscale(1) brightness(1.3) contrast(9000)","mix-blend-mode":"luminosity",opacity:.95},children:["Sign in with ",a.name]}),i&&c1("img",{loading:"lazy",height:24,src:i})]})]}):null,("email"===a.type||"credentials"===a.type||"webauthn"===a.type)&&e>0&&"email"!==c[e-1].type&&"credentials"!==c[e-1].type&&"webauthn"!==c[e-1].type&&c1("hr",{}),"email"===a.type&&c1("form",{action:a.signinUrl,method:"POST",children:[c1("input",{type:"hidden",name:"csrfToken",value:b}),c1("label",{className:"section-header",htmlFor:`input-email-for-${a.id}-provider`,children:"Email"}),c1("input",{id:`input-email-for-${a.id}-provider`,autoFocus:!0,type:"email",name:"email",value:f,placeholder:"<EMAIL>",required:!0}),c1("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",a.name]})]}),"credentials"===a.type&&c1("form",{action:a.callbackUrl,method:"POST",children:[c1("input",{type:"hidden",name:"csrfToken",value:b}),Object.keys(a.credentials).map(b=>c1("div",{children:[c1("label",{className:"section-header",htmlFor:`input-${b}-for-${a.id}-provider`,children:a.credentials[b].label??b}),c1("input",{name:b,id:`input-${b}-for-${a.id}-provider`,type:a.credentials[b].type??"text",placeholder:a.credentials[b].placeholder??"",...a.credentials[b]})]},`input-group-${a.id}`)),c1("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",a.name]})]}),"webauthn"===a.type&&c1("form",{action:a.callbackUrl,method:"POST",id:`${a.id}-form`,children:[c1("input",{type:"hidden",name:"csrfToken",value:b}),Object.keys(a.formFields).map(b=>c1("div",{children:[c1("label",{className:"section-header",htmlFor:`input-${b}-for-${a.id}-provider`,children:a.formFields[b].label??b}),c1("input",{name:b,"data-form-field":!0,id:`input-${b}-for-${a.id}-provider`,type:a.formFields[b].type??"text",placeholder:a.formFields[b].placeholder??"",...a.formFields[b]})]},`input-group-${a.id}`)),c1("button",{id:`submitButton-${a.id}`,type:"submit",tabIndex:0,children:["Sign in with ",a.name]})]}),("email"===a.type||"credentials"===a.type||"webauthn"===a.type)&&e+1<c.length&&c1("hr",{})]},a.id)})]}),i&&c1(ct,{children:c1("script",{dangerouslySetInnerHTML:{__html:`
const currentURL = window.location.href;
const authURL = currentURL.substring(0, currentURL.lastIndexOf('/'));
(${c2})(authURL, "${i}");
`}})})]})}({csrfToken:a.csrfToken,providers:a.providers?.filter(a=>["email","oauth","oidc"].includes(a.type)||"credentials"===a.type&&a.credentials||"webauthn"===a.type&&a.formFields||!1),callbackUrl:a.callbackUrl,theme:a.theme,error:h,...d}),title:"Sign In",headTags:j})},signout:()=>f?.signOut?{redirect:f.signOut,cookies:e}:c5({cookies:e,theme:c,html:function(a){let{url:b,csrfToken:c,theme:d}=a;return c1("div",{className:"signout",children:[d?.brandColor&&c1("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${d.brandColor}
        }
      `}}),d?.buttonText&&c1("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${d.buttonText}
        }
      `}}),c1("div",{className:"card",children:[d?.logo&&c1("img",{src:d.logo,alt:"Logo",className:"logo"}),c1("h1",{children:"Signout"}),c1("p",{children:"Are you sure you want to sign out?"}),c1("form",{action:b?.toString(),method:"POST",children:[c1("input",{type:"hidden",name:"csrfToken",value:c}),c1("button",{id:"submitButton",type:"submit",children:"Sign out"})]})]})]})}({csrfToken:a.csrfToken,url:b,theme:c}),title:"Sign Out"}),verifyRequest:a=>f?.verifyRequest?{redirect:f.verifyRequest,cookies:e}:c5({cookies:e,theme:c,html:function(a){let{url:b,theme:c}=a;return c1("div",{className:"verify-request",children:[c.brandColor&&c1("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${c.brandColor}
        }
      `}}),c1("div",{className:"card",children:[c.logo&&c1("img",{src:c.logo,alt:"Logo",className:"logo"}),c1("h1",{children:"Check your email"}),c1("p",{children:"A sign in link has been sent to your email address."}),c1("p",{children:c1("a",{className:"site",href:b.origin,children:b.host})})]})]})}({url:b,theme:c,...a}),title:"Verify Request"}),error:a=>f?.error?{redirect:`${f.error}${f.error.includes("?")?"&":"?"}error=${a}`,cookies:e}:c5({cookies:e,theme:c,...function(a){let{url:b,error:c="default",theme:d}=a,e=`${b}/signin`,f={default:{status:200,heading:"Error",message:c1("p",{children:c1("a",{className:"site",href:b?.origin,children:b?.host})})},Configuration:{status:500,heading:"Server error",message:c1("div",{children:[c1("p",{children:"There is a problem with the server configuration."}),c1("p",{children:"Check the server logs for more information."})]})},AccessDenied:{status:403,heading:"Access Denied",message:c1("div",{children:[c1("p",{children:"You do not have permission to sign in."}),c1("p",{children:c1("a",{className:"button",href:e,children:"Sign in"})})]})},Verification:{status:403,heading:"Unable to sign in",message:c1("div",{children:[c1("p",{children:"The sign in link is no longer valid."}),c1("p",{children:"It may have been used already or it may have expired."})]}),signin:c1("a",{className:"button",href:e,children:"Sign in"})}},{status:g,heading:h,message:i,signin:j}=f[c]??f.default;return{status:g,html:c1("div",{className:"error",children:[d?.brandColor&&c1("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${d?.brandColor}
        }
      `}}),c1("div",{className:"card",children:[d?.logo&&c1("img",{src:d?.logo,alt:"Logo",className:"logo"}),c1("h1",{children:h}),c1("div",{className:"message",children:i}),j]})]})}}({url:b,theme:c,error:a}),title:"Error"})}}function c7(a,b=Date.now()){return new Date(b+1e3*a)}async function c8(a,b,c,d){if(!c?.providerAccountId||!c.type)throw Error("Missing or invalid provider account");if(!["email","oauth","oidc","webauthn"].includes(c.type))throw Error("Provider not supported");let{adapter:e,jwt:f,events:g,session:{strategy:h,generateSessionToken:i}}=d;if(!e)return{user:b,account:c};let j=c,{createUser:k,updateUser:l,getUser:m,getUserByAccount:n,getUserByEmail:o,linkAccount:p,createSession:q,getSessionAndUser:r,deleteSession:s}=e,t=null,u=null,v=!1,w="jwt"===h;if(a)if(w)try{let b=d.cookies.sessionToken.name;(t=await f.decode({...f,token:a,salt:b}))&&"sub"in t&&t.sub&&(u=await m(t.sub))}catch{}else{let b=await r(a);b&&(t=b.session,u=b.user)}if("email"===j.type){let c=await o(b.email);return c?(u?.id!==c.id&&!w&&a&&await s(a),u=await l({id:c.id,emailVerified:new Date}),await g.updateUser?.({user:u})):(u=await k({...b,emailVerified:new Date}),await g.createUser?.({user:u}),v=!0),{session:t=w?{}:await q({sessionToken:i(),userId:u.id,expires:c7(d.session.maxAge)}),user:u,isNewUser:v}}if("webauthn"===j.type){let a=await n({providerAccountId:j.providerAccountId,provider:j.provider});if(a){if(u){if(a.id===u.id){let a={...j,userId:u.id};return{session:t,user:u,isNewUser:v,account:a}}throw new T("The account is already associated with another user",{provider:j.provider})}t=w?{}:await q({sessionToken:i(),userId:a.id,expires:c7(d.session.maxAge)});let b={...j,userId:a.id};return{session:t,user:a,isNewUser:v,account:b}}{if(u){await p({...j,userId:u.id}),await g.linkAccount?.({user:u,account:j,profile:b});let a={...j,userId:u.id};return{session:t,user:u,isNewUser:v,account:a}}if(b.email?await o(b.email):null)throw new T("Another account already exists with the same e-mail address",{provider:j.provider});u=await k({...b}),await g.createUser?.({user:u}),await p({...j,userId:u.id}),await g.linkAccount?.({user:u,account:j,profile:b}),t=w?{}:await q({sessionToken:i(),userId:u.id,expires:c7(d.session.maxAge)});let a={...j,userId:u.id};return{session:t,user:u,isNewUser:!0,account:a}}}let x=await n({providerAccountId:j.providerAccountId,provider:j.provider});if(x){if(u){if(x.id===u.id)return{session:t,user:u,isNewUser:v};throw new C("The account is already associated with another user",{provider:j.provider})}return{session:t=w?{}:await q({sessionToken:i(),userId:x.id,expires:c7(d.session.maxAge)}),user:x,isNewUser:v}}{let{provider:a}=d,{type:c,provider:e,providerAccountId:f,userId:h,...l}=j;if(j=Object.assign(a.account(l)??{},{providerAccountId:f,provider:e,type:c,userId:h}),u)return await p({...j,userId:u.id}),await g.linkAccount?.({user:u,account:j,profile:b}),{session:t,user:u,isNewUser:v};let m=b.email?await o(b.email):null;if(m){let a=d.provider;if(a?.allowDangerousEmailAccountLinking)u=m,v=!1;else throw new C("Another account already exists with the same e-mail address",{provider:j.provider})}else u=await k({...b,emailVerified:null}),v=!0;return await g.createUser?.({user:u}),await p({...j,userId:u.id}),await g.linkAccount?.({user:u,account:j,profile:b}),{session:t=w?{}:await q({sessionToken:i(),userId:u.id,expires:c7(d.session.maxAge)}),user:u,isNewUser:v}}}function c9(a,b){if(null==a)return!1;try{return a instanceof b||Object.getPrototypeOf(a)[Symbol.toStringTag]===b.prototype[Symbol.toStringTag]}catch{return!1}}"undefined"!=typeof navigator&&navigator.userAgent?.startsWith?.("Mozilla/5.0 ")||(f="oauth4webapi/v3.6.0");let da="ERR_INVALID_ARG_VALUE",db="ERR_INVALID_ARG_TYPE";function dc(a,b,c){let d=TypeError(a,{cause:c});return Object.assign(d,{code:b}),d}let dd=Symbol(),de=Symbol(),df=Symbol(),dg=Symbol(),dh=Symbol(),di=Symbol();Symbol();let dj=new TextEncoder,dk=new TextDecoder;function dl(a){return"string"==typeof a?dj.encode(a):dk.decode(a)}function dm(a){return"string"==typeof a?h(a):g(a)}g=Uint8Array.prototype.toBase64?a=>(a instanceof ArrayBuffer&&(a=new Uint8Array(a)),a.toBase64({alphabet:"base64url",omitPadding:!0})):a=>{a instanceof ArrayBuffer&&(a=new Uint8Array(a));let b=[];for(let c=0;c<a.byteLength;c+=32768)b.push(String.fromCharCode.apply(null,a.subarray(c,c+32768)));return btoa(b.join("")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")},h=Uint8Array.fromBase64?a=>{try{return Uint8Array.fromBase64(a,{alphabet:"base64url"})}catch(a){throw dc("The input to be decoded is not correctly encoded.",da,a)}}:a=>{try{let b=atob(a.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"")),c=new Uint8Array(b.length);for(let a=0;a<b.length;a++)c[a]=b.charCodeAt(a);return c}catch(a){throw dc("The input to be decoded is not correctly encoded.",da,a)}};class dn extends Error{code;constructor(a,b){super(a,b),this.name=this.constructor.name,this.code=ep,Error.captureStackTrace?.(this,this.constructor)}}class dp extends Error{code;constructor(a,b){super(a,b),this.name=this.constructor.name,b?.code&&(this.code=b?.code),Error.captureStackTrace?.(this,this.constructor)}}function dq(a,b,c){return new dp(a,{code:b,cause:c})}function dr(a){return!(null===a||"object"!=typeof a||Array.isArray(a))}function ds(a){c9(a,Headers)&&(a=Object.fromEntries(a.entries()));let b=new Headers(a??{});if(f&&!b.has("user-agent")&&b.set("user-agent",f),b.has("authorization"))throw dc('"options.headers" must not include the "authorization" header name',da);return b}function dt(a,b){if(void 0!==b){if("function"==typeof b&&(b=b(a.href)),!(b instanceof AbortSignal))throw dc('"options.signal" must return or be an instance of AbortSignal',db);return b}}function du(a){return a.includes("//")?a.replace("//","/"):a}async function dv(a,b,c,d){if(!(a instanceof URL))throw dc(`"${b}" must be an instance of URL`,db);dL(a,d?.[dd]!==!0);let e=c(new URL(a.href)),f=ds(d?.headers);return f.set("accept","application/json"),(d?.[dg]||fetch)(e.href,{body:void 0,headers:Object.fromEntries(f.entries()),method:"GET",redirect:"manual",signal:dt(e,d?.signal)})}async function dw(a,b){return dv(a,"issuerIdentifier",a=>{switch(b?.algorithm){case void 0:case"oidc":a.pathname=du(`${a.pathname}/.well-known/openid-configuration`);break;case"oauth2":!function(a,b,c=!1){"/"===a.pathname?a.pathname=b:a.pathname=du(`${b}/${c?a.pathname:a.pathname.replace(/(\/)$/,"")}`)}(a,".well-known/oauth-authorization-server");break;default:throw dc('"options.algorithm" must be "oidc" (default), or "oauth2"',da)}return a},b)}function dx(a,b,c,d,e){try{if("number"!=typeof a||!Number.isFinite(a))throw dc(`${c} must be a number`,db,e);if(a>0)return;if(b){if(0!==a)throw dc(`${c} must be a non-negative number`,da,e);return}throw dc(`${c} must be a positive number`,da,e)}catch(a){if(d)throw dq(a.message,d,e);throw a}}function dy(a,b,c,d){try{if("string"!=typeof a)throw dc(`${b} must be a string`,db,d);if(0===a.length)throw dc(`${b} must not be empty`,da,d)}catch(a){if(c)throw dq(a.message,c,d);throw a}}async function dz(a,b){if(!(a instanceof URL)&&a!==eL)throw dc('"expectedIssuerIdentifier" must be an instance of URL',db);if(!c9(b,Response))throw dc('"response" must be an instance of Response',db);if(200!==b.status)throw dq('"response" is not a conform Authorization Server Metadata response (unexpected HTTP status code)',ev,b);eD(b);let c=await eK(b);if(dy(c.issuer,'"response" body "issuer" property',et,{body:c}),a!==eL&&new URL(c.issuer).href!==a.href)throw dq('"response" body "issuer" property does not match the expected value',eA,{expected:a.href,body:c,attribute:"issuer"});return c}function dA(a){var b=a,c="application/json";if(d0(b)!==c)throw function(a,...b){let c='"response" content-type must be ';if(b.length>2){let a=b.pop();c+=`${b.join(", ")}, or ${a}`}else 2===b.length?c+=`${b[0]} or ${b[1]}`:c+=b[0];return dq(c,eu,a)}(b,c)}function dB(){return dm(crypto.getRandomValues(new Uint8Array(32)))}async function dC(a){return dy(a,"codeVerifier"),dm(await crypto.subtle.digest("SHA-256",dl(a)))}function dD(a){let b=a?.[de];return"number"==typeof b&&Number.isFinite(b)?b:0}function dE(a){let b=a?.[df];return"number"==typeof b&&Number.isFinite(b)&&-1!==Math.sign(b)?b:30}function dF(){return Math.floor(Date.now()/1e3)}function dG(a){if("object"!=typeof a||null===a)throw dc('"as" must be an object',db);dy(a.issuer,'"as.issuer"')}function dH(a){if("object"!=typeof a||null===a)throw dc('"client" must be an object',db);dy(a.client_id,'"client.client_id"')}function dI(a,b){let c=dF()+dD(b);return{jti:dB(),aud:a.issuer,exp:c+60,iat:c,nbf:c,iss:b.client_id,sub:b.client_id}}async function dJ(a,b,c){if(!c.usages.includes("sign"))throw dc('CryptoKey instances used for signing assertions must include "sign" in their "usages"',da);let d=`${dm(dl(JSON.stringify(a)))}.${dm(dl(JSON.stringify(b)))}`,e=dm(await crypto.subtle.sign(function(a){switch(a.algorithm.name){case"ECDSA":return{name:a.algorithm.name,hash:function(a){let{algorithm:b}=a;switch(b.namedCurve){case"P-256":return"SHA-256";case"P-384":return"SHA-384";case"P-521":return"SHA-512";default:throw new dn("unsupported ECDSA namedCurve",{cause:a})}}(a)};case"RSA-PSS":switch(eE(a),a.algorithm.hash.name){case"SHA-256":case"SHA-384":case"SHA-512":return{name:a.algorithm.name,saltLength:parseInt(a.algorithm.hash.name.slice(-3),10)>>3};default:throw new dn("unsupported RSA-PSS hash name",{cause:a})}case"RSASSA-PKCS1-v1_5":return eE(a),a.algorithm.name;case"Ed25519":return a.algorithm.name}throw new dn("unsupported CryptoKey algorithm name",{cause:a})}(c),c,dl(d)));return`${d}.${e}`}let dK=URL.parse?(a,b)=>URL.parse(a,b):(a,b)=>{try{return new URL(a,b)}catch{return null}};function dL(a,b){if(b&&"https:"!==a.protocol)throw dq("only requests to HTTPS are allowed",ew,a);if("https:"!==a.protocol&&"http:"!==a.protocol)throw dq("only HTTP and HTTPS requests are allowed",ex,a)}function dM(a,b,c,d){let e;if("string"!=typeof a||!(e=dK(a)))throw dq(`authorization server metadata does not contain a valid ${c?`"as.mtls_endpoint_aliases.${b}"`:`"as.${b}"`}`,void 0===a?eB:eC,{attribute:c?`mtls_endpoint_aliases.${b}`:b});return dL(e,d),e}function dN(a,b,c,d){return c&&a.mtls_endpoint_aliases&&b in a.mtls_endpoint_aliases?dM(a.mtls_endpoint_aliases[b],b,c,d):dM(a[b],b,c,d)}class dO extends Error{cause;code;error;status;error_description;response;constructor(a,b){super(a,b),this.name=this.constructor.name,this.code=eo,this.cause=b.cause,this.error=b.cause.error,this.status=b.response.status,this.error_description=b.cause.error_description,Object.defineProperty(this,"response",{enumerable:!1,value:b.response}),Error.captureStackTrace?.(this,this.constructor)}}class dP extends Error{cause;code;error;error_description;constructor(a,b){super(a,b),this.name=this.constructor.name,this.code=eq,this.cause=b.cause,this.error=b.cause.get("error"),this.error_description=b.cause.get("error_description")??void 0,Error.captureStackTrace?.(this,this.constructor)}}class dQ extends Error{cause;code;response;status;constructor(a,b){super(a,b),this.name=this.constructor.name,this.code=en,this.cause=b.cause,this.status=b.response.status,this.response=b.response,Object.defineProperty(this,"response",{enumerable:!1}),Error.captureStackTrace?.(this,this.constructor)}}let dR="[a-zA-Z0-9!#$%&\\'\\*\\+\\-\\.\\^_`\\|~]+",dS=RegExp("^[,\\s]*("+dR+")\\s(.*)"),dT=RegExp("^[,\\s]*("+dR+')\\s*=\\s*"((?:[^"\\\\]|\\\\.)*)"[,\\s]*(.*)'),dU=RegExp("^[,\\s]*"+("("+dR+")\\s*=\\s*(")+dR+")[,\\s]*(.*)"),dV=RegExp("^([a-zA-Z0-9\\-\\._\\~\\+\\/]+[=]{0,2})(?:$|[,\\s])(.*)");async function dW(a){if(a.status>399&&a.status<500){eD(a),dA(a);try{let b=await a.clone().json();if(dr(b)&&"string"==typeof b.error&&b.error.length)return b}catch{}}}async function dX(a,b,c){if(a.status!==b){let b;if(b=await dW(a))throw await a.body?.cancel(),new dO("server responded with an error in the response body",{cause:b,response:a});throw dq(`"response" is not a conform ${c} response (unexpected HTTP status code)`,ev,a)}}function dY(a){if(!ed.has(a))throw dc('"options.DPoP" is not a valid DPoPHandle',da)}async function dZ(a,b,c,d,e,f){if(dy(a,'"accessToken"'),!(c instanceof URL))throw dc('"url" must be an instance of URL',db);dL(c,f?.[dd]!==!0),d=ds(d),f?.DPoP&&(dY(f.DPoP),await f.DPoP.addProof(c,d,b.toUpperCase(),a)),d.set("authorization",`${d.has("dpop")?"DPoP":"Bearer"} ${a}`);let g=await (f?.[dg]||fetch)(c.href,{body:e,headers:Object.fromEntries(d.entries()),method:b,redirect:"manual",signal:dt(c,f?.signal)});return f?.DPoP?.cacheNonce(g),g}async function d$(a,b,c,d){dG(a),dH(b);let e=dN(a,"userinfo_endpoint",b.use_mtls_endpoint_aliases,d?.[dd]!==!0),f=ds(d?.headers);return b.userinfo_signed_response_alg?f.set("accept","application/jwt"):(f.set("accept","application/json"),f.append("accept","application/jwt")),dZ(c,"GET",e,f,null,{...d,[de]:dD(b)})}let d_=Symbol();function d0(a){return a.headers.get("content-type")?.split(";")[0]}async function d1(a,b,c,d,e){let f;if(dG(a),dH(b),!c9(d,Response))throw dc('"response" must be an instance of Response',db);if(d8(d),200!==d.status)throw dq('"response" is not a conform UserInfo Endpoint response (unexpected HTTP status code)',ev,d);if(eD(d),"application/jwt"===d0(d)){let{claims:c,jwt:g}=await eF(await d.text(),eG.bind(void 0,b.userinfo_signed_response_alg,a.userinfo_signing_alg_values_supported,void 0),dD(b),dE(b),e?.[di]).then(d9.bind(void 0,b.client_id)).then(eb.bind(void 0,a));d5.set(d,g),f=c}else{if(b.userinfo_signed_response_alg)throw dq("JWT UserInfo Response expected",er,d);f=await eK(d)}if(dy(f.sub,'"response" body "sub" property',et,{body:f}),c===d_);else if(dy(c,'"expectedSubject"'),f.sub!==c)throw dq('unexpected "response" body "sub" property value',eA,{expected:c,body:f,attribute:"sub"});return f}async function d2(a,b,c,d,e,f,g){return await c(a,b,e,f),f.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),(g?.[dg]||fetch)(d.href,{body:e,headers:Object.fromEntries(f.entries()),method:"POST",redirect:"manual",signal:dt(d,g?.signal)})}async function d3(a,b,c,d,e,f){let g=dN(a,"token_endpoint",b.use_mtls_endpoint_aliases,f?.[dd]!==!0);e.set("grant_type",d);let h=ds(f?.headers);h.set("accept","application/json"),f?.DPoP!==void 0&&(dY(f.DPoP),await f.DPoP.addProof(g,h,"POST"));let i=await d2(a,b,c,g,e,h,f);return f?.DPoP?.cacheNonce(i),i}let d4=new WeakMap,d5=new WeakMap;function d6(a){if(!a.id_token)return;let b=d4.get(a);if(!b)throw dc('"ref" was already garbage collected or did not resolve from the proper sources',da);return b}async function d7(a,b,c,d,e){if(dG(a),dH(b),!c9(c,Response))throw dc('"response" must be an instance of Response',db);d8(c),await dX(c,200,"Token Endpoint"),eD(c);let f=await eK(c);if(dy(f.access_token,'"response" body "access_token" property',et,{body:f}),dy(f.token_type,'"response" body "token_type" property',et,{body:f}),f.token_type=f.token_type.toLowerCase(),"dpop"!==f.token_type&&"bearer"!==f.token_type)throw new dn("unsupported `token_type` value",{cause:{body:f}});if(void 0!==f.expires_in){let a="number"!=typeof f.expires_in?parseFloat(f.expires_in):f.expires_in;dx(a,!0,'"response" body "expires_in" property',et,{body:f}),f.expires_in=a}if(void 0!==f.refresh_token&&dy(f.refresh_token,'"response" body "refresh_token" property',et,{body:f}),void 0!==f.scope&&"string"!=typeof f.scope)throw dq('"response" body "scope" property must be a string',et,{body:f});if(void 0!==f.id_token){dy(f.id_token,'"response" body "id_token" property',et,{body:f});let g=["aud","exp","iat","iss","sub"];!0===b.require_auth_time&&g.push("auth_time"),void 0!==b.default_max_age&&(dx(b.default_max_age,!0,'"client.default_max_age"'),g.push("auth_time")),d?.length&&g.push(...d);let{claims:h,jwt:i}=await eF(f.id_token,eG.bind(void 0,b.id_token_signed_response_alg,a.id_token_signing_alg_values_supported,"RS256"),dD(b),dE(b),e?.[di]).then(eh.bind(void 0,g)).then(ec.bind(void 0,a)).then(ea.bind(void 0,b.client_id));if(Array.isArray(h.aud)&&1!==h.aud.length){if(void 0===h.azp)throw dq('ID Token "aud" (audience) claim includes additional untrusted audiences',ez,{claims:h,claim:"aud"});if(h.azp!==b.client_id)throw dq('unexpected ID Token "azp" (authorized party) claim value',ez,{expected:b.client_id,claims:h,claim:"azp"})}void 0!==h.auth_time&&dx(h.auth_time,!1,'ID Token "auth_time" (authentication time)',et,{claims:h}),d5.set(c,i),d4.set(f,h)}return f}function d8(a){let b;if(b=function(a){if(!c9(a,Response))throw dc('"response" must be an instance of Response',db);let b=a.headers.get("www-authenticate");if(null===b)return;let c=[],d=b;for(;d;){let a,b=d.match(dS),e=b?.["1"].toLowerCase();if(d=b?.["2"],!e)return;let f={};for(;d;){let c,e;if(b=d.match(dT)){if([,c,e,d]=b,e.includes("\\"))try{e=JSON.parse(`"${e}"`)}catch{}f[c.toLowerCase()]=e;continue}if(b=d.match(dU)){[,c,e,d]=b,f[c.toLowerCase()]=e;continue}if(b=d.match(dV)){if(Object.keys(f).length)break;[,a,d]=b;break}return}let g={scheme:e,parameters:f};a&&(g.token68=a),c.push(g)}if(c.length)return c}(a))throw new dQ("server responded with a challenge in the WWW-Authenticate HTTP Header",{cause:b,response:a})}function d9(a,b){return void 0!==b.claims.aud?ea(a,b):b}function ea(a,b){if(Array.isArray(b.claims.aud)){if(!b.claims.aud.includes(a))throw dq('unexpected JWT "aud" (audience) claim value',ez,{expected:a,claims:b.claims,claim:"aud"})}else if(b.claims.aud!==a)throw dq('unexpected JWT "aud" (audience) claim value',ez,{expected:a,claims:b.claims,claim:"aud"});return b}function eb(a,b){return void 0!==b.claims.iss?ec(a,b):b}function ec(a,b){let c=a[eM]?.(b)??a.issuer;if(b.claims.iss!==c)throw dq('unexpected JWT "iss" (issuer) claim value',ez,{expected:c,claims:b.claims,claim:"iss"});return b}let ed=new WeakSet,ee=Symbol();async function ef(a,b,c,d,e,f,g){if(dG(a),dH(b),!ed.has(d))throw dc('"callbackParameters" must be an instance of URLSearchParams obtained from "validateAuthResponse()", or "validateJwtAuthResponse()',da);dy(e,'"redirectUri"');let h=eH(d,"code");if(!h)throw dq('no authorization code in "callbackParameters"',et);let i=new URLSearchParams(g?.additionalParameters);return i.set("redirect_uri",e),i.set("code",h),f!==ee&&(dy(f,'"codeVerifier"'),i.set("code_verifier",f)),d3(a,b,c,"authorization_code",i,g)}let eg={aud:"audience",c_hash:"code hash",client_id:"client id",exp:"expiration time",iat:"issued at",iss:"issuer",jti:"jwt id",nonce:"nonce",s_hash:"state hash",sub:"subject",ath:"access token hash",htm:"http method",htu:"http uri",cnf:"confirmation",auth_time:"authentication time"};function eh(a,b){for(let c of a)if(void 0===b.claims[c])throw dq(`JWT "${c}" (${eg[c]}) claim missing`,et,{claims:b.claims});return b}let ei=Symbol(),ej=Symbol();async function ek(a,b,c,d){return"string"==typeof d?.expectedNonce||"number"==typeof d?.maxAge||d?.requireIdToken?el(a,b,c,d.expectedNonce,d.maxAge,{[di]:d[di]}):em(a,b,c,d)}async function el(a,b,c,d,e,f){let g=[];switch(d){case void 0:d=ei;break;case ei:break;default:dy(d,'"expectedNonce" argument'),g.push("nonce")}switch(e??=b.default_max_age){case void 0:e=ej;break;case ej:break;default:dx(e,!0,'"maxAge" argument'),g.push("auth_time")}let h=await d7(a,b,c,g,f);dy(h.id_token,'"response" body "id_token" property',et,{body:h});let i=d6(h);if(e!==ej){let a=dF()+dD(b),c=dE(b);if(i.auth_time+e<a-c)throw dq("too much time has elapsed since the last End-User authentication",ey,{claims:i,now:a,tolerance:c,claim:"auth_time"})}if(d===ei){if(void 0!==i.nonce)throw dq('unexpected ID Token "nonce" claim value',ez,{expected:void 0,claims:i,claim:"nonce"})}else if(i.nonce!==d)throw dq('unexpected ID Token "nonce" claim value',ez,{expected:d,claims:i,claim:"nonce"});return h}async function em(a,b,c,d){let e=await d7(a,b,c,void 0,d),f=d6(e);if(f){if(void 0!==b.default_max_age){dx(b.default_max_age,!0,'"client.default_max_age"');let a=dF()+dD(b),c=dE(b);if(f.auth_time+b.default_max_age<a-c)throw dq("too much time has elapsed since the last End-User authentication",ey,{claims:f,now:a,tolerance:c,claim:"auth_time"})}if(void 0!==f.nonce)throw dq('unexpected ID Token "nonce" claim value',ez,{expected:void 0,claims:f,claim:"nonce"})}return e}let en="OAUTH_WWW_AUTHENTICATE_CHALLENGE",eo="OAUTH_RESPONSE_BODY_ERROR",ep="OAUTH_UNSUPPORTED_OPERATION",eq="OAUTH_AUTHORIZATION_RESPONSE_ERROR",er="OAUTH_JWT_USERINFO_EXPECTED",es="OAUTH_PARSE_ERROR",et="OAUTH_INVALID_RESPONSE",eu="OAUTH_RESPONSE_IS_NOT_JSON",ev="OAUTH_RESPONSE_IS_NOT_CONFORM",ew="OAUTH_HTTP_REQUEST_FORBIDDEN",ex="OAUTH_REQUEST_PROTOCOL_FORBIDDEN",ey="OAUTH_JWT_TIMESTAMP_CHECK_FAILED",ez="OAUTH_JWT_CLAIM_COMPARISON_FAILED",eA="OAUTH_JSON_ATTRIBUTE_COMPARISON_FAILED",eB="OAUTH_MISSING_SERVER_METADATA",eC="OAUTH_INVALID_SERVER_METADATA";function eD(a){if(a.bodyUsed)throw dc('"response" body has been used already',da)}function eE(a){let{algorithm:b}=a;if("number"!=typeof b.modulusLength||b.modulusLength<2048)throw new dn(`unsupported ${b.name} modulusLength`,{cause:a})}async function eF(a,b,c,d,e){let f,g,{0:h,1:i,length:j}=a.split(".");if(5===j)if(void 0!==e)a=await e(a),{0:h,1:i,length:j}=a.split(".");else throw new dn("JWE decryption is not configured",{cause:a});if(3!==j)throw dq("Invalid JWT",et,a);try{f=JSON.parse(dl(dm(h)))}catch(a){throw dq("failed to parse JWT Header body as base64url encoded JSON",es,a)}if(!dr(f))throw dq("JWT Header must be a top level object",et,a);if(b(f),void 0!==f.crit)throw new dn('no JWT "crit" header parameter extensions are supported',{cause:{header:f}});try{g=JSON.parse(dl(dm(i)))}catch(a){throw dq("failed to parse JWT Payload body as base64url encoded JSON",es,a)}if(!dr(g))throw dq("JWT Payload must be a top level object",et,a);let k=dF()+c;if(void 0!==g.exp){if("number"!=typeof g.exp)throw dq('unexpected JWT "exp" (expiration time) claim type',et,{claims:g});if(g.exp<=k-d)throw dq('unexpected JWT "exp" (expiration time) claim value, expiration is past current timestamp',ey,{claims:g,now:k,tolerance:d,claim:"exp"})}if(void 0!==g.iat&&"number"!=typeof g.iat)throw dq('unexpected JWT "iat" (issued at) claim type',et,{claims:g});if(void 0!==g.iss&&"string"!=typeof g.iss)throw dq('unexpected JWT "iss" (issuer) claim type',et,{claims:g});if(void 0!==g.nbf){if("number"!=typeof g.nbf)throw dq('unexpected JWT "nbf" (not before) claim type',et,{claims:g});if(g.nbf>k+d)throw dq('unexpected JWT "nbf" (not before) claim value',ey,{claims:g,now:k,tolerance:d,claim:"nbf"})}if(void 0!==g.aud&&"string"!=typeof g.aud&&!Array.isArray(g.aud))throw dq('unexpected JWT "aud" (audience) claim type',et,{claims:g});return{header:f,claims:g,jwt:a}}function eG(a,b,c,d){if(void 0!==a){if("string"==typeof a?d.alg!==a:!a.includes(d.alg))throw dq('unexpected JWT "alg" header parameter',et,{header:d,expected:a,reason:"client configuration"});return}if(Array.isArray(b)){if(!b.includes(d.alg))throw dq('unexpected JWT "alg" header parameter',et,{header:d,expected:b,reason:"authorization server metadata"});return}if(void 0!==c){if("string"==typeof c?d.alg!==c:"function"==typeof c?!c(d.alg):!c.includes(d.alg))throw dq('unexpected JWT "alg" header parameter',et,{header:d,expected:c,reason:"default value"});return}throw dq('missing client or server configuration to verify used JWT "alg" header parameter',void 0,{client:a,issuer:b,fallback:c})}function eH(a,b){let{0:c,length:d}=a.getAll(b);if(d>1)throw dq(`"${b}" parameter must be provided only once`,et);return c}let eI=Symbol(),eJ=Symbol();async function eK(a,b=dA){let c;try{c=await a.json()}catch(c){throw b(a),dq('failed to parse "response" body as JSON',es,c)}if(!dr(c))throw dq('"response" body must be a top level object',et,{body:c});return c}let eL=Symbol(),eM=Symbol();async function eN(a,b,c){let{cookies:d,logger:e}=c,f=d[a],g=new Date;g.setTime(g.getTime()+9e5),e.debug(`CREATE_${a.toUpperCase()}`,{name:f.name,payload:b,COOKIE_TTL:900,expires:g});let h=await bM({...c.jwt,maxAge:900,token:{value:b},salt:f.name}),i={...f.options,expires:g};return{name:f.name,value:h,options:i}}async function eO(a,b,c){try{let{logger:d,cookies:e,jwt:f}=c;if(d.debug(`PARSE_${a.toUpperCase()}`,{cookie:b}),!b)throw new w(`${a} cookie was missing`);let g=await bN({...f,token:b,salt:e[a].name});if(g?.value)return g.value;throw Error("Invalid cookie")}catch(b){throw new w(`${a} value could not be parsed`,{cause:b})}}function eP(a,b,c){let{logger:d,cookies:e}=b,f=e[a];d.debug(`CLEAR_${a.toUpperCase()}`,{cookie:f}),c.push({name:f.name,value:"",options:{...e[a].options,maxAge:0}})}function eQ(a,b){return async function(c,d,e){let{provider:f,logger:g}=e;if(!f?.checks?.includes(a))return;let h=c?.[e.cookies[b].name];g.debug(`USE_${b.toUpperCase()}`,{value:h});let i=await eO(b,h,e);return eP(b,e,d),i}}let eR={async create(a){let b=dB(),c=await dC(b);return{cookie:await eN("pkceCodeVerifier",b,a),value:c}},use:eQ("pkce","pkceCodeVerifier")},eS="encodedState",eT={async create(a,b){let{provider:c}=a;if(!c.checks.includes("state")){if(b)throw new w("State data was provided but the provider is not configured to use state");return}let d={origin:b,random:dB()},e=await bM({secret:a.jwt.secret,token:d,salt:eS,maxAge:900});return{cookie:await eN("state",e,a),value:e}},use:eQ("state","state"),async decode(a,b){try{b.logger.debug("DECODE_STATE",{state:a});let c=await bN({secret:b.jwt.secret,token:a,salt:eS});if(c)return c;throw Error("Invalid state")}catch(a){throw new w("State could not be decoded",{cause:a})}}},eU={async create(a){if(!a.provider.checks.includes("nonce"))return;let b=dB();return{cookie:await eN("nonce",b,a),value:b}},use:eQ("nonce","nonce")},eV="encodedWebauthnChallenge",eW={create:async(a,b,c)=>({cookie:await eN("webauthnChallenge",await bM({secret:a.jwt.secret,token:{challenge:b,registerData:c},salt:eV,maxAge:900}),a)}),async use(a,b,c){let d=b?.[a.cookies.webauthnChallenge.name],e=await eO("webauthnChallenge",d,a),f=await bN({secret:a.jwt.secret,token:e,salt:eV});if(eP("webauthnChallenge",a,c),!f)throw new w("WebAuthn challenge was missing");return f}};function eX(a){return encodeURIComponent(a).replace(/%20/g,"+")}async function eY(a,b,c){let d,e,f,{logger:g,provider:h}=c,{token:i,userinfo:j}=h;if(i?.url&&"authjs.dev"!==i.url.host||j?.url&&"authjs.dev"!==j.url.host)d={issuer:h.issuer??"https://authjs.dev",token_endpoint:i?.url.toString(),userinfo_endpoint:j?.url.toString()};else{let a=new URL(h.issuer),b=await dw(a,{[dd]:!0,[dg]:h[b4]});if(!(d=await dz(a,b)).token_endpoint)throw TypeError("TODO: Authorization server did not provide a token endpoint.");if(!d.userinfo_endpoint)throw TypeError("TODO: Authorization server did not provide a userinfo endpoint.")}let k={client_id:h.clientId,...h.client};switch(k.token_endpoint_auth_method){case void 0:case"client_secret_basic":e=(a,b,c,d)=>{d.set("authorization",function(a,b){let c=eX(a),d=eX(b),e=btoa(`${c}:${d}`);return`Basic ${e}`}(h.clientId,h.clientSecret))};break;case"client_secret_post":var l;dy(l=h.clientSecret,'"clientSecret"'),e=(a,b,c,d)=>{c.set("client_id",b.client_id),c.set("client_secret",l)};break;case"client_secret_jwt":e=function(a,b){let c;dy(a,'"clientSecret"');let d=void 0;return async(b,e,f,g)=>{c||=await crypto.subtle.importKey("raw",dl(a),{hash:"SHA-256",name:"HMAC"},!1,["sign"]);let h={alg:"HS256"},i=dI(b,e);d?.(h,i);let j=`${dm(dl(JSON.stringify(h)))}.${dm(dl(JSON.stringify(i)))}`,k=await crypto.subtle.sign(c.algorithm,c,dl(j));f.set("client_id",e.client_id),f.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),f.set("client_assertion",`${j}.${dm(new Uint8Array(k))}`)}}(h.clientSecret);break;case"private_key_jwt":e=function(a,b){let{key:c,kid:d}=a instanceof CryptoKey?{key:a}:a?.key instanceof CryptoKey?(void 0!==a.kid&&dy(a.kid,'"kid"'),{key:a.key,kid:a.kid}):{};var e='"clientPrivateKey.key"';if(!(c instanceof CryptoKey))throw dc(`${e} must be a CryptoKey`,db);if("private"!==c.type)throw dc(`${e} must be a private CryptoKey`,da);return async(a,e,f,g)=>{let h={alg:function(a){switch(a.algorithm.name){case"RSA-PSS":switch(a.algorithm.hash.name){case"SHA-256":return"PS256";case"SHA-384":return"PS384";case"SHA-512":return"PS512";default:throw new dn("unsupported RsaHashedKeyAlgorithm hash name",{cause:a})}case"RSASSA-PKCS1-v1_5":switch(a.algorithm.hash.name){case"SHA-256":return"RS256";case"SHA-384":return"RS384";case"SHA-512":return"RS512";default:throw new dn("unsupported RsaHashedKeyAlgorithm hash name",{cause:a})}case"ECDSA":switch(a.algorithm.namedCurve){case"P-256":return"ES256";case"P-384":return"ES384";case"P-521":return"ES512";default:throw new dn("unsupported EcKeyAlgorithm namedCurve",{cause:a})}case"Ed25519":case"EdDSA":return"Ed25519";default:throw new dn("unsupported CryptoKey algorithm name",{cause:a})}}(c),kid:d},i=dI(a,e);b?.[dh]?.(h,i),f.set("client_id",e.client_id),f.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),f.set("client_assertion",await dJ(h,i,c))}}(h.token.clientPrivateKey,{[dh](a,b){b.aud=[d.issuer,d.token_endpoint]}});break;case"none":e=(a,b,c,d)=>{c.set("client_id",b.client_id)};break;default:throw Error("unsupported client authentication method")}let m=[],n=await eT.use(b,m,c);try{f=function(a,b,c,d){var e;if(dG(a),dH(b),c instanceof URL&&(c=c.searchParams),!(c instanceof URLSearchParams))throw dc('"parameters" must be an instance of URLSearchParams, or URL',db);if(eH(c,"response"))throw dq('"parameters" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()',et,{parameters:c});let f=eH(c,"iss"),g=eH(c,"state");if(!f&&a.authorization_response_iss_parameter_supported)throw dq('response parameter "iss" (issuer) missing',et,{parameters:c});if(f&&f!==a.issuer)throw dq('unexpected "iss" (issuer) response parameter value',et,{expected:a.issuer,parameters:c});switch(d){case void 0:case eJ:if(void 0!==g)throw dq('unexpected "state" response parameter encountered',et,{expected:void 0,parameters:c});break;case eI:break;default:if(dy(d,'"expectedState" argument'),g!==d)throw dq(void 0===g?'response parameter "state" missing':'unexpected "state" response parameter value',et,{expected:d,parameters:c})}if(eH(c,"error"))throw new dP("authorization response from the server is an error",{cause:c});let h=eH(c,"id_token"),i=eH(c,"token");if(void 0!==h||void 0!==i)throw new dn("implicit and hybrid flows are not supported");return e=new URLSearchParams(c),ed.add(e),e}(d,k,new URLSearchParams(a),h.checks.includes("state")?n:eI)}catch(a){if(a instanceof dP){let b={providerId:h.id,...Object.fromEntries(a.cause.entries())};throw g.debug("OAuthCallbackError",b),new D("OAuth Provider returned an error",b)}throw a}let o=await eR.use(b,m,c),p=h.callbackUrl;!c.isOnRedirectProxy&&h.redirectProxyUrl&&(p=h.redirectProxyUrl);let q=await ef(d,k,e,f,p,o??"decoy",{[dd]:!0,[dg]:(...a)=>(h.checks.includes("pkce")||a[1].body.delete("code_verifier"),(h[b4]??fetch)(...a))});h.token?.conform&&(q=await h.token.conform(q.clone())??q);let r={},s="oidc"===h.type;if(h[b5])switch(h.id){case"microsoft-entra-id":case"azure-ad":{let{tid:a}=function(a){let b,c;if("string"!=typeof a)throw new az("JWTs must use Compact JWS serialization, JWT must be a string");let{1:d,length:e}=a.split(".");if(5===e)throw new az("Only JWTs using Compact JWS serialization can be decoded");if(3!==e)throw new az("Invalid JWT");if(!d)throw new az("JWTs must contain a payload");try{b=ar(d)}catch{throw new az("Failed to base64url decode the payload")}try{c=JSON.parse(aj.decode(b))}catch{throw new az("Failed to parse the decoded payload as JSON")}if(!aC(c))throw new az("Invalid JWT Claims Set");return c}((await q.clone().json()).id_token);if("string"==typeof a){let b=d.issuer?.match(/microsoftonline\.com\/(\w+)\/v2\.0/)?.[1]??"common",c=new URL(d.issuer.replace(b,a)),e=await dw(c,{[dg]:h[b4]});d=await dz(c,e)}}}let t=await ek(d,k,q,{expectedNonce:await eU.use(b,m,c),requireIdToken:s});if(s){let b=d6(t);if(r=b,h[b5]&&"apple"===h.id)try{r.user=JSON.parse(a?.user)}catch{}if(!1===h.idToken){let a=await d$(d,k,t.access_token,{[dg]:h[b4],[dd]:!0});r=await d1(d,k,b.sub,a)}}else if(j?.request){let a=await j.request({tokens:t,provider:h});a instanceof Object&&(r=a)}else if(j?.url){let a=await d$(d,k,t.access_token,{[dg]:h[b4]});r=await a.json()}else throw TypeError("No userinfo endpoint configured");return t.expires_in&&(t.expires_at=Math.floor(Date.now()/1e3)+Number(t.expires_in)),{...await eZ(r,h,t,g),profile:r,cookies:m}}async function eZ(a,b,c,d){try{let d=await b.profile(a,c);return{user:{...d,id:crypto.randomUUID(),email:d.email?.toLowerCase()},account:{...c,provider:b.id,type:b.type,providerAccountId:d.id??crypto.randomUUID()}}}catch(c){d.debug("getProfile error details",a),d.error(new E(c,{provider:b.id}))}}async function e$(a,b,c,d){let e=await e3(a,b,c),{cookie:f}=await eW.create(a,e.challenge,c);return{status:200,cookies:[...d??[],f],body:{action:"register",options:e},headers:{"Content-Type":"application/json"}}}async function e_(a,b,c,d){let e=await e2(a,b,c),{cookie:f}=await eW.create(a,e.challenge);return{status:200,cookies:[...d??[],f],body:{action:"authenticate",options:e},headers:{"Content-Type":"application/json"}}}async function e0(a,b,c){let d,{adapter:e,provider:f}=a,g=b.body&&"string"==typeof b.body.data?JSON.parse(b.body.data):void 0;if(!g||"object"!=typeof g||!("id"in g)||"string"!=typeof g.id)throw new m("Invalid WebAuthn Authentication response");let h=e6(e5(g.id)),i=await e.getAuthenticator(h);if(!i)throw new m(`WebAuthn authenticator not found in database: ${JSON.stringify({credentialID:h})}`);let{challenge:j}=await eW.use(a,b.cookies,c);try{var k;let c=f.getRelayingParty(a,b);d=await f.simpleWebAuthn.verifyAuthenticationResponse({...f.verifyAuthenticationOptions,expectedChallenge:j,response:g,authenticator:{...k=i,credentialDeviceType:k.credentialDeviceType,transports:e7(k.transports),credentialID:e5(k.credentialID),credentialPublicKey:e5(k.credentialPublicKey)},expectedOrigin:c.origin,expectedRPID:c.id})}catch(a){throw new S(a)}let{verified:l,authenticationInfo:n}=d;if(!l)throw new S("WebAuthn authentication response could not be verified");try{let{newCounter:a}=n;await e.updateAuthenticatorCounter(i.credentialID,a)}catch(a){throw new o(`Failed to update authenticator counter. This may cause future authentication attempts to fail. ${JSON.stringify({credentialID:h,oldCounter:i.counter,newCounter:n.newCounter})}`,a)}let p=await e.getAccount(i.providerAccountId,f.id);if(!p)throw new m(`WebAuthn account not found in database: ${JSON.stringify({credentialID:h,providerAccountId:i.providerAccountId})}`);let q=await e.getUser(p.userId);if(!q)throw new m(`WebAuthn user not found in database: ${JSON.stringify({credentialID:h,providerAccountId:i.providerAccountId,userID:p.userId})}`);return{account:p,user:q}}async function e1(a,b,c){var d;let e,{provider:f}=a,g=b.body&&"string"==typeof b.body.data?JSON.parse(b.body.data):void 0;if(!g||"object"!=typeof g||!("id"in g)||"string"!=typeof g.id)throw new m("Invalid WebAuthn Registration response");let{challenge:h,registerData:i}=await eW.use(a,b.cookies,c);if(!i)throw new m("Missing user registration data in WebAuthn challenge cookie");try{let c=f.getRelayingParty(a,b);e=await f.simpleWebAuthn.verifyRegistrationResponse({...f.verifyRegistrationOptions,expectedChallenge:h,response:g,expectedOrigin:c.origin,expectedRPID:c.id})}catch(a){throw new S(a)}if(!e.verified||!e.registrationInfo)throw new S("WebAuthn registration response could not be verified");let j={providerAccountId:e6(e.registrationInfo.credentialID),provider:a.provider.id,type:f.type},k={providerAccountId:j.providerAccountId,counter:e.registrationInfo.counter,credentialID:e6(e.registrationInfo.credentialID),credentialPublicKey:e6(e.registrationInfo.credentialPublicKey),credentialBackedUp:e.registrationInfo.credentialBackedUp,credentialDeviceType:e.registrationInfo.credentialDeviceType,transports:(d=g.response.transports,d?.join(","))};return{user:i,account:j,authenticator:k}}async function e2(a,b,c){let{provider:d,adapter:e}=a,f=c&&c.id?await e.listAuthenticatorsByUserId(c.id):null,g=d.getRelayingParty(a,b);return await d.simpleWebAuthn.generateAuthenticationOptions({...d.authenticationOptions,rpID:g.id,allowCredentials:f?.map(a=>({id:e5(a.credentialID),type:"public-key",transports:e7(a.transports)}))})}async function e3(a,b,c){let{provider:d,adapter:e}=a,f=c.id?await e.listAuthenticatorsByUserId(c.id):null,g=bZ(32),h=d.getRelayingParty(a,b);return await d.simpleWebAuthn.generateRegistrationOptions({...d.registrationOptions,userID:g,userName:c.email,userDisplayName:c.name??void 0,rpID:h.id,rpName:h.name,excludeCredentials:f?.map(a=>({id:e5(a.credentialID),type:"public-key",transports:e7(a.transports)}))})}function e4(a){let{provider:b,adapter:c}=a;if(!c)throw new y("An adapter is required for the WebAuthn provider");if(!b||"webauthn"!==b.type)throw new L("Provider must be WebAuthn");return{...a,provider:b,adapter:c}}function e5(a){return new Uint8Array(Buffer.from(a,"base64"))}function e6(a){return Buffer.from(a).toString("base64")}function e7(a){return a?a.split(","):void 0}async function e8(a,b,c,d){if(!b.provider)throw new L("Callback route called without provider");let{query:e,body:f,method:g,headers:h}=a,{provider:i,adapter:j,url:k,callbackUrl:l,pages:n,jwt:o,events:p,callbacks:r,session:{strategy:s,maxAge:t},logger:v}=b,w="jwt"===s;try{if("oauth"===i.type||"oidc"===i.type){let g,h=i.authorization?.url.searchParams.get("response_mode")==="form_post"?f:e;if(b.isOnRedirectProxy&&h?.state){let a=await eT.decode(h.state,b);if(a?.origin&&new URL(a.origin).origin!==b.url.origin){let b=`${a.origin}?${new URLSearchParams(h)}`;return v.debug("Proxy redirecting to",b),{redirect:b,cookies:d}}}let m=await eY(h,a.cookies,b);m.cookies.length&&d.push(...m.cookies),v.debug("authorization result",m);let{user:q,account:s,profile:u}=m;if(!q||!s||!u)return{redirect:`${k}/signin`,cookies:d};if(j){let{getUserByAccount:a}=j;g=await a({providerAccountId:s.providerAccountId,provider:i.id})}let x=await e9({user:g??q,account:s,profile:u},b);if(x)return{redirect:x,cookies:d};let{user:y,session:z,isNewUser:A}=await c8(c.value,q,s,b);if(w){let a={name:y.name,email:y.email,picture:y.image,sub:y.id?.toString()},e=await r.jwt({token:a,user:y,account:s,profile:u,isNewUser:A,trigger:A?"signUp":"signIn"});if(null===e)d.push(...c.clean());else{let a=b.cookies.sessionToken.name,f=await o.encode({...o,token:e,salt:a}),g=new Date;g.setTime(g.getTime()+1e3*t);let h=c.chunk(f,{expires:g});d.push(...h)}}else d.push({name:b.cookies.sessionToken.name,value:z.sessionToken,options:{...b.cookies.sessionToken.options,expires:z.expires}});if(await p.signIn?.({user:y,account:s,profile:u,isNewUser:A}),A&&n.newUser)return{redirect:`${n.newUser}${n.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:l})}`,cookies:d};return{redirect:l,cookies:d}}if("email"===i.type){let a=e?.token,f=e?.email;if(!a){let b=TypeError("Missing token. The sign-in URL was manually opened without token or the link was not sent correctly in the email.",{cause:{hasToken:!!a}});throw b.name="Configuration",b}let g=i.secret??b.secret,h=await j.useVerificationToken({identifier:f,token:await bY(`${a}${g}`)}),k=!!h,m=k&&h.expires.valueOf()<Date.now();if(!k||m||f&&h.identifier!==f)throw new N({hasInvite:k,expired:m});let{identifier:q}=h,s=await j.getUserByEmail(q)??{id:crypto.randomUUID(),email:q,emailVerified:null},u={providerAccountId:s.email,userId:s.id,type:"email",provider:i.id},v=await e9({user:s,account:u},b);if(v)return{redirect:v,cookies:d};let{user:x,session:y,isNewUser:z}=await c8(c.value,s,u,b);if(w){let a={name:x.name,email:x.email,picture:x.image,sub:x.id?.toString()},e=await r.jwt({token:a,user:x,account:u,isNewUser:z,trigger:z?"signUp":"signIn"});if(null===e)d.push(...c.clean());else{let a=b.cookies.sessionToken.name,f=await o.encode({...o,token:e,salt:a}),g=new Date;g.setTime(g.getTime()+1e3*t);let h=c.chunk(f,{expires:g});d.push(...h)}}else d.push({name:b.cookies.sessionToken.name,value:y.sessionToken,options:{...b.cookies.sessionToken.options,expires:y.expires}});if(await p.signIn?.({user:x,account:u,isNewUser:z}),z&&n.newUser)return{redirect:`${n.newUser}${n.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:l})}`,cookies:d};return{redirect:l,cookies:d}}if("credentials"===i.type&&"POST"===g){let a=f??{};Object.entries(e??{}).forEach(([a,b])=>k.searchParams.set(a,b));let j=await i.authorize(a,new Request(k,{headers:h,method:g,body:JSON.stringify(f)}));if(j)j.id=j.id?.toString()??crypto.randomUUID();else throw new u;let m={providerAccountId:j.id,type:"credentials",provider:i.id},n=await e9({user:j,account:m,credentials:a},b);if(n)return{redirect:n,cookies:d};let q={name:j.name,email:j.email,picture:j.image,sub:j.id},s=await r.jwt({token:q,user:j,account:m,isNewUser:!1,trigger:"signIn"});if(null===s)d.push(...c.clean());else{let a=b.cookies.sessionToken.name,e=await o.encode({...o,token:s,salt:a}),f=new Date;f.setTime(f.getTime()+1e3*t);let g=c.chunk(e,{expires:f});d.push(...g)}return await p.signIn?.({user:j,account:m}),{redirect:l,cookies:d}}else if("webauthn"===i.type&&"POST"===g){let e,f,g,h=a.body?.action;if("string"!=typeof h||"authenticate"!==h&&"register"!==h)throw new m("Invalid action parameter");let i=e4(b);switch(h){case"authenticate":{let b=await e0(i,a,d);e=b.user,f=b.account;break}case"register":{let c=await e1(b,a,d);e=c.user,f=c.account,g=c.authenticator}}await e9({user:e,account:f},b);let{user:j,isNewUser:k,session:q,account:s}=await c8(c.value,e,f,b);if(!s)throw new m("Error creating or finding account");if(g&&j.id&&await i.adapter.createAuthenticator({...g,userId:j.id}),w){let a={name:j.name,email:j.email,picture:j.image,sub:j.id?.toString()},e=await r.jwt({token:a,user:j,account:s,isNewUser:k,trigger:k?"signUp":"signIn"});if(null===e)d.push(...c.clean());else{let a=b.cookies.sessionToken.name,f=await o.encode({...o,token:e,salt:a}),g=new Date;g.setTime(g.getTime()+1e3*t);let h=c.chunk(f,{expires:g});d.push(...h)}}else d.push({name:b.cookies.sessionToken.name,value:q.sessionToken,options:{...b.cookies.sessionToken.options,expires:q.expires}});if(await p.signIn?.({user:j,account:s,isNewUser:k}),k&&n.newUser)return{redirect:`${n.newUser}${n.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:l})}`,cookies:d};return{redirect:l,cookies:d}}throw new L(`Callback for provider type (${i.type}) is not supported`)}catch(b){if(b instanceof m)throw b;let a=new q(b,{provider:i.id});throw v.debug("callback route error details",{method:g,query:e,body:f}),a}}async function e9(a,b){let c,{signIn:d,redirect:e}=b.callbacks;try{c=await d(a)}catch(a){if(a instanceof m)throw a;throw new p(a)}if(!c)throw new p("AccessDenied");if("string"==typeof c)return await e({url:c,baseUrl:b.url.origin})}async function fa(a,b,c,d,e){let{adapter:f,jwt:g,events:h,callbacks:i,logger:j,session:{strategy:k,maxAge:l}}=a,m={body:null,headers:{"Content-Type":"application/json"},cookies:c},n=b.value;if(!n)return m;if("jwt"===k){try{let c=a.cookies.sessionToken.name,f=await g.decode({...g,token:n,salt:c});if(!f)throw Error("Invalid JWT");let j=await i.jwt({token:f,...d&&{trigger:"update"},session:e}),k=c7(l);if(null!==j){let a={user:{name:j.name,email:j.email,image:j.picture},expires:k.toISOString()},d=await i.session({session:a,token:j});m.body=d;let e=await g.encode({...g,token:j,salt:c}),f=b.chunk(e,{expires:k});m.cookies?.push(...f),await h.session?.({session:d,token:j})}else m.cookies?.push(...b.clean())}catch(a){j.error(new x(a)),m.cookies?.push(...b.clean())}return m}try{let{getSessionAndUser:c,deleteSession:g,updateSession:j}=f,k=await c(n);if(k&&k.session.expires.valueOf()<Date.now()&&(await g(n),k=null),k){let{user:b,session:c}=k,f=a.session.updateAge,g=c.expires.valueOf()-1e3*l+1e3*f,o=c7(l);g<=Date.now()&&await j({sessionToken:n,expires:o});let p=await i.session({session:{...c,user:b},user:b,newSession:e,...d?{trigger:"update"}:{}});m.body=p,m.cookies?.push({name:a.cookies.sessionToken.name,value:n,options:{...a.cookies.sessionToken.options,expires:o}}),await h.session?.({session:p})}else n&&m.cookies?.push(...b.clean())}catch(a){j.error(new F(a))}return m}async function fb(a,b){let c,d,{logger:e,provider:f}=b,g=f.authorization?.url;if(!g||"authjs.dev"===g.host){let a=new URL(f.issuer),b=await dw(a,{[dg]:f[b4],[dd]:!0}),c=await dz(a,b);if(!c.authorization_endpoint)throw TypeError("Authorization server did not provide an authorization endpoint.");g=new URL(c.authorization_endpoint)}let h=g.searchParams,i=f.callbackUrl;!b.isOnRedirectProxy&&f.redirectProxyUrl&&(i=f.redirectProxyUrl,d=f.callbackUrl,e.debug("using redirect proxy",{redirect_uri:i,data:d}));let j=Object.assign({response_type:"code",client_id:f.clientId,redirect_uri:i,...f.authorization?.params},Object.fromEntries(f.authorization?.url.searchParams??[]),a);for(let a in j)h.set(a,j[a]);let k=[];f.authorization?.url.searchParams.get("response_mode")==="form_post"&&(b.cookies.state.options.sameSite="none",b.cookies.state.options.secure=!0,b.cookies.nonce.options.sameSite="none",b.cookies.nonce.options.secure=!0);let l=await eT.create(b,d);if(l&&(h.set("state",l.value),k.push(l.cookie)),f.checks?.includes("pkce"))if(c&&!c.code_challenge_methods_supported?.includes("S256"))"oidc"===f.type&&(f.checks=["nonce"]);else{let{value:a,cookie:c}=await eR.create(b);h.set("code_challenge",a),h.set("code_challenge_method","S256"),k.push(c)}let m=await eU.create(b);return m&&(h.set("nonce",m.value),k.push(m.cookie)),"oidc"!==f.type||g.searchParams.has("scope")||g.searchParams.set("scope","openid profile email"),e.debug("authorization url is ready",{url:g,cookies:k,provider:f}),{redirect:g.toString(),cookies:k}}async function fc(a,b){let c,{body:d}=a,{provider:e,callbacks:f,adapter:g}=b,h=(e.normalizeIdentifier??function(a){if(!a)throw Error("Missing email from request body.");let[b,c]=a.toLowerCase().trim().split("@");return c=c.split(",")[0],`${b}@${c}`})(d?.email),i={id:crypto.randomUUID(),email:h,emailVerified:null},j=await g.getUserByEmail(h)??i,k={providerAccountId:h,userId:j.id,type:"email",provider:e.id};try{c=await f.signIn({user:j,account:k,email:{verificationRequest:!0}})}catch(a){throw new p(a)}if(!c)throw new p("AccessDenied");if("string"==typeof c)return{redirect:await f.redirect({url:c,baseUrl:b.url.origin})};let{callbackUrl:l,theme:m}=b,n=await e.generateVerificationToken?.()??bZ(32),o=new Date(Date.now()+(e.maxAge??86400)*1e3),q=e.secret??b.secret,r=new URL(b.basePath,b.url.origin),s=e.sendVerificationRequest({identifier:h,token:n,expires:o,url:`${r}/callback/${e.id}?${new URLSearchParams({callbackUrl:l,token:n,email:h})}`,provider:e,theme:m,request:new Request(a.url,{headers:a.headers,method:a.method,body:"POST"===a.method?JSON.stringify(a.body??{}):void 0})}),t=g.createVerificationToken?.({identifier:h,token:await bY(`${n}${q}`),expires:o});return await Promise.all([s,t]),{redirect:`${r}/verify-request?${new URLSearchParams({provider:e.id,type:e.type})}`}}async function fd(a,b,c){let d=`${c.url.origin}${c.basePath}/signin`;if(!c.provider)return{redirect:d,cookies:b};switch(c.provider.type){case"oauth":case"oidc":{let{redirect:d,cookies:e}=await fb(a.query,c);return e&&b.push(...e),{redirect:d,cookies:b}}case"email":return{...await fc(a,c),cookies:b};default:return{redirect:d,cookies:b}}}async function fe(a,b,c){let{jwt:d,events:e,callbackUrl:f,logger:g,session:h}=c,i=b.value;if(!i)return{redirect:f,cookies:a};try{if("jwt"===h.strategy){let a=c.cookies.sessionToken.name,b=await d.decode({...d,token:i,salt:a});await e.signOut?.({token:b})}else{let a=await c.adapter?.deleteSession(i);await e.signOut?.({session:a})}}catch(a){g.error(new I(a))}return a.push(...b.clean()),{redirect:f,cookies:a}}async function ff(a,b){let{adapter:c,jwt:d,session:{strategy:e}}=a,f=b.value;if(!f)return null;if("jwt"===e){let b=a.cookies.sessionToken.name,c=await d.decode({...d,token:f,salt:b});if(c&&c.sub)return{id:c.sub,name:c.name,email:c.email,image:c.picture}}else{let a=await c?.getSessionAndUser(f);if(a)return a.user}return null}async function fg(a,b,c,d){let e=e4(b),{provider:f}=e,{action:g}=a.query??{};if("register"!==g&&"authenticate"!==g&&void 0!==g)return{status:400,body:{error:"Invalid action"},cookies:d,headers:{"Content-Type":"application/json"}};let h=await ff(b,c),i=h?{user:h,exists:!0}:await f.getUserInfo(b,a),j=i?.user;switch(function(a,b,c){let{user:d,exists:e=!1}=c??{};switch(a){case"authenticate":return"authenticate";case"register":if(d&&b===e)return"register";break;case void 0:if(!b)if(!d)return"authenticate";else if(e)return"authenticate";else return"register"}return null}(g,!!h,i)){case"authenticate":return e_(e,a,j,d);case"register":if("string"==typeof j?.email)return e$(e,a,j,d);break;default:return{status:400,body:{error:"Invalid request"},cookies:d,headers:{"Content-Type":"application/json"}}}}async function fh(a,b){let{action:c,providerId:d,error:e,method:f}=a,g=b.skipCSRFCheck===b2,{options:h,cookies:i}=await cb({authOptions:b,action:c,providerId:d,url:a.url,callbackUrl:a.body?.callbackUrl??a.query?.callbackUrl,csrfToken:a.body?.csrfToken,cookies:a.cookies,isPost:"POST"===f,csrfDisabled:g}),j=new l(h.cookies.sessionToken,a.cookies,h.logger);if("GET"===f){let b=c6({...h,query:a.query,cookies:i});switch(c){case"callback":return await e8(a,h,j,i);case"csrf":return b.csrf(g,h,i);case"error":return b.error(e);case"providers":return b.providers(h.providers);case"session":return await fa(h,j,i);case"signin":return b.signin(d,e);case"signout":return b.signout();case"verify-request":return b.verifyRequest();case"webauthn-options":return await fg(a,h,j,i)}}else{let{csrfTokenVerified:b}=h;switch(c){case"callback":return"credentials"===h.provider.type&&b_(c,b),await e8(a,h,j,i);case"session":return b_(c,b),await fa(h,j,i,!0,a.body?.data);case"signin":return b_(c,b),await fd(a,i,h);case"signout":return b_(c,b),await fe(i,j,h)}}throw new J(`Cannot handle action: ${c}`)}function fi(a,b,c,d,e){let f,g=e?.basePath,h=d.AUTH_URL??d.NEXTAUTH_URL;if(h)f=new URL(h),g&&"/"!==g&&"/"!==f.pathname&&(f.pathname!==g&&bT(e).warn("env-url-basepath-mismatch"),f.pathname="/");else{let a=c.get("x-forwarded-host")??c.get("host"),d=c.get("x-forwarded-proto")??b??"https",e=d.endsWith(":")?d:d+":";f=new URL(`${e}//${a}`)}let i=f.toString().replace(/\/$/,"");if(g){let b=g?.replace(/(^\/|\/$)/g,"")??"";return new URL(`${i}/${b}/${a}`)}return new URL(`${i}/${a}`)}async function fj(a,b){let c=bT(b),d=await bW(a,b);if(!d)return Response.json("Bad request.",{status:400});let e=function(a,b){let{url:c}=a,d=[];if(!V&&b.debug&&d.push("debug-enabled"),!b.trustHost)return new M(`Host must be trusted. URL was: ${a.url}`);if(!b.secret?.length)return new B("Please define a `secret`");let e=a.query?.callbackUrl;if(e&&!W(e,c.origin))return new t(`Invalid callback URL. Received: ${e}`);let{callbackUrl:f}=k(b.useSecureCookies??"https:"===c.protocol),g=a.cookies?.[b.cookies?.callbackUrl?.name??f.name];if(g&&!W(g,c.origin))return new t(`Invalid callback URL. Received: ${g}`);let h=!1;for(let a of b.providers){let b="function"==typeof a?a():a;if(("oauth"===b.type||"oidc"===b.type)&&!(b.issuer??b.options?.issuer)){let a,{authorization:c,token:d,userinfo:e}=b;if("string"==typeof c||c?.url?"string"==typeof d||d?.url?"string"==typeof e||e?.url||(a="userinfo"):a="token":a="authorization",a)return new v(`Provider "${b.id}" is missing both \`issuer\` and \`${a}\` endpoint config. At least one of them is required`)}if("credentials"===b.type)X=!0;else if("email"===b.type)Y=!0;else if("webauthn"===b.type){var i;if(Z=!0,b.simpleWebAuthnBrowserVersion&&(i=b.simpleWebAuthnBrowserVersion,!/^v\d+(?:\.\d+){0,2}$/.test(i)))return new m(`Invalid provider config for "${b.id}": simpleWebAuthnBrowserVersion "${b.simpleWebAuthnBrowserVersion}" must be a valid semver string.`);if(b.enableConditionalUI){if(h)return new Q("Multiple webauthn providers have 'enableConditionalUI' set to True. Only one provider can have this option enabled at a time");if(h=!0,!Object.values(b.formFields).some(a=>a.autocomplete&&a.autocomplete.toString().indexOf("webauthn")>-1))return new R(`Provider "${b.id}" has 'enableConditionalUI' set to True, but none of its formFields have 'webauthn' in their autocomplete param`)}}}if(X){let a=b.session?.strategy==="database",c=!b.providers.some(a=>"credentials"!==("function"==typeof a?a():a).type);if(a&&c)return new K("Signing in with credentials only supported if JWT strategy is enabled");if(b.providers.some(a=>{let b="function"==typeof a?a():a;return"credentials"===b.type&&!b.authorize}))return new A("Must define an authorize() handler to use credentials authentication provider")}let{adapter:j,session:l}=b,n=[];if(Y||l?.strategy==="database"||!l?.strategy&&j)if(Y){if(!j)return new y("Email login requires an adapter");n.push(...$)}else{if(!j)return new y("Database session requires an adapter");n.push(..._)}if(Z){if(!b.experimental?.enableWebAuthn)return new U("WebAuthn is an experimental feature. To enable it, set `experimental.enableWebAuthn` to `true` in your config");if(d.push("experimental-webauthn"),!j)return new y("WebAuthn requires an adapter");n.push(...aa)}if(j){let a=n.filter(a=>!(a in j));if(a.length)return new z(`Required adapter methods were missing: ${a.join(", ")}`)}return V||(V=!0),d}(d,b);if(Array.isArray(e))e.forEach(c.warn);else if(e){if(c.error(e),!new Set(["signin","signout","error","verify-request"]).has(d.action)||"GET"!==d.method)return Response.json({message:"There was a problem with the server configuration. Check the server logs for more information."},{status:500});let{pages:a,theme:f}=b,g=a?.error&&d.url.searchParams.get("callbackUrl")?.startsWith(a.error);if(!a?.error||g)return g&&c.error(new r(`The error page ${a?.error} should not require authentication`)),bX(c6({theme:f}).error("Configuration"));let h=`${d.url.origin}${a.error}?error=Configuration`;return Response.redirect(h)}let f=a.headers?.has("X-Auth-Return-Redirect"),g=b.raw===b3;try{let a=await fh(d,b);if(g)return a;let c=bX(a),e=c.headers.get("Location");if(!f||!e)return c;return Response.json({url:e},{headers:c.headers})}catch(l){c.error(l);let e=l instanceof m;if(e&&g&&!f)throw l;if("POST"===a.method&&"session"===d.action)return Response.json(null,{status:400});let h=new URLSearchParams({error:l instanceof m&&P.has(l.type)?l.type:"Configuration"});l instanceof u&&h.set("code",l.code);let i=e&&l.kind||"error",j=b.pages?.[i]??`${b.basePath}/${i.toLowerCase()}`,k=`${d.url.origin}${j}?${h}`;if(f)return Response.json({url:k});return Response.redirect(k)}}var fk=c(4525);function fl(a){let b=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!b)return a;let{origin:c}=new URL(b),{href:d,origin:e}=a.nextUrl;return new fk.NextRequest(d.replace(e,c),a)}function fm(a){try{a.secret??(a.secret=process.env.AUTH_SECRET??process.env.NEXTAUTH_SECRET);let b=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!b)return;let{pathname:c}=new URL(b);if("/"===c)return;a.basePath||(a.basePath=c)}catch{}finally{a.basePath||(a.basePath="/api/auth"),function(a,b,c=!1){try{let d=a.AUTH_URL;d&&(b.basePath?c||bT(b).warn("env-url-basepath-redundant"):b.basePath=new URL(d).pathname)}catch{}finally{b.basePath??(b.basePath="/auth")}if(!b.secret?.length){b.secret=[];let c=a.AUTH_SECRET;for(let d of(c&&b.secret.push(c),[1,2,3])){let c=a[`AUTH_SECRET_${d}`];c&&b.secret.unshift(c)}}b.redirectProxyUrl??(b.redirectProxyUrl=a.AUTH_REDIRECT_PROXY_URL),b.trustHost??(b.trustHost=!!(a.AUTH_URL??a.AUTH_TRUST_HOST??a.VERCEL??a.CF_PAGES??"production"!==a.NODE_ENV)),b.providers=b.providers.map(b=>{let{id:c}="function"==typeof b?b({}):b,d=c.toUpperCase().replace(/-/g,"_"),e=a[`AUTH_${d}_ID`],f=a[`AUTH_${d}_SECRET`],g=a[`AUTH_${d}_ISSUER`],h=a[`AUTH_${d}_KEY`],i="function"==typeof b?b({clientId:e,clientSecret:f,issuer:g,apiKey:h}):b;return"oauth"===i.type||"oidc"===i.type?(i.clientId??(i.clientId=e),i.clientSecret??(i.clientSecret=f),i.issuer??(i.issuer=g)):"email"===i.type&&(i.apiKey??(i.apiKey=h)),i})}(process.env,a,!0)}}var fn=c(4999);async function fo(a,b){return fj(new Request(fi("session",a.get("x-forwarded-proto"),a,process.env,b),{headers:{cookie:a.get("cookie")??""}}),{...b,callbacks:{...b.callbacks,async session(...a){let c=await b.callbacks?.session?.(...a)??{...a[0].session,expires:a[0].session.expires?.toISOString?.()??a[0].session.expires};return{user:a[0].user??a[0].token,...c}}}})}function fp(a){return"function"==typeof a}function fq(a,b){return"function"==typeof a?async(...c)=>{if(!c.length){let c=await (0,fn.b3)(),d=await a(void 0);return b?.(d),fo(c,d).then(a=>a.json())}if(c[0]instanceof Request){let d=c[0],e=c[1],f=await a(d);return b?.(f),fr([d,e],f)}if(fp(c[0])){let d=c[0];return async(...c)=>{let e=await a(c[0]);return b?.(e),fr(c,e,d)}}let d="req"in c[0]?c[0].req:c[0],e="res"in c[0]?c[0].res:c[1],f=await a(d);return b?.(f),fo(new Headers(d.headers),f).then(async a=>{let b=await a.json();for(let b of a.headers.getSetCookie())"headers"in e?e.headers.append("set-cookie",b):e.appendHeader("set-cookie",b);return b})}:(...b)=>{if(!b.length)return Promise.resolve((0,fn.b3)()).then(b=>fo(b,a).then(a=>a.json()));if(b[0]instanceof Request)return fr([b[0],b[1]],a);if(fp(b[0])){let c=b[0];return async(...b)=>fr(b,a,c).then(a=>a)}let c="req"in b[0]?b[0].req:b[0],d="res"in b[0]?b[0].res:b[1];return fo(new Headers(c.headers),a).then(async a=>{let b=await a.json();for(let b of a.headers.getSetCookie())"headers"in d?d.headers.append("set-cookie",b):d.appendHeader("set-cookie",b);return b})}}async function fr(a,b,c){let d=fl(a[0]),e=await fo(d.headers,b),f=await e.json(),g=!0;b.callbacks?.authorized&&(g=await b.callbacks.authorized({request:d,auth:f}));let h=fk.NextResponse.next?.();if(g instanceof Response){h=g;let a=g.headers.get("Location"),{pathname:c}=d.nextUrl;a&&function(a,b,c){let d=b.replace(`${a}/`,""),e=Object.values(c.pages??{});return(fs.has(d)||e.includes(b))&&b===a}(c,new URL(a).pathname,b)&&(g=!0)}else if(c)d.auth=f,h=await c(d,a[1])??fk.NextResponse.next();else if(!g){let a=b.pages?.signIn??`${b.basePath}/signin`;if(d.nextUrl.pathname!==a){let b=d.nextUrl.clone();b.pathname=a,b.searchParams.set("callbackUrl",d.nextUrl.href),h=fk.NextResponse.redirect(b)}}let i=new Response(h?.body,h);for(let a of e.headers.getSetCookie())i.headers.append("set-cookie",a);return i}let fs=new Set(["providers","session","csrf","signin","signout","callback","verify-request","error"]);var ft=c(7576);async function fu(a,b={},c,d){let e=new Headers(await (0,fn.b3)()),{redirect:f=!0,redirectTo:g,...h}=b instanceof FormData?Object.fromEntries(b):b,i=g?.toString()??e.get("Referer")??"/",j=fi("signin",e.get("x-forwarded-proto"),e,process.env,d);if(!a)return j.searchParams.append("callbackUrl",i),f&&(0,ft.redirect)(j.toString()),j.toString();let k=`${j}/${a}?${new URLSearchParams(c)}`,l={};for(let b of d.providers){let{options:c,...d}="function"==typeof b?b():b,e=c?.id??d.id;if(e===a){l={id:e,type:c?.type??d.type};break}}if(!l.id){let a=`${j}?${new URLSearchParams({callbackUrl:i})}`;return f&&(0,ft.redirect)(a),a}"credentials"===l.type&&(k=k.replace("signin","callback")),e.set("Content-Type","application/x-www-form-urlencoded");let m=new Request(k,{method:"POST",headers:e,body:new URLSearchParams({...h,callbackUrl:i})}),n=await fj(m,{...d,raw:b3,skipCSRFCheck:b2}),o=await (0,fn.UL)();for(let a of n?.cookies??[])o.set(a.name,a.value,a.options);let p=(n instanceof Response?n.headers.get("Location"):n.redirect)??k;return f?(0,ft.redirect)(p):p}async function fv(a,b){let c=new Headers(await (0,fn.b3)());c.set("Content-Type","application/x-www-form-urlencoded");let d=fi("signout",c.get("x-forwarded-proto"),c,process.env,b),e=new URLSearchParams({callbackUrl:a?.redirectTo??c.get("Referer")??"/"}),f=new Request(d,{method:"POST",headers:c,body:e}),g=await fj(f,{...b,raw:b3,skipCSRFCheck:b2}),h=await (0,fn.UL)();for(let a of g?.cookies??[])h.set(a.name,a.value,a.options);return a?.redirect??!0?(0,ft.redirect)(g.redirect):g}async function fw(a,b){let c=new Headers(await (0,fn.b3)());c.set("Content-Type","application/json");let d=new Request(fi("session",c.get("x-forwarded-proto"),c,process.env,b),{method:"POST",headers:c,body:JSON.stringify({data:a})}),e=await fj(d,{...b,raw:b3,skipCSRFCheck:b2}),f=await (0,fn.UL)();for(let a of e?.cookies??[])f.set(a.name,a.value,a.options);return e.body}function fx(a){if("function"==typeof a){let b=async b=>{let c=await a(b);return fm(c),fj(fl(b),c)};return{handlers:{GET:b,POST:b},auth:fq(a,a=>fm(a)),signIn:async(b,c,d)=>{let e=await a(void 0);return fm(e),fu(b,c,d,e)},signOut:async b=>{let c=await a(void 0);return fm(c),fv(b,c)},unstable_update:async b=>{let c=await a(void 0);return fm(c),fw(b,c)}}}fm(a);let b=b=>fj(fl(b),a);return{handlers:{GET:b,POST:b},auth:fq(a),signIn:(b,c,d)=>fu(b,c,d,a),signOut:b=>fv(b,a),unstable_update:b=>fw(b,a)}}},3913:(a,b,c)=>{"use strict";let d=c(3033),e=c(9294),f=c(4971),g=c(6926),h=c(23),i=c(8479),j=c(1617);c(3763);new WeakMap;(0,g.createDedupedByCallsiteServerErrorLoggerDev)(function(a,b){let c=a?`Route "${a}" `:"This route ";return Object.defineProperty(Error(`${c}used ${b}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})})},4069:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{MutableRequestCookiesAdapter:function(){return m},ReadonlyRequestCookiesError:function(){return h},RequestCookiesAdapter:function(){return i},appendMutableCookies:function(){return l},areCookiesMutableInCurrentPhase:function(){return o},getModifiedCookieValues:function(){return k},responseCookiesToRequestCookies:function(){return q},wrapWithMutableAccessCheck:function(){return n}});let d=c(3158),e=c(3763),f=c(9294),g=c(3033);class h extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new h}}class i{static seal(a){return new Proxy(a,{get(a,b,c){switch(b){case"clear":case"delete":case"set":return h.callable;default:return e.ReflectAdapter.get(a,b,c)}}})}}let j=Symbol.for("next.mutated.cookies");function k(a){let b=a[j];return b&&Array.isArray(b)&&0!==b.length?b:[]}function l(a,b){let c=k(b);if(0===c.length)return!1;let e=new d.ResponseCookies(a),f=e.getAll();for(let a of c)e.set(a);for(let a of f)e.set(a);return!0}class m{static wrap(a,b){let c=new d.ResponseCookies(new Headers);for(let b of a.getAll())c.set(b);let g=[],h=new Set,i=()=>{let a=f.workAsyncStorage.getStore();if(a&&(a.pathWasRevalidated=!0),g=c.getAll().filter(a=>h.has(a.name)),b){let a=[];for(let b of g){let c=new d.ResponseCookies(new Headers);c.set(b),a.push(c.toString())}b(a)}},k=new Proxy(c,{get(a,b,c){switch(b){case j:return g;case"delete":return function(...b){h.add("string"==typeof b[0]?b[0]:b[0].name);try{return a.delete(...b),k}finally{i()}};case"set":return function(...b){h.add("string"==typeof b[0]?b[0]:b[0].name);try{return a.set(...b),k}finally{i()}};default:return e.ReflectAdapter.get(a,b,c)}}});return k}}function n(a){let b=new Proxy(a,{get(a,c,d){switch(c){case"delete":return function(...c){return p("cookies().delete"),a.delete(...c),b};case"set":return function(...c){return p("cookies().set"),a.set(...c),b};default:return e.ReflectAdapter.get(a,c,d)}}});return b}function o(a){return"action"===a.phase}function p(a){if(!o((0,g.getExpectedRequestStore)(a)))throw new h}function q(a){let b=new d.RequestCookies(new Headers);for(let c of a.getAll())b.set(c);return b}},4525:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{ImageResponse:function(){return d.ImageResponse},NextRequest:function(){return e.NextRequest},NextResponse:function(){return f.NextResponse},URLPattern:function(){return h.URLPattern},after:function(){return i.after},connection:function(){return j.connection},unstable_rootParams:function(){return k.unstable_rootParams},userAgent:function(){return g.userAgent},userAgentFromString:function(){return g.userAgentFromString}});let d=c(2174),e=c(6268),f=c(3426),g=c(3182),h=c(1243),i=c(3381),j=c(2944),k=c(2079)},4871:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"after",{enumerable:!0,get:function(){return e}});let d=c(9294);function e(a){let b=d.workAsyncStorage.getStore();if(!b)throw Object.defineProperty(Error("`after` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context"),"__NEXT_ERROR_CODE",{value:"E468",enumerable:!1,configurable:!0});let{afterContext:c}=b;return c.after(a)}},4999:(a,b,c)=>{"use strict";c.d(b,{UL:()=>d.U,b3:()=>e.b});var d=c(9933),e=c(6280);c(3913)},5282:(a,b,c)=>{"use strict";c.d(b,{G:()=>f,eq:()=>e});var d=c(8269);let e=d.ZS.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class f extends Error{get errors(){return this.issues}constructor(a){super(),this.issues=[],this.addIssue=a=>{this.issues=[...this.issues,a]},this.addIssues=(a=[])=>{this.issues=[...this.issues,...a]};let b=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,b):this.__proto__=b,this.name="ZodError",this.issues=a}format(a){let b=a||function(a){return a.message},c={_errors:[]},d=a=>{for(let e of a.issues)if("invalid_union"===e.code)e.unionErrors.map(d);else if("invalid_return_type"===e.code)d(e.returnTypeError);else if("invalid_arguments"===e.code)d(e.argumentsError);else if(0===e.path.length)c._errors.push(b(e));else{let a=c,d=0;for(;d<e.path.length;){let c=e.path[d];d===e.path.length-1?(a[c]=a[c]||{_errors:[]},a[c]._errors.push(b(e))):a[c]=a[c]||{_errors:[]},a=a[c],d++}}};return d(this),c}static assert(a){if(!(a instanceof f))throw Error(`Not a ZodError: ${a}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,d.ZS.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(a=a=>a.message){let b={},c=[];for(let d of this.issues)if(d.path.length>0){let c=d.path[0];b[c]=b[c]||[],b[c].push(a(d))}else c.push(a(d));return{formErrors:c,fieldErrors:b}}get formErrors(){return this.flatten()}}f.create=a=>new f(a)},5538:(a,b)=>{"use strict";b.q=function(a,b){if("string"!=typeof a)throw TypeError("argument str must be a string");var c={},d=a.length;if(d<2)return c;var e=b&&b.decode||j,f=0,g=0,k=0;do{if(-1===(g=a.indexOf("=",f)))break;if(-1===(k=a.indexOf(";",f)))k=d;else if(g>k){f=a.lastIndexOf(";",g-1)+1;continue}var l=h(a,f,g),m=i(a,g,l),n=a.slice(l,m);if(!c.hasOwnProperty(n)){var o=h(a,g+1,k),p=i(a,k,o);34===a.charCodeAt(o)&&34===a.charCodeAt(p-1)&&(o++,p--);var q=a.slice(o,p);c[n]=function(a,b){try{return b(a)}catch(b){return a}}(q,e)}f=k+1}while(f<d);return c},b.l=function(a,b,h){var i=h&&h.encode||encodeURIComponent;if("function"!=typeof i)throw TypeError("option encode is invalid");if(!d.test(a))throw TypeError("argument name is invalid");var j=i(b);if(!e.test(j))throw TypeError("argument val is invalid");var k=a+"="+j;if(!h)return k;if(null!=h.maxAge){var l=Math.floor(h.maxAge);if(!isFinite(l))throw TypeError("option maxAge is invalid");k+="; Max-Age="+l}if(h.domain){if(!f.test(h.domain))throw TypeError("option domain is invalid");k+="; Domain="+h.domain}if(h.path){if(!g.test(h.path))throw TypeError("option path is invalid");k+="; Path="+h.path}if(h.expires){var m,n=h.expires;if(m=n,"[object Date]"!==c.call(m)||isNaN(n.valueOf()))throw TypeError("option expires is invalid");k+="; Expires="+n.toUTCString()}if(h.httpOnly&&(k+="; HttpOnly"),h.secure&&(k+="; Secure"),h.partitioned&&(k+="; Partitioned"),h.priority)switch("string"==typeof h.priority?h.priority.toLowerCase():h.priority){case"low":k+="; Priority=Low";break;case"medium":k+="; Priority=Medium";break;case"high":k+="; Priority=High";break;default:throw TypeError("option priority is invalid")}if(h.sameSite)switch("string"==typeof h.sameSite?h.sameSite.toLowerCase():h.sameSite){case!0:case"strict":k+="; SameSite=Strict";break;case"lax":k+="; SameSite=Lax";break;case"none":k+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return k};var c=Object.prototype.toString,d=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,e=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,f=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,g=/^[\u0020-\u003A\u003D-\u007E]*$/;function h(a,b,c){do{var d=a.charCodeAt(b);if(32!==d&&9!==d)return b}while(++b<c);return c}function i(a,b,c){for(;b>c;){var d=a.charCodeAt(--b);if(32!==d&&9!==d)return b+1}return c}function j(a){return -1!==a.indexOf("%")?decodeURIComponent(a):a}},6280:(a,b,c)=>{"use strict";Object.defineProperty(b,"b",{enumerable:!0,get:function(){return m}});let d=c(2584),e=c(9294),f=c(3033),g=c(4971),h=c(23),i=c(8388),j=c(6926);c(4523);let k=c(8719),l=c(1617);function m(){let a=e.workAsyncStorage.getStore(),b=f.workUnitAsyncStorage.getStore();if(a){if(b&&"after"===b.phase&&!(0,k.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${a.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(a.forceStatic)return o(d.HeadersAdapter.seal(new Headers({})));if(b){if("cache"===b.type)throw Object.defineProperty(Error(`Route ${a.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});else if("unstable-cache"===b.type)throw Object.defineProperty(Error(`Route ${a.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(a.dynamicShouldError)throw Object.defineProperty(new h.StaticGenBailoutError(`Route ${a.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(b)switch(b.type){case"prerender":var c=b;let e=n.get(c);if(e)return e;let f=(0,i.makeHangingPromise)(c.renderSignal,"`headers()`");return n.set(c,f),f;case"prerender-client":let j="`headers`";throw Object.defineProperty(new l.InvariantError(`${j} must not be used within a client component. Next.js should be preventing ${j} from being included in client components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E693",enumerable:!1,configurable:!0});case"prerender-ppr":(0,g.postponeWithTracking)(a.route,"headers",b.dynamicTracking);break;case"prerender-legacy":(0,g.throwToInterruptStaticGeneration)("headers",a,b)}(0,g.trackDynamicDataInDynamicRender)(a,b)}return o((0,f.getExpectedRequestStore)("headers").headers)}c(3763);let n=new WeakMap;function o(a){let b=n.get(a);if(b)return b;let c=Promise.resolve(a);return n.set(a,c),Object.defineProperties(c,{append:{value:a.append.bind(a)},delete:{value:a.delete.bind(a)},get:{value:a.get.bind(a)},has:{value:a.has.bind(a)},set:{value:a.set.bind(a)},getSetCookie:{value:a.getSetCookie.bind(a)},forEach:{value:a.forEach.bind(a)},keys:{value:a.keys.bind(a)},values:{value:a.values.bind(a)},entries:{value:a.entries.bind(a)},[Symbol.iterator]:{value:a[Symbol.iterator].bind(a)}}),c}(0,j.createDedupedByCallsiteServerErrorLoggerDev)(function(a,b){let c=a?`Route "${a}" `:"This route ";return Object.defineProperty(Error(`${c}used ${b}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})})},6467:(a,b,c)=>{"use strict";function d(a){return{createUser:({id:b,...c})=>a.user.create(e(c)),getUser:b=>a.user.findUnique({where:{id:b}}),getUserByEmail:b=>a.user.findUnique({where:{email:b}}),async getUserByAccount(b){let c=await a.account.findUnique({where:{provider_providerAccountId:b},include:{user:!0}});return c?.user??null},updateUser:({id:b,...c})=>a.user.update({where:{id:b},...e(c)}),deleteUser:b=>a.user.delete({where:{id:b}}),linkAccount:b=>a.account.create({data:b}),unlinkAccount:b=>a.account.delete({where:{provider_providerAccountId:b}}),async getSessionAndUser(b){let c=await a.session.findUnique({where:{sessionToken:b},include:{user:!0}});if(!c)return null;let{user:d,...e}=c;return{user:d,session:e}},createSession:b=>a.session.create(e(b)),updateSession:b=>a.session.update({where:{sessionToken:b.sessionToken},...e(b)}),deleteSession:b=>a.session.delete({where:{sessionToken:b}}),async createVerificationToken(b){let c=await a.verificationToken.create(e(b));return"id"in c&&c.id&&delete c.id,c},async useVerificationToken(b){try{let c=await a.verificationToken.delete({where:{identifier_token:b}});return"id"in c&&c.id&&delete c.id,c}catch(a){if(a&&"object"==typeof a&&"code"in a&&"P2025"===a.code)return null;throw a}},getAccount:async(b,c)=>a.account.findFirst({where:{providerAccountId:b,provider:c}}),createAuthenticator:async b=>a.authenticator.create(e(b)),getAuthenticator:async b=>a.authenticator.findUnique({where:{credentialID:b}}),listAuthenticatorsByUserId:async b=>a.authenticator.findMany({where:{userId:b}}),updateAuthenticatorCounter:async(b,c)=>a.authenticator.update({where:{credentialID:b},data:{counter:c}})}}function e(a){let b={};for(let c in a)void 0!==a[c]&&(b[c]=a[c]);return{data:b}}c.d(b,{y:()=>d})},6897:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{getRedirectError:function(){return g},getRedirectStatusCodeFromError:function(){return l},getRedirectTypeFromError:function(){return k},getURLFromRedirectError:function(){return j},permanentRedirect:function(){return i},redirect:function(){return h}});let d=c(2836),e=c(9026),f=c(9121).actionAsyncStorage;function g(a,b,c){void 0===c&&(c=d.RedirectStatusCode.TemporaryRedirect);let f=Object.defineProperty(Error(e.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return f.digest=e.REDIRECT_ERROR_CODE+";"+b+";"+a+";"+c+";",f}function h(a,b){var c;throw null!=b||(b=(null==f||null==(c=f.getStore())?void 0:c.isAction)?e.RedirectType.push:e.RedirectType.replace),g(a,b,d.RedirectStatusCode.TemporaryRedirect)}function i(a,b){throw void 0===b&&(b=e.RedirectType.replace),g(a,b,d.RedirectStatusCode.PermanentRedirect)}function j(a){return(0,e.isRedirectError)(a)?a.digest.split(";").slice(2,-2).join(";"):null}function k(a){if(!(0,e.isRedirectError)(a))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return a.digest.split(";",2)[1]}function l(a){if(!(0,e.isRedirectError)(a))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(a.digest.split(";").at(-2))}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},7576:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{ReadonlyURLSearchParams:function(){return k},RedirectType:function(){return e.RedirectType},forbidden:function(){return g.forbidden},notFound:function(){return f.notFound},permanentRedirect:function(){return d.permanentRedirect},redirect:function(){return d.redirect},unauthorized:function(){return h.unauthorized},unstable_rethrow:function(){return i.unstable_rethrow}});let d=c(6897),e=c(9026),f=c(2765),g=c(8976),h=c(899),i=c(163);class j extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class k extends URLSearchParams{append(){throw new j}delete(){throw new j}set(){throw new j}sort(){throw new j}}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},8269:(a,b,c)=>{"use strict";var d,e;c.d(b,{CR:()=>g,ZS:()=>d,Zp:()=>f}),function(a){a.assertEqual=a=>{},a.assertIs=function(a){},a.assertNever=function(a){throw Error()},a.arrayToEnum=a=>{let b={};for(let c of a)b[c]=c;return b},a.getValidEnumValues=b=>{let c=a.objectKeys(b).filter(a=>"number"!=typeof b[b[a]]),d={};for(let a of c)d[a]=b[a];return a.objectValues(d)},a.objectValues=b=>a.objectKeys(b).map(function(a){return b[a]}),a.objectKeys="function"==typeof Object.keys?a=>Object.keys(a):a=>{let b=[];for(let c in a)Object.prototype.hasOwnProperty.call(a,c)&&b.push(c);return b},a.find=(a,b)=>{for(let c of a)if(b(c))return c},a.isInteger="function"==typeof Number.isInteger?a=>Number.isInteger(a):a=>"number"==typeof a&&Number.isFinite(a)&&Math.floor(a)===a,a.joinValues=function(a,b=" | "){return a.map(a=>"string"==typeof a?`'${a}'`:a).join(b)},a.jsonStringifyReplacer=(a,b)=>"bigint"==typeof b?b.toString():b}(d||(d={})),(e||(e={})).mergeShapes=(a,b)=>({...a,...b});let f=d.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),g=a=>{switch(typeof a){case"undefined":return f.undefined;case"string":return f.string;case"number":return Number.isNaN(a)?f.nan:f.number;case"boolean":return f.boolean;case"function":return f.function;case"bigint":return f.bigint;case"symbol":return f.symbol;case"object":if(Array.isArray(a))return f.array;if(null===a)return f.null;if(a.then&&"function"==typeof a.then&&a.catch&&"function"==typeof a.catch)return f.promise;if("undefined"!=typeof Map&&a instanceof Map)return f.map;if("undefined"!=typeof Set&&a instanceof Set)return f.set;if("undefined"!=typeof Date&&a instanceof Date)return f.date;return f.object;default:return f.unknown}}},8976:(a,b,c)=>{"use strict";function d(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"forbidden",{enumerable:!0,get:function(){return d}}),c(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},9933:(a,b,c)=>{"use strict";Object.defineProperty(b,"U",{enumerable:!0,get:function(){return n}});let d=c(4069),e=c(3158),f=c(9294),g=c(3033),h=c(4971),i=c(23),j=c(8388),k=c(6926);c(4523);let l=c(8719),m=c(1617);function n(){let a="cookies",b=f.workAsyncStorage.getStore(),c=g.workUnitAsyncStorage.getStore();if(b){if(c&&"after"===c.phase&&!(0,l.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${b.route} used "cookies" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "cookies" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E88",enumerable:!1,configurable:!0});if(b.forceStatic)return p(d.RequestCookiesAdapter.seal(new e.RequestCookies(new Headers({}))));if(c){if("cache"===c.type)throw Object.defineProperty(Error(`Route ${b.route} used "cookies" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E398",enumerable:!1,configurable:!0});else if("unstable-cache"===c.type)throw Object.defineProperty(Error(`Route ${b.route} used "cookies" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E157",enumerable:!1,configurable:!0})}if(b.dynamicShouldError)throw Object.defineProperty(new i.StaticGenBailoutError(`Route ${b.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`cookies\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E549",enumerable:!1,configurable:!0});if(c)switch(c.type){case"prerender":var k=c;let f=o.get(k);if(f)return f;let g=(0,j.makeHangingPromise)(k.renderSignal,"`cookies()`");return o.set(k,g),g;case"prerender-client":let n="`cookies`";throw Object.defineProperty(new m.InvariantError(`${n} must not be used within a client component. Next.js should be preventing ${n} from being included in client components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E693",enumerable:!1,configurable:!0});case"prerender-ppr":(0,h.postponeWithTracking)(b.route,a,c.dynamicTracking);break;case"prerender-legacy":(0,h.throwToInterruptStaticGeneration)(a,b,c)}(0,h.trackDynamicDataInDynamicRender)(b,c)}let n=(0,g.getExpectedRequestStore)(a);return p((0,d.areCookiesMutableInCurrentPhase)(n)?n.userspaceMutableCookies:n.cookies)}c(3763);let o=new WeakMap;function p(a){let b=o.get(a);if(b)return b;let c=Promise.resolve(a);return o.set(a,c),Object.defineProperties(c,{[Symbol.iterator]:{value:a[Symbol.iterator]?a[Symbol.iterator].bind(a):q.bind(a)},size:{get:()=>a.size},get:{value:a.get.bind(a)},getAll:{value:a.getAll.bind(a)},has:{value:a.has.bind(a)},set:{value:a.set.bind(a)},delete:{value:a.delete.bind(a)},clear:{value:"function"==typeof a.clear?a.clear.bind(a):r.bind(a,c)},toString:{value:a.toString.bind(a)}}),c}function q(){return this.getAll().map(a=>[a.name,a]).values()}function r(a){for(let a of this.getAll())this.delete(a.name);return a}(0,k.createDedupedByCallsiteServerErrorLoggerDev)(function(a,b){let c=a?`Route "${a}" `:"This route ";return Object.defineProperty(Error(`${c}used ${b}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})})}};