import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'

// 定义类型
export interface Category {
  id: string
  name: string
  description?: string
  color: string
  icon?: string
  createdAt: Date
  updatedAt: Date
  createdById: string
  _count?: {
    prompts: number
  }
}

export interface Prompt {
  id: string
  title: string
  content: string
  description?: string
  tags: string[]
  usageCount: number
  isPublic: boolean
  createdAt: Date
  updatedAt: Date
  categoryId?: string
  createdById: string
  category?: {
    id: string
    name: string
    color: string
    icon?: string
  }
  createdBy?: {
    id: string
    name?: string
    image?: string
  }
}

// UI状态接口
interface UIState {
  // 侧边栏状态
  sidebarOpen: boolean
  setSidebarOpen: (open: boolean) => void
  
  // 搜索状态
  searchQuery: string
  setSearchQuery: (query: string) => void
  
  // 当前选中的分类
  selectedCategoryId?: string
  setSelectedCategoryId: (categoryId?: string) => void
  
  // 视图模式
  viewMode: 'grid' | 'list'
  setViewMode: (mode: 'grid' | 'list') => void
  
  // 排序方式
  sortBy: 'createdAt' | 'updatedAt' | 'usageCount' | 'title'
  setSortBy: (sortBy: 'createdAt' | 'updatedAt' | 'usageCount' | 'title') => void
  
  sortOrder: 'asc' | 'desc'
  setSortOrder: (order: 'asc' | 'desc') => void
  
  // 模态框状态
  modals: {
    createPrompt: boolean
    editPrompt: boolean
    createCategory: boolean
    editCategory: boolean
    promptDetail: boolean
  }
  setModal: (modal: keyof UIState['modals'], open: boolean) => void
  
  // 当前编辑的项目
  currentEditingPrompt?: Prompt
  setCurrentEditingPrompt: (prompt?: Prompt) => void
  
  currentEditingCategory?: Category
  setCurrentEditingCategory: (category?: Category) => void
  
  currentViewingPrompt?: Prompt
  setCurrentViewingPrompt: (prompt?: Prompt) => void
}

// 搜索历史状态接口
interface SearchHistoryState {
  searchHistory: string[]
  addSearchHistory: (query: string) => void
  clearSearchHistory: () => void
  removeSearchHistory: (query: string) => void
}

// 用户偏好设置接口
interface UserPreferencesState {
  // 主题设置
  theme: 'light' | 'dark' | 'system'
  setTheme: (theme: 'light' | 'dark' | 'system') => void
  
  // 语言设置
  language: 'zh-CN' | 'en-US'
  setLanguage: (language: 'zh-CN' | 'en-US') => void
  
  // 每页显示数量
  pageSize: number
  setPageSize: (size: number) => void
  
  // 是否显示描述
  showDescription: boolean
  setShowDescription: (show: boolean) => void
  
  // 是否显示标签
  showTags: boolean
  setShowTags: (show: boolean) => void
  
  // 是否显示使用次数
  showUsageCount: boolean
  setShowUsageCount: (show: boolean) => void
}

// 创建UI状态store
export const useUIStore = create<UIState>()(
  devtools(
    persist(
      (set) => ({
        sidebarOpen: true,
        setSidebarOpen: (open) => set({ sidebarOpen: open }),
        
        searchQuery: '',
        setSearchQuery: (query) => set({ searchQuery: query }),
        
        selectedCategoryId: undefined,
        setSelectedCategoryId: (categoryId) => set({ selectedCategoryId: categoryId }),
        
        viewMode: 'grid',
        setViewMode: (mode) => set({ viewMode: mode }),
        
        sortBy: 'createdAt',
        setSortBy: (sortBy) => set({ sortBy }),
        
        sortOrder: 'desc',
        setSortOrder: (order) => set({ sortOrder: order }),
        
        modals: {
          createPrompt: false,
          editPrompt: false,
          createCategory: false,
          editCategory: false,
          promptDetail: false,
        },
        setModal: (modal, open) => 
          set((state) => ({
            modals: { ...state.modals, [modal]: open }
          })),
        
        currentEditingPrompt: undefined,
        setCurrentEditingPrompt: (prompt) => set({ currentEditingPrompt: prompt }),
        
        currentEditingCategory: undefined,
        setCurrentEditingCategory: (category) => set({ currentEditingCategory: category }),
        
        currentViewingPrompt: undefined,
        setCurrentViewingPrompt: (prompt) => set({ currentViewingPrompt: prompt }),
      }),
      {
        name: 'ui-store',
        partialize: (state) => ({
          sidebarOpen: state.sidebarOpen,
          viewMode: state.viewMode,
          sortBy: state.sortBy,
          sortOrder: state.sortOrder,
        }),
      }
    ),
    { name: 'ui-store' }
  )
)

// 创建搜索历史store
export const useSearchHistoryStore = create<SearchHistoryState>()(
  devtools(
    persist(
      (set, get) => ({
        searchHistory: [],
        addSearchHistory: (query) => {
          if (!query.trim()) return
          
          const { searchHistory } = get()
          const newHistory = [query, ...searchHistory.filter(h => h !== query)].slice(0, 10)
          set({ searchHistory: newHistory })
        },
        clearSearchHistory: () => set({ searchHistory: [] }),
        removeSearchHistory: (query) => 
          set((state) => ({
            searchHistory: state.searchHistory.filter(h => h !== query)
          })),
      }),
      {
        name: 'search-history-store',
      }
    ),
    { name: 'search-history-store' }
  )
)

// 创建用户偏好设置store
export const useUserPreferencesStore = create<UserPreferencesState>()(
  devtools(
    persist(
      (set) => ({
        theme: 'system',
        setTheme: (theme) => set({ theme }),
        
        language: 'zh-CN',
        setLanguage: (language) => set({ language }),
        
        pageSize: 20,
        setPageSize: (size) => set({ pageSize: size }),
        
        showDescription: true,
        setShowDescription: (show) => set({ showDescription: show }),
        
        showTags: true,
        setShowTags: (show) => set({ showTags: show }),
        
        showUsageCount: true,
        setShowUsageCount: (show) => set({ showUsageCount: show }),
      }),
      {
        name: 'user-preferences-store',
      }
    ),
    { name: 'user-preferences-store' }
  )
)
