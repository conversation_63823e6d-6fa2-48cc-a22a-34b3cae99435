import { z } from "zod";
import { createTRPCRouter, protectedProcedure, publicProcedure } from "~/server/api/trpc";

export const categoryRouter = createTRPCRouter({
  // 获取所有分类
  getAll: publicProcedure.query(async ({ ctx }) => {
    return ctx.db.category.findMany({
      orderBy: { createdAt: "desc" },
      include: {
        _count: {
          select: { prompts: true },
        },
      },
    });
  }),

  // 根据ID获取分类
  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      return ctx.db.category.findUnique({
        where: { id: input.id },
        include: {
          prompts: {
            orderBy: { createdAt: "desc" },
            take: 10, // 只返回最新的10个提示词
          },
          _count: {
            select: { prompts: true },
          },
        },
      });
    }),

  // 创建分类
  create: protectedProcedure
    .input(
      z.object({
        name: z.string().min(1, "分类名称不能为空").max(50, "分类名称不能超过50个字符"),
        description: z.string().optional(),
        color: z.string().regex(/^#[0-9A-F]{6}$/i, "颜色格式不正确").default("#3B82F6"),
        icon: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.db.category.create({
        data: {
          ...input,
          createdById: ctx.session.user.id,
        },
      });
    }),

  // 更新分类
  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string().min(1, "分类名称不能为空").max(50, "分类名称不能超过50个字符").optional(),
        description: z.string().optional(),
        color: z.string().regex(/^#[0-9A-F]{6}$/i, "颜色格式不正确").optional(),
        icon: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { id, ...updateData } = input;
      
      // 检查分类是否存在且属于当前用户
      const category = await ctx.db.category.findUnique({
        where: { id },
      });

      if (!category) {
        throw new Error("分类不存在");
      }

      if (category.createdById !== ctx.session.user.id) {
        throw new Error("无权限修改此分类");
      }

      return ctx.db.category.update({
        where: { id },
        data: updateData,
      });
    }),

  // 删除分类
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // 检查分类是否存在且属于当前用户
      const category = await ctx.db.category.findUnique({
        where: { id: input.id },
        include: {
          _count: {
            select: { prompts: true },
          },
        },
      });

      if (!category) {
        throw new Error("分类不存在");
      }

      if (category.createdById !== ctx.session.user.id) {
        throw new Error("无权限删除此分类");
      }

      if (category._count.prompts > 0) {
        throw new Error("该分类下还有提示词，无法删除");
      }

      return ctx.db.category.delete({
        where: { id: input.id },
      });
    }),

  // 获取分类统计信息
  getStats: publicProcedure.query(async ({ ctx }) => {
    const stats = await ctx.db.category.findMany({
      select: {
        id: true,
        name: true,
        color: true,
        _count: {
          select: { prompts: true },
        },
      },
      orderBy: {
        prompts: {
          _count: "desc",
        },
      },
    });

    return stats;
  }),
});
