{"name": "unist-util-visit", "version": "5.0.0", "description": "unist utility to visit nodes", "license": "MIT", "keywords": ["unist", "unist-util", "util", "utility", "remark", "retext", "rehype", "mdast", "hast", "xast", "nlcst", "natural", "language", "markdown", "html", "xml", "tree", "ast", "node", "visit", "walk"], "repository": "syntax-tree/unist-util-visit", "bugs": "https://github.com/syntax-tree/unist-util-visit/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "sideEffects": false, "type": "module", "exports": "./index.js", "files": ["lib/", "index.d.ts", "index.js"], "dependencies": {"@types/unist": "^3.0.0", "unist-util-is": "^6.0.0", "unist-util-visit-parents": "^6.0.0"}, "devDependencies": {"@types/mdast": "^4.0.0", "@types/node": "^20.0.0", "c8": "^8.0.0", "mdast-util-from-markdown": "^1.0.0", "mdast-util-gfm": "^2.0.0", "micromark-extension-gfm": "^2.0.0", "prettier": "^2.0.0", "remark-cli": "^11.0.0", "remark-preset-wooorm": "^9.0.0", "tsd": "^0.28.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "xo": "^0.54.0"}, "scripts": {"prepack": "npm run build && npm run format", "build": "tsc --build --clean && tsc --build && tsd && type-coverage", "format": "remark . -qfo && prettier . -w --loglevel warn && xo --fix", "test-api": "node --conditions development test.js", "test-coverage": "c8 --100 --reporter lcov npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "prettier": {"bracketSpacing": false, "semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm"]}, "typeCoverage": {"atLeast": 100, "detail": true, "#": "needed `any`s", "ignoreFiles": ["lib/index.d.ts"], "ignoreCatch": true, "strict": true}, "xo": {"overrides": [{"files": ["**/*.ts"], "rules": {"import/no-extraneous-dependencies": "off"}}], "prettier": true}}