"use strict";exports.id=880,exports.ids=[880],exports.modules={457:(a,b,c)=>{c.d(b,{EN:()=>f,Iw:()=>u,OA:()=>v,OX:()=>t,bJ:()=>m,gt:()=>j,qO:()=>i,ri:()=>w,t9:()=>o,u$:()=>l,vJ:()=>y});var d=c(893),e=c(8761);let f=({shape:a})=>a;var g=(0,d.f1)((0,d.Vh)(),1),h=class extends Error{};function i(a){if(a instanceof j||a instanceof Error&&"TRPCError"===a.name)return a;let b=new j({code:"INTERNAL_SERVER_ERROR",cause:a});return a instanceof Error&&a.stack&&(b.stack=a.stack),b}var j=class extends Error{constructor(a){var b,c;let d=function(a){if(a instanceof Error)return a;let b=typeof a;if("undefined"!==b&&"function"!==b&&null!==a){if("object"!==b)return Error(String(a));if((0,e.Gv)(a))return Object.assign(new h,a)}}(a.cause);super(null!=(b=null!=(c=a.message)?c:null==d?void 0:d.message)?b:a.code,{cause:d}),(0,g.default)(this,"cause",void 0),(0,g.default)(this,"code",void 0),this.code=a.code,this.name="TRPCError",null!=this.cause||(this.cause=d)}},k=(0,d.f1)((0,d.jr)(),1);function l(a){return"input"in a?a:{input:a,output:a}}let m={input:{serialize:a=>a,deserialize:a=>a},output:{serialize:a=>a,deserialize:a=>a}};function n(a,b){return"error"in b?(0,k.default)((0,k.default)({},b),{},{error:a.transformer.output.serialize(b.error)}):"data"in b.result?(0,k.default)((0,k.default)({},b),{},{result:(0,k.default)((0,k.default)({},b.result),{},{data:a.transformer.output.serialize(b.result.data)})}):b}function o(a,b){return Array.isArray(b)?b.map(b=>n(a,b)):n(a,b)}var p=(0,d.f1)((0,d.jr)(),1);let q=Symbol("lazy"),r={_ctx:null,_errorShape:null,_meta:null,queries:{},mutations:{},subscriptions:{},errorFormatter:f,transformer:m},s=["then","call","apply"];function t(a){return function(b){let c=new Set(Object.keys(b).filter(a=>s.includes(a)));if(c.size>0)throw Error("Reserved words used in `router({})` call: "+Array.from(c).join(", "));let d=(0,e.QQ)({}),f=(0,e.QQ)({}),g=function a(b,c=[]){let g=(0,e.QQ)({});for(let[h,i]of Object.entries(null!=b?b:{})){if("function"==typeof i&&q in i){f[[...c,h].join(".")]=function b(c){return{ref:c.ref,load:function(a){let b=Symbol(),c=b;return()=>(c===b&&(c=a()),c)}(async()=>{let d=await c.ref(),e=[...c.path,c.key],g=e.join(".");for(let[h,i]of(c.aggregate[c.key]=a(d._def.record,e),delete f[g],Object.entries(d._def.lazy)))f[[...e,h].join(".")]=b({ref:i.ref,path:e,key:h,aggregate:c.aggregate[c.key]})})}}({path:c,ref:i,key:h,aggregate:g});continue}if((0,e.Gv)(i)&&(0,e.Gv)(i._def)&&"router"in i._def){g[h]=a(i._def.record,[...c,h]);continue}if("function"!=typeof i){g[h]=a(i,[...c,h]);continue}let b=[...c,h].join(".");if(d[b])throw Error(`Duplicate key: ${b}`);d[b]=i,g[h]=i}return g}(b),h=(0,p.default)((0,p.default)({_config:a,router:!0,procedures:d,lazy:f},r),{},{record:g});return(0,p.default)((0,p.default)({},g),{},{_def:h,createCaller:v()({_def:h})})}}async function u(a,b){let{_def:c}=a,d=c.procedures[b];for(;!d;){let a=Object.keys(c.lazy).find(a=>b.startsWith(a));if(!a)return null;let e=c.lazy[a];await e.load(),d=c.procedures[b]}return d}function v(){return function(a){let{_def:b}=a;return function(c,f){return(0,d.vX)(async({path:d,args:g})=>{let h,k=d.join(".");if(1===d.length&&"_def"===d[0])return b;let l=await u(a,k);try{if(!l)throw new j({code:"NOT_FOUND",message:`No procedure found on path "${d}"`});return h=(0,e.Tn)(c)?await Promise.resolve(c()):c,await l({path:k,getRawInput:async()=>g[0],ctx:h,type:l._def.type,signal:null==f?void 0:f.signal})}catch(a){var m,n;throw null==f||null==(m=f.onError)||m.call(f,{ctx:h,error:i(a),input:g[0],path:k,type:null!=(n=null==l?void 0:l._def.type)?n:"unknown"}),a}})}}}function w(...a){var b;let c=(0,e.uf)({},...a.map(a=>a._def.record));return t({errorFormatter:a.reduce((a,b)=>{if(b._def._config.errorFormatter&&b._def._config.errorFormatter!==f){if(a!==f&&a!==b._def._config.errorFormatter)throw Error("You seem to have several error formatters");return b._def._config.errorFormatter}return a},f),transformer:a.reduce((a,b)=>{if(b._def._config.transformer&&b._def._config.transformer!==m){if(a!==m&&a!==b._def._config.transformer)throw Error("You seem to have several transformers");return b._def._config.transformer}return a},m),isDev:a.every(a=>a._def._config.isDev),allowOutsideOfServer:a.every(a=>a._def._config.allowOutsideOfServer),isServer:a.every(a=>a._def._config.isServer),$types:null==(b=a[0])?void 0:b._def._config.$types})(c)}let x=Symbol();function y(a){return Array.isArray(a)&&a[2]===x}},893:(a,b,c)=>{c.d(b,{E$:()=>r,KQ:()=>y,P$:()=>k,Vh:()=>v,f1:()=>l,jr:()=>w,vX:()=>o});var d=c(8761),e=Object.create,f=Object.defineProperty,g=Object.getOwnPropertyDescriptor,h=Object.getOwnPropertyNames,i=Object.getPrototypeOf,j=Object.prototype.hasOwnProperty,k=(a,b)=>function(){return b||(0,a[h(a)[0]])((b={exports:{}}).exports,b),b.exports},l=(a,b,c)=>(c=null!=a?e(i(a)):{},((a,b,c,d)=>{if(b&&"object"==typeof b||"function"==typeof b)for(var e,i=h(b),k=0,l=i.length;k<l;k++)e=i[k],j.call(a,e)||e===c||f(a,e,{get:(a=>b[a]).bind(null,e),enumerable:!(d=g(b,e))||d.enumerable});return a})(!b&&a&&a.__esModule?c:f(c,"default",{value:a,enumerable:!0}),a));let m=()=>{},n=a=>{Object.freeze&&Object.freeze(a)},o=a=>(function a(b,c,d){let e=c.join(".");return null!=d[e]||(d[e]=new Proxy(m,{get(e,f){if("string"==typeof f&&"then"!==f)return a(b,[...c,f],d)},apply(a,d,e){let f=c[c.length-1],g={args:e,path:c};return"call"===f?g={args:e.length>=2?[e[1]]:[],path:c.slice(0,-1)}:"apply"===f&&(g={args:e.length>=2?e[1]:[],path:c.slice(0,-1)}),n(g.args),n(g.path),b(g)}})),d[e]})(a,[],Object.create(null)),p={PARSE_ERROR:400,BAD_REQUEST:400,UNAUTHORIZED:401,PAYMENT_REQUIRED:402,FORBIDDEN:403,NOT_FOUND:404,METHOD_NOT_SUPPORTED:405,TIMEOUT:408,CONFLICT:409,PRECONDITION_FAILED:412,PAYLOAD_TOO_LARGE:413,UNSUPPORTED_MEDIA_TYPE:415,UNPROCESSABLE_CONTENT:422,TOO_MANY_REQUESTS:429,CLIENT_CLOSED_REQUEST:499,INTERNAL_SERVER_ERROR:500,NOT_IMPLEMENTED:501,BAD_GATEWAY:502,SERVICE_UNAVAILABLE:503,GATEWAY_TIMEOUT:504};function q(a){var b;return null!=(b=p[a])?b:500}function r(a){let b=new Set((Array.isArray(a)?a:[a]).map(a=>{if("error"in a&&(0,d.Gv)(a.error.data)){var b;return"number"==typeof(null==(b=a.error.data)?void 0:b.httpStatus)?a.error.data.httpStatus:q(d.uB[a.error.code])}return 200}));return 1!==b.size?207:b.values().next().value}var s=k({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/typeof.js"(a,b){function c(a){return b.exports=c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a},b.exports.__esModule=!0,b.exports.default=b.exports,c(a)}b.exports=c,b.exports.__esModule=!0,b.exports.default=b.exports}}),t=k({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPrimitive.js"(a,b){var c=s().default;b.exports=function(a,b){if("object"!=c(a)||!a)return a;var d=a[Symbol.toPrimitive];if(void 0!==d){var e=d.call(a,b||"default");if("object"!=c(e))return e;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)},b.exports.__esModule=!0,b.exports.default=b.exports}}),u=k({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPropertyKey.js"(a,b){var c=s().default,d=t();b.exports=function(a){var b=d(a,"string");return"symbol"==c(b)?b:b+""},b.exports.__esModule=!0,b.exports.default=b.exports}}),v=k({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/defineProperty.js"(a,b){var c=u();b.exports=function(a,b,d){return(b=c(b))in a?Object.defineProperty(a,b,{value:d,enumerable:!0,configurable:!0,writable:!0}):a[b]=d,a},b.exports.__esModule=!0,b.exports.default=b.exports}}),w=k({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectSpread2.js"(a,b){var c=v();function d(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}b.exports=function(a){for(var b=1;b<arguments.length;b++){var e=null!=arguments[b]?arguments[b]:{};b%2?d(Object(e),!0).forEach(function(b){c(a,b,e[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(e)):d(Object(e)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(e,b))})}return a},b.exports.__esModule=!0,b.exports.default=b.exports}}),x=l(w(),1);function y(a){let{path:b,error:c,config:e}=a,{code:f}=a.error,g={message:c.message,code:d.Yk[f],data:{code:f,httpStatus:q(c.code)}};return e.isDev&&"string"==typeof a.error.stack&&(g.data.stack=a.error.stack),"string"==typeof b&&(g.data.path=b),e.errorFormatter((0,x.default)((0,x.default)({},a),{},{shape:g}))}},3041:(a,b,c)=>{var d,e;c.d(b,{Ay:()=>P});class f{constructor(){this.keyToValue=new Map,this.valueToKey=new Map}set(a,b){this.keyToValue.set(a,b),this.valueToKey.set(b,a)}getByKey(a){return this.keyToValue.get(a)}getByValue(a){return this.valueToKey.get(a)}clear(){this.keyToValue.clear(),this.valueToKey.clear()}}class g{constructor(a){this.generateIdentifier=a,this.kv=new f}register(a,b){this.kv.getByValue(a)||(b||(b=this.generateIdentifier(a)),this.kv.set(b,a))}clear(){this.kv.clear()}getIdentifier(a){return this.kv.getByValue(a)}getValue(a){return this.kv.getByKey(a)}}class h extends g{constructor(){super(a=>a.name),this.classToAllowedProps=new Map}register(a,b){"object"==typeof b?(b.allowProps&&this.classToAllowedProps.set(a,b.allowProps),super.register(a,b.identifier)):super.register(a,b)}getAllowedProps(a){return this.classToAllowedProps.get(a)}}function i(a,b){Object.entries(a).forEach(([a,c])=>b(c,a))}function j(a,b){return -1!==a.indexOf(b)}function k(a,b){for(let c=0;c<a.length;c++){let d=a[c];if(b(d))return d}}class l{constructor(){this.transfomers={}}register(a){this.transfomers[a.name]=a}findApplicable(a){return function(a,b){let c=function(a){if("values"in Object)return Object.values(a);let b=[];for(let c in a)a.hasOwnProperty(c)&&b.push(a[c]);return b}(a);if("find"in c)return c.find(b);for(let a=0;a<c.length;a++){let d=c[a];if(b(d))return d}}(this.transfomers,b=>b.isApplicable(a))}findByName(a){return this.transfomers[a]}}let m=a=>void 0===a,n=a=>"object"==typeof a&&null!==a&&a!==Object.prototype&&(null===Object.getPrototypeOf(a)||Object.getPrototypeOf(a)===Object.prototype),o=a=>n(a)&&0===Object.keys(a).length,p=a=>Array.isArray(a),q=a=>a instanceof Map,r=a=>a instanceof Set,s=a=>"Symbol"===Object.prototype.toString.call(a).slice(8,-1),t=a=>"number"==typeof a&&isNaN(a),u=a=>a.replace(/\./g,"\\."),v=a=>a.map(String).map(u).join("."),w=a=>{let b=[],c="";for(let d=0;d<a.length;d++){let e=a.charAt(d);if("\\"===e&&"."===a.charAt(d+1)){c+=".",d++;continue}if("."===e){b.push(c),c="";continue}c+=e}let d=c;return b.push(d),b};function x(a,b,c,d){return{isApplicable:a,annotation:b,transform:c,untransform:d}}let y=[x(m,"undefined",()=>null,()=>void 0),x(a=>"bigint"==typeof a,"bigint",a=>a.toString(),a=>"undefined"!=typeof BigInt?BigInt(a):(console.error("Please add a BigInt polyfill."),a)),x(a=>a instanceof Date&&!isNaN(a.valueOf()),"Date",a=>a.toISOString(),a=>new Date(a)),x(a=>a instanceof Error,"Error",(a,b)=>{let c={name:a.name,message:a.message};return b.allowedErrorProps.forEach(b=>{c[b]=a[b]}),c},(a,b)=>{let c=Error(a.message);return c.name=a.name,c.stack=a.stack,b.allowedErrorProps.forEach(b=>{c[b]=a[b]}),c}),x(a=>a instanceof RegExp,"regexp",a=>""+a,a=>new RegExp(a.slice(1,a.lastIndexOf("/")),a.slice(a.lastIndexOf("/")+1))),x(r,"set",a=>[...a.values()],a=>new Set(a)),x(q,"map",a=>[...a.entries()],a=>new Map(a)),x(a=>t(a)||(a=>a===1/0||a===-1/0)(a),"number",a=>t(a)?"NaN":a>0?"Infinity":"-Infinity",Number),x(a=>0===a&&1/a==-1/0,"number",()=>"-0",Number),x(a=>a instanceof URL,"URL",a=>a.toString(),a=>new URL(a))];function z(a,b,c,d){return{isApplicable:a,annotation:b,transform:c,untransform:d}}let A=z((a,b)=>!!s(a)&&!!b.symbolRegistry.getIdentifier(a),(a,b)=>["symbol",b.symbolRegistry.getIdentifier(a)],a=>a.description,(a,b,c)=>{let d=c.symbolRegistry.getValue(b[1]);if(!d)throw Error("Trying to deserialize unknown symbol");return d}),B=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array,Uint8ClampedArray].reduce((a,b)=>(a[b.name]=b,a),{}),C=z(a=>ArrayBuffer.isView(a)&&!(a instanceof DataView),a=>["typed-array",a.constructor.name],a=>[...a],(a,b)=>{let c=B[b[1]];if(!c)throw Error("Trying to deserialize unknown typed array");return new c(a)});function D(a,b){return!!a?.constructor&&!!b.classRegistry.getIdentifier(a.constructor)}let E=z(D,(a,b)=>["class",b.classRegistry.getIdentifier(a.constructor)],(a,b)=>{let c=b.classRegistry.getAllowedProps(a.constructor);if(!c)return{...a};let d={};return c.forEach(b=>{d[b]=a[b]}),d},(a,b,c)=>{let d=c.classRegistry.getValue(b[1]);if(!d)throw Error(`Trying to deserialize unknown class '${b[1]}' - check https://github.com/blitz-js/superjson/issues/116#issuecomment-773996564`);return Object.assign(Object.create(d.prototype),a)}),F=z((a,b)=>!!b.customTransformerRegistry.findApplicable(a),(a,b)=>["custom",b.customTransformerRegistry.findApplicable(a).name],(a,b)=>b.customTransformerRegistry.findApplicable(a).serialize(a),(a,b,c)=>{let d=c.customTransformerRegistry.findByName(b[1]);if(!d)throw Error("Trying to deserialize unknown custom value");return d.deserialize(a)}),G=[E,A,F,C],H=(a,b)=>{let c=k(G,c=>c.isApplicable(a,b));if(c)return{value:c.transform(a,b),type:c.annotation(a,b)};let d=k(y,c=>c.isApplicable(a,b));if(d)return{value:d.transform(a,b),type:d.annotation}},I={};y.forEach(a=>{I[a.annotation]=a});let J=(a,b)=>{if(b>a.size)throw Error("index out of bounds");let c=a.keys();for(;b>0;)c.next(),b--;return c.next().value};function K(a){if(j(a,"__proto__"))throw Error("__proto__ is not allowed as a property");if(j(a,"prototype"))throw Error("prototype is not allowed as a property");if(j(a,"constructor"))throw Error("constructor is not allowed as a property")}let L=(a,b,c)=>{if(K(b),0===b.length)return c(a);let d=a;for(let a=0;a<b.length-1;a++){let c=b[a];if(p(d))d=d[+c];else if(n(d))d=d[c];else if(r(d))d=J(d,+c);else if(q(d)){if(a===b.length-2)break;let e=+c,f=0==+b[++a]?"key":"value",g=J(d,e);switch(f){case"key":d=g;break;case"value":d=d.get(g)}}}let e=b[b.length-1];if(p(d)?d[+e]=c(d[+e]):n(d)&&(d[e]=c(d[e])),r(d)){let a=J(d,+e),b=c(a);a!==b&&(d.delete(a),d.add(b))}if(q(d)){let a=J(d,+b[b.length-2]);switch(0==+e?"key":"value"){case"key":{let b=c(a);d.set(b,d.get(a)),b!==a&&d.delete(a);break}case"value":d.set(a,c(d.get(a)))}}return a},M=(a,b,c,d,e=[],f=[],g=new Map)=>{let h=(a=>"boolean"==typeof a||null===a||m(a)||(a=>"number"==typeof a&&!isNaN(a))(a)||"string"==typeof a||s(a))(a);if(!h){!function(a,b,c){let d=c.get(a);d?d.push(b):c.set(a,[b])}(a,e,b);let c=g.get(a);if(c)return d?{transformedValue:null}:c}if(!((a,b)=>n(a)||p(a)||q(a)||r(a)||D(a,b))(a,c)){let b=H(a,c),d=b?{transformedValue:b.value,annotations:[b.type]}:{transformedValue:a};return h||g.set(a,d),d}if(j(f,a))return{transformedValue:null};let k=H(a,c),l=k?.value??a,t=p(l)?[]:{},v={};i(l,(h,j)=>{if("__proto__"===j||"constructor"===j||"prototype"===j)throw Error(`Detected property ${j}. This is a prototype pollution risk, please remove it from your object.`);let k=M(h,b,c,d,[...e,j],[...f,a],g);t[j]=k.transformedValue,p(k.annotations)?v[j]=k.annotations:n(k.annotations)&&i(k.annotations,(a,b)=>{v[u(j)+"."+b]=a})});let w=o(v)?{transformedValue:t,annotations:k?[k.type]:void 0}:{transformedValue:t,annotations:k?[k.type,v]:v};return h||g.set(a,w),w};function N(a){return Object.prototype.toString.call(a).slice(8,-1)}function O(a){return"Array"===N(a)}d=function(a){return"Null"===N(a)},e=function(a){return"Undefined"===N(a)};class P{constructor({dedupe:a=!1}={}){this.classRegistry=new h,this.symbolRegistry=new g(a=>a.description??""),this.customTransformerRegistry=new l,this.allowedErrorProps=[],this.dedupe=a}serialize(a){let b=new Map,c=M(a,b,this,this.dedupe),d={json:c.transformedValue};c.annotations&&(d.meta={...d.meta,values:c.annotations});let e=function(a,b){let c,d={};return(a.forEach(a=>{if(a.length<=1)return;b||(a=a.map(a=>a.map(String)).sort((a,b)=>a.length-b.length));let[e,...f]=a;0===e.length?c=f.map(v):d[v(e)]=f.map(v)}),c)?o(d)?[c]:[c,d]:o(d)?void 0:d}(b,this.dedupe);return e&&(d.meta={...d.meta,referentialEqualities:e}),d}deserialize(a){var b,c,d;let{json:e,meta:f}=a,g=function a(b,c={}){return O(b)?b.map(b=>a(b,c)):!function(a){if("Object"!==N(a))return!1;let b=Object.getPrototypeOf(a);return!!b&&b.constructor===Object&&b===Object.prototype}(b)?b:[...Object.getOwnPropertyNames(b),...Object.getOwnPropertySymbols(b)].reduce((d,e)=>{if(O(c.props)&&!c.props.includes(e))return d;let f=a(b[e],c);var g=c.nonenumerable;let h=({}).propertyIsEnumerable.call(b,e)?"enumerable":"nonenumerable";return"enumerable"===h&&(d[e]=f),g&&"nonenumerable"===h&&Object.defineProperty(d,e,{value:f,enumerable:!1,writable:!0,configurable:!0}),d},{})}(e);return f?.values&&(b=g,c=f.values,d=this,function a(b,c,d=[]){if(!b)return;if(!p(b))return void i(b,(b,e)=>a(b,c,[...d,...w(e)]));let[e,f]=b;f&&i(f,(b,e)=>{a(b,c,[...d,...w(e)])}),c(e,d)}(c,(a,c)=>{b=L(b,c,b=>((a,b,c)=>{if(p(b))switch(b[0]){case"symbol":return A.untransform(a,b,c);case"class":return E.untransform(a,b,c);case"custom":return F.untransform(a,b,c);case"typed-array":return C.untransform(a,b,c);default:throw Error("Unknown transformation: "+b)}{let d=I[b];if(!d)throw Error("Unknown transformation: "+b);return d.untransform(a,c)}})(b,a,d))}),g=b),f?.referentialEqualities&&(g=function(a,b){function c(b,c){let d=((a,b)=>{K(b);for(let c=0;c<b.length;c++){let d=b[c];if(r(a))a=J(a,+d);else if(q(a)){let e=+d,f=0==+b[++c]?"key":"value",g=J(a,e);switch(f){case"key":a=g;break;case"value":a=a.get(g)}}else a=a[d]}return a})(a,w(c));b.map(w).forEach(b=>{a=L(a,b,()=>d)})}if(p(b)){let[d,e]=b;d.forEach(b=>{a=L(a,w(b),()=>a)}),e&&i(e,c)}else i(b,c);return a}(g,f.referentialEqualities)),g}stringify(a){return JSON.stringify(this.serialize(a))}parse(a){return this.deserialize(JSON.parse(a))}registerClass(a,b){this.classRegistry.register(a,b)}registerSymbol(a,b){this.symbolRegistry.register(a,b)}registerCustom(a,b){this.customTransformerRegistry.register({name:b,...a})}allowErrorProps(...a){this.allowedErrorProps.push(...a)}}P.defaultInstance=new P,P.serialize=P.defaultInstance.serialize.bind(P.defaultInstance),P.deserialize=P.defaultInstance.deserialize.bind(P.defaultInstance),P.stringify=P.defaultInstance.stringify.bind(P.defaultInstance),P.parse=P.defaultInstance.parse.bind(P.defaultInstance),P.registerClass=P.defaultInstance.registerClass.bind(P.defaultInstance),P.registerSymbol=P.defaultInstance.registerSymbol.bind(P.defaultInstance),P.registerCustom=P.defaultInstance.registerCustom.bind(P.defaultInstance),P.allowErrorProps=P.defaultInstance.allowErrorProps.bind(P.defaultInstance),P.serialize,P.deserialize,P.stringify,P.parse,P.registerClass,P.registerCustom,P.registerSymbol,P.allowErrorProps},4328:(a,b,c)=>{c.d(b,{Al:()=>A});var d,e,f,g=c(893),h=c(457),i=c(8761),j=(0,g.f1)((0,g.jr)(),1);let k="middlewareMarker";var l=(0,g.f1)((0,g.Vh)(),1),m=class extends Error{constructor(a){var b;super(null==(b=a[0])?void 0:b.message),(0,l.default)(this,"issues",void 0),this.name="SchemaError",this.issues=a}};function n(a){let b="~standard"in a;if("function"==typeof a&&"function"==typeof a.assert)return a.assert.bind(a);if("function"==typeof a&&!b)return a;if("function"==typeof a.parseAsync)return a.parseAsync.bind(a);if("function"==typeof a.parse)return a.parse.bind(a);if("function"==typeof a.validateSync)return a.validateSync.bind(a);if("function"==typeof a.create)return a.create.bind(a);if("function"==typeof a.assert)return b=>(a.assert(b),b);if(b)return async b=>{let c=await a["~standard"].validate(b);if(c.issues)throw new m(c.issues);return c.value};throw Error("Could not find a validator fn")}var o=(0,g.P$)({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectWithoutPropertiesLoose.js"(a,b){b.exports=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(b.includes(d))continue;c[d]=a[d]}return c},b.exports.__esModule=!0,b.exports.default=b.exports}}),p=(0,g.P$)({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectWithoutProperties.js"(a,b){var c=o();b.exports=function(a,b){if(null==a)return{};var d,e,f=c(a,b);if(Object.getOwnPropertySymbols){var g=Object.getOwnPropertySymbols(a);for(e=0;e<g.length;e++)d=g[e],b.includes(d)||({}).propertyIsEnumerable.call(a,d)&&(f[d]=a[d])}return f},b.exports.__esModule=!0,b.exports.default=b.exports}}),q=(0,g.f1)(p(),1),r=(0,g.f1)((0,g.jr)(),1);let s=["middlewares","inputs","meta"];function t(a,b){let{middlewares:c=[],inputs:d,meta:e}=b,f=(0,q.default)(b,s);return u((0,r.default)((0,r.default)({},(0,i.uf)(a,f)),{},{inputs:[...a.inputs,...null!=d?d:[]],middlewares:[...a.middlewares,...c],meta:a.meta&&e?(0,r.default)((0,r.default)({},a.meta),e):null!=e?e:a.meta}))}function u(a={}){let b=(0,r.default)({procedure:!0,inputs:[],middlewares:[]},a);return{_def:b,input(a){let c=n(a);return t(b,{inputs:[a],middlewares:[function(a){let b=async function(b){let c,d=await b.getRawInput();try{c=await a(d)}catch(a){throw new h.gt({code:"BAD_REQUEST",cause:a})}let e=(0,i.Gv)(b.input)&&(0,i.Gv)(c)?(0,j.default)((0,j.default)({},b.input),c):c;return b.next({input:e})};return b._type="input",b}(c)]})},output(a){let c=n(a);return t(b,{output:a,middlewares:[function(a){let b=async function({next:b}){let c=await b();if(!c.ok)return c;try{let b=await a(c.data);return(0,j.default)((0,j.default)({},c),{},{data:b})}catch(a){throw new h.gt({message:"Output validation failed",code:"INTERNAL_SERVER_ERROR",cause:a})}};return b._type="output",b}(c)]})},meta:a=>t(b,{meta:a}),use:a=>t(b,{middlewares:"_middlewares"in a?a._middlewares:[a]}),unstable_concat:a=>t(b,a._def),concat:a=>t(b,a._def),query:a=>v((0,r.default)((0,r.default)({},b),{},{type:"query"}),a),mutation:a=>v((0,r.default)((0,r.default)({},b),{},{type:"mutation"}),a),subscription:a=>v((0,r.default)((0,r.default)({},b),{},{type:"subscription"}),a),experimental_caller:a=>t(b,{caller:a})}}function v(a,b){let c=t(a,{resolver:b,middlewares:[async function(a){return{marker:k,ok:!0,data:await b(a),ctx:a.ctx}}]}),d=(0,r.default)((0,r.default)({},c._def),{},{type:a.type,experimental_caller:!!c._def.caller,meta:c._def.meta,$types:null}),e=function(a){async function b(b){if(!b||!("getRawInput"in b))throw Error(w);let c=await x(0,a,b);if(!c)throw new h.gt({code:"INTERNAL_SERVER_ERROR",message:"No result from middlewares - did you forget to `return next()`?"});if(!c.ok)throw c.error;return c.data}return b._def=a,b.procedure=!0,b.meta=a.meta,b}(c._def),f=c._def.caller;if(!f)return e;let g=async(...a)=>await f({args:a,invoke:e,_def:d});return g._def=d,g}let w=`
This is a client-only function.
If you want to call this function on the server, see https://trpc.io/docs/v11/server/server-side-calls
`.trim();async function x(a,b,c){try{let d=b.middlewares[a];return await d((0,r.default)((0,r.default)({},c),{},{meta:b.meta,input:c.input,next(d){var e;return x(a+1,b,(0,r.default)((0,r.default)({},c),{},{ctx:(null==d?void 0:d.ctx)?(0,r.default)((0,r.default)({},c.ctx),d.ctx):c.ctx,input:d&&"input"in d?d.input:c.input,getRawInput:null!=(e=null==d?void 0:d.getRawInput)?e:c.getRawInput}))}}))}catch(a){return{ok:!1,error:(0,h.qO)(a),marker:k}}}let y="undefined"==typeof window||"Deno"in window||(null==(d=globalThis.process)||null==(d=d.env)?void 0:d.NODE_ENV)==="test"||!!(null==(e=globalThis.process)||null==(e=e.env)?void 0:e.JEST_WORKER_ID)||!!(null==(f=globalThis.process)||null==(f=f.env)?void 0:f.VITEST_WORKER_ID);var z=(0,g.f1)((0,g.jr)(),1);let A=new class a{context(){return new a}meta(){return new a}create(a){var b,c,d,e,f,g,i;let j=(0,z.default)((0,z.default)({},a),{},{transformer:(0,h.u$)(null!=(b=null==a?void 0:a.transformer)?b:h.bJ),isDev:null!=(c=null==a?void 0:a.isDev)?c:(null==(d=globalThis.process)?void 0:d.env.NODE_ENV)!=="production",allowOutsideOfServer:null!=(e=null==a?void 0:a.allowOutsideOfServer)&&e,errorFormatter:null!=(f=null==a?void 0:a.errorFormatter)?f:h.EN,isServer:null!=(g=null==a?void 0:a.isServer)?g:y,$types:null});if(!(null!=(i=null==a?void 0:a.isServer)?i:y)&&(null==a?void 0:a.allowOutsideOfServer)!==!0)throw Error("You're trying to use @trpc/server in a non-server environment. This is not supported by default.");return{_config:j,procedure:u({meta:null==a?void 0:a.defaultMeta}),middleware:function(a){return function a(b){return{_middlewares:b,unstable_pipe:c=>a([...b,..."_middlewares"in c?c._middlewares:[c]])}}([a])},router:(0,h.OX)(j),mergeRouters:h.ri,createCallerFactory:(0,h.OA)()}}}},8761:(a,b,c)=>{c.d(b,{D_:()=>m,Gv:()=>g,QQ:()=>i,Td:()=>k,Tn:()=>h,Yk:()=>d,eF:()=>l,uB:()=>e,uf:()=>f});let d={PARSE_ERROR:-32700,BAD_REQUEST:-32600,INTERNAL_SERVER_ERROR:-32603,NOT_IMPLEMENTED:-32603,BAD_GATEWAY:-32603,SERVICE_UNAVAILABLE:-32603,GATEWAY_TIMEOUT:-32603,UNAUTHORIZED:-32001,PAYMENT_REQUIRED:-32002,FORBIDDEN:-32003,NOT_FOUND:-32004,METHOD_NOT_SUPPORTED:-32005,TIMEOUT:-32008,CONFLICT:-32009,PRECONDITION_FAILED:-32012,PAYLOAD_TOO_LARGE:-32013,UNSUPPORTED_MEDIA_TYPE:-32015,UNPROCESSABLE_CONTENT:-32022,TOO_MANY_REQUESTS:-32029,CLIENT_CLOSED_REQUEST:-32099},e={[-32700]:"PARSE_ERROR",[-32600]:"BAD_REQUEST",[-32603]:"INTERNAL_SERVER_ERROR",[-32001]:"UNAUTHORIZED",[-32002]:"PAYMENT_REQUIRED",[-32003]:"FORBIDDEN",[-32004]:"NOT_FOUND",[-32005]:"METHOD_NOT_SUPPORTED",[-32008]:"TIMEOUT",[-32009]:"CONFLICT",[-32012]:"PRECONDITION_FAILED",[-32013]:"PAYLOAD_TOO_LARGE",[-32015]:"UNSUPPORTED_MEDIA_TYPE",[-32022]:"UNPROCESSABLE_CONTENT",[-32029]:"TOO_MANY_REQUESTS",[-32099]:"CLIENT_CLOSED_REQUEST"};function f(a,...b){let c=Object.assign(Object.create(null),a);for(let a of b)for(let b in a){if(b in c&&c[b]!==a[b])throw Error(`Duplicate key ${b}`);c[b]=a[b]}return c}function g(a){return!!a&&!Array.isArray(a)&&"object"==typeof a}function h(a){return"function"==typeof a}function i(a){return Object.assign(Object.create(null),a)}d.BAD_GATEWAY,d.SERVICE_UNAVAILABLE,d.GATEWAY_TIMEOUT,d.INTERNAL_SERVER_ERROR;let j="function"==typeof Symbol&&!!Symbol.asyncIterator;function k(a){return j&&g(a)&&Symbol.asyncIterator in a}let l=a=>a();function m(a){return a}}};