'use client'

import { toast } from 'react-hot-toast'
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>dalTitle, 
  ModalContent,
  ModalCloseButton 
} from '~/components/ui/Modal'
import { PromptForm, type PromptFormData } from '~/components/PromptForm'
import { useModals } from '~/hooks/useStore'
import { api } from '~/trpc/react'

export function EditPromptModal() {
  const { modals, currentEditingPrompt, closeEditPrompt } = useModals()
  
  // 更新提示词的mutation
  const updatePromptMutation = api.prompt.update.useMutation({
    onSuccess: () => {
      toast.success('提示词更新成功！')
      closeEditPrompt()
      // 刷新提示词列表
      void utils.prompt.getAll.invalidate()
      void utils.prompt.getLatest.invalidate()
      void utils.prompt.getPopular.invalidate()
      void utils.prompt.getById.invalidate()
    },
    onError: (error) => {
      toast.error(error.message || '更新失败，请重试')
    },
  })
  
  // 获取utils用于刷新数据
  const utils = api.useUtils()
  
  const handleSubmit = async (data: PromptFormData) => {
    if (!currentEditingPrompt) return
    
    try {
      await updatePromptMutation.mutateAsync({
        id: currentEditingPrompt.id,
        title: data.title,
        content: data.content,
        description: data.description || undefined,
        tags: data.tags,
        categoryId: data.categoryId || undefined,
        isPublic: data.isPublic,
      })
    } catch (error) {
      // 错误已在onError中处理
    }
  }

  if (!currentEditingPrompt) return null

  return (
    <Modal
      open={modals.editPrompt}
      onClose={closeEditPrompt}
      size="xl"
    >
      <ModalHeader>
        <ModalTitle>编辑提示词</ModalTitle>
        <ModalCloseButton onClose={closeEditPrompt} />
      </ModalHeader>

      <ModalContent>
        <PromptForm
          prompt={currentEditingPrompt}
          onSubmit={handleSubmit}
          onCancel={closeEditPrompt}
          isLoading={updatePromptMutation.isPending}
        />
      </ModalContent>
    </Modal>
  )
}
