import { z } from "zod";
import { createTRPCRouter, protectedProcedure, publicProcedure } from "~/server/api/trpc";

export const statsRouter = createTRPCRouter({
  // 获取总体统计信息
  getOverview: publicProcedure.query(async ({ ctx }) => {
    const [
      totalPrompts,
      totalCategories,
      totalUsers,
      totalUsages,
    ] = await Promise.all([
      ctx.db.prompt.count({ where: { isPublic: true } }),
      ctx.db.category.count(),
      ctx.db.user.count(),
      ctx.db.promptUsage.count(),
    ]);

    return {
      totalPrompts,
      totalCategories,
      totalUsers,
      totalUsages,
    };
  }),

  // 获取分类统计
  getCategoryStats: publicProcedure.query(async ({ ctx }) => {
    const categoryStats = await ctx.db.category.findMany({
      select: {
        id: true,
        name: true,
        color: true,
        icon: true,
        _count: {
          select: { prompts: true },
        },
      },
      orderBy: {
        prompts: {
          _count: "desc",
        },
      },
    });

    return categoryStats.map(category => ({
      id: category.id,
      name: category.name,
      color: category.color,
      icon: category.icon,
      promptCount: category._count.prompts,
    }));
  }),

  // 获取使用趋势（最近30天）
  getUsageTrend: publicProcedure.query(async ({ ctx }) => {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const usageData = await ctx.db.promptUsage.findMany({
      where: {
        createdAt: {
          gte: thirtyDaysAgo,
        },
      },
      select: {
        createdAt: true,
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    // 按日期分组统计
    const dailyUsage = new Map<string, number>();
    
    // 初始化最近30天的数据
    for (let i = 29; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      dailyUsage.set(dateStr!, 0);
    }

    // 统计实际使用数据
    usageData.forEach(usage => {
      const dateStr = usage.createdAt.toISOString().split('T')[0];
      if (dateStr) {
        dailyUsage.set(dateStr, (dailyUsage.get(dateStr) || 0) + 1);
      }
    });

    return Array.from(dailyUsage.entries()).map(([date, count]) => ({
      date,
      count,
    }));
  }),

  // 获取热门标签
  getPopularTags: publicProcedure
    .input(z.object({ limit: z.number().min(1).max(50).default(20) }))
    .query(async ({ ctx, input }) => {
      const prompts = await ctx.db.prompt.findMany({
        where: { isPublic: true },
        select: { tags: true },
      });

      // 统计标签使用频率
      const tagCount = new Map<string, number>();
      prompts.forEach(prompt => {
        prompt.tags.forEach(tag => {
          tagCount.set(tag, (tagCount.get(tag) || 0) + 1);
        });
      });

      // 排序并返回前N个
      return Array.from(tagCount.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, input.limit)
        .map(([tag, count]) => ({ tag, count }));
    }),

  // 获取用户个人统计（需要登录）
  getMyStats: protectedProcedure.query(async ({ ctx }) => {
    const userId = ctx.session.user.id;

    const [
      myPrompts,
      myCategories,
      myUsages,
      myTotalUsageCount,
    ] = await Promise.all([
      ctx.db.prompt.count({ where: { createdById: userId } }),
      ctx.db.category.count({ where: { createdById: userId } }),
      ctx.db.promptUsage.count({ where: { userId } }),
      ctx.db.prompt.aggregate({
        where: { createdById: userId },
        _sum: { usageCount: true },
      }),
    ]);

    // 获取我最常用的分类
    const myTopCategories = await ctx.db.category.findMany({
      where: { createdById: userId },
      select: {
        id: true,
        name: true,
        color: true,
        _count: {
          select: { prompts: true },
        },
      },
      orderBy: {
        prompts: {
          _count: "desc",
        },
      },
      take: 5,
    });

    // 获取我最受欢迎的提示词
    const myTopPrompts = await ctx.db.prompt.findMany({
      where: { createdById: userId },
      select: {
        id: true,
        title: true,
        usageCount: true,
        category: {
          select: {
            name: true,
            color: true,
          },
        },
      },
      orderBy: { usageCount: "desc" },
      take: 5,
    });

    return {
      myPrompts,
      myCategories,
      myUsages,
      myTotalUsageCount: myTotalUsageCount._sum.usageCount || 0,
      myTopCategories: myTopCategories.map(cat => ({
        id: cat.id,
        name: cat.name,
        color: cat.color,
        promptCount: cat._count.prompts,
      })),
      myTopPrompts,
    };
  }),

  // 获取最近使用的提示词（需要登录）
  getRecentUsage: protectedProcedure
    .input(z.object({ limit: z.number().min(1).max(50).default(10) }))
    .query(async ({ ctx, input }) => {
      const recentUsages = await ctx.db.promptUsage.findMany({
        where: { userId: ctx.session.user.id },
        take: input.limit,
        orderBy: { createdAt: "desc" },
        include: {
          prompt: {
            select: {
              id: true,
              title: true,
              description: true,
              category: {
                select: {
                  name: true,
                  color: true,
                  icon: true,
                },
              },
            },
          },
        },
      });

      return recentUsages.map(usage => ({
        id: usage.id,
        usedAt: usage.createdAt,
        prompt: usage.prompt,
      }));
    }),

  // 获取使用排行榜
  getUsageLeaderboard: publicProcedure
    .input(z.object({ limit: z.number().min(1).max(50).default(10) }))
    .query(async ({ ctx, input }) => {
      return ctx.db.prompt.findMany({
        where: { isPublic: true },
        take: input.limit,
        orderBy: { usageCount: "desc" },
        select: {
          id: true,
          title: true,
          description: true,
          usageCount: true,
          category: {
            select: {
              name: true,
              color: true,
              icon: true,
            },
          },
          createdBy: {
            select: {
              name: true,
              image: true,
            },
          },
        },
      });
    }),
});
