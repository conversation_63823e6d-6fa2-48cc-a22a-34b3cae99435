'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { api } from '~/trpc/react'
import { PromptCard } from '~/components/PromptCard'
import { Button } from '~/components/ui/Button'
import { Input } from '~/components/ui/Input'
import { Badge } from '~/components/ui/Badge'
import { useUI, useModals } from '~/hooks/useStore'

export function HomePage() {
  const [searchQuery, setSearchQuery] = useState('')
  const { openCreatePrompt } = useModals()
  
  // 获取数据
  const { data: latestPrompts, isLoading: isLoadingPrompts } = api.prompt.getLatest.useQuery({ limit: 8 })
  const { data: popularPrompts } = api.prompt.getPopular.useQuery({ limit: 6 })
  const { data: categories } = api.category.getAll.useQuery()
  const { data: stats } = api.stats.getOverview.useQuery()
  
  // 复制提示词的mutation
  const copyPromptMutation = api.prompt.copy.useMutation()
  
  const handleCopyPrompt = async (promptId: string) => {
    try {
      await copyPromptMutation.mutateAsync({ id: promptId })
    } catch (error) {
      console.error('更新使用次数失败:', error)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      {/* 头部区域 */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-slate-200 dark:bg-slate-900/80 dark:border-slate-700 sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <h1 className="text-2xl font-bold text-slate-900 dark:text-white">
                提示词管理工具
              </h1>
              {stats && (
                <div className="hidden md:flex items-center gap-4 text-sm text-slate-600 dark:text-slate-400">
                  <span>{stats.totalPrompts} 个提示词</span>
                  <span>•</span>
                  <span>{stats.totalCategories} 个分类</span>
                  <span>•</span>
                  <span>{stats.totalUsages} 次使用</span>
                </div>
              )}
            </div>
            
            <div className="flex items-center gap-4">
              <div className="relative">
                <Input
                  placeholder="搜索提示词..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-64"
                />
                <svg
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
              
              <Button onClick={openCreatePrompt}>
                <svg
                  className="w-4 h-4 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 4v16m8-8H4"
                  />
                </svg>
                新建提示词
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        {/* 分类快速导航 */}
        {categories && categories.length > 0 && (
          <section className="mb-8">
            <h2 className="text-lg font-semibold text-slate-900 dark:text-white mb-4">
              分类导航
            </h2>
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <Badge
                  key={category.id}
                  variant="outline"
                  className="cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors"
                  style={{ borderColor: category.color }}
                >
                  <div 
                    className="w-2 h-2 rounded-full mr-2"
                    style={{ backgroundColor: category.color }}
                  />
                  {category.name}
                  {category._count && (
                    <span className="ml-1 text-xs text-slate-500">
                      ({category._count.prompts})
                    </span>
                  )}
                </Badge>
              ))}
            </div>
          </section>
        )}

        {/* 热门提示词 */}
        {popularPrompts && popularPrompts.length > 0 && (
          <section className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-slate-900 dark:text-white">
                🔥 热门提示词
              </h2>
              <Button variant="ghost" size="sm">
                查看更多
              </Button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {popularPrompts.map((prompt, index) => (
                <motion.div
                  key={prompt.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <PromptCard
                    prompt={prompt}
                    onCopy={handleCopyPrompt}
                  />
                </motion.div>
              ))}
            </div>
          </section>
        )}

        {/* 最新提示词 */}
        <section>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-slate-900 dark:text-white">
              ✨ 最新提示词
            </h2>
            <Button variant="ghost" size="sm">
              查看全部
            </Button>
          </div>
          
          {isLoadingPrompts ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {Array.from({ length: 8 }).map((_, index) => (
                <div
                  key={index}
                  className="h-64 bg-slate-200 dark:bg-slate-700 rounded-lg animate-pulse"
                />
              ))}
            </div>
          ) : latestPrompts && latestPrompts.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {latestPrompts.map((prompt, index) => (
                <motion.div
                  key={prompt.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <PromptCard
                    prompt={prompt}
                    onCopy={handleCopyPrompt}
                  />
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-slate-400 dark:text-slate-600 mb-4">
                <svg
                  className="w-16 h-16 mx-auto mb-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-slate-900 dark:text-white mb-2">
                还没有提示词
              </h3>
              <p className="text-slate-600 dark:text-slate-400 mb-4">
                创建您的第一个提示词，开始管理您的AI助手对话模板
              </p>
              <Button onClick={openCreatePrompt}>
                创建第一个提示词
              </Button>
            </div>
          )}
        </section>
      </main>
    </div>
  )
}
