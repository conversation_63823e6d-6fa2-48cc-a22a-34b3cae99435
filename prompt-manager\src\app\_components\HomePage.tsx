'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { api } from '~/trpc/react'
import { PromptList } from '~/components/PromptList'
import { PromptDetailModal } from '~/components/PromptDetailModal'
import { CreatePromptModal } from '~/components/CreatePromptModal'
import { EditPromptModal } from '~/components/EditPromptModal'
import { CreateCategoryModal } from '~/components/CreateCategoryModal'
import { EditCategoryModal } from '~/components/EditCategoryModal'
import { StatsDashboard } from '~/components/StatsDashboard'
import { CategorySidebar } from '~/components/CategorySidebar'
import { ClientOnly } from '~/components/ClientOnly'
import { PageTransition } from '~/components/PageTransition'
import { AnimatedButton, FloatingActionButton } from '~/components/AnimatedButton'
import { SearchAndFilter } from '~/components/SearchAndFilter'
import { But<PERSON> } from '~/components/ui/Button'
import { Badge } from '~/components/ui/Badge'
import { useUI, useModals, useUserPreferences } from '~/hooks/useStore'

export function HomePage() {
  const [currentPage, setCurrentPage] = useState(1)
  const [currentView, setCurrentView] = useState<'prompts' | 'stats'>('prompts')
  const { openCreatePrompt } = useModals()
  const {
    searchQuery,
    selectedCategoryId,
    setSelectedCategoryId,
    sortBy,
    sortOrder
  } = useUI()
  const { pageSize } = useUserPreferences()

  // 获取提示词列表数据
  const { data: promptsData, isLoading: isLoadingPrompts } = api.prompt.getAll.useQuery({
    page: currentPage,
    limit: pageSize,
    search: searchQuery || undefined,
    categoryId: selectedCategoryId || undefined,
    sortBy,
    sortOrder,
  })

  // 获取其他数据
  const { data: categories } = api.category.getAll.useQuery()
  const { data: stats } = api.stats.getOverview.useQuery()

  // 复制提示词的mutation
  const copyPromptMutation = api.prompt.copy.useMutation()

  const handleCopyPrompt = async (promptId: string) => {
    try {
      await copyPromptMutation.mutateAsync({ id: promptId })
    } catch (error) {
      console.error('更新使用次数失败:', error)
    }
  }

  // 处理搜索
  const handleSearch = (query: string) => {
    setCurrentPage(1) // 重置到第一页
  }

  // 处理分类筛选
  const handleCategoryFilter = (categoryId: string | null) => {
    setSelectedCategoryId(categoryId || undefined)
    setCurrentPage(1) // 重置到第一页
  }

  // 清除所有筛选
  const handleClearFilters = () => {
    setSelectedCategoryId(undefined)
    setCurrentPage(1)
  }

  // 处理排序
  const handleSortChange = (newSortBy: string, newSortOrder: 'asc' | 'desc') => {
    setCurrentPage(1) // 重置到第一页
  }

  // 处理分页
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* 装饰性背景元素 */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-indigo-400/10 to-cyan-400/10 rounded-full blur-3xl"></div>
      </div>

      {/* 头部区域 */}
      <header className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl border-b border-slate-200/50 dark:border-slate-700/50 sticky top-0 z-40 shadow-sm">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              <div className="relative">
                <div className="absolute -inset-1 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg blur opacity-25"></div>
                <div className="relative bg-white dark:bg-slate-800 rounded-lg px-4 py-2 border border-slate-200/50 dark:border-slate-700/50">
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    提示词管理工具
                  </h1>
                </div>
              </div>
              {stats && (
                <div className="hidden md:flex items-center gap-6 text-sm">
                  <div className="flex items-center gap-2 bg-white/60 dark:bg-slate-700/60 backdrop-blur-sm rounded-lg px-3 py-2 border border-white/20">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span className="font-medium text-slate-700 dark:text-slate-300">{stats.totalPrompts} 个提示词</span>
                  </div>
                  <div className="flex items-center gap-2 bg-white/60 dark:bg-slate-700/60 backdrop-blur-sm rounded-lg px-3 py-2 border border-white/20">
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                    <span className="font-medium text-slate-700 dark:text-slate-300">{stats.totalCategories} 个分类</span>
                  </div>
                  <div className="flex items-center gap-2 bg-white/60 dark:bg-slate-700/60 backdrop-blur-sm rounded-lg px-3 py-2 border border-white/20">
                    <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
                    <span className="font-medium text-slate-700 dark:text-slate-300">{stats.totalUsages} 次使用</span>
                  </div>
                </div>
              )}
            </div>
            
            <div className="flex items-center gap-4">
              {/* 视图切换 */}
              <div className="flex bg-white/60 dark:bg-slate-700/60 backdrop-blur-sm rounded-xl p-1 shadow-lg border border-white/20">
                <button
                  onClick={() => setCurrentView('prompts')}
                  className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 relative ${
                    currentView === 'prompts'
                      ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg transform scale-105'
                      : 'text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-100 hover:bg-white/50 dark:hover:bg-slate-600/50'
                  }`}
                >
                  {currentView === 'prompts' && (
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg blur opacity-50 -z-10"></div>
                  )}
                  <span className="relative flex items-center gap-2">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    提示词
                  </span>
                </button>
                <button
                  onClick={() => setCurrentView('stats')}
                  className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 relative ${
                    currentView === 'stats'
                      ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg transform scale-105'
                      : 'text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-100 hover:bg-white/50 dark:hover:bg-slate-600/50'
                  }`}
                >
                  {currentView === 'stats' && (
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg blur opacity-50 -z-10"></div>
                  )}
                  <span className="relative flex items-center gap-2">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    数据统计
                  </span>
                </button>
              </div>

              <AnimatedButton
                onClick={openCreatePrompt}
                animationType="bounce"
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl flex items-center gap-2 shadow-lg hover:shadow-xl transition-all duration-200 relative overflow-hidden group border-0"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-400 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                <svg className="w-5 h-5 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                <span className="relative z-10 font-medium">新建提示词</span>
              </AnimatedButton>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容区域 */}
      <div className="flex h-[calc(100vh-80px)]">
        {/* 左侧分类边栏 */}
        <ClientOnly
          fallback={
            <div className="bg-white dark:bg-slate-900 border-r border-slate-200 dark:border-slate-700 flex flex-col w-64 flex-shrink-0">
              <div className="flex items-center justify-between p-4 border-b border-slate-200 dark:border-slate-700">
                <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100">分类目录</h3>
                <button className="p-1 hover:bg-slate-100 dark:hover:bg-slate-800 rounded">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
              </div>
              <div className="flex-1 overflow-y-auto p-2">
                <div className="space-y-1">
                  <div className="flex items-center justify-between p-2 rounded-lg bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-slate-400" />
                      <span className="text-sm font-medium">全部分类</span>
                    </div>
                    <span className="text-xs text-slate-500 dark:text-slate-400 bg-slate-100 dark:bg-slate-700 px-1.5 py-0.5 rounded">0</span>
                  </div>
                </div>
              </div>
              <div className="p-2 border-t border-slate-200 dark:border-slate-700">
                <button className="w-full flex items-center gap-2 p-2 text-sm text-slate-600 dark:text-slate-400 hover:bg-slate-50 dark:hover:bg-slate-800 rounded-lg transition-colors">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  <span>新建分类</span>
                </button>
              </div>
            </div>
          }
        >
          <CategorySidebar
            selectedCategoryId={selectedCategoryId}
            onCategorySelect={handleCategoryFilter}
            className="flex-shrink-0"
          />
        </ClientOnly>

        {/* 右侧主内容区域 */}
        <main className="flex-1 overflow-y-auto bg-gradient-to-br from-white/50 to-slate-50/50 dark:from-slate-800/50 dark:to-slate-900/50">
          <div className="container mx-auto px-6 py-8">
            <PageTransition transitionKey={currentView}>
          {currentView === 'prompts' ? (
            <>
              {/* 搜索和筛选 */}
              <section className="mb-8">
                <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl p-6 shadow-lg border border-white/20 dark:border-slate-700/20">
                  <SearchAndFilter
                    onSearch={handleSearch}
                    onSortChange={handleSortChange}
                    hasActiveFilters={!!searchQuery || !!selectedCategoryId}
                    onClearFilters={handleClearFilters}
                  />
                </div>
              </section>

              {/* 提示词列表 */}
              <section>
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-4">
                    <div className="relative">
                      <div className="absolute -inset-1 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg blur opacity-25"></div>
                      <h2 className="relative text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        {searchQuery || selectedCategoryId ? '搜索结果' : '所有提示词'}
                      </h2>
                    </div>
                    <div className="bg-white/60 dark:bg-slate-700/60 backdrop-blur-sm rounded-lg px-3 py-1 border border-white/20">
                      <span className="text-sm font-medium text-slate-600 dark:text-slate-400">
                        共 {promptsData?.prompts?.length || 0} 个提示词
                      </span>
                    </div>
                  </div>
                </div>

                <PromptList
                  prompts={promptsData?.prompts || []}
                  isLoading={isLoadingPrompts}
                  onCopy={handleCopyPrompt}
                  showPagination={true}
                  currentPage={currentPage}
                  totalPages={promptsData?.pagination.totalPages || 1}
                  onPageChange={handlePageChange}
                />
              </section>
            </>
          ) : (
            /* 统计视图 */
            <StatsDashboard />
          )}
            </PageTransition>
          </div>
        </main>
      </div>

      {/* 浮动操作按钮 */}
      <FloatingActionButton onClick={openCreatePrompt}>
        <svg
          className="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 4v16m8-8H4"
          />
        </svg>
      </FloatingActionButton>

      {/* 模态框 */}
      <PromptDetailModal />
      <CreatePromptModal />
      <EditPromptModal />
      <CreateCategoryModal />
      <EditCategoryModal />
    </div>
  )
}
