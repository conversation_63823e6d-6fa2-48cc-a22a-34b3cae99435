'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { api } from '~/trpc/react'
import { PromptList } from '~/components/PromptList'
import { PromptDetailModal } from '~/components/PromptDetailModal'
import { CreatePromptModal } from '~/components/CreatePromptModal'
import { EditPromptModal } from '~/components/EditPromptModal'
import { CreateCategoryModal } from '~/components/CreateCategoryModal'
import { EditCategoryModal } from '~/components/EditCategoryModal'
import { StatsDashboard } from '~/components/StatsDashboard'
import { CategorySidebar } from '~/components/CategorySidebarNew'
import { ClientOnly } from '~/components/ClientOnly'
import { PageTransition } from '~/components/PageTransition'
import { AnimatedButton, FloatingActionButton } from '~/components/AnimatedButton'
import { SearchAndFilter } from '~/components/SearchAndFilter'
import { But<PERSON> } from '~/components/ui/Button'
import { Badge } from '~/components/ui/Badge'
import { useUI, useModals, useUserPreferences } from '~/hooks/useStore'

export function HomePage() {
  const [currentPage, setCurrentPage] = useState(1)
  const [currentView, setCurrentView] = useState<'prompts' | 'stats'>('prompts')
  const { openCreatePrompt } = useModals()
  const {
    searchQuery,
    selectedCategoryId,
    setSelectedCategoryId,
    sortBy,
    sortOrder
  } = useUI()
  const { pageSize } = useUserPreferences()

  // 获取提示词列表数据
  const { data: promptsData, isLoading: isLoadingPrompts } = api.prompt.getAll.useQuery({
    page: currentPage,
    limit: pageSize,
    search: searchQuery || undefined,
    categoryId: selectedCategoryId || undefined,
    sortBy,
    sortOrder,
  })

  // 获取其他数据
  const { data: categories } = api.category.getAll.useQuery()
  const { data: stats } = api.stats.getOverview.useQuery()

  // 复制提示词的mutation
  const copyPromptMutation = api.prompt.copy.useMutation()

  const handleCopyPrompt = async (promptId: string) => {
    try {
      await copyPromptMutation.mutateAsync({ id: promptId })
    } catch (error) {
      console.error('更新使用次数失败:', error)
    }
  }

  // 处理搜索
  const handleSearch = (query: string) => {
    setCurrentPage(1) // 重置到第一页
  }

  // 处理分类筛选
  const handleCategoryFilter = (categoryId: string | null) => {
    setSelectedCategoryId(categoryId || undefined)
    setCurrentPage(1) // 重置到第一页
  }

  // 清除所有筛选
  const handleClearFilters = () => {
    setSelectedCategoryId(undefined)
    setCurrentPage(1)
  }

  // 处理排序
  const handleSortChange = (newSortBy: string, newSortOrder: 'asc' | 'desc') => {
    setCurrentPage(1) // 重置到第一页
  }

  // 处理分页
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  return (
    <div className="min-h-screen bg-base-200">
      {/* 头部导航栏 */}
      <div className="navbar bg-base-100 shadow-lg">
        <div className="navbar-start">
          <div className="flex items-center gap-4">
            <h1 className="text-xl font-bold text-primary">
              提示词管理工具
            </h1>
            {stats && (
              <div className="hidden lg:flex items-center gap-4">
                <div className="stats stats-horizontal shadow">
                  <div className="stat">
                    <div className="stat-title">提示词</div>
                    <div className="stat-value text-primary text-lg">{stats.totalPrompts}</div>
                  </div>
                  <div className="stat">
                    <div className="stat-title">分类</div>
                    <div className="stat-value text-secondary text-lg">{stats.totalCategories}</div>
                  </div>
                  <div className="stat">
                    <div className="stat-title">使用次数</div>
                    <div className="stat-value text-accent text-lg">{stats.totalUsages}</div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
            
        <div className="navbar-end">
          <div className="flex items-center gap-4">
            {/* 视图切换 */}
            <div className="tabs tabs-boxed">
              <button
                onClick={() => setCurrentView('prompts')}
                className={`tab ${currentView === 'prompts' ? 'tab-active' : ''}`}
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                提示词
              </button>
              <button
                onClick={() => setCurrentView('stats')}
                className={`tab ${currentView === 'stats' ? 'tab-active' : ''}`}
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                数据统计
              </button>
            </div>

            {/* 新建提示词按钮 */}
            <button
              onClick={openCreatePrompt}
              className="btn btn-primary"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              新建提示词
            </button>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="drawer lg:drawer-open">
        <input id="drawer-toggle" type="checkbox" className="drawer-toggle" />

        {/* 主内容区域 */}
        <div className="drawer-content flex flex-col">
          {/* 移动端菜单按钮 */}
          <div className="navbar lg:hidden">
            <div className="flex-none">
              <label htmlFor="drawer-toggle" className="btn btn-square btn-ghost">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </label>
            </div>
          </div>

          {/* 主内容区域 */}
          <main className="flex-1 p-6">
            <PageTransition transitionKey={currentView}>
              {currentView === 'prompts' ? (
                <>
                  {/* 搜索和筛选 */}
                  <div className="card bg-base-100 shadow-xl mb-6">
                    <div className="card-body">
                      <SearchAndFilter
                        onSearch={handleSearch}
                        onSortChange={handleSortChange}
                        hasActiveFilters={!!searchQuery || !!selectedCategoryId}
                        onClearFilters={handleClearFilters}
                      />
                    </div>
                  </div>

                  {/* 提示词列表 */}
                  <div className="card bg-base-100 shadow-xl">
                    <div className="card-body">
                      <div className="flex items-center justify-between mb-6">
                        <h2 className="card-title text-2xl">
                          {searchQuery || selectedCategoryId ? '搜索结果' : '所有提示词'}
                        </h2>
                        <div className="badge badge-neutral">
                          共 {promptsData?.prompts?.length || 0} 个提示词
                        </div>
                      </div>

                      <PromptList
                        prompts={promptsData?.prompts || []}
                        isLoading={isLoadingPrompts}
                        onCopy={handleCopyPrompt}
                        showPagination={true}
                        currentPage={currentPage}
                        totalPages={promptsData?.pagination.totalPages || 1}
                        onPageChange={handlePageChange}
                      />
                    </div>
                  </div>
                </>
              ) : (
                /* 统计视图 */
                <StatsDashboard />
              )}
            </PageTransition>
          </main>
        </div>

        {/* 侧边栏 */}
        <div className="drawer-side">
          <label htmlFor="drawer-toggle" className="drawer-overlay"></label>
          <aside className="min-h-full w-64 bg-base-100">
            <ClientOnly
              fallback={
                <div className="p-4">
                  <h3 className="text-lg font-semibold mb-4">分类目录</h3>
                  <div className="space-y-2">
                    <div className="btn btn-ghost btn-block justify-start">
                      <span className="badge badge-primary">0</span>
                      全部分类
                    </div>
                  </div>
                </div>
              }
            >
              <CategorySidebar
                selectedCategoryId={selectedCategoryId}
                onCategorySelect={handleCategoryFilter}
              />
            </ClientOnly>
          </aside>
        </div>
      </div>

      {/* 浮动操作按钮 */}
      <FloatingActionButton onClick={openCreatePrompt}>
        <svg
          className="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 4v16m8-8H4"
          />
        </svg>
      </FloatingActionButton>

      {/* 模态框 */}
      <PromptDetailModal />
      <CreatePromptModal />
      <EditPromptModal />
      <CreateCategoryModal />
      <EditCategoryModal />
    </div>
  )
}
