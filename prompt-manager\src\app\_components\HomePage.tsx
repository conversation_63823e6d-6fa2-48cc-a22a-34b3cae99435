'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { api } from '~/trpc/react'
import { PromptList } from '~/components/PromptList'
import { PromptDetailModal } from '~/components/PromptDetailModal'
import { CreatePromptModal } from '~/components/CreatePromptModal'
import { EditPromptModal } from '~/components/EditPromptModal'
import { CreateCategoryModal } from '~/components/CreateCategoryModal'
import { EditCategoryModal } from '~/components/EditCategoryModal'
import { StatsDashboard } from '~/components/StatsDashboard'
import { CategorySidebar } from '~/components/CategorySidebar'
import { PageTransition } from '~/components/PageTransition'
import { AnimatedButton, FloatingActionButton } from '~/components/AnimatedButton'
import { SearchAndFilter } from '~/components/SearchAndFilter'
import { Button } from '~/components/ui/Button'
import { Badge } from '~/components/ui/Badge'
import { useUI, useModals, useUserPreferences } from '~/hooks/useStore'

export function HomePage() {
  const [currentPage, setCurrentPage] = useState(1)
  const [currentView, setCurrentView] = useState<'prompts' | 'stats'>('prompts')
  const { openCreatePrompt } = useModals()
  const {
    searchQuery,
    selectedCategoryId,
    setSelectedCategoryId,
    sortBy,
    sortOrder
  } = useUI()
  const { pageSize } = useUserPreferences()

  // 获取提示词列表数据
  const { data: promptsData, isLoading: isLoadingPrompts } = api.prompt.getAll.useQuery({
    page: currentPage,
    limit: pageSize,
    search: searchQuery || undefined,
    categoryId: selectedCategoryId || undefined,
    sortBy,
    sortOrder,
  })

  // 获取其他数据
  const { data: categories } = api.category.getAll.useQuery()
  const { data: stats } = api.stats.getOverview.useQuery()

  // 复制提示词的mutation
  const copyPromptMutation = api.prompt.copy.useMutation()

  const handleCopyPrompt = async (promptId: string) => {
    try {
      await copyPromptMutation.mutateAsync({ id: promptId })
    } catch (error) {
      console.error('更新使用次数失败:', error)
    }
  }

  // 处理搜索
  const handleSearch = (query: string) => {
    setCurrentPage(1) // 重置到第一页
  }

  // 处理分类筛选
  const handleCategoryFilter = (categoryId: string | null) => {
    setSelectedCategoryId(categoryId || undefined)
    setCurrentPage(1) // 重置到第一页
  }

  // 清除所有筛选
  const handleClearFilters = () => {
    setSelectedCategoryId(undefined)
    setCurrentPage(1)
  }

  // 处理排序
  const handleSortChange = (newSortBy: string, newSortOrder: 'asc' | 'desc') => {
    setCurrentPage(1) // 重置到第一页
  }

  // 处理分页
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      {/* 头部区域 */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-slate-200 dark:bg-slate-900/80 dark:border-slate-700 sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <h1 className="text-2xl font-bold text-slate-900 dark:text-white">
                提示词管理工具
              </h1>
              {stats && (
                <div className="hidden md:flex items-center gap-4 text-sm text-slate-600 dark:text-slate-400">
                  <span>{stats.totalPrompts} 个提示词</span>
                  <span>•</span>
                  <span>{stats.totalCategories} 个分类</span>
                  <span>•</span>
                  <span>{stats.totalUsages} 次使用</span>
                </div>
              )}
            </div>
            
            <div className="flex items-center gap-4">
              {/* 视图切换 */}
              <div className="flex items-center bg-slate-100 dark:bg-slate-800 rounded-lg p-1">
                <button
                  onClick={() => setCurrentView('prompts')}
                  className={`px-3 py-1 text-sm rounded-md transition-colors ${
                    currentView === 'prompts'
                      ? 'bg-white dark:bg-slate-700 text-slate-900 dark:text-white shadow-sm'
                      : 'text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white'
                  }`}
                >
                  提示词
                </button>
                <button
                  onClick={() => setCurrentView('stats')}
                  className={`px-3 py-1 text-sm rounded-md transition-colors ${
                    currentView === 'stats'
                      ? 'bg-white dark:bg-slate-700 text-slate-900 dark:text-white shadow-sm'
                      : 'text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white'
                  }`}
                >
                  数据统计
                </button>
              </div>

              <AnimatedButton
                onClick={openCreatePrompt}
                animationType="bounce"
              >
                <svg
                  className="w-4 h-4 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 4v16m8-8H4"
                  />
                </svg>
                新建提示词
              </AnimatedButton>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容区域 */}
      <div className="flex h-[calc(100vh-80px)]">
        {/* 左侧分类边栏 */}
        <CategorySidebar
          selectedCategoryId={selectedCategoryId}
          onCategorySelect={handleCategoryFilter}
          className="flex-shrink-0"
        />

        {/* 右侧主内容区域 */}
        <main className="flex-1 overflow-y-auto">
          <div className="container mx-auto px-4 py-8">
            <PageTransition key={currentView}>
          {currentView === 'prompts' ? (
            <>
              {/* 搜索和筛选 */}
              <section className="mb-8">
                <SearchAndFilter
                  onSearch={handleSearch}
                  onSortChange={handleSortChange}
                  hasActiveFilters={!!searchQuery || !!selectedCategoryId}
                  onClearFilters={handleClearFilters}
                />
              </section>



              {/* 提示词列表 */}
              <section>
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-semibold text-slate-900 dark:text-white">
                    {searchQuery || selectedCategoryId ? '搜索结果' : '所有提示词'}
                  </h2>
                </div>

                <PromptList
                  prompts={promptsData?.prompts || []}
                  isLoading={isLoadingPrompts}
                  onCopy={handleCopyPrompt}
                  showPagination={true}
                  currentPage={currentPage}
                  totalPages={promptsData?.pagination.totalPages || 1}
                  onPageChange={handlePageChange}
                />
              </section>
            </>
          ) : (
            /* 统计视图 */
            <StatsDashboard />
          )}
            </PageTransition>
          </div>
        </main>
      </div>

      {/* 浮动操作按钮 */}
      <FloatingActionButton onClick={openCreatePrompt}>
        <svg
          className="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 4v16m8-8H4"
          />
        </svg>
      </FloatingActionButton>

      {/* 模态框 */}
      <PromptDetailModal />
      <CreatePromptModal />
      <EditPromptModal />
      <CreateCategoryModal />
      <EditCategoryModal />
    </div>
  )
}
