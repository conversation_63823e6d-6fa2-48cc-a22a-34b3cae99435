/**
 * Plugin to add support for parsing from HTML.
 *
 * > 👉 **Note**: this is not an XML parser.
 * > It supports SVG as embedded in HTML.
 * > It does not support the features available in XML.
 * > Passing SVG files might break but fragments of modern SVG should be fine.
 * > Use [`xast-util-from-xml`][xast-util-from-xml] to parse XML.
 *
 * @param {Options | null | undefined} [options]
 *   Configuration (optional).
 * @returns {undefined}
 *   Nothing.
 */
export default function rehypeParse(options?: Options | null | undefined): undefined;
export default class rehypeParse {
    /**
     * Plugin to add support for parsing from HTML.
     *
     * > 👉 **Note**: this is not an XML parser.
     * > It supports SVG as embedded in HTML.
     * > It does not support the features available in XML.
     * > Passing SVG files might break but fragments of modern SVG should be fine.
     * > Use [`xast-util-from-xml`][xast-util-from-xml] to parse XML.
     *
     * @param {Options | null | undefined} [options]
     *   Configuration (optional).
     * @returns {undefined}
     *   Nothing.
     */
    constructor(options?: Options | null | undefined);
    parser: (document: string, file: import("vfile").VFile) => Root;
}
/**
 * Configuration.
 */
export type Options = Omit<FromHtmlOptions, "onerror"> & RehypeParseFields;
/**
 * Extra fields.
 */
export type RehypeParseFields = {
    /**
     * Whether to emit parse errors while parsing (default: `false`).
     *
     * > 👉 **Note**: parse errors are currently being added to HTML.
     * > Not all errors emitted by parse5 (or us) are specced yet.
     * > Some documentation may still be missing.
     */
    emitParseErrors?: boolean | null | undefined;
};
import type { Root } from 'hast';
import type { Options as FromHtmlOptions } from 'hast-util-from-html';
//# sourceMappingURL=index.d.ts.map