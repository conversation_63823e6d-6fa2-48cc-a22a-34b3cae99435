"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-to-html";
exports.ids = ["vendor-chunks/hast-util-to-html"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/handle/comment.js":
/*!**************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/handle/comment.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   comment: () => (/* binding */ comment)\n/* harmony export */ });\n/* harmony import */ var stringify_entities__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stringify-entities */ \"(ssr)/./node_modules/stringify-entities/lib/index.js\");\n/**\n * @import {Comment, Parents} from 'hast'\n * @import {State} from '../index.js'\n */\n\n\n\nconst htmlCommentRegex = /^>|^->|<!--|-->|--!>|<!-$/g\n\n// Declare arrays as variables so it can be cached by `stringifyEntities`\nconst bogusCommentEntitySubset = ['>']\nconst commentEntitySubset = ['<', '>']\n\n/**\n * Serialize a comment.\n *\n * @param {Comment} node\n *   Node to handle.\n * @param {number | undefined} _1\n *   Index of `node` in `parent.\n * @param {Parents | undefined} _2\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction comment(node, _1, _2, state) {\n  // See: <https://html.spec.whatwg.org/multipage/syntax.html#comments>\n  return state.settings.bogusComments\n    ? '<?' +\n        (0,stringify_entities__WEBPACK_IMPORTED_MODULE_0__.stringifyEntities)(\n          node.value,\n          Object.assign({}, state.settings.characterReferences, {\n            subset: bogusCommentEntitySubset\n          })\n        ) +\n        '>'\n    : '<!--' + node.value.replace(htmlCommentRegex, encode) + '-->'\n\n  /**\n   * @param {string} $0\n   */\n  function encode($0) {\n    return (0,stringify_entities__WEBPACK_IMPORTED_MODULE_0__.stringifyEntities)(\n      $0,\n      Object.assign({}, state.settings.characterReferences, {\n        subset: commentEntitySubset\n      })\n    )\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/handle/comment.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/handle/doctype.js":
/*!**************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/handle/doctype.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   doctype: () => (/* binding */ doctype)\n/* harmony export */ });\n/**\n * @import {Doctype, Parents} from 'hast'\n * @import {State} from '../index.js'\n */\n\n/**\n * Serialize a doctype.\n *\n * @param {Doctype} _1\n *   Node to handle.\n * @param {number | undefined} _2\n *   Index of `node` in `parent.\n * @param {Parents | undefined} _3\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction doctype(_1, _2, _3, state) {\n  return (\n    '<!' +\n    (state.settings.upperDoctype ? 'DOCTYPE' : 'doctype') +\n    (state.settings.tightDoctype ? '' : ' ') +\n    'html>'\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLWh0bWwvbGliL2hhbmRsZS9kb2N0eXBlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLFlBQVksa0JBQWtCO0FBQzlCLFlBQVksT0FBTztBQUNuQjs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEI7QUFDQSxXQUFXLG9CQUFvQjtBQUMvQjtBQUNBLFdBQVcscUJBQXFCO0FBQ2hDO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcQ3Vyc29yIFByb2plY3RcXHdlYnNpdGVcXEF1Z21lbnQyXFxwcm9tcHQtbWFuYWdlclxcbm9kZV9tb2R1bGVzXFxoYXN0LXV0aWwtdG8taHRtbFxcbGliXFxoYW5kbGVcXGRvY3R5cGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtEb2N0eXBlLCBQYXJlbnRzfSBmcm9tICdoYXN0J1xuICogQGltcG9ydCB7U3RhdGV9IGZyb20gJy4uL2luZGV4LmpzJ1xuICovXG5cbi8qKlxuICogU2VyaWFsaXplIGEgZG9jdHlwZS5cbiAqXG4gKiBAcGFyYW0ge0RvY3R5cGV9IF8xXG4gKiAgIE5vZGUgdG8gaGFuZGxlLlxuICogQHBhcmFtIHtudW1iZXIgfCB1bmRlZmluZWR9IF8yXG4gKiAgIEluZGV4IG9mIGBub2RlYCBpbiBgcGFyZW50LlxuICogQHBhcmFtIHtQYXJlbnRzIHwgdW5kZWZpbmVkfSBfM1xuICogICBQYXJlbnQgb2YgYG5vZGVgLlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kIGFib3V0IHRoZSBjdXJyZW50IHN0YXRlLlxuICogQHJldHVybnMge3N0cmluZ31cbiAqICAgU2VyaWFsaXplZCBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gZG9jdHlwZShfMSwgXzIsIF8zLCBzdGF0ZSkge1xuICByZXR1cm4gKFxuICAgICc8IScgK1xuICAgIChzdGF0ZS5zZXR0aW5ncy51cHBlckRvY3R5cGUgPyAnRE9DVFlQRScgOiAnZG9jdHlwZScpICtcbiAgICAoc3RhdGUuc2V0dGluZ3MudGlnaHREb2N0eXBlID8gJycgOiAnICcpICtcbiAgICAnaHRtbD4nXG4gIClcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/handle/doctype.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/handle/element.js":
/*!**************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/handle/element.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   element: () => (/* binding */ element)\n/* harmony export */ });\n/* harmony import */ var ccount__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ccount */ \"(ssr)/./node_modules/ccount/index.js\");\n/* harmony import */ var comma_separated_tokens__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! comma-separated-tokens */ \"(ssr)/./node_modules/comma-separated-tokens/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/lib/find.js\");\n/* harmony import */ var space_separated_tokens__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! space-separated-tokens */ \"(ssr)/./node_modules/space-separated-tokens/index.js\");\n/* harmony import */ var stringify_entities__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! stringify-entities */ \"(ssr)/./node_modules/stringify-entities/lib/index.js\");\n/* harmony import */ var _omission_closing_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../omission/closing.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/omission/closing.js\");\n/* harmony import */ var _omission_opening_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../omission/opening.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/omission/opening.js\");\n/**\n * @import {Element, Parents, Properties} from 'hast'\n * @import {State} from '../index.js'\n */\n\n\n\n\n\n\n\n\n\n/**\n * Maps of subsets.\n *\n * Each value is a matrix of tuples.\n * The value at `0` causes parse errors, the value at `1` is valid.\n * Of both, the value at `0` is unsafe, and the value at `1` is safe.\n *\n * @type {Record<'double' | 'name' | 'single' | 'unquoted', Array<[Array<string>, Array<string>]>>}\n */\nconst constants = {\n  // See: <https://html.spec.whatwg.org/#attribute-name-state>.\n  name: [\n    ['\\t\\n\\f\\r &/=>'.split(''), '\\t\\n\\f\\r \"&\\'/=>`'.split('')],\n    ['\\0\\t\\n\\f\\r \"&\\'/<=>'.split(''), '\\0\\t\\n\\f\\r \"&\\'/<=>`'.split('')]\n  ],\n  // See: <https://html.spec.whatwg.org/#attribute-value-(unquoted)-state>.\n  unquoted: [\n    ['\\t\\n\\f\\r &>'.split(''), '\\0\\t\\n\\f\\r \"&\\'<=>`'.split('')],\n    ['\\0\\t\\n\\f\\r \"&\\'<=>`'.split(''), '\\0\\t\\n\\f\\r \"&\\'<=>`'.split('')]\n  ],\n  // See: <https://html.spec.whatwg.org/#attribute-value-(single-quoted)-state>.\n  single: [\n    [\"&'\".split(''), '\"&\\'`'.split('')],\n    [\"\\0&'\".split(''), '\\0\"&\\'`'.split('')]\n  ],\n  // See: <https://html.spec.whatwg.org/#attribute-value-(double-quoted)-state>.\n  double: [\n    ['\"&'.split(''), '\"&\\'`'.split('')],\n    ['\\0\"&'.split(''), '\\0\"&\\'`'.split('')]\n  ]\n}\n\n/**\n * Serialize an element node.\n *\n * @param {Element} node\n *   Node to handle.\n * @param {number | undefined} index\n *   Index of `node` in `parent.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction element(node, index, parent, state) {\n  const schema = state.schema\n  const omit = schema.space === 'svg' ? false : state.settings.omitOptionalTags\n  let selfClosing =\n    schema.space === 'svg'\n      ? state.settings.closeEmptyElements\n      : state.settings.voids.includes(node.tagName.toLowerCase())\n  /** @type {Array<string>} */\n  const parts = []\n  /** @type {string} */\n  let last\n\n  if (schema.space === 'html' && node.tagName === 'svg') {\n    state.schema = property_information__WEBPACK_IMPORTED_MODULE_0__.svg\n  }\n\n  const attributes = serializeAttributes(state, node.properties)\n\n  const content = state.all(\n    schema.space === 'html' && node.tagName === 'template' ? node.content : node\n  )\n\n  state.schema = schema\n\n  // If the node is categorised as void, but it has children, remove the\n  // categorisation.\n  // This enables for example `menuitem`s, which are void in W3C HTML but not\n  // void in WHATWG HTML, to be stringified properly.\n  // Note: `menuitem` has since been removed from the HTML spec, and so is no\n  // longer void.\n  if (content) selfClosing = false\n\n  if (attributes || !omit || !(0,_omission_opening_js__WEBPACK_IMPORTED_MODULE_1__.opening)(node, index, parent)) {\n    parts.push('<', node.tagName, attributes ? ' ' + attributes : '')\n\n    if (\n      selfClosing &&\n      (schema.space === 'svg' || state.settings.closeSelfClosing)\n    ) {\n      last = attributes.charAt(attributes.length - 1)\n      if (\n        !state.settings.tightSelfClosing ||\n        last === '/' ||\n        (last && last !== '\"' && last !== \"'\")\n      ) {\n        parts.push(' ')\n      }\n\n      parts.push('/')\n    }\n\n    parts.push('>')\n  }\n\n  parts.push(content)\n\n  if (!selfClosing && (!omit || !(0,_omission_closing_js__WEBPACK_IMPORTED_MODULE_2__.closing)(node, index, parent))) {\n    parts.push('</' + node.tagName + '>')\n  }\n\n  return parts.join('')\n}\n\n/**\n * @param {State} state\n * @param {Properties | null | undefined} properties\n * @returns {string}\n */\nfunction serializeAttributes(state, properties) {\n  /** @type {Array<string>} */\n  const values = []\n  let index = -1\n  /** @type {string} */\n  let key\n\n  if (properties) {\n    for (key in properties) {\n      if (properties[key] !== null && properties[key] !== undefined) {\n        const value = serializeAttribute(state, key, properties[key])\n        if (value) values.push(value)\n      }\n    }\n  }\n\n  while (++index < values.length) {\n    const last = state.settings.tightAttributes\n      ? values[index].charAt(values[index].length - 1)\n      : undefined\n\n    // In tight mode, don’t add a space after quoted attributes.\n    if (index !== values.length - 1 && last !== '\"' && last !== \"'\") {\n      values[index] += ' '\n    }\n  }\n\n  return values.join('')\n}\n\n/**\n * @param {State} state\n * @param {string} key\n * @param {Properties[keyof Properties]} value\n * @returns {string}\n */\nfunction serializeAttribute(state, key, value) {\n  const info = (0,property_information__WEBPACK_IMPORTED_MODULE_3__.find)(state.schema, key)\n  const x =\n    state.settings.allowParseErrors && state.schema.space === 'html' ? 0 : 1\n  const y = state.settings.allowDangerousCharacters ? 0 : 1\n  let quote = state.quote\n  /** @type {string | undefined} */\n  let result\n\n  if (info.overloadedBoolean && (value === info.attribute || value === '')) {\n    value = true\n  } else if (\n    (info.boolean || info.overloadedBoolean) &&\n    (typeof value !== 'string' || value === info.attribute || value === '')\n  ) {\n    value = Boolean(value)\n  }\n\n  if (\n    value === null ||\n    value === undefined ||\n    value === false ||\n    (typeof value === 'number' && Number.isNaN(value))\n  ) {\n    return ''\n  }\n\n  const name = (0,stringify_entities__WEBPACK_IMPORTED_MODULE_4__.stringifyEntities)(\n    info.attribute,\n    Object.assign({}, state.settings.characterReferences, {\n      // Always encode without parse errors in non-HTML.\n      subset: constants.name[x][y]\n    })\n  )\n\n  // No value.\n  // There is currently only one boolean property in SVG: `[download]` on\n  // `<a>`.\n  // This property does not seem to work in browsers (Firefox, Safari, Chrome),\n  // so I can’t test if dropping the value works.\n  // But I assume that it should:\n  //\n  // ```html\n  // <!doctype html>\n  // <svg viewBox=\"0 0 100 100\">\n  //   <a href=https://example.com download>\n  //     <circle cx=50 cy=40 r=35 />\n  //   </a>\n  // </svg>\n  // ```\n  //\n  // See: <https://github.com/wooorm/property-information/blob/main/lib/svg.js>\n  if (value === true) return name\n\n  // `spaces` doesn’t accept a second argument, but it’s given here just to\n  // keep the code cleaner.\n  value = Array.isArray(value)\n    ? (info.commaSeparated ? comma_separated_tokens__WEBPACK_IMPORTED_MODULE_5__.stringify : space_separated_tokens__WEBPACK_IMPORTED_MODULE_6__.stringify)(value, {\n        padLeft: !state.settings.tightCommaSeparatedLists\n      })\n    : String(value)\n\n  if (state.settings.collapseEmptyAttributes && !value) return name\n\n  // Check unquoted value.\n  if (state.settings.preferUnquoted) {\n    result = (0,stringify_entities__WEBPACK_IMPORTED_MODULE_4__.stringifyEntities)(\n      value,\n      Object.assign({}, state.settings.characterReferences, {\n        attribute: true,\n        subset: constants.unquoted[x][y]\n      })\n    )\n  }\n\n  // If we don’t want unquoted, or if `value` contains character references when\n  // unquoted…\n  if (result !== value) {\n    // If the alternative is less common than `quote`, switch.\n    if (\n      state.settings.quoteSmart &&\n      (0,ccount__WEBPACK_IMPORTED_MODULE_7__.ccount)(value, quote) > (0,ccount__WEBPACK_IMPORTED_MODULE_7__.ccount)(value, state.alternative)\n    ) {\n      quote = state.alternative\n    }\n\n    result =\n      quote +\n      (0,stringify_entities__WEBPACK_IMPORTED_MODULE_4__.stringifyEntities)(\n        value,\n        Object.assign({}, state.settings.characterReferences, {\n          // Always encode without parse errors in non-HTML.\n          subset: (quote === \"'\" ? constants.single : constants.double)[x][y],\n          attribute: true\n        })\n      ) +\n      quote\n  }\n\n  // Don’t add a `=` for unquoted empties.\n  return name + (result ? '=' + result : result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/handle/element.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/handle/index.js":
/*!************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/handle/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handle: () => (/* binding */ handle)\n/* harmony export */ });\n/* harmony import */ var zwitch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zwitch */ \"(ssr)/./node_modules/zwitch/index.js\");\n/* harmony import */ var _comment_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./comment.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/handle/comment.js\");\n/* harmony import */ var _doctype_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./doctype.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/handle/doctype.js\");\n/* harmony import */ var _element_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./element.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/handle/element.js\");\n/* harmony import */ var _raw_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./raw.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/handle/raw.js\");\n/* harmony import */ var _root_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./root.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/handle/root.js\");\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./text.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/handle/text.js\");\n/**\n * @import {Nodes, Parents} from 'hast'\n * @import {State} from '../index.js'\n */\n\n\n\n\n\n\n\n\n\n/**\n * @type {(node: Nodes, index: number | undefined, parent: Parents | undefined, state: State) => string}\n */\nconst handle = (0,zwitch__WEBPACK_IMPORTED_MODULE_0__.zwitch)('type', {\n  invalid,\n  unknown,\n  handlers: {comment: _comment_js__WEBPACK_IMPORTED_MODULE_1__.comment, doctype: _doctype_js__WEBPACK_IMPORTED_MODULE_2__.doctype, element: _element_js__WEBPACK_IMPORTED_MODULE_3__.element, raw: _raw_js__WEBPACK_IMPORTED_MODULE_4__.raw, root: _root_js__WEBPACK_IMPORTED_MODULE_5__.root, text: _text_js__WEBPACK_IMPORTED_MODULE_6__.text}\n})\n\n/**\n * Fail when a non-node is found in the tree.\n *\n * @param {unknown} node\n *   Unknown value.\n * @returns {never}\n *   Never.\n */\nfunction invalid(node) {\n  throw new Error('Expected node, not `' + node + '`')\n}\n\n/**\n * Fail when a node with an unknown type is found in the tree.\n *\n * @param {unknown} node_\n *  Unknown node.\n * @returns {never}\n *   Never.\n */\nfunction unknown(node_) {\n  // `type` is guaranteed by runtime JS.\n  const node = /** @type {Nodes} */ (node_)\n  throw new Error('Cannot compile unknown node `' + node.type + '`')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/handle/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/handle/raw.js":
/*!**********************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/handle/raw.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   raw: () => (/* binding */ raw)\n/* harmony export */ });\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./text.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/handle/text.js\");\n/**\n * @import {Parents} from 'hast'\n * @import {Raw} from 'mdast-util-to-hast'\n * @import {State} from '../index.js'\n */\n\n\n\n/**\n * Serialize a raw node.\n *\n * @param {Raw} node\n *   Node to handle.\n * @param {number | undefined} index\n *   Index of `node` in `parent.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction raw(node, index, parent, state) {\n  return state.settings.allowDangerousHtml\n    ? node.value\n    : (0,_text_js__WEBPACK_IMPORTED_MODULE_0__.text)(node, index, parent, state)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLWh0bWwvbGliL2hhbmRsZS9yYXcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLFlBQVksU0FBUztBQUNyQixZQUFZLEtBQUs7QUFDakIsWUFBWSxPQUFPO0FBQ25COztBQUU4Qjs7QUFFOUI7QUFDQTtBQUNBO0FBQ0EsV0FBVyxLQUFLO0FBQ2hCO0FBQ0EsV0FBVyxvQkFBb0I7QUFDL0I7QUFDQSxXQUFXLHFCQUFxQjtBQUNoQztBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0EsTUFBTSw4Q0FBSTtBQUNWIiwic291cmNlcyI6WyJEOlxcQ3Vyc29yIFByb2plY3RcXHdlYnNpdGVcXEF1Z21lbnQyXFxwcm9tcHQtbWFuYWdlclxcbm9kZV9tb2R1bGVzXFxoYXN0LXV0aWwtdG8taHRtbFxcbGliXFxoYW5kbGVcXHJhdy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge1BhcmVudHN9IGZyb20gJ2hhc3QnXG4gKiBAaW1wb3J0IHtSYXd9IGZyb20gJ21kYXN0LXV0aWwtdG8taGFzdCdcbiAqIEBpbXBvcnQge1N0YXRlfSBmcm9tICcuLi9pbmRleC5qcydcbiAqL1xuXG5pbXBvcnQge3RleHR9IGZyb20gJy4vdGV4dC5qcydcblxuLyoqXG4gKiBTZXJpYWxpemUgYSByYXcgbm9kZS5cbiAqXG4gKiBAcGFyYW0ge1Jhd30gbm9kZVxuICogICBOb2RlIHRvIGhhbmRsZS5cbiAqIEBwYXJhbSB7bnVtYmVyIHwgdW5kZWZpbmVkfSBpbmRleFxuICogICBJbmRleCBvZiBgbm9kZWAgaW4gYHBhcmVudC5cbiAqIEBwYXJhbSB7UGFyZW50cyB8IHVuZGVmaW5lZH0gcGFyZW50XG4gKiAgIFBhcmVudCBvZiBgbm9kZWAuXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQgYWJvdXQgdGhlIGN1cnJlbnQgc3RhdGUuXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICogICBTZXJpYWxpemVkIG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiByYXcobm9kZSwgaW5kZXgsIHBhcmVudCwgc3RhdGUpIHtcbiAgcmV0dXJuIHN0YXRlLnNldHRpbmdzLmFsbG93RGFuZ2Vyb3VzSHRtbFxuICAgID8gbm9kZS52YWx1ZVxuICAgIDogdGV4dChub2RlLCBpbmRleCwgcGFyZW50LCBzdGF0ZSlcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/handle/raw.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/handle/root.js":
/*!***********************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/handle/root.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   root: () => (/* binding */ root)\n/* harmony export */ });\n/**\n * @import {Parents, Root} from 'hast'\n * @import {State} from '../index.js'\n */\n\n/**\n * Serialize a root.\n *\n * @param {Root} node\n *   Node to handle.\n * @param {number | undefined} _1\n *   Index of `node` in `parent.\n * @param {Parents | undefined} _2\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction root(node, _1, _2, state) {\n  return state.all(node)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLWh0bWwvbGliL2hhbmRsZS9yb290LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLFlBQVksZUFBZTtBQUMzQixZQUFZLE9BQU87QUFDbkI7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxNQUFNO0FBQ2pCO0FBQ0EsV0FBVyxvQkFBb0I7QUFDL0I7QUFDQSxXQUFXLHFCQUFxQjtBQUNoQztBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUDtBQUNBIiwic291cmNlcyI6WyJEOlxcQ3Vyc29yIFByb2plY3RcXHdlYnNpdGVcXEF1Z21lbnQyXFxwcm9tcHQtbWFuYWdlclxcbm9kZV9tb2R1bGVzXFxoYXN0LXV0aWwtdG8taHRtbFxcbGliXFxoYW5kbGVcXHJvb3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtQYXJlbnRzLCBSb290fSBmcm9tICdoYXN0J1xuICogQGltcG9ydCB7U3RhdGV9IGZyb20gJy4uL2luZGV4LmpzJ1xuICovXG5cbi8qKlxuICogU2VyaWFsaXplIGEgcm9vdC5cbiAqXG4gKiBAcGFyYW0ge1Jvb3R9IG5vZGVcbiAqICAgTm9kZSB0byBoYW5kbGUuXG4gKiBAcGFyYW0ge251bWJlciB8IHVuZGVmaW5lZH0gXzFcbiAqICAgSW5kZXggb2YgYG5vZGVgIGluIGBwYXJlbnQuXG4gKiBAcGFyYW0ge1BhcmVudHMgfCB1bmRlZmluZWR9IF8yXG4gKiAgIFBhcmVudCBvZiBgbm9kZWAuXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQgYWJvdXQgdGhlIGN1cnJlbnQgc3RhdGUuXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICogICBTZXJpYWxpemVkIG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiByb290KG5vZGUsIF8xLCBfMiwgc3RhdGUpIHtcbiAgcmV0dXJuIHN0YXRlLmFsbChub2RlKVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/handle/root.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/handle/text.js":
/*!***********************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/handle/text.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/* harmony import */ var stringify_entities__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stringify-entities */ \"(ssr)/./node_modules/stringify-entities/lib/index.js\");\n/**\n * @import {Parents, Text} from 'hast'\n * @import {Raw} from 'mdast-util-to-hast'\n * @import {State} from '../index.js'\n */\n\n\n\n// Declare array as variable so it can be cached by `stringifyEntities`\nconst textEntitySubset = ['<', '&']\n\n/**\n * Serialize a text node.\n *\n * @param {Raw | Text} node\n *   Node to handle.\n * @param {number | undefined} _\n *   Index of `node` in `parent.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction text(node, _, parent, state) {\n  // Check if content of `node` should be escaped.\n  return parent &&\n    parent.type === 'element' &&\n    (parent.tagName === 'script' || parent.tagName === 'style')\n    ? node.value\n    : (0,stringify_entities__WEBPACK_IMPORTED_MODULE_0__.stringifyEntities)(\n        node.value,\n        Object.assign({}, state.settings.characterReferences, {\n          subset: textEntitySubset\n        })\n      )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/handle/text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   all: () => (/* binding */ all),\n/* harmony export */   toHtml: () => (/* binding */ toHtml)\n/* harmony export */ });\n/* harmony import */ var html_void_elements__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! html-void-elements */ \"(ssr)/./node_modules/html-void-elements/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/index.js\");\n/* harmony import */ var _handle_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./handle/index.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/handle/index.js\");\n/**\n * @import {Nodes, Parents, RootContent} from 'hast'\n * @import {Schema} from 'property-information'\n * @import {Options as StringifyEntitiesOptions} from 'stringify-entities'\n */\n\n/**\n * @typedef {Omit<StringifyEntitiesOptions, 'attribute' | 'escapeOnly' | 'subset'>} CharacterReferences\n *\n * @typedef Options\n *   Configuration.\n * @property {boolean | null | undefined} [allowDangerousCharacters=false]\n *   Do not encode some characters which cause XSS vulnerabilities in older\n *   browsers (default: `false`).\n *\n *   > ⚠️ **Danger**: only set this if you completely trust the content.\n * @property {boolean | null | undefined} [allowDangerousHtml=false]\n *   Allow `raw` nodes and insert them as raw HTML (default: `false`).\n *\n *   When `false`, `Raw` nodes are encoded.\n *\n *   > ⚠️ **Danger**: only set this if you completely trust the content.\n * @property {boolean | null | undefined} [allowParseErrors=false]\n *   Do not encode characters which cause parse errors (even though they work),\n *   to save bytes (default: `false`).\n *\n *   Not used in the SVG space.\n *\n *   > 👉 **Note**: intentionally creates parse errors in markup (how parse\n *   > errors are handled is well defined, so this works but isn’t pretty).\n * @property {boolean | null | undefined} [bogusComments=false]\n *   Use “bogus comments” instead of comments to save byes: `<?charlie>`\n *   instead of `<!--charlie-->` (default: `false`).\n *\n *   > 👉 **Note**: intentionally creates parse errors in markup (how parse\n *   > errors are handled is well defined, so this works but isn’t pretty).\n * @property {CharacterReferences | null | undefined} [characterReferences]\n *   Configure how to serialize character references (optional).\n * @property {boolean | null | undefined} [closeEmptyElements=false]\n *   Close SVG elements without any content with slash (`/`) on the opening tag\n *   instead of an end tag: `<circle />` instead of `<circle></circle>`\n *   (default: `false`).\n *\n *   See `tightSelfClosing` to control whether a space is used before the\n *   slash.\n *\n *   Not used in the HTML space.\n * @property {boolean | null | undefined} [closeSelfClosing=false]\n *   Close self-closing nodes with an extra slash (`/`): `<img />` instead of\n *   `<img>` (default: `false`).\n *\n *   See `tightSelfClosing` to control whether a space is used before the\n *   slash.\n *\n *   Not used in the SVG space.\n * @property {boolean | null | undefined} [collapseEmptyAttributes=false]\n *   Collapse empty attributes: get `class` instead of `class=\"\"` (default:\n *   `false`).\n *\n *   Not used in the SVG space.\n *\n *   > 👉 **Note**: boolean attributes (such as `hidden`) are always collapsed.\n * @property {boolean | null | undefined} [omitOptionalTags=false]\n *   Omit optional opening and closing tags (default: `false`).\n *\n *   For example, in `<ol><li>one</li><li>two</li></ol>`, both `</li>` closing\n *   tags can be omitted.\n *   The first because it’s followed by another `li`, the last because it’s\n *   followed by nothing.\n *\n *   Not used in the SVG space.\n * @property {boolean | null | undefined} [preferUnquoted=false]\n *   Leave attributes unquoted if that results in less bytes (default: `false`).\n *\n *   Not used in the SVG space.\n * @property {boolean | null | undefined} [quoteSmart=false]\n *   Use the other quote if that results in less bytes (default: `false`).\n * @property {Quote | null | undefined} [quote='\"']\n *   Preferred quote to use (default: `'\"'`).\n * @property {Space | null | undefined} [space='html']\n *   When an `<svg>` element is found in the HTML space, this package already\n *   automatically switches to and from the SVG space when entering and exiting\n *   it (default: `'html'`).\n *\n *   > 👉 **Note**: hast is not XML.\n *   > It supports SVG as embedded in HTML.\n *   > It does not support the features available in XML.\n *   > Passing SVG might break but fragments of modern SVG should be fine.\n *   > Use [`xast`][xast] if you need to support SVG as XML.\n * @property {boolean | null | undefined} [tightAttributes=false]\n *   Join attributes together, without whitespace, if possible: get\n *   `class=\"a b\"title=\"c d\"` instead of `class=\"a b\" title=\"c d\"` to save\n *   bytes (default: `false`).\n *\n *   Not used in the SVG space.\n *\n *   > 👉 **Note**: intentionally creates parse errors in markup (how parse\n *   > errors are handled is well defined, so this works but isn’t pretty).\n * @property {boolean | null | undefined} [tightCommaSeparatedLists=false]\n *   Join known comma-separated attribute values with just a comma (`,`),\n *   instead of padding them on the right as well (`,␠`, where `␠` represents a\n *   space) (default: `false`).\n * @property {boolean | null | undefined} [tightDoctype=false]\n *   Drop unneeded spaces in doctypes: `<!doctypehtml>` instead of\n *   `<!doctype html>` to save bytes (default: `false`).\n *\n *   > 👉 **Note**: intentionally creates parse errors in markup (how parse\n *   > errors are handled is well defined, so this works but isn’t pretty).\n * @property {boolean | null | undefined} [tightSelfClosing=false]\n *   Do not use an extra space when closing self-closing elements: `<img/>`\n *   instead of `<img />` (default: `false`).\n *\n *   > 👉 **Note**: only used if `closeSelfClosing: true` or\n *   > `closeEmptyElements: true`.\n * @property {boolean | null | undefined} [upperDoctype=false]\n *   Use a `<!DOCTYPE…` instead of `<!doctype…` (default: `false`).\n *\n *   Useless except for XHTML.\n * @property {ReadonlyArray<string> | null | undefined} [voids]\n *   Tag names of elements to serialize without closing tag (default: `html-void-elements`).\n *\n *   Not used in the SVG space.\n *\n *   > 👉 **Note**: It’s highly unlikely that you want to pass this, because\n *   > hast is not for XML, and HTML will not add more void elements.\n *\n * @typedef {'\"' | \"'\"} Quote\n *   HTML quotes for attribute values.\n *\n * @typedef {Omit<Required<{[key in keyof Options]: Exclude<Options[key], null | undefined>}>, 'space' | 'quote'>} Settings\n *\n * @typedef {'html' | 'svg'} Space\n *   Namespace.\n *\n * @typedef State\n *   Info passed around about the current state.\n * @property {(node: Parents | undefined) => string} all\n *   Serialize the children of a parent node.\n * @property {Quote} alternative\n *   Alternative quote.\n * @property {(node: Nodes, index: number | undefined, parent: Parents | undefined) => string} one\n *   Serialize one node.\n * @property {Quote} quote\n *   Preferred quote.\n * @property {Schema} schema\n *   Current schema.\n * @property {Settings} settings\n *   User configuration.\n */\n\n\n\n\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/** @type {CharacterReferences} */\nconst emptyCharacterReferences = {}\n\n/** @type {Array<never>} */\nconst emptyChildren = []\n\n/**\n * Serialize hast as HTML.\n *\n * @param {Array<RootContent> | Nodes} tree\n *   Tree to serialize.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized HTML.\n */\nfunction toHtml(tree, options) {\n  const options_ = options || emptyOptions\n  const quote = options_.quote || '\"'\n  const alternative = quote === '\"' ? \"'\" : '\"'\n\n  if (quote !== '\"' && quote !== \"'\") {\n    throw new Error('Invalid quote `' + quote + '`, expected `\\'` or `\"`')\n  }\n\n  /** @type {State} */\n  const state = {\n    one,\n    all,\n    settings: {\n      omitOptionalTags: options_.omitOptionalTags || false,\n      allowParseErrors: options_.allowParseErrors || false,\n      allowDangerousCharacters: options_.allowDangerousCharacters || false,\n      quoteSmart: options_.quoteSmart || false,\n      preferUnquoted: options_.preferUnquoted || false,\n      tightAttributes: options_.tightAttributes || false,\n      upperDoctype: options_.upperDoctype || false,\n      tightDoctype: options_.tightDoctype || false,\n      bogusComments: options_.bogusComments || false,\n      tightCommaSeparatedLists: options_.tightCommaSeparatedLists || false,\n      tightSelfClosing: options_.tightSelfClosing || false,\n      collapseEmptyAttributes: options_.collapseEmptyAttributes || false,\n      allowDangerousHtml: options_.allowDangerousHtml || false,\n      voids: options_.voids || html_void_elements__WEBPACK_IMPORTED_MODULE_0__.htmlVoidElements,\n      characterReferences:\n        options_.characterReferences || emptyCharacterReferences,\n      closeSelfClosing: options_.closeSelfClosing || false,\n      closeEmptyElements: options_.closeEmptyElements || false\n    },\n    schema: options_.space === 'svg' ? property_information__WEBPACK_IMPORTED_MODULE_1__.svg : property_information__WEBPACK_IMPORTED_MODULE_1__.html,\n    quote,\n    alternative\n  }\n\n  return state.one(\n    Array.isArray(tree) ? {type: 'root', children: tree} : tree,\n    undefined,\n    undefined\n  )\n}\n\n/**\n * Serialize a node.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {Nodes} node\n *   Node to handle.\n * @param {number | undefined} index\n *   Index of `node` in `parent.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {string}\n *   Serialized node.\n */\nfunction one(node, index, parent) {\n  return (0,_handle_index_js__WEBPACK_IMPORTED_MODULE_2__.handle)(node, index, parent, this)\n}\n\n/**\n * Serialize all children of `parent`.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {Parents | undefined} parent\n *   Parent whose children to serialize.\n * @returns {string}\n */\nfunction all(parent) {\n  /** @type {Array<string>} */\n  const results = []\n  const children = (parent && parent.children) || emptyChildren\n  let index = -1\n\n  while (++index < children.length) {\n    results[index] = this.one(children[index], index, parent)\n  }\n\n  return results.join('')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/omission/closing.js":
/*!****************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/omission/closing.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   closing: () => (/* binding */ closing)\n/* harmony export */ });\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-whitespace */ \"(ssr)/./node_modules/hast-util-whitespace/lib/index.js\");\n/* harmony import */ var _util_siblings_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/siblings.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/omission/util/siblings.js\");\n/* harmony import */ var _omission_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./omission.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/omission/omission.js\");\n/**\n * @import {Element, Parents} from 'hast'\n */\n\n\n\n\n\nconst closing = (0,_omission_js__WEBPACK_IMPORTED_MODULE_0__.omission)({\n  body,\n  caption: headOrColgroupOrCaption,\n  colgroup: headOrColgroupOrCaption,\n  dd,\n  dt,\n  head: headOrColgroupOrCaption,\n  html,\n  li,\n  optgroup,\n  option,\n  p,\n  rp: rubyElement,\n  rt: rubyElement,\n  tbody,\n  td: cells,\n  tfoot,\n  th: cells,\n  thead,\n  tr\n})\n\n/**\n * Macro for `</head>`, `</colgroup>`, and `</caption>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction headOrColgroupOrCaption(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index, true)\n  return (\n    !next ||\n    (next.type !== 'comment' &&\n      !(next.type === 'text' && (0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__.whitespace)(next.value.charAt(0))))\n  )\n}\n\n/**\n * Whether to omit `</html>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction html(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return !next || next.type !== 'comment'\n}\n\n/**\n * Whether to omit `</body>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction body(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return !next || next.type !== 'comment'\n}\n\n/**\n * Whether to omit `</p>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction p(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return next\n    ? next.type === 'element' &&\n        (next.tagName === 'address' ||\n          next.tagName === 'article' ||\n          next.tagName === 'aside' ||\n          next.tagName === 'blockquote' ||\n          next.tagName === 'details' ||\n          next.tagName === 'div' ||\n          next.tagName === 'dl' ||\n          next.tagName === 'fieldset' ||\n          next.tagName === 'figcaption' ||\n          next.tagName === 'figure' ||\n          next.tagName === 'footer' ||\n          next.tagName === 'form' ||\n          next.tagName === 'h1' ||\n          next.tagName === 'h2' ||\n          next.tagName === 'h3' ||\n          next.tagName === 'h4' ||\n          next.tagName === 'h5' ||\n          next.tagName === 'h6' ||\n          next.tagName === 'header' ||\n          next.tagName === 'hgroup' ||\n          next.tagName === 'hr' ||\n          next.tagName === 'main' ||\n          next.tagName === 'menu' ||\n          next.tagName === 'nav' ||\n          next.tagName === 'ol' ||\n          next.tagName === 'p' ||\n          next.tagName === 'pre' ||\n          next.tagName === 'section' ||\n          next.tagName === 'table' ||\n          next.tagName === 'ul')\n    : !parent ||\n        // Confusing parent.\n        !(\n          parent.type === 'element' &&\n          (parent.tagName === 'a' ||\n            parent.tagName === 'audio' ||\n            parent.tagName === 'del' ||\n            parent.tagName === 'ins' ||\n            parent.tagName === 'map' ||\n            parent.tagName === 'noscript' ||\n            parent.tagName === 'video')\n        )\n}\n\n/**\n * Whether to omit `</li>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction li(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return !next || (next.type === 'element' && next.tagName === 'li')\n}\n\n/**\n * Whether to omit `</dt>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction dt(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return Boolean(\n    next &&\n      next.type === 'element' &&\n      (next.tagName === 'dt' || next.tagName === 'dd')\n  )\n}\n\n/**\n * Whether to omit `</dd>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction dd(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'dt' || next.tagName === 'dd'))\n  )\n}\n\n/**\n * Whether to omit `</rt>` or `</rp>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction rubyElement(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'rp' || next.tagName === 'rt'))\n  )\n}\n\n/**\n * Whether to omit `</optgroup>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction optgroup(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return !next || (next.type === 'element' && next.tagName === 'optgroup')\n}\n\n/**\n * Whether to omit `</option>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction option(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'option' || next.tagName === 'optgroup'))\n  )\n}\n\n/**\n * Whether to omit `</thead>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction thead(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return Boolean(\n    next &&\n      next.type === 'element' &&\n      (next.tagName === 'tbody' || next.tagName === 'tfoot')\n  )\n}\n\n/**\n * Whether to omit `</tbody>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction tbody(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'tbody' || next.tagName === 'tfoot'))\n  )\n}\n\n/**\n * Whether to omit `</tfoot>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction tfoot(_, index, parent) {\n  return !(0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n}\n\n/**\n * Whether to omit `</tr>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction tr(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return !next || (next.type === 'element' && next.tagName === 'tr')\n}\n\n/**\n * Whether to omit `</td>` or `</th>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction cells(_, index, parent) {\n  const next = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'td' || next.tagName === 'th'))\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/omission/closing.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/omission/omission.js":
/*!*****************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/omission/omission.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   omission: () => (/* binding */ omission)\n/* harmony export */ });\n/**\n * @import {Element, Parents} from 'hast'\n */\n\n/**\n * @callback OmitHandle\n *   Check if a tag can be omitted.\n * @param {Element} element\n *   Element to check.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether to omit a tag.\n *\n */\n\nconst own = {}.hasOwnProperty\n\n/**\n * Factory to check if a given node can have a tag omitted.\n *\n * @param {Record<string, OmitHandle>} handlers\n *   Omission handlers, where each key is a tag name, and each value is the\n *   corresponding handler.\n * @returns {OmitHandle}\n *   Whether to omit a tag of an element.\n */\nfunction omission(handlers) {\n  return omit\n\n  /**\n   * Check if a given node can have a tag omitted.\n   *\n   * @type {OmitHandle}\n   */\n  function omit(node, index, parent) {\n    return (\n      own.call(handlers, node.tagName) &&\n      handlers[node.tagName](node, index, parent)\n    )\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/omission/omission.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/omission/opening.js":
/*!****************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/omission/opening.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   opening: () => (/* binding */ opening)\n/* harmony export */ });\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-whitespace */ \"(ssr)/./node_modules/hast-util-whitespace/lib/index.js\");\n/* harmony import */ var _util_siblings_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/siblings.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/omission/util/siblings.js\");\n/* harmony import */ var _closing_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./closing.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/omission/closing.js\");\n/* harmony import */ var _omission_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./omission.js */ \"(ssr)/./node_modules/hast-util-to-html/lib/omission/omission.js\");\n/**\n * @import {Element, Parents} from 'hast'\n */\n\n\n\n\n\n\nconst opening = (0,_omission_js__WEBPACK_IMPORTED_MODULE_0__.omission)({\n  body,\n  colgroup,\n  head,\n  html,\n  tbody\n})\n\n/**\n * Whether to omit `<html>`.\n *\n * @param {Element} node\n *   Element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction html(node) {\n  const head = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(node, -1)\n  return !head || head.type !== 'comment'\n}\n\n/**\n * Whether to omit `<head>`.\n *\n * @param {Element} node\n *   Element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction head(node) {\n  /** @type {Set<string>} */\n  const seen = new Set()\n\n  // Whether `srcdoc` or not,\n  // make sure the content model at least doesn’t have too many `base`s/`title`s.\n  for (const child of node.children) {\n    if (\n      child.type === 'element' &&\n      (child.tagName === 'base' || child.tagName === 'title')\n    ) {\n      if (seen.has(child.tagName)) return false\n      seen.add(child.tagName)\n    }\n  }\n\n  // “May be omitted if the element is empty,\n  // or if the first thing inside the head element is an element.”\n  const child = node.children[0]\n  return !child || child.type === 'element'\n}\n\n/**\n * Whether to omit `<body>`.\n *\n * @param {Element} node\n *   Element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction body(node) {\n  const head = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(node, -1, true)\n\n  return (\n    !head ||\n    (head.type !== 'comment' &&\n      !(head.type === 'text' && (0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__.whitespace)(head.value.charAt(0))) &&\n      !(\n        head.type === 'element' &&\n        (head.tagName === 'meta' ||\n          head.tagName === 'link' ||\n          head.tagName === 'script' ||\n          head.tagName === 'style' ||\n          head.tagName === 'template')\n      ))\n  )\n}\n\n/**\n * Whether to omit `<colgroup>`.\n * The spec describes some logic for the opening tag, but it’s easier to\n * implement in the closing tag, to the same effect, so we handle it there\n * instead.\n *\n * @param {Element} node\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction colgroup(node, index, parent) {\n  const previous = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingBefore)(parent, index)\n  const head = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(node, -1, true)\n\n  // Previous colgroup was already omitted.\n  if (\n    parent &&\n    previous &&\n    previous.type === 'element' &&\n    previous.tagName === 'colgroup' &&\n    (0,_closing_js__WEBPACK_IMPORTED_MODULE_3__.closing)(previous, parent.children.indexOf(previous), parent)\n  ) {\n    return false\n  }\n\n  return Boolean(head && head.type === 'element' && head.tagName === 'col')\n}\n\n/**\n * Whether to omit `<tbody>`.\n *\n * @param {Element} node\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction tbody(node, index, parent) {\n  const previous = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingBefore)(parent, index)\n  const head = (0,_util_siblings_js__WEBPACK_IMPORTED_MODULE_1__.siblingAfter)(node, -1)\n\n  // Previous table section was already omitted.\n  if (\n    parent &&\n    previous &&\n    previous.type === 'element' &&\n    (previous.tagName === 'thead' || previous.tagName === 'tbody') &&\n    (0,_closing_js__WEBPACK_IMPORTED_MODULE_3__.closing)(previous, parent.children.indexOf(previous), parent)\n  ) {\n    return false\n  }\n\n  return Boolean(head && head.type === 'element' && head.tagName === 'tr')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/omission/opening.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-to-html/lib/omission/util/siblings.js":
/*!**********************************************************************!*\
  !*** ./node_modules/hast-util-to-html/lib/omission/util/siblings.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   siblingAfter: () => (/* binding */ siblingAfter),\n/* harmony export */   siblingBefore: () => (/* binding */ siblingBefore)\n/* harmony export */ });\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-whitespace */ \"(ssr)/./node_modules/hast-util-whitespace/lib/index.js\");\n/**\n * @import {Parents, RootContent} from 'hast'\n */\n\n\n\nconst siblingAfter = siblings(1)\nconst siblingBefore = siblings(-1)\n\n/** @type {Array<RootContent>} */\nconst emptyChildren = []\n\n/**\n * Factory to check siblings in a direction.\n *\n * @param {number} increment\n */\nfunction siblings(increment) {\n  return sibling\n\n  /**\n   * Find applicable siblings in a direction.\n   *\n   * @template {Parents} Parent\n   *   Parent type.\n   * @param {Parent | undefined} parent\n   *   Parent.\n   * @param {number | undefined} index\n   *   Index of child in `parent`.\n   * @param {boolean | undefined} [includeWhitespace=false]\n   *   Whether to include whitespace (default: `false`).\n   * @returns {Parent extends {children: Array<infer Child>} ? Child | undefined : never}\n   *   Child of parent.\n   */\n  function sibling(parent, index, includeWhitespace) {\n    const siblings = parent ? parent.children : emptyChildren\n    let offset = (index || 0) + increment\n    let next = siblings[offset]\n\n    if (!includeWhitespace) {\n      while (next && (0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_0__.whitespace)(next)) {\n        offset += increment\n        next = siblings[offset]\n      }\n    }\n\n    // @ts-expect-error: it’s a correct child.\n    return next\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-html/lib/omission/util/siblings.js\n");

/***/ })

};
;