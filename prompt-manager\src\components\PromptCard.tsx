'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { toast } from 'react-hot-toast'
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from '~/components/ui/Card'
import { Badge } from '~/components/ui/Badge'
import { Button } from '~/components/ui/Button'
import { useModals } from '~/hooks/useStore'
import { copyToClipboard, formatRelativeTime, truncateText } from '~/lib/utils'
import type { Prompt } from '~/store'

interface PromptCardProps {
  prompt: Prompt
  onCopy?: (promptId: string) => void
  showActions?: boolean
  className?: string
}

export function PromptCard({ 
  prompt, 
  onCopy, 
  showActions = true,
  className 
}: PromptCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  const { openPromptDetail, openEditPrompt } = useModals()

  const handleCopy = async (e: React.MouseEvent) => {
    e.stopPropagation()
    
    const success = await copyToClipboard(prompt.content)
    if (success) {
      toast.success('提示词已复制到剪贴板')
      onCopy?.(prompt.id)
    } else {
      toast.error('复制失败，请重试')
    }
  }

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation()
    openEditPrompt(prompt)
  }

  const handleCardClick = () => {
    openPromptDetail(prompt)
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      whileHover={{
        y: -8,
        scale: 1.02,
        boxShadow: "0 20px 40px -12px rgba(0, 0, 0, 0.15), 0 8px 16px -8px rgba(0, 0, 0, 0.1)"
      }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      className={className}
    >
      <Card
        className="h-full cursor-pointer transition-all duration-300 hover:shadow-2xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 overflow-hidden group relative"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={handleCardClick}
      >
        {/* 装饰性渐变背景 */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-transparent to-purple-50/50 dark:from-slate-800/50 dark:to-slate-700/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        <CardHeader className="pb-3 relative z-10">
          <div className="flex items-start justify-between">
            <CardTitle className="text-lg font-bold line-clamp-2 bg-gradient-to-r from-slate-900 to-slate-700 dark:from-slate-100 dark:to-slate-300 bg-clip-text text-transparent">
              {prompt.title}
            </CardTitle>

            {/* 使用次数徽章 */}
            <div className="ml-2 shrink-0 relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full blur opacity-50"></div>
              <Badge className="relative bg-gradient-to-r from-blue-500 to-purple-500 text-white border-0 shadow-lg">
                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                {prompt.usageCount}次
              </Badge>
            </div>
          </div>

          {/* 分类标签 */}
          {prompt.category && (
            <div className="flex items-center gap-2 mt-3">
              <div className="relative">
                <div
                  className="w-4 h-4 rounded-full shadow-lg"
                  style={{ backgroundColor: prompt.category.color }}
                />
                <div
                  className="absolute inset-0 w-4 h-4 rounded-full blur opacity-50"
                  style={{ backgroundColor: prompt.category.color }}
                />
              </div>
              <span className="text-sm font-medium text-slate-600 dark:text-slate-400 bg-white/60 dark:bg-slate-700/60 px-2 py-1 rounded-lg backdrop-blur-sm">
                {prompt.category.name}
              </span>
            </div>
          )}
        </CardHeader>

        <CardContent className="pb-3 relative z-10">
          {/* 描述 */}
          {prompt.description && (
            <p className="text-sm text-slate-600 dark:text-slate-400 mb-4 line-clamp-2 font-medium">
              {prompt.description}
            </p>
          )}

          {/* 内容预览 */}
          <div className="text-sm bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-700 rounded-xl p-4 mb-4 border border-slate-200/50 dark:border-slate-600/50 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 to-purple-50/30 dark:from-blue-900/10 dark:to-purple-900/10"></div>
            <p className="line-clamp-3 relative z-10 text-slate-700 dark:text-slate-300 leading-relaxed">
              {truncateText(prompt.content, 150)}
            </p>
            <div className="absolute bottom-0 right-0 w-8 h-8 bg-gradient-to-tl from-white/80 to-transparent dark:from-slate-800/80 rounded-tl-xl"></div>
          </div>

          {/* 标签 */}
          {prompt.tags.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-3">
              {prompt.tags.slice(0, 3).map((tag, index) => (
                <Badge
                  key={index}
                  className="text-xs bg-white/60 dark:bg-slate-700/60 text-slate-600 dark:text-slate-400 border border-slate-200/50 dark:border-slate-600/50 hover:bg-gradient-to-r hover:from-blue-500 hover:to-purple-500 hover:text-white transition-all duration-200 backdrop-blur-sm"
                >
                  {tag}
                </Badge>
              ))}
              {prompt.tags.length > 3 && (
                <Badge className="text-xs bg-gradient-to-r from-slate-500 to-slate-600 text-white border-0">
                  +{prompt.tags.length - 3}
                </Badge>
              )}
            </div>
          )}
        </CardContent>

        <CardFooter className="pt-0">
          <div className="flex items-center justify-between w-full">
            {/* 创建时间和作者 */}
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <span>{formatRelativeTime(prompt.createdAt)}</span>
              {prompt.createdBy?.name && (
                <>
                  <span>•</span>
                  <span>{prompt.createdBy.name}</span>
                </>
              )}
            </div>
            
            {/* 操作按钮 */}
            {showActions && (
              <motion.div
                className="flex items-center gap-1"
                initial={{ opacity: 0, x: 10 }}
                animate={{
                  opacity: isHovered ? 1 : 0,
                  x: isHovered ? 0 : 10
                }}
                transition={{ duration: 0.2 }}
              >
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={handleCopy}
                  className="h-8 px-2"
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                    />
                  </svg>
                </Button>
                
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={handleEdit}
                  className="h-8 px-2"
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                    />
                  </svg>
                </Button>
              </motion.div>
            )}
          </div>
        </CardFooter>
      </Card>
    </motion.div>
  )
}
