'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { toast } from 'react-hot-toast'
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '~/components/ui/Card'
import { Badge } from '~/components/ui/Badge'
import { Button } from '~/components/ui/Button'
import { useModals } from '~/hooks/useStore'
import { copyToClipboard, formatRelativeTime, truncateText } from '~/lib/utils'
import type { Prompt } from '~/store'

interface PromptCardProps {
  prompt: Prompt
  onCopy?: (promptId: string) => void
  showActions?: boolean
  className?: string
}

export function PromptCard({ 
  prompt, 
  onCopy, 
  showActions = true,
  className 
}: PromptCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  const { openPromptDetail, openEditPrompt } = useModals()

  const handleCopy = async (e: React.MouseEvent) => {
    e.stopPropagation()
    
    const success = await copyToClipboard(prompt.content)
    if (success) {
      toast.success('提示词已复制到剪贴板')
      onCopy?.(prompt.id)
    } else {
      toast.error('复制失败，请重试')
    }
  }

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation()
    openEditPrompt(prompt)
  }

  const handleCardClick = () => {
    openPromptDetail(prompt)
  }

  return (
    <div className={`card bg-base-100 shadow-xl hover:shadow-2xl transition-all duration-300 cursor-pointer ${className}`}>
      <div
        className="card-body"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={handleCardClick}
      >
        {/* 标题和使用次数 */}
        <div className="flex items-start justify-between mb-3">
          <h2 className="card-title text-lg line-clamp-2 flex-1">
            {prompt.title}
          </h2>
          <div className="badge badge-primary ml-2 shrink-0">
            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            {prompt.usageCount}次
          </div>
        </div>

        {/* 分类标签 */}
        {prompt.category && (
          <div className="flex items-center gap-2 mb-3">
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: prompt.category.color }}
            />
            <span className="badge badge-outline badge-sm">
              {prompt.category.name}
            </span>
          </div>
        )}

        {/* 描述 */}
        {prompt.description && (
          <p className="text-sm opacity-70 mb-3 line-clamp-2">
            {prompt.description}
          </p>
        )}

        {/* 内容预览 */}
        <div className="bg-base-200 rounded-lg p-3 mb-4">
          <p className="text-sm line-clamp-3 leading-relaxed">
            {truncateText(prompt.content, 150)}
          </p>
        </div>

        {/* 标签 */}
        {prompt.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-4">
            {prompt.tags.slice(0, 3).map((tag, index) => (
              <div key={index} className="badge badge-ghost badge-sm">
                {tag}
              </div>
            ))}
            {prompt.tags.length > 3 && (
              <div className="badge badge-neutral badge-sm">
                +{prompt.tags.length - 3}
              </div>
            )}
          </div>
        )}

        {/* 底部信息和操作 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-xs opacity-60">
            <span>{formatRelativeTime(prompt.createdAt)}</span>
            {prompt.createdBy?.name && (
              <>
                <span>•</span>
                <span>{prompt.createdBy.name}</span>
              </>
            )}
          </div>

          <div className="card-actions">
            {isHovered && (
              <button
                onClick={handleEdit}
                className="btn btn-ghost btn-sm btn-square"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </button>
            )}

            <button
              onClick={handleCopy}
              className="btn btn-ghost btn-sm btn-square"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
