'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { toast } from 'react-hot-toast'
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from '~/components/ui/Card'
import { Badge } from '~/components/ui/Badge'
import { Button } from '~/components/ui/Button'
import { useModals } from '~/hooks/useStore'
import { copyToClipboard, formatRelativeTime, truncateText } from '~/lib/utils'
import type { Prompt } from '~/store'

interface PromptCardProps {
  prompt: Prompt
  onCopy?: (promptId: string) => void
  showActions?: boolean
  className?: string
}

export function PromptCard({ 
  prompt, 
  onCopy, 
  showActions = true,
  className 
}: PromptCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  const { openPromptDetail, openEditPrompt } = useModals()

  const handleCopy = async (e: React.MouseEvent) => {
    e.stopPropagation()
    
    const success = await copyToClipboard(prompt.content)
    if (success) {
      toast.success('提示词已复制到剪贴板')
      onCopy?.(prompt.id)
    } else {
      toast.error('复制失败，请重试')
    }
  }

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation()
    openEditPrompt(prompt)
  }

  const handleCardClick = () => {
    openPromptDetail(prompt)
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      whileHover={{ y: -4 }}
      transition={{ duration: 0.2 }}
      className={className}
    >
      <Card 
        className="h-full cursor-pointer transition-all duration-200 hover:shadow-lg"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={handleCardClick}
      >
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <CardTitle className="text-lg font-semibold line-clamp-2">
              {prompt.title}
            </CardTitle>
            
            {/* 使用次数徽章 */}
            <Badge variant="secondary" className="ml-2 shrink-0">
              {prompt.usageCount}次
            </Badge>
          </div>
          
          {/* 分类标签 */}
          {prompt.category && (
            <div className="flex items-center gap-2 mt-2">
              <div 
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: prompt.category.color }}
              />
              <span className="text-sm text-muted-foreground">
                {prompt.category.name}
              </span>
            </div>
          )}
        </CardHeader>

        <CardContent className="pb-3">
          {/* 描述 */}
          {prompt.description && (
            <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
              {prompt.description}
            </p>
          )}
          
          {/* 内容预览 */}
          <div className="text-sm bg-muted/50 rounded-md p-3 mb-3">
            <p className="line-clamp-3">
              {truncateText(prompt.content, 150)}
            </p>
          </div>
          
          {/* 标签 */}
          {prompt.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-3">
              {prompt.tags.slice(0, 3).map((tag, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {prompt.tags.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{prompt.tags.length - 3}
                </Badge>
              )}
            </div>
          )}
        </CardContent>

        <CardFooter className="pt-0">
          <div className="flex items-center justify-between w-full">
            {/* 创建时间和作者 */}
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <span>{formatRelativeTime(prompt.createdAt)}</span>
              {prompt.createdBy?.name && (
                <>
                  <span>•</span>
                  <span>{prompt.createdBy.name}</span>
                </>
              )}
            </div>
            
            {/* 操作按钮 */}
            {showActions && (
              <motion.div 
                className="flex items-center gap-1"
                initial={{ opacity: 0 }}
                animate={{ opacity: isHovered ? 1 : 0 }}
                transition={{ duration: 0.2 }}
              >
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={handleCopy}
                  className="h-8 px-2"
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                    />
                  </svg>
                </Button>
                
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={handleEdit}
                  className="h-8 px-2"
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                    />
                  </svg>
                </Button>
              </motion.div>
            )}
          </div>
        </CardFooter>
      </Card>
    </motion.div>
  )
}
