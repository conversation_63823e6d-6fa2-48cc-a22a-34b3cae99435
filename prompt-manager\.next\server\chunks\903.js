"use strict";exports.id=903,exports.ids=[903],exports.modules={3641:(a,b,c)=>{c.d(b,{db:()=>g});var d=c(6330),e=c(8516);let f=globalThis,g=f.prisma??new d.PrismaClient({log:"development"===e._.NODE_ENV?["query","error","warn"]:["error"]});"production"!==e._.NODE_ENV&&(f.prisma=g)},4903:(a,b,c)=>{c.d(b,{P:()=>i,u:()=>j});var d=c(2289),e=c(7645);let f=(0,e.LO)({getAll:e.JI.query(async({ctx:a})=>a.db.category.findMany({orderBy:{createdAt:"desc"},include:{_count:{select:{prompts:!0}}}})),getById:e.JI.input(d.Ik({id:d.Yj()})).query(async({ctx:a,input:b})=>a.db.category.findUnique({where:{id:b.id},include:{prompts:{orderBy:{createdAt:"desc"},take:10},_count:{select:{prompts:!0}}}})),create:e.sy.input(d.Ik({name:d.Yj().min(1,"分类名称不能为空").max(50,"分类名称不能超过50个字符"),description:d.Yj().optional(),color:d.Yj().regex(/^#[0-9A-F]{6}$/i,"颜色格式不正确").default("#3B82F6"),icon:d.Yj().optional()})).mutation(async({ctx:a,input:b})=>a.db.category.create({data:{...b,createdById:a.session.user.id}})),update:e.sy.input(d.Ik({id:d.Yj(),name:d.Yj().min(1,"分类名称不能为空").max(50,"分类名称不能超过50个字符").optional(),description:d.Yj().optional(),color:d.Yj().regex(/^#[0-9A-F]{6}$/i,"颜色格式不正确").optional(),icon:d.Yj().optional()})).mutation(async({ctx:a,input:b})=>{let{id:c,...d}=b,e=await a.db.category.findUnique({where:{id:c}});if(!e)throw Error("分类不存在");if(e.createdById!==a.session.user.id)throw Error("无权限修改此分类");return a.db.category.update({where:{id:c},data:d})}),delete:e.sy.input(d.Ik({id:d.Yj()})).mutation(async({ctx:a,input:b})=>{let c=await a.db.category.findUnique({where:{id:b.id},include:{_count:{select:{prompts:!0}}}});if(!c)throw Error("分类不存在");if(c.createdById!==a.session.user.id)throw Error("无权限删除此分类");if(c._count.prompts>0)throw Error("该分类下还有提示词，无法删除");return a.db.category.delete({where:{id:b.id}})}),getStats:e.JI.query(async({ctx:a})=>await a.db.category.findMany({select:{id:!0,name:!0,color:!0,_count:{select:{prompts:!0}}},orderBy:{prompts:{_count:"desc"}}}))}),g=(0,e.LO)({getAll:e.JI.input(d.Ik({page:d.ai().min(1).default(1),limit:d.ai().min(1).max(100).default(20),search:d.Yj().optional(),categoryId:d.Yj().optional(),sortBy:d.k5(["createdAt","updatedAt","usageCount","title"]).default("createdAt"),sortOrder:d.k5(["asc","desc"]).default("desc")})).query(async({ctx:a,input:b})=>{let{page:c,limit:d,search:e,categoryId:f,sortBy:g,sortOrder:h}=b,i={isPublic:!0};e&&(i.OR=[{title:{contains:e,mode:"insensitive"}},{description:{contains:e,mode:"insensitive"}},{content:{contains:e,mode:"insensitive"}},{tags:{hasSome:[e]}}]),f&&(i.categoryId=f);let j=await a.db.prompt.count({where:i});return{prompts:await a.db.prompt.findMany({where:i,skip:(c-1)*d,take:d,orderBy:{[g]:h},include:{category:{select:{id:!0,name:!0,color:!0,icon:!0}},createdBy:{select:{id:!0,name:!0,image:!0}}}}),pagination:{page:c,limit:d,total:j,totalPages:Math.ceil(j/d)}}}),getById:e.JI.input(d.Ik({id:d.Yj()})).query(async({ctx:a,input:b})=>a.db.prompt.findUnique({where:{id:b.id},include:{category:{select:{id:!0,name:!0,color:!0,icon:!0}},createdBy:{select:{id:!0,name:!0,image:!0}}}})),create:e.sy.input(d.Ik({title:d.Yj().min(1,"标题不能为空").max(100,"标题不能超过100个字符"),content:d.Yj().min(1,"内容不能为空"),description:d.Yj().max(200,"描述不能超过200个字符").optional(),tags:d.YO(d.Yj()).max(10,"标签不能超过10个").default([]),categoryId:d.Yj().optional(),isPublic:d.zM().default(!0)})).mutation(async({ctx:a,input:b})=>a.db.prompt.create({data:{...b,createdById:a.session.user.id},include:{category:{select:{id:!0,name:!0,color:!0,icon:!0}}}})),update:e.sy.input(d.Ik({id:d.Yj(),title:d.Yj().min(1,"标题不能为空").max(100,"标题不能超过100个字符").optional(),content:d.Yj().min(1,"内容不能为空").optional(),description:d.Yj().max(200,"描述不能超过200个字符").optional(),tags:d.YO(d.Yj()).max(10,"标签不能超过10个").optional(),categoryId:d.Yj().optional(),isPublic:d.zM().optional()})).mutation(async({ctx:a,input:b})=>{let{id:c,...d}=b,e=await a.db.prompt.findUnique({where:{id:c}});if(!e)throw Error("提示词不存在");if(e.createdById!==a.session.user.id)throw Error("无权限修改此提示词");return a.db.prompt.update({where:{id:c},data:d,include:{category:{select:{id:!0,name:!0,color:!0,icon:!0}}}})}),delete:e.sy.input(d.Ik({id:d.Yj()})).mutation(async({ctx:a,input:b})=>{let c=await a.db.prompt.findUnique({where:{id:b.id}});if(!c)throw Error("提示词不存在");if(c.createdById!==a.session.user.id)throw Error("无权限删除此提示词");return a.db.prompt.delete({where:{id:b.id}})}),copy:e.JI.input(d.Ik({id:d.Yj()})).mutation(async({ctx:a,input:b})=>{let c=await a.db.prompt.update({where:{id:b.id},data:{usageCount:{increment:1}}});return a.session?.user?.id&&await a.db.promptUsage.create({data:{promptId:b.id,userId:a.session.user.id}}),c}),getMine:e.sy.input(d.Ik({page:d.ai().min(1).default(1),limit:d.ai().min(1).max(100).default(20),search:d.Yj().optional(),categoryId:d.Yj().optional()})).query(async({ctx:a,input:b})=>{let{page:c,limit:d,search:e,categoryId:f}=b,g={createdById:a.session.user.id};e&&(g.OR=[{title:{contains:e,mode:"insensitive"}},{description:{contains:e,mode:"insensitive"}},{content:{contains:e,mode:"insensitive"}}]),f&&(g.categoryId=f);let h=await a.db.prompt.count({where:g});return{prompts:await a.db.prompt.findMany({where:g,skip:(c-1)*d,take:d,orderBy:{updatedAt:"desc"},include:{category:{select:{id:!0,name:!0,color:!0,icon:!0}}}}),pagination:{page:c,limit:d,total:h,totalPages:Math.ceil(h/d)}}}),getPopular:e.JI.input(d.Ik({limit:d.ai().min(1).max(50).default(10)})).query(async({ctx:a,input:b})=>a.db.prompt.findMany({where:{isPublic:!0},take:b.limit,orderBy:{usageCount:"desc"},include:{category:{select:{id:!0,name:!0,color:!0,icon:!0}},createdBy:{select:{id:!0,name:!0,image:!0}}}})),getLatest:e.JI.input(d.Ik({limit:d.ai().min(1).max(50).default(10)})).query(async({ctx:a,input:b})=>a.db.prompt.findMany({where:{isPublic:!0},take:b.limit,orderBy:{createdAt:"desc"},include:{category:{select:{id:!0,name:!0,color:!0,icon:!0}},createdBy:{select:{id:!0,name:!0,image:!0}}}}))}),h=(0,e.LO)({getOverview:e.JI.query(async({ctx:a})=>{let[b,c,d,e]=await Promise.all([a.db.prompt.count({where:{isPublic:!0}}),a.db.category.count(),a.db.user.count(),a.db.promptUsage.count()]);return{totalPrompts:b,totalCategories:c,totalUsers:d,totalUsages:e}}),getCategoryStats:e.JI.query(async({ctx:a})=>(await a.db.category.findMany({select:{id:!0,name:!0,color:!0,icon:!0,_count:{select:{prompts:!0}}},orderBy:{prompts:{_count:"desc"}}})).map(a=>({id:a.id,name:a.name,color:a.color,icon:a.icon,promptCount:a._count.prompts}))),getUsageTrend:e.JI.query(async({ctx:a})=>{let b=new Date;b.setDate(b.getDate()-30);let c=await a.db.promptUsage.findMany({where:{createdAt:{gte:b}},select:{createdAt:!0},orderBy:{createdAt:"asc"}}),d=new Map;for(let a=29;a>=0;a--){let b=new Date;b.setDate(b.getDate()-a);let c=b.toISOString().split("T")[0];d.set(c,0)}return c.forEach(a=>{let b=a.createdAt.toISOString().split("T")[0];b&&d.set(b,(d.get(b)||0)+1)}),Array.from(d.entries()).map(([a,b])=>({date:a,count:b}))}),getPopularTags:e.JI.input(d.Ik({limit:d.ai().min(1).max(50).default(20)})).query(async({ctx:a,input:b})=>{let c=await a.db.prompt.findMany({where:{isPublic:!0},select:{tags:!0}}),d=new Map;return c.forEach(a=>{a.tags.forEach(a=>{d.set(a,(d.get(a)||0)+1)})}),Array.from(d.entries()).sort((a,b)=>b[1]-a[1]).slice(0,b.limit).map(([a,b])=>({tag:a,count:b}))}),getMyStats:e.sy.query(async({ctx:a})=>{let b=a.session.user.id,[c,d,e,f]=await Promise.all([a.db.prompt.count({where:{createdById:b}}),a.db.category.count({where:{createdById:b}}),a.db.promptUsage.count({where:{userId:b}}),a.db.prompt.aggregate({where:{createdById:b},_sum:{usageCount:!0}})]),g=await a.db.category.findMany({where:{createdById:b},select:{id:!0,name:!0,color:!0,_count:{select:{prompts:!0}}},orderBy:{prompts:{_count:"desc"}},take:5}),h=await a.db.prompt.findMany({where:{createdById:b},select:{id:!0,title:!0,usageCount:!0,category:{select:{name:!0,color:!0}}},orderBy:{usageCount:"desc"},take:5});return{myPrompts:c,myCategories:d,myUsages:e,myTotalUsageCount:f._sum.usageCount||0,myTopCategories:g.map(a=>({id:a.id,name:a.name,color:a.color,promptCount:a._count.prompts})),myTopPrompts:h}}),getRecentUsage:e.sy.input(d.Ik({limit:d.ai().min(1).max(50).default(10)})).query(async({ctx:a,input:b})=>(await a.db.promptUsage.findMany({where:{userId:a.session.user.id},take:b.limit,orderBy:{createdAt:"desc"},include:{prompt:{select:{id:!0,title:!0,description:!0,category:{select:{name:!0,color:!0,icon:!0}}}}}})).map(a=>({id:a.id,usedAt:a.createdAt,prompt:a.prompt}))),getUsageLeaderboard:e.JI.input(d.Ik({limit:d.ai().min(1).max(50).default(10)})).query(async({ctx:a,input:b})=>a.db.prompt.findMany({where:{isPublic:!0},take:b.limit,orderBy:{usageCount:"desc"},select:{id:!0,title:!0,description:!0,usageCount:!0,category:{select:{name:!0,color:!0,icon:!0}},createdBy:{select:{name:!0,image:!0}}}}))}),i=(0,e.LO)({category:f,prompt:g,stats:h}),j=(0,e.OA)(i)},7556:(a,b,c)=>{c.d(b,{j2:()=>n,Y9:()=>k});var d=c(3643),e=c(1120),f=c(6467),g=c(81),h=c(3641);let i={providers:[g.A],adapter:(0,f.y)(h.db),callbacks:{session:({session:a,user:b})=>({...a,user:{...a.user,id:b.id}})}},{auth:j,handlers:k,signIn:l,signOut:m}=(0,d.Ay)(i),n=(0,e.cache)(j)},7645:(a,b,c)=>{c.d(b,{JI:()=>o,LO:()=>m,OA:()=>l,dW:()=>j,sy:()=>p});var d=c(4328),e=c(457),f=c(3041),g=c(5282),h=c(7556),i=c(3641);let j=async a=>{let b=await (0,h.j2)();return{db:i.db,session:b,...a}},k=d.Al.context().create({transformer:f.Ay,errorFormatter:({shape:a,error:b})=>({...a,data:{...a.data,zodError:b.cause instanceof g.G?b.cause.flatten():null}})}),l=k.createCallerFactory,m=k.router,n=k.middleware(async({next:a,path:b})=>{let c=Date.now();if(k._config.isDev){let a=Math.floor(400*Math.random())+100;await new Promise(b=>setTimeout(b,a))}let d=await a(),e=Date.now();return console.log(`[TRPC] ${b} took ${e-c}ms to execute`),d}),o=k.procedure.use(n),p=k.procedure.use(n).use(({ctx:a,next:b})=>{if(!a.session?.user)throw new e.gt({code:"UNAUTHORIZED"});return b({ctx:{session:{...a.session,user:a.session.user}}})})},8516:(a,b,c)=>{c.d(b,{_:()=>f});var d=c(2871),e=c(2289);let f=(0,d.w)({server:{AUTH_SECRET:e.Yj(),AUTH_DISCORD_ID:e.Yj().optional(),AUTH_DISCORD_SECRET:e.Yj().optional(),DATABASE_URL:e.Yj().url(),NODE_ENV:e.k5(["development","test","production"]).default("development")},client:{},runtimeEnv:{AUTH_SECRET:process.env.AUTH_SECRET,AUTH_DISCORD_ID:process.env.AUTH_DISCORD_ID,AUTH_DISCORD_SECRET:process.env.AUTH_DISCORD_SECRET,DATABASE_URL:process.env.DATABASE_URL,NODE_ENV:"production"},skipValidation:!!process.env.SKIP_ENV_VALIDATION,emptyStringAsUndefined:!0})}};