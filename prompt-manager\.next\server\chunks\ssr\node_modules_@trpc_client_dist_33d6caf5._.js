module.exports = {

"[project]/node_modules/@trpc/client/dist/objectSpread2-BvkFp-_Y.mjs [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

//#region rolldown:runtime
__turbopack_context__.s({
    "__commonJS": ()=>__commonJS,
    "__toESM": ()=>__toESM,
    "require_defineProperty": ()=>require_defineProperty,
    "require_objectSpread2": ()=>require_objectSpread2
});
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __commonJS = (cb, mod)=>function() {
        return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = {
            exports: {}
        }).exports, mod), mod.exports;
    };
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") for(var keys = __getOwnPropNames(from), i = 0, n = keys.length, key; i < n; i++){
        key = keys[i];
        if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ((k)=>from[k]).bind(null, key),
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toESM = (mod, isNodeMode, target)=>(target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", {
        value: mod,
        enumerable: true
    }) : target, mod));
//#endregion
//#region ../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/typeof.js
var require_typeof = __commonJS({
    "../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/typeof.js" (exports, module) {
        function _typeof$2(o) {
            "@babel/helpers - typeof";
            return module.exports = _typeof$2 = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o$1) {
                return typeof o$1;
            } : function(o$1) {
                return o$1 && "function" == typeof Symbol && o$1.constructor === Symbol && o$1 !== Symbol.prototype ? "symbol" : typeof o$1;
            }, module.exports.__esModule = true, module.exports["default"] = module.exports, _typeof$2(o);
        }
        module.exports = _typeof$2, module.exports.__esModule = true, module.exports["default"] = module.exports;
    }
});
//#endregion
//#region ../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPrimitive.js
var require_toPrimitive = __commonJS({
    "../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPrimitive.js" (exports, module) {
        var _typeof$1 = require_typeof()["default"];
        function toPrimitive$1(t, r) {
            if ("object" != _typeof$1(t) || !t) return t;
            var e = t[Symbol.toPrimitive];
            if (void 0 !== e) {
                var i = e.call(t, r || "default");
                if ("object" != _typeof$1(i)) return i;
                throw new TypeError("@@toPrimitive must return a primitive value.");
            }
            return ("string" === r ? String : Number)(t);
        }
        module.exports = toPrimitive$1, module.exports.__esModule = true, module.exports["default"] = module.exports;
    }
});
//#endregion
//#region ../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPropertyKey.js
var require_toPropertyKey = __commonJS({
    "../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPropertyKey.js" (exports, module) {
        var _typeof = require_typeof()["default"];
        var toPrimitive = require_toPrimitive();
        function toPropertyKey$1(t) {
            var i = toPrimitive(t, "string");
            return "symbol" == _typeof(i) ? i : i + "";
        }
        module.exports = toPropertyKey$1, module.exports.__esModule = true, module.exports["default"] = module.exports;
    }
});
//#endregion
//#region ../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/defineProperty.js
var require_defineProperty = __commonJS({
    "../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/defineProperty.js" (exports, module) {
        var toPropertyKey = require_toPropertyKey();
        function _defineProperty(e, r, t) {
            return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {
                value: t,
                enumerable: !0,
                configurable: !0,
                writable: !0
            }) : e[r] = t, e;
        }
        module.exports = _defineProperty, module.exports.__esModule = true, module.exports["default"] = module.exports;
    }
});
//#endregion
//#region ../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectSpread2.js
var require_objectSpread2 = __commonJS({
    "../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectSpread2.js" (exports, module) {
        var defineProperty = require_defineProperty();
        function ownKeys(e, r) {
            var t = Object.keys(e);
            if (Object.getOwnPropertySymbols) {
                var o = Object.getOwnPropertySymbols(e);
                r && (o = o.filter(function(r$1) {
                    return Object.getOwnPropertyDescriptor(e, r$1).enumerable;
                })), t.push.apply(t, o);
            }
            return t;
        }
        function _objectSpread2(e) {
            for(var r = 1; r < arguments.length; r++){
                var t = null != arguments[r] ? arguments[r] : {};
                r % 2 ? ownKeys(Object(t), !0).forEach(function(r$1) {
                    defineProperty(e, r$1, t[r$1]);
                }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r$1) {
                    Object.defineProperty(e, r$1, Object.getOwnPropertyDescriptor(t, r$1));
                });
            }
            return e;
        }
        module.exports = _objectSpread2, module.exports.__esModule = true, module.exports["default"] = module.exports;
    }
});
;
 //# sourceMappingURL=objectSpread2-BvkFp-_Y.mjs.map
}),
"[project]/node_modules/@trpc/client/dist/splitLink-B7Cuf2c_.mjs [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "createChain": ()=>createChain,
    "splitLink": ()=>splitLink
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$UMO3vUa_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/observable-UMO3vUa_.mjs [app-ssr] (ecmascript)");
;
//#region src/links/internals/createChain.ts
/** @internal */ function createChain(opts) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$UMO3vUa_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["observable"])((observer)=>{
        function execute(index = 0, op = opts.op) {
            const next = opts.links[index];
            if (!next) throw new Error("No more links to execute - did you forget to add an ending link?");
            const subscription = next({
                op,
                next (nextOp) {
                    const nextObserver = execute(index + 1, nextOp);
                    return nextObserver;
                }
            });
            return subscription;
        }
        const obs$ = execute();
        return obs$.subscribe(observer);
    });
}
//#endregion
//#region src/links/splitLink.ts
function asArray(value) {
    return Array.isArray(value) ? value : [
        value
    ];
}
function splitLink(opts) {
    return (runtime)=>{
        const yes = asArray(opts.true).map((link)=>link(runtime));
        const no = asArray(opts.false).map((link)=>link(runtime));
        return (props)=>{
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$UMO3vUa_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["observable"])((observer)=>{
                const links = opts.condition(props.op) ? yes : no;
                return createChain({
                    op: props.op,
                    links
                }).subscribe(observer);
            });
        };
    };
}
;
 //# sourceMappingURL=splitLink-B7Cuf2c_.mjs.map
}),
"[project]/node_modules/@trpc/client/dist/TRPCClientError-CjKyS10w.mjs [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "TRPCClientError": ()=>TRPCClientError,
    "isTRPCClientError": ()=>isTRPCClientError
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/objectSpread2-BvkFp-_Y.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/utils-DdbbrDku.mjs [app-ssr] (ecmascript)");
;
;
//#region src/TRPCClientError.ts
var import_defineProperty = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["require_defineProperty"])(), 1);
var import_objectSpread2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["require_objectSpread2"])(), 1);
function isTRPCClientError(cause) {
    return cause instanceof TRPCClientError;
}
function isTRPCErrorResponse(obj) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isObject"])(obj) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isObject"])(obj["error"]) && typeof obj["error"]["code"] === "number" && typeof obj["error"]["message"] === "string";
}
function getMessageFromUnknownError(err, fallback) {
    if (typeof err === "string") return err;
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isObject"])(err) && typeof err["message"] === "string") return err["message"];
    return fallback;
}
var TRPCClientError = class TRPCClientError extends Error {
    constructor(message, opts){
        var _opts$result, _opts$result2;
        const cause = opts === null || opts === void 0 ? void 0 : opts.cause;
        super(message, {
            cause
        });
        (0, import_defineProperty.default)(this, "cause", void 0);
        (0, import_defineProperty.default)(this, "shape", void 0);
        (0, import_defineProperty.default)(this, "data", void 0);
        (0, import_defineProperty.default)(this, "meta", void 0);
        this.meta = opts === null || opts === void 0 ? void 0 : opts.meta;
        this.cause = cause;
        this.shape = opts === null || opts === void 0 || (_opts$result = opts.result) === null || _opts$result === void 0 ? void 0 : _opts$result.error;
        this.data = opts === null || opts === void 0 || (_opts$result2 = opts.result) === null || _opts$result2 === void 0 ? void 0 : _opts$result2.error.data;
        this.name = "TRPCClientError";
        Object.setPrototypeOf(this, TRPCClientError.prototype);
    }
    static from(_cause, opts = {}) {
        const cause = _cause;
        if (isTRPCClientError(cause)) {
            if (opts.meta) cause.meta = (0, import_objectSpread2.default)((0, import_objectSpread2.default)({}, cause.meta), opts.meta);
            return cause;
        }
        if (isTRPCErrorResponse(cause)) return new TRPCClientError(cause.error.message, (0, import_objectSpread2.default)((0, import_objectSpread2.default)({}, opts), {}, {
            result: cause
        }));
        return new TRPCClientError(getMessageFromUnknownError(cause, "Unknown error"), (0, import_objectSpread2.default)((0, import_objectSpread2.default)({}, opts), {}, {
            cause
        }));
    }
};
;
 //# sourceMappingURL=TRPCClientError-CjKyS10w.mjs.map
}),
"[project]/node_modules/@trpc/client/dist/unstable-internals-Bg7n9BBj.mjs [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

//#region src/internals/transformer.ts
/**
* @internal
*/ /**
* @internal
*/ __turbopack_context__.s({
    "getTransformer": ()=>getTransformer
});
function getTransformer(transformer) {
    const _transformer = transformer;
    if (!_transformer) return {
        input: {
            serialize: (data)=>data,
            deserialize: (data)=>data
        },
        output: {
            serialize: (data)=>data,
            deserialize: (data)=>data
        }
    };
    if ("input" in _transformer) return _transformer;
    return {
        input: _transformer,
        output: _transformer
    };
}
;
 //# sourceMappingURL=unstable-internals-Bg7n9BBj.mjs.map
}),
"[project]/node_modules/@trpc/client/dist/httpUtils-Bkv1johT.mjs [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "fetchHTTPResponse": ()=>fetchHTTPResponse,
    "getBody": ()=>getBody,
    "getFetch": ()=>getFetch,
    "getUrl": ()=>getUrl,
    "httpRequest": ()=>httpRequest,
    "jsonHttpRequester": ()=>jsonHttpRequester,
    "resolveHTTPLinkOptions": ()=>resolveHTTPLinkOptions
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/objectSpread2-BvkFp-_Y.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$unstable$2d$internals$2d$Bg7n9BBj$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/unstable-internals-Bg7n9BBj.mjs [app-ssr] (ecmascript)");
;
;
//#region src/getFetch.ts
const isFunction = (fn)=>typeof fn === "function";
function getFetch(customFetchImpl) {
    if (customFetchImpl) return customFetchImpl;
    if ("undefined" !== "undefined" && isFunction(window.fetch)) //TURBOPACK unreachable
    ;
    if (typeof globalThis !== "undefined" && isFunction(globalThis.fetch)) return globalThis.fetch;
    throw new Error("No fetch implementation found");
}
//#endregion
//#region src/links/internals/httpUtils.ts
var import_objectSpread2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["require_objectSpread2"])(), 1);
function resolveHTTPLinkOptions(opts) {
    return {
        url: opts.url.toString(),
        fetch: opts.fetch,
        transformer: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$unstable$2d$internals$2d$Bg7n9BBj$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTransformer"])(opts.transformer),
        methodOverride: opts.methodOverride
    };
}
function arrayToDict(array) {
    const dict = {};
    for(let index = 0; index < array.length; index++){
        const element = array[index];
        dict[index] = element;
    }
    return dict;
}
const METHOD = {
    query: "GET",
    mutation: "POST",
    subscription: "PATCH"
};
function getInput(opts) {
    return "input" in opts ? opts.transformer.input.serialize(opts.input) : arrayToDict(opts.inputs.map((_input)=>opts.transformer.input.serialize(_input)));
}
const getUrl = (opts)=>{
    const parts = opts.url.split("?");
    const base = parts[0].replace(/\/$/, "");
    let url = base + "/" + opts.path;
    const queryParts = [];
    if (parts[1]) queryParts.push(parts[1]);
    if ("inputs" in opts) queryParts.push("batch=1");
    if (opts.type === "query" || opts.type === "subscription") {
        const input = getInput(opts);
        if (input !== void 0 && opts.methodOverride !== "POST") queryParts.push(`input=${encodeURIComponent(JSON.stringify(input))}`);
    }
    if (queryParts.length) url += "?" + queryParts.join("&");
    return url;
};
const getBody = (opts)=>{
    if (opts.type === "query" && opts.methodOverride !== "POST") return void 0;
    const input = getInput(opts);
    return input !== void 0 ? JSON.stringify(input) : void 0;
};
const jsonHttpRequester = (opts)=>{
    return httpRequest((0, import_objectSpread2.default)((0, import_objectSpread2.default)({}, opts), {}, {
        contentTypeHeader: "application/json",
        getUrl,
        getBody
    }));
};
/**
* Polyfill for DOMException with AbortError name
*/ var AbortError = class extends Error {
    constructor(){
        const name = "AbortError";
        super(name);
        this.name = name;
        this.message = name;
    }
};
/**
* Polyfill for `signal.throwIfAborted()`
*
* @see https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal/throwIfAborted
*/ const throwIfAborted = (signal)=>{
    var _signal$throwIfAborte;
    if (!(signal === null || signal === void 0 ? void 0 : signal.aborted)) return;
    (_signal$throwIfAborte = signal.throwIfAborted) === null || _signal$throwIfAborte === void 0 || _signal$throwIfAborte.call(signal);
    if (typeof DOMException !== "undefined") throw new DOMException("AbortError", "AbortError");
    throw new AbortError();
};
async function fetchHTTPResponse(opts) {
    var _opts$methodOverride;
    throwIfAborted(opts.signal);
    const url = opts.getUrl(opts);
    const body = opts.getBody(opts);
    const { type } = opts;
    const resolvedHeaders = await (async ()=>{
        const heads = await opts.headers();
        if (Symbol.iterator in heads) return Object.fromEntries(heads);
        return heads;
    })();
    const headers = (0, import_objectSpread2.default)((0, import_objectSpread2.default)((0, import_objectSpread2.default)({}, opts.contentTypeHeader ? {
        "content-type": opts.contentTypeHeader
    } : {}), opts.trpcAcceptHeader ? {
        "trpc-accept": opts.trpcAcceptHeader
    } : void 0), resolvedHeaders);
    return getFetch(opts.fetch)(url, {
        method: (_opts$methodOverride = opts.methodOverride) !== null && _opts$methodOverride !== void 0 ? _opts$methodOverride : METHOD[type],
        signal: opts.signal,
        body,
        headers
    });
}
async function httpRequest(opts) {
    const meta = {};
    const res = await fetchHTTPResponse(opts);
    meta.response = res;
    const json = await res.json();
    meta.responseJSON = json;
    return {
        json,
        meta
    };
}
;
 //# sourceMappingURL=httpUtils-Bkv1johT.mjs.map
}),
"[project]/node_modules/@trpc/client/dist/httpLink-CYOcG9kQ.mjs [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "httpLink": ()=>httpLink,
    "isFormData": ()=>isFormData,
    "isNonJsonSerializable": ()=>isNonJsonSerializable,
    "isOctetType": ()=>isOctetType
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/objectSpread2-BvkFp-_Y.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$TRPCClientError$2d$CjKyS10w$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/TRPCClientError-CjKyS10w.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpUtils$2d$Bkv1johT$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/httpUtils-Bkv1johT.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$UMO3vUa_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/observable-UMO3vUa_.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/tracked-gU3ttYjg.mjs [app-ssr] (ecmascript)");
;
;
;
;
;
//#region src/links/internals/contentTypes.ts
function isOctetType(input) {
    return input instanceof Uint8Array || input instanceof Blob;
}
function isFormData(input) {
    return input instanceof FormData;
}
function isNonJsonSerializable(input) {
    return isOctetType(input) || isFormData(input);
}
//#endregion
//#region src/links/httpLink.ts
var import_objectSpread2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["require_objectSpread2"])(), 1);
const universalRequester = (opts)=>{
    if ("input" in opts) {
        const { input } = opts;
        if (isFormData(input)) {
            if (opts.type !== "mutation" && opts.methodOverride !== "POST") throw new Error("FormData is only supported for mutations");
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpUtils$2d$Bkv1johT$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["httpRequest"])((0, import_objectSpread2.default)((0, import_objectSpread2.default)({}, opts), {}, {
                contentTypeHeader: void 0,
                getUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpUtils$2d$Bkv1johT$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getUrl"],
                getBody: ()=>input
            }));
        }
        if (isOctetType(input)) {
            if (opts.type !== "mutation" && opts.methodOverride !== "POST") throw new Error("Octet type input is only supported for mutations");
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpUtils$2d$Bkv1johT$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["httpRequest"])((0, import_objectSpread2.default)((0, import_objectSpread2.default)({}, opts), {}, {
                contentTypeHeader: "application/octet-stream",
                getUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpUtils$2d$Bkv1johT$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getUrl"],
                getBody: ()=>input
            }));
        }
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpUtils$2d$Bkv1johT$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsonHttpRequester"])(opts);
};
/**
* @see https://trpc.io/docs/client/links/httpLink
*/ function httpLink(opts) {
    const resolvedOpts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpUtils$2d$Bkv1johT$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["resolveHTTPLinkOptions"])(opts);
    return ()=>{
        return ({ op })=>{
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$UMO3vUa_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["observable"])((observer)=>{
                const { path, input, type } = op;
                /* istanbul ignore if -- @preserve */ if (type === "subscription") throw new Error("Subscriptions are unsupported by `httpLink` - use `httpSubscriptionLink` or `wsLink`");
                const request = universalRequester((0, import_objectSpread2.default)((0, import_objectSpread2.default)({}, resolvedOpts), {}, {
                    type,
                    path,
                    input,
                    signal: op.signal,
                    headers () {
                        if (!opts.headers) return {};
                        if (typeof opts.headers === "function") return opts.headers({
                            op
                        });
                        return opts.headers;
                    }
                }));
                let meta = void 0;
                request.then((res)=>{
                    meta = res.meta;
                    const transformed = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["transformResult"])(res.json, resolvedOpts.transformer.output);
                    if (!transformed.ok) {
                        observer.error(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$TRPCClientError$2d$CjKyS10w$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCClientError"].from(transformed.error, {
                            meta
                        }));
                        return;
                    }
                    observer.next({
                        context: res.meta,
                        result: transformed.result
                    });
                    observer.complete();
                }).catch((cause)=>{
                    observer.error(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$TRPCClientError$2d$CjKyS10w$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCClientError"].from(cause, {
                        meta
                    }));
                });
                return ()=>{};
            });
        };
    };
}
;
 //# sourceMappingURL=httpLink-CYOcG9kQ.mjs.map
}),
"[project]/node_modules/@trpc/client/dist/httpBatchLink-CA96-gnJ.mjs [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "abortSignalToPromise": ()=>abortSignalToPromise,
    "allAbortSignals": ()=>allAbortSignals,
    "dataLoader": ()=>dataLoader,
    "httpBatchLink": ()=>httpBatchLink,
    "raceAbortSignals": ()=>raceAbortSignals
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/objectSpread2-BvkFp-_Y.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$TRPCClientError$2d$CjKyS10w$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/TRPCClientError-CjKyS10w.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpUtils$2d$Bkv1johT$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/httpUtils-Bkv1johT.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$UMO3vUa_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/observable-UMO3vUa_.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/tracked-gU3ttYjg.mjs [app-ssr] (ecmascript)");
;
;
;
;
;
//#region src/internals/dataLoader.ts
/**
* A function that should never be called unless we messed something up.
*/ const throwFatalError = ()=>{
    throw new Error("Something went wrong. Please submit an issue at https://github.com/trpc/trpc/issues/new");
};
/**
* Dataloader that's very inspired by https://github.com/graphql/dataloader
* Less configuration, no caching, and allows you to cancel requests
* When cancelling a single fetch the whole batch will be cancelled only when _all_ items are cancelled
*/ function dataLoader(batchLoader) {
    let pendingItems = null;
    let dispatchTimer = null;
    const destroyTimerAndPendingItems = ()=>{
        clearTimeout(dispatchTimer);
        dispatchTimer = null;
        pendingItems = null;
    };
    /**
	* Iterate through the items and split them into groups based on the `batchLoader`'s validate function
	*/ function groupItems(items) {
        const groupedItems = [
            []
        ];
        let index = 0;
        while(true){
            const item = items[index];
            if (!item) break;
            const lastGroup = groupedItems[groupedItems.length - 1];
            if (item.aborted) {
                var _item$reject;
                (_item$reject = item.reject) === null || _item$reject === void 0 || _item$reject.call(item, new Error("Aborted"));
                index++;
                continue;
            }
            const isValid = batchLoader.validate(lastGroup.concat(item).map((it)=>it.key));
            if (isValid) {
                lastGroup.push(item);
                index++;
                continue;
            }
            if (lastGroup.length === 0) {
                var _item$reject2;
                (_item$reject2 = item.reject) === null || _item$reject2 === void 0 || _item$reject2.call(item, new Error("Input is too big for a single dispatch"));
                index++;
                continue;
            }
            groupedItems.push([]);
        }
        return groupedItems;
    }
    function dispatch() {
        const groupedItems = groupItems(pendingItems);
        destroyTimerAndPendingItems();
        for (const items of groupedItems){
            if (!items.length) continue;
            const batch = {
                items
            };
            for (const item of items)item.batch = batch;
            const promise = batchLoader.fetch(batch.items.map((_item)=>_item.key));
            promise.then(async (result)=>{
                await Promise.all(result.map(async (valueOrPromise, index)=>{
                    const item = batch.items[index];
                    try {
                        var _item$resolve;
                        const value = await Promise.resolve(valueOrPromise);
                        (_item$resolve = item.resolve) === null || _item$resolve === void 0 || _item$resolve.call(item, value);
                    } catch (cause) {
                        var _item$reject3;
                        (_item$reject3 = item.reject) === null || _item$reject3 === void 0 || _item$reject3.call(item, cause);
                    }
                    item.batch = null;
                    item.reject = null;
                    item.resolve = null;
                }));
                for (const item of batch.items){
                    var _item$reject4;
                    (_item$reject4 = item.reject) === null || _item$reject4 === void 0 || _item$reject4.call(item, new Error("Missing result"));
                    item.batch = null;
                }
            }).catch((cause)=>{
                for (const item of batch.items){
                    var _item$reject5;
                    (_item$reject5 = item.reject) === null || _item$reject5 === void 0 || _item$reject5.call(item, cause);
                    item.batch = null;
                }
            });
        }
    }
    function load(key) {
        var _dispatchTimer;
        const item = {
            aborted: false,
            key,
            batch: null,
            resolve: throwFatalError,
            reject: throwFatalError
        };
        const promise = new Promise((resolve, reject)=>{
            var _pendingItems;
            item.reject = reject;
            item.resolve = resolve;
            (_pendingItems = pendingItems) !== null && _pendingItems !== void 0 || (pendingItems = []);
            pendingItems.push(item);
        });
        (_dispatchTimer = dispatchTimer) !== null && _dispatchTimer !== void 0 || (dispatchTimer = setTimeout(dispatch));
        return promise;
    }
    return {
        load
    };
}
//#endregion
//#region src/internals/signals.ts
/**
* Like `Promise.all()` but for abort signals
* - When all signals have been aborted, the merged signal will be aborted
* - If one signal is `null`, no signal will be aborted
*/ function allAbortSignals(...signals) {
    const ac = new AbortController();
    const count = signals.length;
    let abortedCount = 0;
    const onAbort = ()=>{
        if (++abortedCount === count) ac.abort();
    };
    for (const signal of signals)if (signal === null || signal === void 0 ? void 0 : signal.aborted) onAbort();
    else signal === null || signal === void 0 || signal.addEventListener("abort", onAbort, {
        once: true
    });
    return ac.signal;
}
/**
* Like `Promise.race` but for abort signals
*
* Basically, a ponyfill for
* [`AbortSignal.any`](https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal/any_static).
*/ function raceAbortSignals(...signals) {
    const ac = new AbortController();
    for (const signal of signals)if (signal === null || signal === void 0 ? void 0 : signal.aborted) ac.abort();
    else signal === null || signal === void 0 || signal.addEventListener("abort", ()=>ac.abort(), {
        once: true
    });
    return ac.signal;
}
function abortSignalToPromise(signal) {
    return new Promise((_, reject)=>{
        if (signal.aborted) {
            reject(signal.reason);
            return;
        }
        signal.addEventListener("abort", ()=>{
            reject(signal.reason);
        }, {
            once: true
        });
    });
}
//#endregion
//#region src/links/httpBatchLink.ts
var import_objectSpread2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["require_objectSpread2"])(), 1);
/**
* @see https://trpc.io/docs/client/links/httpBatchLink
*/ function httpBatchLink(opts) {
    var _opts$maxURLLength, _opts$maxItems;
    const resolvedOpts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpUtils$2d$Bkv1johT$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["resolveHTTPLinkOptions"])(opts);
    const maxURLLength = (_opts$maxURLLength = opts.maxURLLength) !== null && _opts$maxURLLength !== void 0 ? _opts$maxURLLength : Infinity;
    const maxItems = (_opts$maxItems = opts.maxItems) !== null && _opts$maxItems !== void 0 ? _opts$maxItems : Infinity;
    return ()=>{
        const batchLoader = (type)=>{
            return {
                validate (batchOps) {
                    if (maxURLLength === Infinity && maxItems === Infinity) return true;
                    if (batchOps.length > maxItems) return false;
                    const path = batchOps.map((op)=>op.path).join(",");
                    const inputs = batchOps.map((op)=>op.input);
                    const url = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpUtils$2d$Bkv1johT$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getUrl"])((0, import_objectSpread2.default)((0, import_objectSpread2.default)({}, resolvedOpts), {}, {
                        type,
                        path,
                        inputs,
                        signal: null
                    }));
                    return url.length <= maxURLLength;
                },
                async fetch (batchOps) {
                    const path = batchOps.map((op)=>op.path).join(",");
                    const inputs = batchOps.map((op)=>op.input);
                    const signal = allAbortSignals(...batchOps.map((op)=>op.signal));
                    const res = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpUtils$2d$Bkv1johT$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsonHttpRequester"])((0, import_objectSpread2.default)((0, import_objectSpread2.default)({}, resolvedOpts), {}, {
                        path,
                        inputs,
                        type,
                        headers () {
                            if (!opts.headers) return {};
                            if (typeof opts.headers === "function") return opts.headers({
                                opList: batchOps
                            });
                            return opts.headers;
                        },
                        signal
                    }));
                    const resJSON = Array.isArray(res.json) ? res.json : batchOps.map(()=>res.json);
                    const result = resJSON.map((item)=>({
                            meta: res.meta,
                            json: item
                        }));
                    return result;
                }
            };
        };
        const query = dataLoader(batchLoader("query"));
        const mutation = dataLoader(batchLoader("mutation"));
        const loaders = {
            query,
            mutation
        };
        return ({ op })=>{
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$UMO3vUa_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["observable"])((observer)=>{
                /* istanbul ignore if -- @preserve */ if (op.type === "subscription") throw new Error("Subscriptions are unsupported by `httpLink` - use `httpSubscriptionLink` or `wsLink`");
                const loader = loaders[op.type];
                const promise = loader.load(op);
                let _res = void 0;
                promise.then((res)=>{
                    _res = res;
                    const transformed = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["transformResult"])(res.json, resolvedOpts.transformer.output);
                    if (!transformed.ok) {
                        observer.error(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$TRPCClientError$2d$CjKyS10w$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCClientError"].from(transformed.error, {
                            meta: res.meta
                        }));
                        return;
                    }
                    observer.next({
                        context: res.meta,
                        result: transformed.result
                    });
                    observer.complete();
                }).catch((err)=>{
                    observer.error(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$TRPCClientError$2d$CjKyS10w$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCClientError"].from(err, {
                        meta: _res === null || _res === void 0 ? void 0 : _res.meta
                    }));
                });
                return ()=>{};
            });
        };
    };
}
;
 //# sourceMappingURL=httpBatchLink-CA96-gnJ.mjs.map
}),
"[project]/node_modules/@trpc/client/dist/loggerLink-ineCN1PO.mjs [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "loggerLink": ()=>loggerLink
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/objectSpread2-BvkFp-_Y.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$UMO3vUa_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/observable-UMO3vUa_.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$CUiPknO$2d2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/observable-CUiPknO-.mjs [app-ssr] (ecmascript)");
;
;
//#region src/links/loggerLink.ts
var import_objectSpread2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["require_objectSpread2"])(), 1);
function isFormData(value) {
    if (typeof FormData === "undefined") return false;
    return value instanceof FormData;
}
const palettes = {
    css: {
        query: [
            "72e3ff",
            "3fb0d8"
        ],
        mutation: [
            "c5a3fc",
            "904dfc"
        ],
        subscription: [
            "ff49e1",
            "d83fbe"
        ]
    },
    ansi: {
        regular: {
            query: [
                "\x1B[30;46m",
                "\x1B[97;46m"
            ],
            mutation: [
                "\x1B[30;45m",
                "\x1B[97;45m"
            ],
            subscription: [
                "\x1B[30;42m",
                "\x1B[97;42m"
            ]
        },
        bold: {
            query: [
                "\x1B[1;30;46m",
                "\x1B[1;97;46m"
            ],
            mutation: [
                "\x1B[1;30;45m",
                "\x1B[1;97;45m"
            ],
            subscription: [
                "\x1B[1;30;42m",
                "\x1B[1;97;42m"
            ]
        }
    }
};
function constructPartsAndArgs(opts) {
    const { direction, type, withContext, path, id, input } = opts;
    const parts = [];
    const args = [];
    if (opts.colorMode === "none") parts.push(direction === "up" ? ">>" : "<<", type, `#${id}`, path);
    else if (opts.colorMode === "ansi") {
        const [lightRegular, darkRegular] = palettes.ansi.regular[type];
        const [lightBold, darkBold] = palettes.ansi.bold[type];
        const reset = "\x1B[0m";
        parts.push(direction === "up" ? lightRegular : darkRegular, direction === "up" ? ">>" : "<<", type, direction === "up" ? lightBold : darkBold, `#${id}`, path, reset);
    } else {
        const [light, dark] = palettes.css[type];
        const css = `
    background-color: #${direction === "up" ? light : dark};
    color: ${direction === "up" ? "black" : "white"};
    padding: 2px;
  `;
        parts.push("%c", direction === "up" ? ">>" : "<<", type, `#${id}`, `%c${path}%c`, "%O");
        args.push(css, `${css}; font-weight: bold;`, `${css}; font-weight: normal;`);
    }
    if (direction === "up") args.push(withContext ? {
        input,
        context: opts.context
    } : {
        input
    });
    else args.push((0, import_objectSpread2.default)({
        input,
        result: opts.result,
        elapsedMs: opts.elapsedMs
    }, withContext && {
        context: opts.context
    }));
    return {
        parts,
        args
    };
}
const defaultLogger = ({ c = console, colorMode = "css", withContext })=>(props)=>{
        const rawInput = props.input;
        const input = isFormData(rawInput) ? Object.fromEntries(rawInput) : rawInput;
        const { parts, args } = constructPartsAndArgs((0, import_objectSpread2.default)((0, import_objectSpread2.default)({}, props), {}, {
            colorMode,
            input,
            withContext
        }));
        const fn = props.direction === "down" && props.result && (props.result instanceof Error || "error" in props.result.result && props.result.result.error) ? "error" : "log";
        c[fn].apply(null, [
            parts.join(" ")
        ].concat(args));
    };
/**
* @see https://trpc.io/docs/v11/client/links/loggerLink
*/ function loggerLink(opts = {}) {
    var _opts$colorMode, _opts$withContext;
    const { enabled = ()=>true } = opts;
    const colorMode = (_opts$colorMode = opts.colorMode) !== null && _opts$colorMode !== void 0 ? _opts$colorMode : ("TURBOPACK compile-time truthy", 1) ? "ansi" : "TURBOPACK unreachable";
    const withContext = (_opts$withContext = opts.withContext) !== null && _opts$withContext !== void 0 ? _opts$withContext : colorMode === "css";
    const { logger = defaultLogger({
        c: opts.console,
        colorMode,
        withContext
    }) } = opts;
    return ()=>{
        return ({ op, next })=>{
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$UMO3vUa_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["observable"])((observer)=>{
                if (enabled((0, import_objectSpread2.default)((0, import_objectSpread2.default)({}, op), {}, {
                    direction: "up"
                }))) logger((0, import_objectSpread2.default)((0, import_objectSpread2.default)({}, op), {}, {
                    direction: "up"
                }));
                const requestStartTime = Date.now();
                function logResult(result) {
                    const elapsedMs = Date.now() - requestStartTime;
                    if (enabled((0, import_objectSpread2.default)((0, import_objectSpread2.default)({}, op), {}, {
                        direction: "down",
                        result
                    }))) logger((0, import_objectSpread2.default)((0, import_objectSpread2.default)({}, op), {}, {
                        direction: "down",
                        elapsedMs,
                        result
                    }));
                }
                return next(op).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$CUiPknO$2d2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["tap"])({
                    next (result) {
                        logResult(result);
                    },
                    error (result) {
                        logResult(result);
                    }
                })).subscribe(observer);
            });
        };
    };
}
;
 //# sourceMappingURL=loggerLink-ineCN1PO.mjs.map
}),
"[project]/node_modules/@trpc/client/dist/wsLink-H5IjZfJW.mjs [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "createWSClient": ()=>createWSClient,
    "resultOf": ()=>resultOf,
    "wsLink": ()=>wsLink
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/objectSpread2-BvkFp-_Y.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$TRPCClientError$2d$CjKyS10w$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/TRPCClientError-CjKyS10w.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$unstable$2d$internals$2d$Bg7n9BBj$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/unstable-internals-Bg7n9BBj.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$CUiPknO$2d2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/observable-CUiPknO-.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$UMO3vUa_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/observable-UMO3vUa_.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/utils-DdbbrDku.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/tracked-gU3ttYjg.mjs [app-ssr] (ecmascript)");
;
;
;
;
;
//#region src/links/wsLink/wsClient/options.ts
const lazyDefaults = {
    enabled: false,
    closeMs: 0
};
const keepAliveDefaults = {
    enabled: false,
    pongTimeoutMs: 1e3,
    intervalMs: 5e3
};
/**
* Calculates a delay for exponential backoff based on the retry attempt index.
* The delay starts at 0 for the first attempt and doubles for each subsequent attempt,
* capped at 30 seconds.
*/ const exponentialBackoff = (attemptIndex)=>{
    return attemptIndex === 0 ? 0 : Math.min(1e3 * 2 ** attemptIndex, 3e4);
};
//#endregion
//#region src/links/internals/urlWithConnectionParams.ts
/**
* Get the result of a value or function that returns a value
* It also optionally accepts typesafe arguments for the function
*/ const resultOf = (value, ...args)=>{
    return typeof value === "function" ? value(...args) : value;
};
//#endregion
//#region src/links/wsLink/wsClient/utils.ts
var import_defineProperty$3 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["require_defineProperty"])(), 1);
var TRPCWebSocketClosedError = class TRPCWebSocketClosedError extends Error {
    constructor(opts){
        super(opts.message, {
            cause: opts.cause
        });
        this.name = "TRPCWebSocketClosedError";
        Object.setPrototypeOf(this, TRPCWebSocketClosedError.prototype);
    }
};
/**
* Utility class for managing a timeout that can be started, stopped, and reset.
* Useful for scenarios where the timeout duration is reset dynamically based on events.
*/ var ResettableTimeout = class {
    constructor(onTimeout, timeoutMs){
        this.onTimeout = onTimeout;
        this.timeoutMs = timeoutMs;
        (0, import_defineProperty$3.default)(this, "timeout", void 0);
    }
    /**
	* Resets the current timeout, restarting it with the same duration.
	* Does nothing if no timeout is active.
	*/ reset() {
        if (!this.timeout) return;
        clearTimeout(this.timeout);
        this.timeout = setTimeout(this.onTimeout, this.timeoutMs);
    }
    start() {
        clearTimeout(this.timeout);
        this.timeout = setTimeout(this.onTimeout, this.timeoutMs);
    }
    stop() {
        clearTimeout(this.timeout);
        this.timeout = void 0;
    }
};
function withResolvers() {
    let resolve;
    let reject;
    const promise = new Promise((res, rej)=>{
        resolve = res;
        reject = rej;
    });
    return {
        promise,
        resolve,
        reject
    };
}
/**
* Resolves a WebSocket URL and optionally appends connection parameters.
*
* If connectionParams are provided, appends 'connectionParams=1' query parameter.
*/ async function prepareUrl(urlOptions) {
    const url = await resultOf(urlOptions.url);
    if (!urlOptions.connectionParams) return url;
    const prefix = url.includes("?") ? "&" : "?";
    const connectionParams = `${prefix}connectionParams=1`;
    return url + connectionParams;
}
async function buildConnectionMessage(connectionParams) {
    const message = {
        method: "connectionParams",
        data: await resultOf(connectionParams)
    };
    return JSON.stringify(message);
}
//#endregion
//#region src/links/wsLink/wsClient/requestManager.ts
var import_defineProperty$2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["require_defineProperty"])(), 1);
/**
* Manages WebSocket requests, tracking their lifecycle and providing utility methods
* for handling outgoing and pending requests.
*
* - **Outgoing requests**: Requests that are queued and waiting to be sent.
* - **Pending requests**: Requests that have been sent and are in flight awaiting a response.
*   For subscriptions, multiple responses may be received until the subscription is closed.
*/ var RequestManager = class {
    constructor(){
        (0, import_defineProperty$2.default)(this, "outgoingRequests", new Array());
        (0, import_defineProperty$2.default)(this, "pendingRequests", {});
    }
    /**
	* Registers a new request by adding it to the outgoing queue and setting up
	* callbacks for lifecycle events such as completion or error.
	*
	* @param message - The outgoing message to be sent.
	* @param callbacks - Callback functions to observe the request's state.
	* @returns A cleanup function to manually remove the request.
	*/ register(message, callbacks) {
        const { promise: end, resolve } = withResolvers();
        this.outgoingRequests.push({
            id: String(message.id),
            message,
            end,
            callbacks: {
                next: callbacks.next,
                complete: ()=>{
                    callbacks.complete();
                    resolve();
                },
                error: (e)=>{
                    callbacks.error(e);
                    resolve();
                }
            }
        });
        return ()=>{
            this.delete(message.id);
            callbacks.complete();
            resolve();
        };
    }
    /**
	* Deletes a request from both the outgoing and pending collections, if it exists.
	*/ delete(messageId) {
        if (messageId === null) return;
        this.outgoingRequests = this.outgoingRequests.filter(({ id })=>id !== String(messageId));
        delete this.pendingRequests[String(messageId)];
    }
    /**
	* Moves all outgoing requests to the pending state and clears the outgoing queue.
	*
	* The caller is expected to handle the actual sending of the requests
	* (e.g., sending them over the network) after this method is called.
	*
	* @returns The list of requests that were transitioned to the pending state.
	*/ flush() {
        const requests = this.outgoingRequests;
        this.outgoingRequests = [];
        for (const request of requests)this.pendingRequests[request.id] = request;
        return requests;
    }
    /**
	* Retrieves all currently pending requests, which are in flight awaiting responses
	* or handling ongoing subscriptions.
	*/ getPendingRequests() {
        return Object.values(this.pendingRequests);
    }
    /**
	* Retrieves a specific pending request by its message ID.
	*/ getPendingRequest(messageId) {
        if (messageId === null) return null;
        return this.pendingRequests[String(messageId)];
    }
    /**
	* Retrieves all outgoing requests, which are waiting to be sent.
	*/ getOutgoingRequests() {
        return this.outgoingRequests;
    }
    /**
	* Retrieves all requests, both outgoing and pending, with their respective states.
	*
	* @returns An array of all requests with their state ("outgoing" or "pending").
	*/ getRequests() {
        return [
            ...this.getOutgoingRequests().map((request)=>({
                    state: "outgoing",
                    message: request.message,
                    end: request.end,
                    callbacks: request.callbacks
                })),
            ...this.getPendingRequests().map((request)=>({
                    state: "pending",
                    message: request.message,
                    end: request.end,
                    callbacks: request.callbacks
                }))
        ];
    }
    /**
	* Checks if there are any pending requests, including ongoing subscriptions.
	*/ hasPendingRequests() {
        return this.getPendingRequests().length > 0;
    }
    /**
	* Checks if there are any pending subscriptions
	*/ hasPendingSubscriptions() {
        return this.getPendingRequests().some((request)=>request.message.method === "subscription");
    }
    /**
	* Checks if there are any outgoing requests waiting to be sent.
	*/ hasOutgoingRequests() {
        return this.outgoingRequests.length > 0;
    }
};
//#endregion
//#region src/links/wsLink/wsClient/wsConnection.ts
var import_defineProperty$1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["require_defineProperty"])(), 1);
/**
* Opens a WebSocket connection asynchronously and returns a promise
* that resolves when the connection is successfully established.
* The promise rejects if an error occurs during the connection attempt.
*/ function asyncWsOpen(ws) {
    const { promise, resolve, reject } = withResolvers();
    ws.addEventListener("open", ()=>{
        ws.removeEventListener("error", reject);
        resolve();
    });
    ws.addEventListener("error", reject);
    return promise;
}
/**
* Sets up a periodic ping-pong mechanism to keep the WebSocket connection alive.
*
* - Sends "PING" messages at regular intervals defined by `intervalMs`.
* - If a "PONG" response is not received within the `pongTimeoutMs`, the WebSocket is closed.
* - The ping timer resets upon receiving any message to maintain activity.
* - Automatically starts the ping process when the WebSocket connection is opened.
* - Cleans up timers when the WebSocket is closed.
*
* @param ws - The WebSocket instance to manage.
* @param options - Configuration options for ping-pong intervals and timeouts.
*/ function setupPingInterval(ws, { intervalMs, pongTimeoutMs }) {
    let pingTimeout;
    let pongTimeout;
    function start() {
        pingTimeout = setTimeout(()=>{
            ws.send("PING");
            pongTimeout = setTimeout(()=>{
                ws.close();
            }, pongTimeoutMs);
        }, intervalMs);
    }
    function reset() {
        clearTimeout(pingTimeout);
        start();
    }
    function pong() {
        clearTimeout(pongTimeout);
        reset();
    }
    ws.addEventListener("open", start);
    ws.addEventListener("message", ({ data })=>{
        clearTimeout(pingTimeout);
        start();
        if (data === "PONG") pong();
    });
    ws.addEventListener("close", ()=>{
        clearTimeout(pingTimeout);
        clearTimeout(pongTimeout);
    });
}
/**
* Manages a WebSocket connection with support for reconnection, keep-alive mechanisms,
* and observable state tracking.
*/ var WsConnection = class WsConnection {
    constructor(opts){
        var _opts$WebSocketPonyfi;
        (0, import_defineProperty$1.default)(this, "id", ++WsConnection.connectCount);
        (0, import_defineProperty$1.default)(this, "WebSocketPonyfill", void 0);
        (0, import_defineProperty$1.default)(this, "urlOptions", void 0);
        (0, import_defineProperty$1.default)(this, "keepAliveOpts", void 0);
        (0, import_defineProperty$1.default)(this, "wsObservable", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$CUiPknO$2d2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["behaviorSubject"])(null));
        (0, import_defineProperty$1.default)(this, "openPromise", null);
        this.WebSocketPonyfill = (_opts$WebSocketPonyfi = opts.WebSocketPonyfill) !== null && _opts$WebSocketPonyfi !== void 0 ? _opts$WebSocketPonyfi : WebSocket;
        if (!this.WebSocketPonyfill) throw new Error("No WebSocket implementation found - you probably don't want to use this on the server, but if you do you need to pass a `WebSocket`-ponyfill");
        this.urlOptions = opts.urlOptions;
        this.keepAliveOpts = opts.keepAlive;
    }
    get ws() {
        return this.wsObservable.get();
    }
    set ws(ws) {
        this.wsObservable.next(ws);
    }
    /**
	* Checks if the WebSocket connection is open and ready to communicate.
	*/ isOpen() {
        return !!this.ws && this.ws.readyState === this.WebSocketPonyfill.OPEN && !this.openPromise;
    }
    /**
	* Checks if the WebSocket connection is closed or in the process of closing.
	*/ isClosed() {
        return !!this.ws && (this.ws.readyState === this.WebSocketPonyfill.CLOSING || this.ws.readyState === this.WebSocketPonyfill.CLOSED);
    }
    async open() {
        var _this = this;
        if (_this.openPromise) return _this.openPromise;
        _this.id = ++WsConnection.connectCount;
        const wsPromise = prepareUrl(_this.urlOptions).then((url)=>new _this.WebSocketPonyfill(url));
        _this.openPromise = wsPromise.then(async (ws)=>{
            _this.ws = ws;
            ws.addEventListener("message", function({ data }) {
                if (data === "PING") this.send("PONG");
            });
            if (_this.keepAliveOpts.enabled) setupPingInterval(ws, _this.keepAliveOpts);
            ws.addEventListener("close", ()=>{
                if (_this.ws === ws) _this.ws = null;
            });
            await asyncWsOpen(ws);
            if (_this.urlOptions.connectionParams) ws.send(await buildConnectionMessage(_this.urlOptions.connectionParams));
        });
        try {
            await _this.openPromise;
        } finally{
            _this.openPromise = null;
        }
    }
    /**
	* Closes the WebSocket connection gracefully.
	* Waits for any ongoing open operation to complete before closing.
	*/ async close() {
        var _this2 = this;
        try {
            await _this2.openPromise;
        } finally{
            var _this$ws;
            (_this$ws = _this2.ws) === null || _this$ws === void 0 || _this$ws.close();
        }
    }
};
(0, import_defineProperty$1.default)(WsConnection, "connectCount", 0);
/**
* Provides a backward-compatible representation of the connection state.
*/ function backwardCompatibility(connection) {
    if (connection.isOpen()) return {
        id: connection.id,
        state: "open",
        ws: connection.ws
    };
    if (connection.isClosed()) return {
        id: connection.id,
        state: "closed",
        ws: connection.ws
    };
    if (!connection.ws) return null;
    return {
        id: connection.id,
        state: "connecting",
        ws: connection.ws
    };
}
//#endregion
//#region src/links/wsLink/wsClient/wsClient.ts
var import_defineProperty = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["require_defineProperty"])(), 1);
var import_objectSpread2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["require_objectSpread2"])(), 1);
/**
* A WebSocket client for managing TRPC operations, supporting lazy initialization,
* reconnection, keep-alive, and request management.
*/ var WsClient = class {
    constructor(opts){
        var _opts$retryDelayMs;
        (0, import_defineProperty.default)(this, "connectionState", void 0);
        (0, import_defineProperty.default)(this, "allowReconnect", false);
        (0, import_defineProperty.default)(this, "requestManager", new RequestManager());
        (0, import_defineProperty.default)(this, "activeConnection", void 0);
        (0, import_defineProperty.default)(this, "reconnectRetryDelay", void 0);
        (0, import_defineProperty.default)(this, "inactivityTimeout", void 0);
        (0, import_defineProperty.default)(this, "callbacks", void 0);
        (0, import_defineProperty.default)(this, "lazyMode", void 0);
        (0, import_defineProperty.default)(this, "reconnecting", null);
        this.callbacks = {
            onOpen: opts.onOpen,
            onClose: opts.onClose,
            onError: opts.onError
        };
        const lazyOptions = (0, import_objectSpread2.default)((0, import_objectSpread2.default)({}, lazyDefaults), opts.lazy);
        this.inactivityTimeout = new ResettableTimeout(()=>{
            if (this.requestManager.hasOutgoingRequests() || this.requestManager.hasPendingRequests()) {
                this.inactivityTimeout.reset();
                return;
            }
            this.close().catch(()=>null);
        }, lazyOptions.closeMs);
        this.activeConnection = new WsConnection({
            WebSocketPonyfill: opts.WebSocket,
            urlOptions: opts,
            keepAlive: (0, import_objectSpread2.default)((0, import_objectSpread2.default)({}, keepAliveDefaults), opts.keepAlive)
        });
        this.activeConnection.wsObservable.subscribe({
            next: (ws)=>{
                if (!ws) return;
                this.setupWebSocketListeners(ws);
            }
        });
        this.reconnectRetryDelay = (_opts$retryDelayMs = opts.retryDelayMs) !== null && _opts$retryDelayMs !== void 0 ? _opts$retryDelayMs : exponentialBackoff;
        this.lazyMode = lazyOptions.enabled;
        this.connectionState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$CUiPknO$2d2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["behaviorSubject"])({
            type: "state",
            state: lazyOptions.enabled ? "idle" : "connecting",
            error: null
        });
        if (!this.lazyMode) this.open().catch(()=>null);
    }
    /**
	* Opens the WebSocket connection. Handles reconnection attempts and updates
	* the connection state accordingly.
	*/ async open() {
        var _this = this;
        _this.allowReconnect = true;
        if (_this.connectionState.get().state !== "connecting") _this.connectionState.next({
            type: "state",
            state: "connecting",
            error: null
        });
        try {
            await _this.activeConnection.open();
        } catch (error) {
            _this.reconnect(new TRPCWebSocketClosedError({
                message: "Initialization error",
                cause: error
            }));
            return _this.reconnecting;
        }
    }
    /**
	* Closes the WebSocket connection and stops managing requests.
	* Ensures all outgoing and pending requests are properly finalized.
	*/ async close() {
        var _this2 = this;
        _this2.allowReconnect = false;
        _this2.inactivityTimeout.stop();
        const requestsToAwait = [];
        for (const request of _this2.requestManager.getRequests())if (request.message.method === "subscription") request.callbacks.complete();
        else if (request.state === "outgoing") request.callbacks.error(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$TRPCClientError$2d$CjKyS10w$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCClientError"].from(new TRPCWebSocketClosedError({
            message: "Closed before connection was established"
        })));
        else requestsToAwait.push(request.end);
        await Promise.all(requestsToAwait).catch(()=>null);
        await _this2.activeConnection.close().catch(()=>null);
        _this2.connectionState.next({
            type: "state",
            state: "idle",
            error: null
        });
    }
    /**
	* Method to request the server.
	* Handles data transformation, batching of requests, and subscription lifecycle.
	*
	* @param op - The operation details including id, type, path, input and signal
	* @param transformer - Data transformer for serializing requests and deserializing responses
	* @param lastEventId - Optional ID of the last received event for subscriptions
	*
	* @returns An observable that emits operation results and handles cleanup
	*/ request({ op: { id, type, path, input, signal }, transformer, lastEventId }) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$UMO3vUa_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["observable"])((observer)=>{
            const abort = this.batchSend({
                id,
                method: type,
                params: {
                    input: transformer.input.serialize(input),
                    path,
                    lastEventId
                }
            }, (0, import_objectSpread2.default)((0, import_objectSpread2.default)({}, observer), {}, {
                next (event) {
                    const transformed = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["transformResult"])(event, transformer.output);
                    if (!transformed.ok) {
                        observer.error(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$TRPCClientError$2d$CjKyS10w$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCClientError"].from(transformed.error));
                        return;
                    }
                    observer.next({
                        result: transformed.result
                    });
                }
            }));
            return ()=>{
                abort();
                if (type === "subscription" && this.activeConnection.isOpen()) this.send({
                    id,
                    method: "subscription.stop"
                });
                signal === null || signal === void 0 || signal.removeEventListener("abort", abort);
            };
        });
    }
    get connection() {
        return backwardCompatibility(this.activeConnection);
    }
    reconnect(closedError) {
        var _this3 = this;
        this.connectionState.next({
            type: "state",
            state: "connecting",
            error: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$TRPCClientError$2d$CjKyS10w$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCClientError"].from(closedError)
        });
        if (this.reconnecting) return;
        const tryReconnect = async (attemptIndex)=>{
            try {
                await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sleep"])(_this3.reconnectRetryDelay(attemptIndex));
                if (_this3.allowReconnect) {
                    await _this3.activeConnection.close();
                    await _this3.activeConnection.open();
                    if (_this3.requestManager.hasPendingRequests()) _this3.send(_this3.requestManager.getPendingRequests().map(({ message })=>message));
                }
                _this3.reconnecting = null;
            } catch (_unused) {
                await tryReconnect(attemptIndex + 1);
            }
        };
        this.reconnecting = tryReconnect(0);
    }
    setupWebSocketListeners(ws) {
        var _this4 = this;
        const handleCloseOrError = (cause)=>{
            const reqs = this.requestManager.getPendingRequests();
            for (const { message, callbacks } of reqs){
                if (message.method === "subscription") continue;
                callbacks.error(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$TRPCClientError$2d$CjKyS10w$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCClientError"].from(cause !== null && cause !== void 0 ? cause : new TRPCWebSocketClosedError({
                    message: "WebSocket closed",
                    cause
                })));
                this.requestManager.delete(message.id);
            }
        };
        ws.addEventListener("open", ()=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["run"])(async ()=>{
                var _this$callbacks$onOpe, _this$callbacks;
                if (_this4.lazyMode) _this4.inactivityTimeout.start();
                (_this$callbacks$onOpe = (_this$callbacks = _this4.callbacks).onOpen) === null || _this$callbacks$onOpe === void 0 || _this$callbacks$onOpe.call(_this$callbacks);
                _this4.connectionState.next({
                    type: "state",
                    state: "pending",
                    error: null
                });
            }).catch((error)=>{
                ws.close(3e3);
                handleCloseOrError(error);
            });
        });
        ws.addEventListener("message", ({ data })=>{
            this.inactivityTimeout.reset();
            if (typeof data !== "string" || [
                "PING",
                "PONG"
            ].includes(data)) return;
            const incomingMessage = JSON.parse(data);
            if ("method" in incomingMessage) {
                this.handleIncomingRequest(incomingMessage);
                return;
            }
            this.handleResponseMessage(incomingMessage);
        });
        ws.addEventListener("close", (event)=>{
            var _this$callbacks$onClo, _this$callbacks2;
            handleCloseOrError(event);
            (_this$callbacks$onClo = (_this$callbacks2 = this.callbacks).onClose) === null || _this$callbacks$onClo === void 0 || _this$callbacks$onClo.call(_this$callbacks2, event);
            if (!this.lazyMode || this.requestManager.hasPendingSubscriptions()) this.reconnect(new TRPCWebSocketClosedError({
                message: "WebSocket closed",
                cause: event
            }));
        });
        ws.addEventListener("error", (event)=>{
            var _this$callbacks$onErr, _this$callbacks3;
            handleCloseOrError(event);
            (_this$callbacks$onErr = (_this$callbacks3 = this.callbacks).onError) === null || _this$callbacks$onErr === void 0 || _this$callbacks$onErr.call(_this$callbacks3, event);
            this.reconnect(new TRPCWebSocketClosedError({
                message: "WebSocket closed",
                cause: event
            }));
        });
    }
    handleResponseMessage(message) {
        const request = this.requestManager.getPendingRequest(message.id);
        if (!request) return;
        request.callbacks.next(message);
        let completed = true;
        if ("result" in message && request.message.method === "subscription") {
            if (message.result.type === "data") request.message.params.lastEventId = message.result.id;
            if (message.result.type !== "stopped") completed = false;
        }
        if (completed) {
            request.callbacks.complete();
            this.requestManager.delete(message.id);
        }
    }
    handleIncomingRequest(message) {
        if (message.method === "reconnect") this.reconnect(new TRPCWebSocketClosedError({
            message: "Server requested reconnect"
        }));
    }
    /**
	* Sends a message or batch of messages directly to the server.
	*/ send(messageOrMessages) {
        if (!this.activeConnection.isOpen()) throw new Error("Active connection is not open");
        const messages = messageOrMessages instanceof Array ? messageOrMessages : [
            messageOrMessages
        ];
        this.activeConnection.ws.send(JSON.stringify(messages.length === 1 ? messages[0] : messages));
    }
    /**
	* Groups requests for batch sending.
	*
	* @returns A function to abort the batched request.
	*/ batchSend(message, callbacks) {
        var _this5 = this;
        this.inactivityTimeout.reset();
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["run"])(async ()=>{
            if (!_this5.activeConnection.isOpen()) await _this5.open();
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sleep"])(0);
            if (!_this5.requestManager.hasOutgoingRequests()) return;
            _this5.send(_this5.requestManager.flush().map(({ message: message$1 })=>message$1));
        }).catch((err)=>{
            this.requestManager.delete(message.id);
            callbacks.error(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$TRPCClientError$2d$CjKyS10w$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCClientError"].from(err));
        });
        return this.requestManager.register(message, callbacks);
    }
};
//#endregion
//#region src/links/wsLink/createWsClient.ts
function createWSClient(opts) {
    return new WsClient(opts);
}
//#endregion
//#region src/links/wsLink/wsLink.ts
function wsLink(opts) {
    const { client } = opts;
    const transformer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$unstable$2d$internals$2d$Bg7n9BBj$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTransformer"])(opts.transformer);
    return ()=>{
        return ({ op })=>{
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$UMO3vUa_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["observable"])((observer)=>{
                const connStateSubscription = op.type === "subscription" ? client.connectionState.subscribe({
                    next (result) {
                        observer.next({
                            result,
                            context: op.context
                        });
                    }
                }) : null;
                const requestSubscription = client.request({
                    op,
                    transformer
                }).subscribe(observer);
                return ()=>{
                    requestSubscription.unsubscribe();
                    connStateSubscription === null || connStateSubscription === void 0 || connStateSubscription.unsubscribe();
                };
            });
        };
    };
}
;
 //# sourceMappingURL=wsLink-H5IjZfJW.mjs.map
}),
"[project]/node_modules/@trpc/client/dist/index.mjs [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "TRPCUntypedClient": ()=>TRPCUntypedClient,
    "clientCallTypeToProcedureType": ()=>clientCallTypeToProcedureType,
    "createTRPCClient": ()=>createTRPCClient,
    "createTRPCClientProxy": ()=>createTRPCClientProxy,
    "createTRPCProxyClient": ()=>createTRPCClient,
    "createTRPCUntypedClient": ()=>createTRPCUntypedClient,
    "experimental_localLink": ()=>experimental_localLink,
    "getUntypedClient": ()=>getUntypedClient,
    "httpBatchStreamLink": ()=>httpBatchStreamLink,
    "httpSubscriptionLink": ()=>httpSubscriptionLink,
    "retryLink": ()=>retryLink,
    "unstable_httpBatchStreamLink": ()=>unstable_httpBatchStreamLink,
    "unstable_httpSubscriptionLink": ()=>unstable_httpSubscriptionLink,
    "unstable_localLink": ()=>unstable_localLink
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/objectSpread2-BvkFp-_Y.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$splitLink$2d$B7Cuf2c_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/splitLink-B7Cuf2c_.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$TRPCClientError$2d$CjKyS10w$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/TRPCClientError-CjKyS10w.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpUtils$2d$Bkv1johT$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/httpUtils-Bkv1johT.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpLink$2d$CYOcG9kQ$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/httpLink-CYOcG9kQ.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpBatchLink$2d$CA96$2d$gnJ$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/httpBatchLink-CA96-gnJ.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$unstable$2d$internals$2d$Bg7n9BBj$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/unstable-internals-Bg7n9BBj.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$loggerLink$2d$ineCN1PO$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/loggerLink-ineCN1PO.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$wsLink$2d$H5IjZfJW$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/wsLink-H5IjZfJW.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$CUiPknO$2d2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/observable-CUiPknO-.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$UMO3vUa_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/observable-UMO3vUa_.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/tracked-gU3ttYjg.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/getErrorShape-Uhlrl4Bk.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$resolveResponse$2d$CzlbRpCI$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/resolveResponse-CzlbRpCI.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/utils-DdbbrDku.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__getErrorShape__as__getTRPCErrorShape$3e$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/getErrorShape-Uhlrl4Bk.mjs [app-ssr] (ecmascript) <export getErrorShape as getTRPCErrorShape>");
;
;
;
;
;
;
;
;
;
;
;
;
;
//#region src/internals/TRPCUntypedClient.ts
var import_defineProperty = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["require_defineProperty"])(), 1);
var import_objectSpread2$4 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["require_objectSpread2"])(), 1);
var TRPCUntypedClient = class {
    constructor(opts){
        (0, import_defineProperty.default)(this, "links", void 0);
        (0, import_defineProperty.default)(this, "runtime", void 0);
        (0, import_defineProperty.default)(this, "requestId", void 0);
        this.requestId = 0;
        this.runtime = {};
        this.links = opts.links.map((link)=>link(this.runtime));
    }
    $request(opts) {
        var _opts$context;
        const chain$ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$splitLink$2d$B7Cuf2c_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createChain"])({
            links: this.links,
            op: (0, import_objectSpread2$4.default)((0, import_objectSpread2$4.default)({}, opts), {}, {
                context: (_opts$context = opts.context) !== null && _opts$context !== void 0 ? _opts$context : {},
                id: ++this.requestId
            })
        });
        return chain$.pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$CUiPknO$2d2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["share"])());
    }
    async requestAsPromise(opts) {
        var _this = this;
        try {
            const req$ = _this.$request(opts);
            const envelope = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$UMO3vUa_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["observableToPromise"])(req$);
            const data = envelope.result.data;
            return data;
        } catch (err) {
            throw __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$TRPCClientError$2d$CjKyS10w$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCClientError"].from(err);
        }
    }
    query(path, input, opts) {
        return this.requestAsPromise({
            type: "query",
            path,
            input,
            context: opts === null || opts === void 0 ? void 0 : opts.context,
            signal: opts === null || opts === void 0 ? void 0 : opts.signal
        });
    }
    mutation(path, input, opts) {
        return this.requestAsPromise({
            type: "mutation",
            path,
            input,
            context: opts === null || opts === void 0 ? void 0 : opts.context,
            signal: opts === null || opts === void 0 ? void 0 : opts.signal
        });
    }
    subscription(path, input, opts) {
        const observable$ = this.$request({
            type: "subscription",
            path,
            input,
            context: opts.context,
            signal: opts.signal
        });
        return observable$.subscribe({
            next (envelope) {
                switch(envelope.result.type){
                    case "state":
                        {
                            var _opts$onConnectionSta;
                            (_opts$onConnectionSta = opts.onConnectionStateChange) === null || _opts$onConnectionSta === void 0 || _opts$onConnectionSta.call(opts, envelope.result);
                            break;
                        }
                    case "started":
                        {
                            var _opts$onStarted;
                            (_opts$onStarted = opts.onStarted) === null || _opts$onStarted === void 0 || _opts$onStarted.call(opts, {
                                context: envelope.context
                            });
                            break;
                        }
                    case "stopped":
                        {
                            var _opts$onStopped;
                            (_opts$onStopped = opts.onStopped) === null || _opts$onStopped === void 0 || _opts$onStopped.call(opts);
                            break;
                        }
                    case "data":
                    case void 0:
                        {
                            var _opts$onData;
                            (_opts$onData = opts.onData) === null || _opts$onData === void 0 || _opts$onData.call(opts, envelope.result.data);
                            break;
                        }
                }
            },
            error (err) {
                var _opts$onError;
                (_opts$onError = opts.onError) === null || _opts$onError === void 0 || _opts$onError.call(opts, err);
            },
            complete () {
                var _opts$onComplete;
                (_opts$onComplete = opts.onComplete) === null || _opts$onComplete === void 0 || _opts$onComplete.call(opts);
            }
        });
    }
};
//#endregion
//#region src/createTRPCUntypedClient.ts
function createTRPCUntypedClient(opts) {
    return new TRPCUntypedClient(opts);
}
//#endregion
//#region src/createTRPCClient.ts
const untypedClientSymbol = Symbol.for("trpc_untypedClient");
const clientCallTypeMap = {
    query: "query",
    mutate: "mutation",
    subscribe: "subscription"
};
/** @internal */ const clientCallTypeToProcedureType = (clientCallType)=>{
    return clientCallTypeMap[clientCallType];
};
/**
* @internal
*/ function createTRPCClientProxy(client) {
    const proxy = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createRecursiveProxy"])(({ path, args })=>{
        const pathCopy = [
            ...path
        ];
        const procedureType = clientCallTypeToProcedureType(pathCopy.pop());
        const fullPath = pathCopy.join(".");
        return client[procedureType](fullPath, ...args);
    });
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createFlatProxy"])((key)=>{
        if (key === untypedClientSymbol) return client;
        return proxy[key];
    });
}
function createTRPCClient(opts) {
    const client = new TRPCUntypedClient(opts);
    const proxy = createTRPCClientProxy(client);
    return proxy;
}
/**
* Get an untyped client from a proxy client
* @internal
*/ function getUntypedClient(client) {
    return client[untypedClientSymbol];
}
//#endregion
//#region src/links/httpBatchStreamLink.ts
var import_objectSpread2$3 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["require_objectSpread2"])(), 1);
/**
* @see https://trpc.io/docs/client/links/httpBatchStreamLink
*/ function httpBatchStreamLink(opts) {
    var _opts$maxURLLength, _opts$maxItems;
    const resolvedOpts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpUtils$2d$Bkv1johT$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["resolveHTTPLinkOptions"])(opts);
    const maxURLLength = (_opts$maxURLLength = opts.maxURLLength) !== null && _opts$maxURLLength !== void 0 ? _opts$maxURLLength : Infinity;
    const maxItems = (_opts$maxItems = opts.maxItems) !== null && _opts$maxItems !== void 0 ? _opts$maxItems : Infinity;
    return ()=>{
        const batchLoader = (type)=>{
            return {
                validate (batchOps) {
                    if (maxURLLength === Infinity && maxItems === Infinity) return true;
                    if (batchOps.length > maxItems) return false;
                    const path = batchOps.map((op)=>op.path).join(",");
                    const inputs = batchOps.map((op)=>op.input);
                    const url = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpUtils$2d$Bkv1johT$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getUrl"])((0, import_objectSpread2$3.default)((0, import_objectSpread2$3.default)({}, resolvedOpts), {}, {
                        type,
                        path,
                        inputs,
                        signal: null
                    }));
                    return url.length <= maxURLLength;
                },
                async fetch (batchOps) {
                    const path = batchOps.map((op)=>op.path).join(",");
                    const inputs = batchOps.map((op)=>op.input);
                    const batchSignals = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpBatchLink$2d$CA96$2d$gnJ$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["allAbortSignals"])(...batchOps.map((op)=>op.signal));
                    const abortController = new AbortController();
                    const responsePromise = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpUtils$2d$Bkv1johT$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["fetchHTTPResponse"])((0, import_objectSpread2$3.default)((0, import_objectSpread2$3.default)({}, resolvedOpts), {}, {
                        signal: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpBatchLink$2d$CA96$2d$gnJ$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["raceAbortSignals"])(batchSignals, abortController.signal),
                        type,
                        contentTypeHeader: "application/json",
                        trpcAcceptHeader: "application/jsonl",
                        getUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpUtils$2d$Bkv1johT$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getUrl"],
                        getBody: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpUtils$2d$Bkv1johT$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getBody"],
                        inputs,
                        path,
                        headers () {
                            if (!opts.headers) return {};
                            if (typeof opts.headers === "function") return opts.headers({
                                opList: batchOps
                            });
                            return opts.headers;
                        }
                    }));
                    const res = await responsePromise;
                    const [head] = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$resolveResponse$2d$CzlbRpCI$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsonlStreamConsumer"])({
                        from: res.body,
                        deserialize: resolvedOpts.transformer.output.deserialize,
                        formatError (opts$1) {
                            const error = opts$1.error;
                            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$TRPCClientError$2d$CjKyS10w$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCClientError"].from({
                                error
                            });
                        },
                        abortController
                    });
                    const promises = Object.keys(batchOps).map(async (key)=>{
                        let json = await Promise.resolve(head[key]);
                        if ("result" in json) {
                            /**
							* Not very pretty, but we need to unwrap nested data as promises
							* Our stream producer will only resolve top-level async values or async values that are directly nested in another async value
							*/ const result = await Promise.resolve(json.result);
                            json = {
                                result: {
                                    data: await Promise.resolve(result.data)
                                }
                            };
                        }
                        return {
                            json,
                            meta: {
                                response: res
                            }
                        };
                    });
                    return promises;
                }
            };
        };
        const query = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpBatchLink$2d$CA96$2d$gnJ$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["dataLoader"])(batchLoader("query"));
        const mutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpBatchLink$2d$CA96$2d$gnJ$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["dataLoader"])(batchLoader("mutation"));
        const loaders = {
            query,
            mutation
        };
        return ({ op })=>{
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$UMO3vUa_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["observable"])((observer)=>{
                /* istanbul ignore if -- @preserve */ if (op.type === "subscription") throw new Error("Subscriptions are unsupported by `httpBatchStreamLink` - use `httpSubscriptionLink` or `wsLink`");
                const loader = loaders[op.type];
                const promise = loader.load(op);
                let _res = void 0;
                promise.then((res)=>{
                    _res = res;
                    if ("error" in res.json) {
                        observer.error(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$TRPCClientError$2d$CjKyS10w$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCClientError"].from(res.json, {
                            meta: res.meta
                        }));
                        return;
                    } else if ("result" in res.json) {
                        observer.next({
                            context: res.meta,
                            result: res.json.result
                        });
                        observer.complete();
                        return;
                    }
                    observer.complete();
                }).catch((err)=>{
                    observer.error(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$TRPCClientError$2d$CjKyS10w$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCClientError"].from(err, {
                        meta: _res === null || _res === void 0 ? void 0 : _res.meta
                    }));
                });
                return ()=>{};
            });
        };
    };
}
/**
* @deprecated use {@link httpBatchStreamLink} instead
*/ const unstable_httpBatchStreamLink = httpBatchStreamLink;
//#endregion
//#region src/internals/inputWithTrackedEventId.ts
var import_objectSpread2$2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["require_objectSpread2"])(), 1);
function inputWithTrackedEventId(input, lastEventId) {
    if (!lastEventId) return input;
    if (input != null && typeof input !== "object") return input;
    return (0, import_objectSpread2$2.default)((0, import_objectSpread2$2.default)({}, input !== null && input !== void 0 ? input : {}), {}, {
        lastEventId
    });
}
//#endregion
//#region ../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/asyncIterator.js
var require_asyncIterator = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__commonJS"])({
    "../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/asyncIterator.js" (exports, module) {
        function _asyncIterator$1(r) {
            var n, t, o, e = 2;
            for("undefined" != typeof Symbol && (t = Symbol.asyncIterator, o = Symbol.iterator); e--;){
                if (t && null != (n = r[t])) return n.call(r);
                if (o && null != (n = r[o])) return new AsyncFromSyncIterator(n.call(r));
                t = "@@asyncIterator", o = "@@iterator";
            }
            throw new TypeError("Object is not async iterable");
        }
        function AsyncFromSyncIterator(r) {
            function AsyncFromSyncIteratorContinuation(r$1) {
                if (Object(r$1) !== r$1) return Promise.reject(new TypeError(r$1 + " is not an object."));
                var n = r$1.done;
                return Promise.resolve(r$1.value).then(function(r$2) {
                    return {
                        value: r$2,
                        done: n
                    };
                });
            }
            return AsyncFromSyncIterator = function AsyncFromSyncIterator$1(r$1) {
                this.s = r$1, this.n = r$1.next;
            }, AsyncFromSyncIterator.prototype = {
                s: null,
                n: null,
                next: function next() {
                    return AsyncFromSyncIteratorContinuation(this.n.apply(this.s, arguments));
                },
                "return": function _return(r$1) {
                    var n = this.s["return"];
                    return void 0 === n ? Promise.resolve({
                        value: r$1,
                        done: !0
                    }) : AsyncFromSyncIteratorContinuation(n.apply(this.s, arguments));
                },
                "throw": function _throw(r$1) {
                    var n = this.s["return"];
                    return void 0 === n ? Promise.reject(r$1) : AsyncFromSyncIteratorContinuation(n.apply(this.s, arguments));
                }
            }, new AsyncFromSyncIterator(r);
        }
        module.exports = _asyncIterator$1, module.exports.__esModule = true, module.exports["default"] = module.exports;
    }
});
//#endregion
//#region src/links/httpSubscriptionLink.ts
var import_asyncIterator = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])(require_asyncIterator(), 1);
async function urlWithConnectionParams(opts) {
    let url = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$wsLink$2d$H5IjZfJW$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["resultOf"])(opts.url);
    if (opts.connectionParams) {
        const params = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$wsLink$2d$H5IjZfJW$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["resultOf"])(opts.connectionParams);
        const prefix = url.includes("?") ? "&" : "?";
        url += prefix + "connectionParams=" + encodeURIComponent(JSON.stringify(params));
    }
    return url;
}
/**
* @see https://trpc.io/docs/client/links/httpSubscriptionLink
*/ function httpSubscriptionLink(opts) {
    const transformer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$unstable$2d$internals$2d$Bg7n9BBj$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTransformer"])(opts.transformer);
    return ()=>{
        return ({ op })=>{
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$UMO3vUa_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["observable"])((observer)=>{
                var _opts$EventSource;
                const { type, path, input } = op;
                /* istanbul ignore if -- @preserve */ if (type !== "subscription") throw new Error("httpSubscriptionLink only supports subscriptions");
                let lastEventId = void 0;
                const ac = new AbortController();
                const signal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpBatchLink$2d$CA96$2d$gnJ$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["raceAbortSignals"])(op.signal, ac.signal);
                const eventSourceStream = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$resolveResponse$2d$CzlbRpCI$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sseStreamConsumer"])({
                    url: async ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpUtils$2d$Bkv1johT$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getUrl"])({
                            transformer,
                            url: await urlWithConnectionParams(opts),
                            input: inputWithTrackedEventId(input, lastEventId),
                            path,
                            type,
                            signal: null
                        }),
                    init: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$wsLink$2d$H5IjZfJW$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["resultOf"])(opts.eventSourceOptions, {
                            op
                        }),
                    signal,
                    deserialize: transformer.output.deserialize,
                    EventSource: (_opts$EventSource = opts.EventSource) !== null && _opts$EventSource !== void 0 ? _opts$EventSource : globalThis.EventSource
                });
                const connectionState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$CUiPknO$2d2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["behaviorSubject"])({
                    type: "state",
                    state: "connecting",
                    error: null
                });
                const connectionSub = connectionState.subscribe({
                    next (state) {
                        observer.next({
                            result: state
                        });
                    }
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["run"])(async ()=>{
                    var _iteratorAbruptCompletion = false;
                    var _didIteratorError = false;
                    var _iteratorError;
                    try {
                        for(var _iterator = (0, import_asyncIterator.default)(eventSourceStream), _step; _iteratorAbruptCompletion = !(_step = await _iterator.next()).done; _iteratorAbruptCompletion = false){
                            const chunk = _step.value;
                            switch(chunk.type){
                                case "ping":
                                    break;
                                case "data":
                                    const chunkData = chunk.data;
                                    let result;
                                    if (chunkData.id) {
                                        lastEventId = chunkData.id;
                                        result = {
                                            id: chunkData.id,
                                            data: chunkData
                                        };
                                    } else result = {
                                        data: chunkData.data
                                    };
                                    observer.next({
                                        result,
                                        context: {
                                            eventSource: chunk.eventSource
                                        }
                                    });
                                    break;
                                case "connected":
                                    {
                                        observer.next({
                                            result: {
                                                type: "started"
                                            },
                                            context: {
                                                eventSource: chunk.eventSource
                                            }
                                        });
                                        connectionState.next({
                                            type: "state",
                                            state: "pending",
                                            error: null
                                        });
                                        break;
                                    }
                                case "serialized-error":
                                    {
                                        const error = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$TRPCClientError$2d$CjKyS10w$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCClientError"].from({
                                            error: chunk.error
                                        });
                                        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["retryableRpcCodes"].includes(chunk.error.code)) {
                                            connectionState.next({
                                                type: "state",
                                                state: "connecting",
                                                error
                                            });
                                            break;
                                        }
                                        throw error;
                                    }
                                case "connecting":
                                    {
                                        const lastState = connectionState.get();
                                        const error = chunk.event && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$TRPCClientError$2d$CjKyS10w$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCClientError"].from(chunk.event);
                                        if (!error && lastState.state === "connecting") break;
                                        connectionState.next({
                                            type: "state",
                                            state: "connecting",
                                            error
                                        });
                                        break;
                                    }
                                case "timeout":
                                    connectionState.next({
                                        type: "state",
                                        state: "connecting",
                                        error: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$TRPCClientError$2d$CjKyS10w$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCClientError"](`Timeout of ${chunk.ms}ms reached while waiting for a response`)
                                    });
                            }
                        }
                    } catch (err) {
                        _didIteratorError = true;
                        _iteratorError = err;
                    } finally{
                        try {
                            if (_iteratorAbruptCompletion && _iterator.return != null) await _iterator.return();
                        } finally{
                            if (_didIteratorError) throw _iteratorError;
                        }
                    }
                    observer.next({
                        result: {
                            type: "stopped"
                        }
                    });
                    connectionState.next({
                        type: "state",
                        state: "idle",
                        error: null
                    });
                    observer.complete();
                }).catch((error)=>{
                    observer.error(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$TRPCClientError$2d$CjKyS10w$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCClientError"].from(error));
                });
                return ()=>{
                    observer.complete();
                    ac.abort();
                    connectionSub.unsubscribe();
                };
            });
        };
    };
}
/**
* @deprecated use {@link httpSubscriptionLink} instead
*/ const unstable_httpSubscriptionLink = httpSubscriptionLink;
//#endregion
//#region src/links/retryLink.ts
var import_objectSpread2$1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["require_objectSpread2"])(), 1);
/**
* @see https://trpc.io/docs/v11/client/links/retryLink
*/ function retryLink(opts) {
    return ()=>{
        return (callOpts)=>{
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$UMO3vUa_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["observable"])((observer)=>{
                let next$;
                let callNextTimeout = void 0;
                let lastEventId = void 0;
                attempt(1);
                function opWithLastEventId() {
                    const op = callOpts.op;
                    if (!lastEventId) return op;
                    return (0, import_objectSpread2$1.default)((0, import_objectSpread2$1.default)({}, op), {}, {
                        input: inputWithTrackedEventId(op.input, lastEventId)
                    });
                }
                function attempt(attempts) {
                    const op = opWithLastEventId();
                    next$ = callOpts.next(op).subscribe({
                        error (error) {
                            var _opts$retryDelayMs, _opts$retryDelayMs2;
                            const shouldRetry = opts.retry({
                                op,
                                attempts,
                                error
                            });
                            if (!shouldRetry) {
                                observer.error(error);
                                return;
                            }
                            const delayMs = (_opts$retryDelayMs = (_opts$retryDelayMs2 = opts.retryDelayMs) === null || _opts$retryDelayMs2 === void 0 ? void 0 : _opts$retryDelayMs2.call(opts, attempts)) !== null && _opts$retryDelayMs !== void 0 ? _opts$retryDelayMs : 0;
                            if (delayMs <= 0) {
                                attempt(attempts + 1);
                                return;
                            }
                            callNextTimeout = setTimeout(()=>attempt(attempts + 1), delayMs);
                        },
                        next (envelope) {
                            if ((!envelope.result.type || envelope.result.type === "data") && envelope.result.id) lastEventId = envelope.result.id;
                            observer.next(envelope);
                        },
                        complete () {
                            observer.complete();
                        }
                    });
                }
                return ()=>{
                    next$.unsubscribe();
                    clearTimeout(callNextTimeout);
                };
            });
        };
    };
}
//#endregion
//#region ../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/usingCtx.js
var require_usingCtx = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__commonJS"])({
    "../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/usingCtx.js" (exports, module) {
        function _usingCtx() {
            var r = "function" == typeof SuppressedError ? SuppressedError : function(r$1, e$1) {
                var n$1 = Error();
                return n$1.name = "SuppressedError", n$1.error = r$1, n$1.suppressed = e$1, n$1;
            }, e = {}, n = [];
            function using(r$1, e$1) {
                if (null != e$1) {
                    if (Object(e$1) !== e$1) throw new TypeError("using declarations can only be used with objects, functions, null, or undefined.");
                    if (r$1) var o = e$1[Symbol.asyncDispose || Symbol["for"]("Symbol.asyncDispose")];
                    if (void 0 === o && (o = e$1[Symbol.dispose || Symbol["for"]("Symbol.dispose")], r$1)) var t = o;
                    if ("function" != typeof o) throw new TypeError("Object is not disposable.");
                    t && (o = function o$1() {
                        try {
                            t.call(e$1);
                        } catch (r$2) {
                            return Promise.reject(r$2);
                        }
                    }), n.push({
                        v: e$1,
                        d: o,
                        a: r$1
                    });
                } else r$1 && n.push({
                    d: e$1,
                    a: r$1
                });
                return e$1;
            }
            return {
                e,
                u: using.bind(null, !1),
                a: using.bind(null, !0),
                d: function d() {
                    var o, t = this.e, s = 0;
                    function next() {
                        for(; o = n.pop();)try {
                            if (!o.a && 1 === s) return s = 0, n.push(o), Promise.resolve().then(next);
                            if (o.d) {
                                var r$1 = o.d.call(o.v);
                                if (o.a) return s |= 2, Promise.resolve(r$1).then(next, err);
                            } else s |= 1;
                        } catch (r$2) {
                            return err(r$2);
                        }
                        if (1 === s) return t !== e ? Promise.reject(t) : Promise.resolve();
                        if (t !== e) throw t;
                    }
                    function err(n$1) {
                        return t = t !== e ? new r(n$1, t) : n$1, next();
                    }
                    return next();
                }
            };
        }
        module.exports = _usingCtx, module.exports.__esModule = true, module.exports["default"] = module.exports;
    }
});
//#endregion
//#region ../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/OverloadYield.js
var require_OverloadYield = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__commonJS"])({
    "../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/OverloadYield.js" (exports, module) {
        function _OverloadYield(e, d) {
            this.v = e, this.k = d;
        }
        module.exports = _OverloadYield, module.exports.__esModule = true, module.exports["default"] = module.exports;
    }
});
//#endregion
//#region ../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/awaitAsyncGenerator.js
var require_awaitAsyncGenerator = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__commonJS"])({
    "../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/awaitAsyncGenerator.js" (exports, module) {
        var OverloadYield$1 = require_OverloadYield();
        function _awaitAsyncGenerator$1(e) {
            return new OverloadYield$1(e, 0);
        }
        module.exports = _awaitAsyncGenerator$1, module.exports.__esModule = true, module.exports["default"] = module.exports;
    }
});
//#endregion
//#region ../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/wrapAsyncGenerator.js
var require_wrapAsyncGenerator = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__commonJS"])({
    "../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/wrapAsyncGenerator.js" (exports, module) {
        var OverloadYield = require_OverloadYield();
        function _wrapAsyncGenerator$1(e) {
            return function() {
                return new AsyncGenerator(e.apply(this, arguments));
            };
        }
        function AsyncGenerator(e) {
            var r, t;
            function resume(r$1, t$1) {
                try {
                    var n = e[r$1](t$1), o = n.value, u = o instanceof OverloadYield;
                    Promise.resolve(u ? o.v : o).then(function(t$2) {
                        if (u) {
                            var i = "return" === r$1 ? "return" : "next";
                            if (!o.k || t$2.done) return resume(i, t$2);
                            t$2 = e[i](t$2).value;
                        }
                        settle(n.done ? "return" : "normal", t$2);
                    }, function(e$1) {
                        resume("throw", e$1);
                    });
                } catch (e$1) {
                    settle("throw", e$1);
                }
            }
            function settle(e$1, n) {
                switch(e$1){
                    case "return":
                        r.resolve({
                            value: n,
                            done: !0
                        });
                        break;
                    case "throw":
                        r.reject(n);
                        break;
                    default:
                        r.resolve({
                            value: n,
                            done: !1
                        });
                }
                (r = r.next) ? resume(r.key, r.arg) : t = null;
            }
            this._invoke = function(e$1, n) {
                return new Promise(function(o, u) {
                    var i = {
                        key: e$1,
                        arg: n,
                        resolve: o,
                        reject: u,
                        next: null
                    };
                    t ? t = t.next = i : (r = t = i, resume(e$1, n));
                });
            }, "function" != typeof e["return"] && (this["return"] = void 0);
        }
        AsyncGenerator.prototype["function" == typeof Symbol && Symbol.asyncIterator || "@@asyncIterator"] = function() {
            return this;
        }, AsyncGenerator.prototype.next = function(e) {
            return this._invoke("next", e);
        }, AsyncGenerator.prototype["throw"] = function(e) {
            return this._invoke("throw", e);
        }, AsyncGenerator.prototype["return"] = function(e) {
            return this._invoke("return", e);
        };
        module.exports = _wrapAsyncGenerator$1, module.exports.__esModule = true, module.exports["default"] = module.exports;
    }
});
//#endregion
//#region src/links/localLink.ts
var import_usingCtx = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])(require_usingCtx(), 1);
var import_awaitAsyncGenerator = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])(require_awaitAsyncGenerator(), 1);
var import_wrapAsyncGenerator = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])(require_wrapAsyncGenerator(), 1);
var import_objectSpread2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["require_objectSpread2"])(), 1);
/**
* localLink is a terminating link that allows you to make tRPC procedure calls directly in your application without going through HTTP.
*
* @see https://trpc.io/docs/links/localLink
*/ function unstable_localLink(opts) {
    const transformer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$unstable$2d$internals$2d$Bg7n9BBj$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTransformer"])(opts.transformer);
    const transformChunk = (chunk)=>{
        if (opts.transformer) return chunk;
        if (chunk === void 0) return chunk;
        const serialized = JSON.stringify(transformer.input.serialize(chunk));
        const deserialized = JSON.parse(transformer.output.deserialize(serialized));
        return deserialized;
    };
    return ()=>({ op })=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$UMO3vUa_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["observable"])((observer)=>{
                let ctx = void 0;
                const ac = new AbortController();
                const signal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpBatchLink$2d$CA96$2d$gnJ$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["raceAbortSignals"])(op.signal, ac.signal);
                const signalPromise = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpBatchLink$2d$CA96$2d$gnJ$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["abortSignalToPromise"])(signal);
                signalPromise.catch(()=>{});
                let input = op.input;
                async function runProcedure(newInput) {
                    input = newInput;
                    ctx = await opts.createContext();
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callProcedure"])({
                        router: opts.router,
                        path: op.path,
                        getRawInput: async ()=>newInput,
                        ctx,
                        type: op.type,
                        signal
                    });
                }
                function onErrorCallback(cause) {
                    var _opts$onError;
                    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$resolveResponse$2d$CzlbRpCI$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isAbortError"])(cause)) return;
                    (_opts$onError = opts.onError) === null || _opts$onError === void 0 || _opts$onError.call(opts, {
                        error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTRPCErrorFromUnknown"])(cause),
                        type: op.type,
                        path: op.path,
                        input,
                        ctx
                    });
                }
                function coerceToTRPCClientError(cause) {
                    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$TRPCClientError$2d$CjKyS10w$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isTRPCClientError"])(cause)) return cause;
                    const error = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTRPCErrorFromUnknown"])(cause);
                    const shape = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__getErrorShape__as__getTRPCErrorShape$3e$__["getTRPCErrorShape"])({
                        config: opts.router._def._config,
                        ctx,
                        error,
                        input,
                        path: op.path,
                        type: op.type
                    });
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$TRPCClientError$2d$CjKyS10w$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCClientError"].from({
                        error: transformChunk(shape)
                    });
                }
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["run"])(async ()=>{
                    switch(op.type){
                        case "query":
                        case "mutation":
                            {
                                const result = await runProcedure(op.input);
                                if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isAsyncIterable"])(result)) {
                                    observer.next({
                                        result: {
                                            data: transformChunk(result)
                                        }
                                    });
                                    observer.complete();
                                    break;
                                }
                                observer.next({
                                    result: {
                                        data: (0, import_wrapAsyncGenerator.default)(function*() {
                                            try {
                                                var _usingCtx$1 = (0, import_usingCtx.default)();
                                                const iterator = _usingCtx$1.a((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$resolveResponse$2d$CzlbRpCI$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["iteratorResource"])(result));
                                                const _finally = _usingCtx$1.u((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$resolveResponse$2d$CzlbRpCI$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["makeResource"])({}, ()=>{
                                                    observer.complete();
                                                }));
                                                try {
                                                    while(true){
                                                        const res = yield (0, import_awaitAsyncGenerator.default)(Promise.race([
                                                            iterator.next(),
                                                            signalPromise
                                                        ]));
                                                        if (res.done) return transformChunk(res.value);
                                                        yield transformChunk(res.value);
                                                    }
                                                } catch (cause) {
                                                    onErrorCallback(cause);
                                                    throw coerceToTRPCClientError(cause);
                                                }
                                            } catch (_) {
                                                _usingCtx$1.e = _;
                                            } finally{
                                                yield (0, import_awaitAsyncGenerator.default)(_usingCtx$1.d());
                                            }
                                        })()
                                    }
                                });
                                break;
                            }
                        case "subscription":
                            try {
                                var _usingCtx3 = (0, import_usingCtx.default)();
                                const connectionState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$CUiPknO$2d2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["behaviorSubject"])({
                                    type: "state",
                                    state: "connecting",
                                    error: null
                                });
                                const connectionSub = connectionState.subscribe({
                                    next (state) {
                                        observer.next({
                                            result: state
                                        });
                                    }
                                });
                                let lastEventId = void 0;
                                const _finally = _usingCtx3.u((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$resolveResponse$2d$CzlbRpCI$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["makeResource"])({}, async ()=>{
                                    observer.complete();
                                    connectionState.next({
                                        type: "state",
                                        state: "idle",
                                        error: null
                                    });
                                    connectionSub.unsubscribe();
                                }));
                                while(true)try {
                                    var _usingCtx4 = (0, import_usingCtx.default)();
                                    const result = await runProcedure(inputWithTrackedEventId(op.input, lastEventId));
                                    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isAsyncIterable"])(result)) throw new Error("Expected an async iterable");
                                    const iterator = _usingCtx4.a((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$resolveResponse$2d$CzlbRpCI$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["iteratorResource"])(result));
                                    observer.next({
                                        result: {
                                            type: "started"
                                        }
                                    });
                                    connectionState.next({
                                        type: "state",
                                        state: "pending",
                                        error: null
                                    });
                                    while(true){
                                        let res;
                                        try {
                                            res = await Promise.race([
                                                iterator.next(),
                                                signalPromise
                                            ]);
                                        } catch (cause) {
                                            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$resolveResponse$2d$CzlbRpCI$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isAbortError"])(cause)) return;
                                            const error = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTRPCErrorFromUnknown"])(cause);
                                            if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["retryableRpcCodes"].includes(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPC_ERROR_CODES_BY_KEY"][error.code])) throw coerceToTRPCClientError(error);
                                            onErrorCallback(error);
                                            connectionState.next({
                                                type: "state",
                                                state: "connecting",
                                                error: coerceToTRPCClientError(error)
                                            });
                                            break;
                                        }
                                        if (res.done) return;
                                        let chunk;
                                        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isTrackedEnvelope"])(res.value)) {
                                            lastEventId = res.value[0];
                                            chunk = {
                                                id: res.value[0],
                                                data: {
                                                    id: res.value[0],
                                                    data: res.value[1]
                                                }
                                            };
                                        } else chunk = {
                                            data: res.value
                                        };
                                        observer.next({
                                            result: (0, import_objectSpread2.default)((0, import_objectSpread2.default)({}, chunk), {}, {
                                                data: transformChunk(chunk.data)
                                            })
                                        });
                                    }
                                } catch (_) {
                                    _usingCtx4.e = _;
                                } finally{
                                    await _usingCtx4.d();
                                }
                                break;
                            } catch (_) {
                                _usingCtx3.e = _;
                            } finally{
                                _usingCtx3.d();
                            }
                    }
                }).catch((cause)=>{
                    onErrorCallback(cause);
                    observer.error(coerceToTRPCClientError(cause));
                });
                return ()=>{
                    ac.abort();
                };
            });
}
/**
* @deprecated Renamed to `unstable_localLink`. This alias will be removed in a future major release.
*/ const experimental_localLink = unstable_localLink;
;
 //# sourceMappingURL=index.mjs.map
}),
"[project]/node_modules/@trpc/client/dist/index.mjs [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$objectSpread2$2d$BvkFp$2d$_Y$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/objectSpread2-BvkFp-_Y.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$splitLink$2d$B7Cuf2c_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/splitLink-B7Cuf2c_.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$TRPCClientError$2d$CjKyS10w$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/TRPCClientError-CjKyS10w.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpUtils$2d$Bkv1johT$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/httpUtils-Bkv1johT.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpLink$2d$CYOcG9kQ$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/httpLink-CYOcG9kQ.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$httpBatchLink$2d$CA96$2d$gnJ$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/httpBatchLink-CA96-gnJ.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$unstable$2d$internals$2d$Bg7n9BBj$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/unstable-internals-Bg7n9BBj.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$loggerLink$2d$ineCN1PO$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/loggerLink-ineCN1PO.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$wsLink$2d$H5IjZfJW$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/wsLink-H5IjZfJW.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$client$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@trpc/client/dist/index.mjs [app-ssr] (ecmascript) <locals>");
}),

};

//# sourceMappingURL=node_modules_%40trpc_client_dist_33d6caf5._.js.map