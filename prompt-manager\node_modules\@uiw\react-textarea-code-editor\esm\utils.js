import { rehype } from 'rehype';
export var processHtml = function processHtml(html, plugins) {
  if (plugins === void 0) {
    plugins = [];
  }
  return rehype().data('settings', {
    fragment: true
  }).use([...plugins]).processSync("" + html).toString();
};
export function htmlEncode(sHtml) {
  return sHtml.replace(/```(tsx?|jsx?|html|xml)(.*)\s+([\s\S]*?)(\s.+)?```/g, str => {
    return str.replace(/[<&"]/g, c => ({
      '<': '&lt;',
      '>': '&gt;',
      '&': '&amp;',
      '"': '&quot;'
    })[c]);
  }).replace(/[<&"]/g, c => ({
    '<': '&lt;',
    '>': '&gt;',
    '&': '&amp;',
    '"': '&quot;'
  })[c]);
}
export function stopPropagation(e) {
  e.stopPropagation();
  e.preventDefault();
}