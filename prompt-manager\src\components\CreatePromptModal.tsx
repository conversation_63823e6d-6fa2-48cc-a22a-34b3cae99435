'use client'

import { toast } from 'react-hot-toast'
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>er, 
  ModalTitle, 
  ModalContent,
  ModalCloseButton 
} from '~/components/ui/Modal'
import { PromptForm, type PromptFormData } from '~/components/PromptForm'
import { useModals } from '~/hooks/useStore'
import { api } from '~/trpc/react'

export function CreatePromptModal() {
  const { modals, closeCreatePrompt } = useModals()
  
  // 创建提示词的mutation
  const createPromptMutation = api.prompt.create.useMutation({
    onSuccess: () => {
      toast.success('提示词创建成功！')
      closeCreatePrompt()
      // 刷新提示词列表
      void utils.prompt.getAll.invalidate()
      void utils.prompt.getLatest.invalidate()
      void utils.prompt.getPopular.invalidate()
      void utils.stats.getOverview.invalidate()
    },
    onError: (error) => {
      toast.error(error.message || '创建失败，请重试')
    },
  })
  
  // 获取utils用于刷新数据
  const utils = api.useUtils()
  
  const handleSubmit = async (data: PromptFormData) => {
    try {
      await createPromptMutation.mutateAsync({
        title: data.title,
        content: data.content,
        description: data.description || undefined,
        tags: data.tags,
        categoryId: data.categoryId || undefined,
        isPublic: data.isPublic,
      })
    } catch (error) {
      // 错误已在onError中处理
    }
  }

  return (
    <Modal
      open={modals.createPrompt}
      onClose={closeCreatePrompt}
      size="xl"
    >
      <ModalHeader>
        <ModalTitle>创建新提示词</ModalTitle>
        <ModalCloseButton onClose={closeCreatePrompt} />
      </ModalHeader>

      <ModalContent>
        <PromptForm
          onSubmit={handleSubmit}
          onCancel={closeCreatePrompt}
          isLoading={createPromptMutation.isPending}
        />
      </ModalContent>
    </Modal>
  )
}
