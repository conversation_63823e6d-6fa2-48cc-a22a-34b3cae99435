"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault")["default"];
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.SelectionText = void 0;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var SelectionText = exports.SelectionText = /*#__PURE__*/function () {
  function SelectionText(elm) {
    (0, _classCallCheck2["default"])(this, SelectionText);
    (0, _defineProperty2["default"])(this, "elm", void 0);
    (0, _defineProperty2["default"])(this, "start", void 0);
    (0, _defineProperty2["default"])(this, "end", void 0);
    (0, _defineProperty2["default"])(this, "value", void 0);
    var selectionStart = elm.selectionStart,
      selectionEnd = elm.selectionEnd;
    this.elm = elm;
    this.start = selectionStart;
    this.end = selectionEnd;
    this.value = this.elm.value;
  }
  return (0, _createClass2["default"])(SelectionText, [{
    key: "position",
    value: function position(start, end) {
      var _this$elm = this.elm,
        selectionStart = _this$elm.selectionStart,
        selectionEnd = _this$elm.selectionEnd;
      this.start = typeof start === 'number' && !isNaN(start) ? start : selectionStart;
      this.end = typeof end === 'number' && !isNaN(end) ? end : selectionEnd;
      this.elm.selectionStart = this.start;
      this.elm.selectionEnd = this.end;
      return this;
    }
  }, {
    key: "insertText",
    value: function insertText(text) {
      // Most of the used APIs only work with the field selected
      this.elm.focus();
      this.elm.setRangeText(text);
      this.value = this.elm.value;
      this.position();
      return this;
    }
  }, {
    key: "getSelectedValue",
    value: function getSelectedValue(start, end) {
      var _this$elm2 = this.elm,
        selectionStart = _this$elm2.selectionStart,
        selectionEnd = _this$elm2.selectionEnd;
      return this.value.slice(typeof start === 'number' && !isNaN(start) ? start : selectionStart, typeof end === 'number' && !isNaN(end) ? start : selectionEnd);
    }
  }, {
    key: "getLineStartNumber",
    value: function getLineStartNumber() {
      var start = this.start;
      while (start > 0) {
        start--;
        if (this.value.charAt(start) === '\n') {
          start++;
          break;
        }
      }
      return start;
    }
    /** Indent on new lines */
  }, {
    key: "getIndentText",
    value: function getIndentText() {
      var start = this.getLineStartNumber();
      var str = this.getSelectedValue(start);
      var indent = '';
      str.replace(/(^(\s)+)/, function (str, old) {
        return indent = old;
      });
      return indent;
    }
  }, {
    key: "lineStarInstert",
    value: function lineStarInstert(text) {
      if (text) {
        var oldStart = this.start;
        var start = this.getLineStartNumber();
        var str = this.getSelectedValue(start);
        this.position(start, this.end).insertText(str.split('\n').map(function (txt) {
          return text + txt;
        }).join('\n')).position(oldStart + text.length, this.end);
      }
      return this;
    }
  }, {
    key: "lineStarRemove",
    value: function lineStarRemove(text) {
      if (text) {
        var oldStart = this.start;
        var start = this.getLineStartNumber();
        var str = this.getSelectedValue(start);
        var reg = new RegExp("^".concat(text), 'g');
        var newStart = oldStart - text.length;
        if (!reg.test(str)) {
          newStart = oldStart;
        }
        this.position(start, this.end).insertText(str.split('\n').map(function (txt) {
          return txt.replace(reg, '');
        }).join('\n')).position(newStart, this.end);
      }
    }
    /** Notify any possible listeners of the change */
  }, {
    key: "notifyChange",
    value: function notifyChange() {
      var event = new Event('input', {
        bubbles: true,
        cancelable: false
      });
      this.elm.dispatchEvent(event);
    }
  }]);
}();