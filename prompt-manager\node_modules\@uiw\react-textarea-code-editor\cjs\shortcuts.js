"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = shortcuts;
var _utils = require("./utils");
var _SelectionText = require("./SelectionText");
function shortcuts(e) {
  var indentWidth = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 2;
  var api = new _SelectionText.SelectionText(e.target);
  /**
   * Support of shortcuts for React v16
   * https://github.com/uiwjs/react-textarea-code-editor/issues/128
   * https://blog.saeloun.com/2021/04/23/react-keyboard-event-code.html
   */
  var code = (e.code || e.nativeEvent.code).toLocaleLowerCase();
  var indent = ' '.repeat(indentWidth);
  if (code === 'tab') {
    (0, _utils.stopPropagation)(e);
    if (api.start === api.end) {
      if (e.shiftKey) {
        api.lineStarRemove(indent);
      } else {
        api.insertText(indent).position(api.start + indentWidth, api.end + indentWidth);
      }
    } else if (api.getSelectedValue().indexOf('\n') > -1 && e.shiftKey) {
      api.lineStarRemove(indent);
    } else if (api.getSelectedValue().indexOf('\n') > -1) {
      api.lineStarInstert(indent);
    } else {
      api.insertText(indent).position(api.start + indentWidth, api.end);
    }
    api.notifyChange();
  } else if (code === 'enter') {
    (0, _utils.stopPropagation)(e);
    var _indent = "\n".concat(api.getIndentText());
    api.insertText(_indent).position(api.start + _indent.length, api.start + _indent.length);
    api.notifyChange();
  } else if (code && /^(quote|backquote|bracketleft|digit9|comma)$/.test(code) && api.getSelectedValue()) {
    (0, _utils.stopPropagation)(e);
    var val = api.getSelectedValue();
    var txt = '';
    switch (code) {
      case 'quote':
        txt = "'".concat(val, "'");
        if (e.shiftKey) {
          txt = "\"".concat(val, "\"");
        }
        break;
      case 'backquote':
        txt = "`".concat(val, "`");
        break;
      case 'bracketleft':
        txt = "[".concat(val, "]");
        if (e.shiftKey) {
          txt = "{".concat(val, "}");
        }
        break;
      case 'digit9':
        txt = "(".concat(val, ")");
        break;
      case 'comma':
        txt = "<".concat(val, ">");
        break;
    }
    api.insertText(txt);
    api.notifyChange();
  }
}
module.exports = exports.default;