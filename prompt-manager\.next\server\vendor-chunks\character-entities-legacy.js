"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/character-entities-legacy";
exports.ids = ["vendor-chunks/character-entities-legacy"];
exports.modules = {

/***/ "(ssr)/./node_modules/character-entities-legacy/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/character-entities-legacy/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   characterEntitiesLegacy: () => (/* binding */ characterEntitiesLegacy)\n/* harmony export */ });\n/**\n * List of legacy HTML named character references that don’t need a trailing semicolon.\n *\n * @type {Array<string>}\n */\nconst characterEntitiesLegacy = [\n  'AElig',\n  'AMP',\n  'Aacute',\n  'Acirc',\n  'Agrave',\n  'Aring',\n  'Atilde',\n  'Auml',\n  'COPY',\n  'Ccedil',\n  'ETH',\n  'Eacute',\n  'Ecirc',\n  'Egrave',\n  'Euml',\n  'GT',\n  'Iacute',\n  'Icirc',\n  'Igrave',\n  'Iuml',\n  'LT',\n  'Ntilde',\n  'Oacute',\n  'Ocirc',\n  'Ograve',\n  'Oslash',\n  'Otilde',\n  'Ouml',\n  'QUOT',\n  'REG',\n  'THORN',\n  'Uacute',\n  'Ucirc',\n  'Ugrave',\n  'Uuml',\n  'Yacute',\n  'aacute',\n  'acirc',\n  'acute',\n  'aelig',\n  'agrave',\n  'amp',\n  'aring',\n  'atilde',\n  'auml',\n  'brvbar',\n  'ccedil',\n  'cedil',\n  'cent',\n  'copy',\n  'curren',\n  'deg',\n  'divide',\n  'eacute',\n  'ecirc',\n  'egrave',\n  'eth',\n  'euml',\n  'frac12',\n  'frac14',\n  'frac34',\n  'gt',\n  'iacute',\n  'icirc',\n  'iexcl',\n  'igrave',\n  'iquest',\n  'iuml',\n  'laquo',\n  'lt',\n  'macr',\n  'micro',\n  'middot',\n  'nbsp',\n  'not',\n  'ntilde',\n  'oacute',\n  'ocirc',\n  'ograve',\n  'ordf',\n  'ordm',\n  'oslash',\n  'otilde',\n  'ouml',\n  'para',\n  'plusmn',\n  'pound',\n  'quot',\n  'raquo',\n  'reg',\n  'sect',\n  'shy',\n  'sup1',\n  'sup2',\n  'sup3',\n  'szlig',\n  'thorn',\n  'times',\n  'uacute',\n  'ucirc',\n  'ugrave',\n  'uml',\n  'uuml',\n  'yacute',\n  'yen',\n  'yuml'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/character-entities-legacy/index.js\n");

/***/ })

};
;