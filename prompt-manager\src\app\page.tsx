import { Suspense } from "react";
import { auth } from "~/server/auth";
import { api, HydrateClient } from "~/trpc/server";
import { HomePage } from "~/app/_components/HomePage";

export default async function Home() {
  const session = await auth();

  // 预取数据
  void api.prompt.getLatest.prefetch({ limit: 8 });
  void api.category.getAll.prefetch();
  void api.stats.getOverview.prefetch();

  return (
    <HydrateClient>
      <Suspense fallback={<div>加载中...</div>}>
        <HomePage />
      </Suspense>
    </HydrateClient>
  );
}
