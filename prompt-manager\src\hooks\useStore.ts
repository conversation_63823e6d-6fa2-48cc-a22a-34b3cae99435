import { useCallback } from 'react'
import { 
  useUIStore, 
  useSearchHistoryStore, 
  useUserPreferencesStore,
  type Prompt,
  type Category 
} from '~/store'

// UI相关的hooks
export const useUI = () => {
  const store = useUIStore()
  
  return {
    // 侧边栏
    sidebarOpen: store.sidebarOpen,
    toggleSidebar: useCallback(() => store.setSidebarOpen(!store.sidebarOpen), [store]),
    openSidebar: useCallback(() => store.setSidebarOpen(true), [store]),
    closeSidebar: useCallback(() => store.setSidebarOpen(false), [store]),
    
    // 搜索
    searchQuery: store.searchQuery,
    setSearchQuery: store.setSearchQuery,
    clearSearch: useCallback(() => store.setSearchQuery(''), [store]),
    
    // 分类筛选
    selectedCategoryId: store.selectedCategoryId,
    setSelectedCategoryId: store.setSelectedCategoryId,
    clearCategoryFilter: useCallback(() => store.setSelectedCategoryId(undefined), [store]),
    
    // 视图模式
    viewMode: store.viewMode,
    setViewMode: store.setViewMode,
    toggleViewMode: useCallback(() => {
      store.setViewMode(store.viewMode === 'grid' ? 'list' : 'grid')
    }, [store]),
    
    // 排序
    sortBy: store.sortBy,
    setSortBy: store.setSortBy,
    sortOrder: store.sortOrder,
    setSortOrder: store.setSortOrder,
    toggleSortOrder: useCallback(() => {
      store.setSortOrder(store.sortOrder === 'asc' ? 'desc' : 'asc')
    }, [store]),
  }
}

// 模态框相关的hooks
export const useModals = () => {
  const store = useUIStore()
  
  return {
    modals: store.modals,
    
    // 提示词相关模态框
    openCreatePrompt: useCallback(() => store.setModal('createPrompt', true), [store]),
    closeCreatePrompt: useCallback(() => store.setModal('createPrompt', false), [store]),
    
    openEditPrompt: useCallback((prompt: Prompt) => {
      store.setCurrentEditingPrompt(prompt)
      store.setModal('editPrompt', true)
    }, [store]),
    closeEditPrompt: useCallback(() => {
      store.setCurrentEditingPrompt(undefined)
      store.setModal('editPrompt', false)
    }, [store]),
    
    openPromptDetail: useCallback((prompt: Prompt) => {
      store.setCurrentViewingPrompt(prompt)
      store.setModal('promptDetail', true)
    }, [store]),
    closePromptDetail: useCallback(() => {
      store.setCurrentViewingPrompt(undefined)
      store.setModal('promptDetail', false)
    }, [store]),
    
    // 分类相关模态框
    openCreateCategory: useCallback(() => store.setModal('createCategory', true), [store]),
    closeCreateCategory: useCallback(() => store.setModal('createCategory', false), [store]),
    
    openEditCategory: useCallback((category: Category) => {
      store.setCurrentEditingCategory(category)
      store.setModal('editCategory', true)
    }, [store]),
    closeEditCategory: useCallback(() => {
      store.setCurrentEditingCategory(undefined)
      store.setModal('editCategory', false)
    }, [store]),
    
    // 当前编辑的项目
    currentEditingPrompt: store.currentEditingPrompt,
    currentEditingCategory: store.currentEditingCategory,
    currentViewingPrompt: store.currentViewingPrompt,
  }
}

// 搜索历史相关的hooks
export const useSearchHistory = () => {
  const store = useSearchHistoryStore()
  
  return {
    searchHistory: store.searchHistory,
    addSearchHistory: store.addSearchHistory,
    clearSearchHistory: store.clearSearchHistory,
    removeSearchHistory: store.removeSearchHistory,
    
    // 便捷方法
    hasHistory: store.searchHistory.length > 0,
    recentSearches: store.searchHistory.slice(0, 5), // 最近5个搜索
  }
}

// 用户偏好设置相关的hooks
export const useUserPreferences = () => {
  const store = useUserPreferencesStore()
  
  return {
    // 主题
    theme: store.theme,
    setTheme: store.setTheme,
    isDarkMode: store.theme === 'dark',
    isLightMode: store.theme === 'light',
    isSystemMode: store.theme === 'system',
    
    // 语言
    language: store.language,
    setLanguage: store.setLanguage,
    isChineseMode: store.language === 'zh-CN',
    isEnglishMode: store.language === 'en-US',
    
    // 显示设置
    pageSize: store.pageSize,
    setPageSize: store.setPageSize,
    showDescription: store.showDescription,
    setShowDescription: store.setShowDescription,
    showTags: store.showTags,
    setShowTags: store.setShowTags,
    showUsageCount: store.showUsageCount,
    setShowUsageCount: store.setShowUsageCount,
    
    // 便捷方法
    toggleDescription: useCallback(() => store.setShowDescription(!store.showDescription), [store]),
    toggleTags: useCallback(() => store.setShowTags(!store.showTags), [store]),
    toggleUsageCount: useCallback(() => store.setShowUsageCount(!store.showUsageCount), [store]),
  }
}

// 组合hook，提供常用的状态和操作
export const useAppState = () => {
  const ui = useUI()
  const modals = useModals()
  const searchHistory = useSearchHistory()
  const preferences = useUserPreferences()
  
  return {
    ui,
    modals,
    searchHistory,
    preferences,
    
    // 全局重置方法
    resetFilters: useCallback(() => {
      ui.clearSearch()
      ui.clearCategoryFilter()
      ui.setSortBy('createdAt')
      ui.setSortOrder('desc')
    }, [ui]),
    
    // 搜索相关的组合操作
    performSearch: useCallback((query: string) => {
      if (query.trim()) {
        searchHistory.addSearchHistory(query.trim())
        ui.setSearchQuery(query.trim())
      }
    }, [ui, searchHistory]),
  }
}
