{"name": "unist-util-filter", "version": "5.0.1", "description": "unist utility to create a new tree with nodes that pass a filter", "license": "MIT", "keywords": ["unist", "unist-util", "util", "utility", "ast", "tree", "node", "clone", "filter", "immutable", "test"], "repository": "syntax-tree/unist-util-filter", "bugs": "https://github.com/syntax-tree/unist-util-filter/issues", "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>> (https://wooorm.com)", "<PERSON> <<EMAIL>>"], "sideEffects": false, "type": "module", "exports": "./index.js", "files": ["lib/", "index.d.ts", "index.js"], "dependencies": {"@types/unist": "^3.0.0", "unist-util-is": "^6.0.0", "unist-util-visit-parents": "^6.0.0"}, "devDependencies": {"@types/mdast": "^4.0.0", "@types/node": "^20.0.0", "c8": "^8.0.0", "prettier": "^3.0.0", "remark-cli": "^11.0.0", "remark-preset-wooorm": "^9.0.0", "tsd": "^0.28.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "unist-builder": "^4.0.0", "xo": "^0.56.0"}, "scripts": {"prepack": "npm run build && npm run format", "build": "tsc --build --clean && tsc --build && tsd && type-coverage", "format": "remark . -qfo && prettier . -w --log-level warn && xo --fix", "test-api": "node --conditions development test.js", "test-coverage": "c8 --100 --reporter lcov npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "prettier": {"bracketSpacing": false, "semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm"]}, "typeCoverage": {"atLeast": 100, "detail": true, "ignoreCatch": true, "#": "needed `any`s", "ignoreFiles": ["lib/complex-types.d.ts"], "strict": true}, "xo": {"overrides": [{"files": ["**/*.ts"], "rules": {"@typescript-eslint/ban-types": "off"}}], "prettier": true}}