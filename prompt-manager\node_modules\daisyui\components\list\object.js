export default {".list":{"display":"flex","flex-direction":"column","font-size":"0.875rem",":where(.list-row)":{"--list-grid-cols":"minmax(0, auto) 1fr","position":"relative","display":"grid","grid-auto-flow":"column","gap":"calc(0.25rem * 4)","border-radius":"var(--radius-box)","padding":"calc(0.25rem * 4)","word-break":"break-word","grid-template-columns":"var(--list-grid-cols)","&:has(.list-col-grow:nth-child(1))":{"--list-grid-cols":"1fr"},"&:has(.list-col-grow:nth-child(2))":{"--list-grid-cols":"minmax(0, auto) 1fr"},"&:has(.list-col-grow:nth-child(3))":{"--list-grid-cols":"minmax(0, auto) minmax(0, auto) 1fr"},"&:has(.list-col-grow:nth-child(4))":{"--list-grid-cols":"minmax(0, auto) minmax(0, auto) minmax(0, auto) 1fr"},"&:has(.list-col-grow:nth-child(5))":{"--list-grid-cols":"minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto) 1fr"},"&:has(.list-col-grow:nth-child(6))":{"--list-grid-cols":"minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto)\n        minmax(0, auto) 1fr"},":not(.list-col-wrap)":{"grid-row-start":"1"}},"& > :not(:last-child)":{"&.list-row, .list-row":{"&:after":{"content":"\"\"","border-bottom":"var(--border) solid","inset-inline":"var(--radius-box)","position":"absolute","bottom":"calc(0.25rem * 0)","border-color":"color-mix(in oklab, var(--color-base-content) 5%, transparent)"}}}},".list-col-wrap":{"grid-row-start":"2"}};