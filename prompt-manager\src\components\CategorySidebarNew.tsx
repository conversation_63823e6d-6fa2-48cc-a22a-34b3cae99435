'use client'

import { useState } from 'react'
import { cn } from '~/lib/utils'
import { api } from '~/trpc/react'
import { useUI } from '~/hooks/useStore'

interface CategorySidebarProps {
  selectedCategoryId?: string | null
  onCategorySelect: (categoryId: string | null) => void
  className?: string
}

export function CategorySidebar({ 
  selectedCategoryId, 
  onCategorySelect, 
  className 
}: CategorySidebarProps) {
  const [hoveredCategoryId, setHoveredCategoryId] = useState<string | null>(null)
  
  const { openCreateCategoryModal, openEditCategoryModal } = useUI()
  const { data: categories, isLoading } = api.category.getAll.useQuery()

  const handleCategoryClick = (categoryId: string | null) => {
    onCategorySelect(categoryId)
  }

  if (isLoading) {
    return (
      <div className={cn('h-full flex flex-col p-4', className)}>
        <div className="skeleton h-6 w-24 mb-4"></div>
        <div className="space-y-2">
          <div className="skeleton h-10 w-full"></div>
          <div className="skeleton h-10 w-full"></div>
          <div className="skeleton h-10 w-full"></div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn('h-full flex flex-col', className)}>
      {/* 头部 */}
      <div className="p-4 border-b border-base-300">
        <h3 className="text-lg font-bold">分类目录</h3>
      </div>

      {/* 分类列表 */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="space-y-2">
          {/* 全部分类选项 */}
          <button
            onClick={() => handleCategoryClick(null)}
            className={`btn btn-ghost w-full justify-between ${
              selectedCategoryId === null ? 'btn-active' : ''
            }`}
          >
            <span>全部分类</span>
            <div className="badge badge-neutral">
              {categories?.reduce((total, cat) => total + cat._count.prompts, 0) || 0}
            </div>
          </button>

          {/* 分类列表 */}
          {categories?.map((category) => (
            <div
              key={category.id}
              className="relative"
              onMouseEnter={() => setHoveredCategoryId(category.id)}
              onMouseLeave={() => setHoveredCategoryId(null)}
            >
              <button
                onClick={() => handleCategoryClick(category.id)}
                className={`btn btn-ghost w-full justify-between ${
                  selectedCategoryId === category.id ? 'btn-active' : ''
                }`}
              >
                <div className="flex items-center gap-2">
                  <div 
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: category.color }}
                  />
                  <span>{category.name}</span>
                </div>
                <div className="badge badge-neutral">
                  {category._count.prompts}
                </div>
              </button>
              
              {/* 编辑按钮 */}
              {hoveredCategoryId === category.id && (
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    openEditCategoryModal(category)
                  }}
                  className="btn btn-ghost btn-xs absolute right-2 top-1/2 transform -translate-y-1/2"
                >
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                </button>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* 底部新建按钮 */}
      <div className="p-4 border-t border-base-300">
        <button
          onClick={openCreateCategoryModal}
          className="btn btn-primary btn-block"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
          新建分类
        </button>
      </div>
    </div>
  )
}
