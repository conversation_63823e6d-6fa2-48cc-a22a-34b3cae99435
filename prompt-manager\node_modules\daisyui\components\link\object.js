export default {".link":{"cursor":"pointer","text-decoration-line":"underline","&:focus":{"--tw-outline-style":"none","outline-style":"none","@media (forced-colors: active)":{"outline":"2px solid transparent","outline-offset":"2px"}},"&:focus-visible":{"outline":"2px solid currentColor","outline-offset":"2px"}},".link-hover":{"text-decoration-line":"none","&:hover":{"@media (hover: hover)":{"text-decoration-line":"underline"}}},".link-primary":{"color":"var(--color-primary)","@media (hover: hover)":{"&:hover":{"color":"color-mix(in oklab, var(--color-primary) 80%, #000)"}}},".link-secondary":{"color":"var(--color-secondary)","@media (hover: hover)":{"&:hover":{"color":"color-mix(in oklab, var(--color-secondary) 80%, #000)"}}},".link-accent":{"color":"var(--color-accent)","@media (hover: hover)":{"&:hover":{"color":"color-mix(in oklab, var(--color-accent) 80%, #000)"}}},".link-neutral":{"color":"var(--color-neutral)","@media (hover: hover)":{"&:hover":{"color":"color-mix(in oklab, var(--color-neutral) 80%, #000)"}}},".link-success":{"color":"var(--color-success)","@media (hover: hover)":{"&:hover":{"color":"color-mix(in oklab, var(--color-success) 80%, #000)"}}},".link-info":{"color":"var(--color-info)","@media (hover: hover)":{"&:hover":{"color":"color-mix(in oklab, var(--color-info) 80%, #000)"}}},".link-warning":{"color":"var(--color-warning)","@media (hover: hover)":{"&:hover":{"color":"color-mix(in oklab, var(--color-warning) 80%, #000)"}}},".link-error":{"color":"var(--color-error)","@media (hover: hover)":{"&:hover":{"color":"color-mix(in oklab, var(--color-error) 80%, #000)"}}}};