'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '~/components/ui/Button'
import { AnimatedButton } from '~/components/AnimatedButton'
import { useUI } from '~/hooks/useStore'
import { api } from '~/trpc/react'
import { cn } from '~/lib/utils'

interface CategorySidebarProps {
  selectedCategoryId?: string | null
  onCategorySelect: (categoryId: string | null) => void
  className?: string
}

export function CategorySidebar({ 
  selectedCategoryId, 
  onCategorySelect, 
  className 
}: CategorySidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [hoveredCategoryId, setHoveredCategoryId] = useState<string | null>(null)
  
  const { openCreateCategoryModal, openEditCategoryModal } = useUI()
  const { data: categories, isLoading } = api.category.getAll.useQuery()

  const handleCategoryClick = (categoryId: string | null) => {
    onCategorySelect(categoryId)
  }

  const handleEditCategory = (e: React.MouseEvent, categoryId: string) => {
    e.stopPropagation()
    openEditCategoryModal(categoryId)
  }

  const sidebarVariants = {
    expanded: { width: 250 },
    collapsed: { width: 60 }
  }

  const contentVariants = {
    expanded: { opacity: 1, x: 0 },
    collapsed: { opacity: 0, x: -20 }
  }

  return (
    <motion.div
      className={cn(
        'bg-white dark:bg-slate-900 border-r border-slate-200 dark:border-slate-700 flex flex-col h-full',
        className
      )}
      variants={sidebarVariants}
      animate={isCollapsed ? 'collapsed' : 'expanded'}
      transition={{ duration: 0.3, ease: 'easeInOut' }}
    >
      {/* 头部 */}
      <div className="p-4 border-b border-slate-200 dark:border-slate-700">
        <div className="flex items-center justify-between">
          <AnimatePresence mode="wait">
            {!isCollapsed && (
              <motion.div
                variants={contentVariants}
                initial="collapsed"
                animate="expanded"
                exit="collapsed"
                transition={{ duration: 0.2 }}
              >
                <h3 className="font-semibold text-slate-900 dark:text-white">
                  分类目录
                </h3>
              </motion.div>
            )}
          </AnimatePresence>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="p-1 h-8 w-8"
          >
            <motion.svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              animate={{ rotate: isCollapsed ? 180 : 0 }}
              transition={{ duration: 0.3 }}
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </motion.svg>
          </Button>
        </div>
        
        <AnimatePresence>
          {!isCollapsed && (
            <motion.div
              variants={contentVariants}
              initial="collapsed"
              animate="expanded"
              exit="collapsed"
              transition={{ duration: 0.2, delay: 0.1 }}
              className="mt-3"
            >
              <AnimatedButton
                onClick={openCreateCategoryModal}
                size="sm"
                className="w-full text-xs"
                animationType="scale"
              >
                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                新建分类
              </AnimatedButton>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* 分类列表 */}
      <div className="flex-1 overflow-y-auto">
        {/* 全部分类选项 */}
        <div className="p-2">
          <motion.div
            className={cn(
              'flex items-center justify-between p-2 rounded-lg cursor-pointer transition-colors',
              selectedCategoryId === null
                ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400'
                : 'hover:bg-slate-50 dark:hover:bg-slate-800 text-slate-700 dark:text-slate-300'
            )}
            onClick={() => handleCategoryClick(null)}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="flex items-center gap-2 min-w-0">
              <div className="w-2 h-2 rounded-full bg-slate-400" />
              <AnimatePresence>
                {!isCollapsed && (
                  <motion.span
                    variants={contentVariants}
                    initial="collapsed"
                    animate="expanded"
                    exit="collapsed"
                    className="text-sm font-medium truncate"
                  >
                    全部分类
                  </motion.span>
                )}
              </AnimatePresence>
            </div>
            <AnimatePresence>
              {!isCollapsed && (
                <motion.span
                  variants={contentVariants}
                  initial="collapsed"
                  animate="expanded"
                  exit="collapsed"
                  className="text-xs text-slate-500 dark:text-slate-400 bg-slate-100 dark:bg-slate-700 px-1.5 py-0.5 rounded"
                >
                  {categories?.reduce((total, cat) => total + cat._count.prompts, 0) || 0}
                </motion.span>
              )}
            </AnimatePresence>
          </motion.div>
        </div>

        {/* 分类列表 */}
        <div className="px-2 pb-2">
          {isLoading ? (
            <div className="space-y-2">
              {Array.from({ length: 3 }).map((_, index) => (
                <div
                  key={index}
                  className="h-10 bg-slate-200 dark:bg-slate-700 rounded-lg animate-pulse"
                />
              ))}
            </div>
          ) : (
            <div className="space-y-1">
              {categories?.map((category) => (
                <motion.div
                  key={category.id}
                  className={cn(
                    'flex items-center justify-between p-2 rounded-lg cursor-pointer transition-colors group',
                    selectedCategoryId === category.id
                      ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400'
                      : 'hover:bg-slate-50 dark:hover:bg-slate-800 text-slate-700 dark:text-slate-300'
                  )}
                  onClick={() => handleCategoryClick(category.id)}
                  onMouseEnter={() => setHoveredCategoryId(category.id)}
                  onMouseLeave={() => setHoveredCategoryId(null)}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="flex items-center gap-2 min-w-0 flex-1">
                    <div 
                      className="w-2 h-2 rounded-full flex-shrink-0"
                      style={{ backgroundColor: category.color }}
                    />
                    <AnimatePresence>
                      {!isCollapsed && (
                        <motion.span
                          variants={contentVariants}
                          initial="collapsed"
                          animate="expanded"
                          exit="collapsed"
                          className="text-sm font-medium truncate"
                          title={category.name}
                        >
                          {category.name}
                        </motion.span>
                      )}
                    </AnimatePresence>
                  </div>
                  
                  <AnimatePresence>
                    {!isCollapsed && (
                      <motion.div
                        variants={contentVariants}
                        initial="collapsed"
                        animate="expanded"
                        exit="collapsed"
                        className="flex items-center gap-1"
                      >
                        <span className="text-xs text-slate-500 dark:text-slate-400 bg-slate-100 dark:bg-slate-700 px-1.5 py-0.5 rounded">
                          {category._count.prompts}
                        </span>
                        
                        <AnimatePresence>
                          {hoveredCategoryId === category.id && (
                            <motion.button
                              initial={{ opacity: 0, scale: 0.8 }}
                              animate={{ opacity: 1, scale: 1 }}
                              exit={{ opacity: 0, scale: 0.8 }}
                              transition={{ duration: 0.15 }}
                              onClick={(e) => handleEditCategory(e, category.id)}
                              className="p-1 hover:bg-slate-200 dark:hover:bg-slate-600 rounded text-slate-400 hover:text-slate-600 dark:hover:text-slate-300"
                            >
                              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                              </svg>
                            </motion.button>
                          )}
                        </AnimatePresence>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* 底部收起提示 */}
      <AnimatePresence>
        {isCollapsed && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="p-2 border-t border-slate-200 dark:border-slate-700"
          >
            <Button
              variant="ghost"
              size="sm"
              onClick={openCreateCategoryModal}
              className="w-full p-2 h-8"
              title="新建分类"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
            </Button>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}
