"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@uiw";
exports.ids = ["vendor-chunks/@uiw"];
exports.modules = {

/***/ "(ssr)/./node_modules/@uiw/react-textarea-code-editor/esm/Editor.js":
/*!********************************************************************!*\
  !*** ./node_modules/@uiw/react-textarea-code-editor/esm/Editor.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SelectionText: () => (/* reexport safe */ _SelectionText_js__WEBPACK_IMPORTED_MODULE_8__.SelectionText),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/objectWithoutPropertiesLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js\");\n/* harmony import */ var _babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@uiw/react-textarea-code-editor/esm/utils.js\");\n/* harmony import */ var _shortcuts_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./shortcuts.js */ \"(ssr)/./node_modules/@uiw/react-textarea-code-editor/esm/shortcuts.js\");\n/* harmony import */ var _styles_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./styles.js */ \"(ssr)/./node_modules/@uiw/react-textarea-code-editor/esm/styles.js\");\n/* harmony import */ var _style_index_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./style/index.css */ \"(ssr)/./node_modules/@uiw/react-textarea-code-editor/esm/style/index.css\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _SelectionText_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./SelectionText.js */ \"(ssr)/./node_modules/@uiw/react-textarea-code-editor/esm/SelectionText.js\");\n\n\nvar _excluded = [\"prefixCls\", \"value\", \"padding\", \"minHeight\", \"placeholder\", \"language\", \"data-color-mode\", \"className\", \"style\", \"rehypePlugins\", \"onChange\", \"indentWidth\"];\n\n\n\n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default().forwardRef((props, ref) => {\n  var {\n      prefixCls = 'w-tc-editor',\n      padding = 10,\n      minHeight = 16,\n      placeholder,\n      language,\n      'data-color-mode': dataColorMode,\n      className,\n      style,\n      rehypePlugins,\n      onChange,\n      indentWidth = 2\n    } = props,\n    other = _babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1___default()(props, _excluded);\n  var [value, setValue] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(props.value || '');\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(() => setValue(props.value || ''), [props.value]);\n  var textRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, () => textRef.current, [textRef]);\n  var contentStyle = {\n    paddingTop: padding,\n    paddingRight: padding,\n    paddingBottom: padding,\n    paddingLeft: padding\n  };\n  var htmlStr = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(() => (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.processHtml)(\"<pre aria-hidden=true><code \" + (language && value ? \"class=\\\"language-\" + language + \"\\\"\" : '') + \" >\" + (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.htmlEncode)(String(value || '')) + \"</code><br /></pre>\", rehypePlugins), [value, language, rehypePlugins]);\n  var preView = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(() => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(\"div\", {\n    style: _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, _styles_js__WEBPACK_IMPORTED_MODULE_5__.editor, contentStyle, {\n      minHeight\n    }),\n    className: prefixCls + \"-preview \" + (language ? \"language-\" + language : ''),\n    dangerouslySetInnerHTML: {\n      __html: htmlStr\n    }\n  }),\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [prefixCls, language, htmlStr]);\n  var change = evn => {\n    setValue(evn.target.value);\n    onChange && onChange(evn);\n  };\n  var keyDown = evn => {\n    if (other.readOnly) return;\n    if (!other.onKeyDown || other.onKeyDown(evn) !== false) {\n      (0,_shortcuts_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(evn, indentWidth);\n    }\n  };\n  var textareaProps = _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({\n    autoComplete: 'off',\n    autoCorrect: 'off',\n    spellCheck: 'false',\n    autoCapitalize: 'off'\n  }, other, {\n    placeholder,\n    onKeyDown: keyDown,\n    style: _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, _styles_js__WEBPACK_IMPORTED_MODULE_5__.editor, _styles_js__WEBPACK_IMPORTED_MODULE_5__.textarea, contentStyle, {\n      minHeight\n    }, placeholder && !value ? {\n      WebkitTextFillColor: 'inherit'\n    } : {}),\n    onChange: change,\n    className: prefixCls + \"-text\",\n    value: value\n  });\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxs)(\"div\", {\n    style: _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, _styles_js__WEBPACK_IMPORTED_MODULE_5__.container, style),\n    className: prefixCls + \" \" + (className || ''),\n    \"data-color-mode\": dataColorMode,\n    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(\"textarea\", _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, textareaProps, {\n      ref: textRef\n    })), preView]\n  });\n}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-textarea-code-editor/esm/Editor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-textarea-code-editor/esm/SelectionText.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@uiw/react-textarea-code-editor/esm/SelectionText.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SelectionText: () => (/* binding */ SelectionText)\n/* harmony export */ });\nclass SelectionText {\n  constructor(elm) {\n    this.elm = void 0;\n    this.start = void 0;\n    this.end = void 0;\n    this.value = void 0;\n    var {\n      selectionStart,\n      selectionEnd\n    } = elm;\n    this.elm = elm;\n    this.start = selectionStart;\n    this.end = selectionEnd;\n    this.value = this.elm.value;\n  }\n  position(start, end) {\n    var {\n      selectionStart,\n      selectionEnd\n    } = this.elm;\n    this.start = typeof start === 'number' && !isNaN(start) ? start : selectionStart;\n    this.end = typeof end === 'number' && !isNaN(end) ? end : selectionEnd;\n    this.elm.selectionStart = this.start;\n    this.elm.selectionEnd = this.end;\n    return this;\n  }\n  insertText(text) {\n    // Most of the used APIs only work with the field selected\n    this.elm.focus();\n    this.elm.setRangeText(text);\n    this.value = this.elm.value;\n    this.position();\n    return this;\n  }\n  getSelectedValue(start, end) {\n    var {\n      selectionStart,\n      selectionEnd\n    } = this.elm;\n    return this.value.slice(typeof start === 'number' && !isNaN(start) ? start : selectionStart, typeof end === 'number' && !isNaN(end) ? start : selectionEnd);\n  }\n  getLineStartNumber() {\n    var start = this.start;\n    while (start > 0) {\n      start--;\n      if (this.value.charAt(start) === '\\n') {\n        start++;\n        break;\n      }\n    }\n    return start;\n  }\n  /** Indent on new lines */\n  getIndentText() {\n    var start = this.getLineStartNumber();\n    var str = this.getSelectedValue(start);\n    var indent = '';\n    str.replace(/(^(\\s)+)/, (str, old) => indent = old);\n    return indent;\n  }\n  lineStarInstert(text) {\n    if (text) {\n      var oldStart = this.start;\n      var start = this.getLineStartNumber();\n      var str = this.getSelectedValue(start);\n      this.position(start, this.end).insertText(str.split('\\n').map(txt => text + txt).join('\\n')).position(oldStart + text.length, this.end);\n    }\n    return this;\n  }\n  lineStarRemove(text) {\n    if (text) {\n      var oldStart = this.start;\n      var start = this.getLineStartNumber();\n      var str = this.getSelectedValue(start);\n      var reg = new RegExp(\"^\" + text, 'g');\n      var newStart = oldStart - text.length;\n      if (!reg.test(str)) {\n        newStart = oldStart;\n      }\n      this.position(start, this.end).insertText(str.split('\\n').map(txt => txt.replace(reg, '')).join('\\n')).position(newStart, this.end);\n    }\n  }\n  /** Notify any possible listeners of the change */\n  notifyChange() {\n    var event = new Event('input', {\n      bubbles: true,\n      cancelable: false\n    });\n    this.elm.dispatchEvent(event);\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-textarea-code-editor/esm/SelectionText.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-textarea-code-editor/esm/index.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@uiw/react-textarea-code-editor/esm/index.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SelectionText: () => (/* reexport safe */ _Editor_js__WEBPACK_IMPORTED_MODULE_3__.SelectionText),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/objectWithoutPropertiesLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js\");\n/* harmony import */ var _babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Editor_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Editor.js */ \"(ssr)/./node_modules/@uiw/react-textarea-code-editor/esm/Editor.js\");\n/* harmony import */ var rehype_prism_plus__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rehype-prism-plus */ \"(ssr)/./node_modules/rehype-prism-plus/dist/index.es.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__);\n\n\nvar _excluded = [\"rehypePlugins\"];\n\n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default().forwardRef((props, ref) => {\n  var {\n      rehypePlugins = [[rehype_prism_plus__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        ignoreMissing: true\n      }]]\n    } = props,\n    reset = _babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1___default()(props, _excluded);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_Editor_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"], _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, reset, {\n    rehypePlugins: rehypePlugins,\n    ref: ref\n  }));\n}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC10ZXh0YXJlYS1jb2RlLWVkaXRvci9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQXNEO0FBQzBDO0FBQ2hHO0FBQzBCO0FBQ087QUFDVztBQUNJO0FBQ3BCO0FBQzVCLDhFQUE0Qix1REFBZ0I7QUFDNUM7QUFDQSx3QkFBd0IseURBQVc7QUFDbkM7QUFDQSxPQUFPO0FBQ1AsTUFBTTtBQUNOLFlBQVksMEZBQTZCO0FBQ3pDLHNCQUFzQixzREFBSSxDQUFDLGtEQUFNLEVBQUUscUVBQVEsR0FBRztBQUM5QztBQUNBO0FBQ0EsR0FBRztBQUNILENBQUMsQ0FBQyIsInNvdXJjZXMiOlsiRDpcXEN1cnNvciBQcm9qZWN0XFx3ZWJzaXRlXFxBdWdtZW50MlxccHJvbXB0LW1hbmFnZXJcXG5vZGVfbW9kdWxlc1xcQHVpd1xccmVhY3QtdGV4dGFyZWEtY29kZS1lZGl0b3JcXGVzbVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IF9leHRlbmRzIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2V4dGVuZHNcIjtcbmltcG9ydCBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXNMb29zZSBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9vYmplY3RXaXRob3V0UHJvcGVydGllc0xvb3NlXCI7XG52YXIgX2V4Y2x1ZGVkID0gW1wicmVoeXBlUGx1Z2luc1wiXTtcbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgRWRpdG9yIGZyb20gXCIuL0VkaXRvci5qc1wiO1xuaW1wb3J0IHJlaHlwZVByaXNtIGZyb20gJ3JlaHlwZS1wcmlzbS1wbHVzJztcbmltcG9ydCB7IGpzeCBhcyBfanN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9FZGl0b3IuanNcIjtcbmV4cG9ydCBkZWZhdWx0IC8qI19fUFVSRV9fKi9SZWFjdC5mb3J3YXJkUmVmKChwcm9wcywgcmVmKSA9PiB7XG4gIHZhciB7XG4gICAgICByZWh5cGVQbHVnaW5zID0gW1tyZWh5cGVQcmlzbSwge1xuICAgICAgICBpZ25vcmVNaXNzaW5nOiB0cnVlXG4gICAgICB9XV1cbiAgICB9ID0gcHJvcHMsXG4gICAgcmVzZXQgPSBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXNMb29zZShwcm9wcywgX2V4Y2x1ZGVkKTtcbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9fanN4KEVkaXRvciwgX2V4dGVuZHMoe30sIHJlc2V0LCB7XG4gICAgcmVoeXBlUGx1Z2luczogcmVoeXBlUGx1Z2lucyxcbiAgICByZWY6IHJlZlxuICB9KSk7XG59KTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-textarea-code-editor/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-textarea-code-editor/esm/shortcuts.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@uiw/react-textarea-code-editor/esm/shortcuts.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ shortcuts)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@uiw/react-textarea-code-editor/esm/utils.js\");\n/* harmony import */ var _SelectionText_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./SelectionText.js */ \"(ssr)/./node_modules/@uiw/react-textarea-code-editor/esm/SelectionText.js\");\n\n\nfunction shortcuts(e, indentWidth) {\n  if (indentWidth === void 0) {\n    indentWidth = 2;\n  }\n  var api = new _SelectionText_js__WEBPACK_IMPORTED_MODULE_1__.SelectionText(e.target);\n  /**\n   * Support of shortcuts for React v16\n   * https://github.com/uiwjs/react-textarea-code-editor/issues/128\n   * https://blog.saeloun.com/2021/04/23/react-keyboard-event-code.html\n   */\n  var code = (e.code || e.nativeEvent.code).toLocaleLowerCase();\n  var indent = ' '.repeat(indentWidth);\n  if (code === 'tab') {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.stopPropagation)(e);\n    if (api.start === api.end) {\n      if (e.shiftKey) {\n        api.lineStarRemove(indent);\n      } else {\n        api.insertText(indent).position(api.start + indentWidth, api.end + indentWidth);\n      }\n    } else if (api.getSelectedValue().indexOf('\\n') > -1 && e.shiftKey) {\n      api.lineStarRemove(indent);\n    } else if (api.getSelectedValue().indexOf('\\n') > -1) {\n      api.lineStarInstert(indent);\n    } else {\n      api.insertText(indent).position(api.start + indentWidth, api.end);\n    }\n    api.notifyChange();\n  } else if (code === 'enter') {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.stopPropagation)(e);\n    var _indent = \"\\n\" + api.getIndentText();\n    api.insertText(_indent).position(api.start + _indent.length, api.start + _indent.length);\n    api.notifyChange();\n  } else if (code && /^(quote|backquote|bracketleft|digit9|comma)$/.test(code) && api.getSelectedValue()) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.stopPropagation)(e);\n    var val = api.getSelectedValue();\n    var txt = '';\n    switch (code) {\n      case 'quote':\n        txt = \"'\" + val + \"'\";\n        if (e.shiftKey) {\n          txt = \"\\\"\" + val + \"\\\"\";\n        }\n        break;\n      case 'backquote':\n        txt = \"`\" + val + \"`\";\n        break;\n      case 'bracketleft':\n        txt = \"[\" + val + \"]\";\n        if (e.shiftKey) {\n          txt = \"{\" + val + \"}\";\n        }\n        break;\n      case 'digit9':\n        txt = \"(\" + val + \")\";\n        break;\n      case 'comma':\n        txt = \"<\" + val + \">\";\n        break;\n    }\n    api.insertText(txt);\n    api.notifyChange();\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-textarea-code-editor/esm/shortcuts.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-textarea-code-editor/esm/style/index.css":
/*!**************************************************************************!*\
  !*** ./node_modules/@uiw/react-textarea-code-editor/esm/style/index.css ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"53cd05a9143a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC10ZXh0YXJlYS1jb2RlLWVkaXRvci9lc20vc3R5bGUvaW5kZXguY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcQ3Vyc29yIFByb2plY3RcXHdlYnNpdGVcXEF1Z21lbnQyXFxwcm9tcHQtbWFuYWdlclxcbm9kZV9tb2R1bGVzXFxAdWl3XFxyZWFjdC10ZXh0YXJlYS1jb2RlLWVkaXRvclxcZXNtXFxzdHlsZVxcaW5kZXguY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNTNjZDA1YTkxNDNhXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-textarea-code-editor/esm/style/index.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-textarea-code-editor/esm/styles.js":
/*!********************************************************************!*\
  !*** ./node_modules/@uiw/react-textarea-code-editor/esm/styles.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   container: () => (/* binding */ container),\n/* harmony export */   editor: () => (/* binding */ editor),\n/* harmony export */   textarea: () => (/* binding */ textarea)\n/* harmony export */ });\nvar container = {\n  position: 'relative',\n  textAlign: 'left',\n  boxSizing: 'border-box',\n  padding: 0,\n  overflow: 'hidden'\n};\nvar textarea = {\n  position: 'absolute',\n  top: 0,\n  left: 0,\n  height: '100%',\n  width: '100%',\n  resize: 'none',\n  color: 'inherit',\n  opacity: 0.8,\n  overflow: 'hidden',\n  MozOsxFontSmoothing: 'grayscale',\n  WebkitFontSmoothing: 'antialiased',\n  WebkitTextFillColor: 'transparent'\n};\nvar editor = {\n  margin: 0,\n  border: 0,\n  background: 'none',\n  boxSizing: 'inherit',\n  display: 'inherit',\n  fontFamily: 'inherit',\n  fontSize: 'inherit',\n  fontStyle: 'inherit',\n  fontVariantLigatures: 'inherit',\n  fontWeight: 'inherit',\n  letterSpacing: 'inherit',\n  lineHeight: 'inherit',\n  tabSize: 'inherit',\n  textIndent: 'inherit',\n  textRendering: 'inherit',\n  textTransform: 'inherit',\n  whiteSpace: 'pre-wrap',\n  wordBreak: 'keep-all',\n  overflowWrap: 'break-word',\n  outline: 0\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC10ZXh0YXJlYS1jb2RlLWVkaXRvci9lc20vc3R5bGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcQ3Vyc29yIFByb2plY3RcXHdlYnNpdGVcXEF1Z21lbnQyXFxwcm9tcHQtbWFuYWdlclxcbm9kZV9tb2R1bGVzXFxAdWl3XFxyZWFjdC10ZXh0YXJlYS1jb2RlLWVkaXRvclxcZXNtXFxzdHlsZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHZhciBjb250YWluZXIgPSB7XG4gIHBvc2l0aW9uOiAncmVsYXRpdmUnLFxuICB0ZXh0QWxpZ246ICdsZWZ0JyxcbiAgYm94U2l6aW5nOiAnYm9yZGVyLWJveCcsXG4gIHBhZGRpbmc6IDAsXG4gIG92ZXJmbG93OiAnaGlkZGVuJ1xufTtcbmV4cG9ydCB2YXIgdGV4dGFyZWEgPSB7XG4gIHBvc2l0aW9uOiAnYWJzb2x1dGUnLFxuICB0b3A6IDAsXG4gIGxlZnQ6IDAsXG4gIGhlaWdodDogJzEwMCUnLFxuICB3aWR0aDogJzEwMCUnLFxuICByZXNpemU6ICdub25lJyxcbiAgY29sb3I6ICdpbmhlcml0JyxcbiAgb3BhY2l0eTogMC44LFxuICBvdmVyZmxvdzogJ2hpZGRlbicsXG4gIE1vek9zeEZvbnRTbW9vdGhpbmc6ICdncmF5c2NhbGUnLFxuICBXZWJraXRGb250U21vb3RoaW5nOiAnYW50aWFsaWFzZWQnLFxuICBXZWJraXRUZXh0RmlsbENvbG9yOiAndHJhbnNwYXJlbnQnXG59O1xuZXhwb3J0IHZhciBlZGl0b3IgPSB7XG4gIG1hcmdpbjogMCxcbiAgYm9yZGVyOiAwLFxuICBiYWNrZ3JvdW5kOiAnbm9uZScsXG4gIGJveFNpemluZzogJ2luaGVyaXQnLFxuICBkaXNwbGF5OiAnaW5oZXJpdCcsXG4gIGZvbnRGYW1pbHk6ICdpbmhlcml0JyxcbiAgZm9udFNpemU6ICdpbmhlcml0JyxcbiAgZm9udFN0eWxlOiAnaW5oZXJpdCcsXG4gIGZvbnRWYXJpYW50TGlnYXR1cmVzOiAnaW5oZXJpdCcsXG4gIGZvbnRXZWlnaHQ6ICdpbmhlcml0JyxcbiAgbGV0dGVyU3BhY2luZzogJ2luaGVyaXQnLFxuICBsaW5lSGVpZ2h0OiAnaW5oZXJpdCcsXG4gIHRhYlNpemU6ICdpbmhlcml0JyxcbiAgdGV4dEluZGVudDogJ2luaGVyaXQnLFxuICB0ZXh0UmVuZGVyaW5nOiAnaW5oZXJpdCcsXG4gIHRleHRUcmFuc2Zvcm06ICdpbmhlcml0JyxcbiAgd2hpdGVTcGFjZTogJ3ByZS13cmFwJyxcbiAgd29yZEJyZWFrOiAna2VlcC1hbGwnLFxuICBvdmVyZmxvd1dyYXA6ICdicmVhay13b3JkJyxcbiAgb3V0bGluZTogMFxufTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-textarea-code-editor/esm/styles.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-textarea-code-editor/esm/utils.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@uiw/react-textarea-code-editor/esm/utils.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   htmlEncode: () => (/* binding */ htmlEncode),\n/* harmony export */   processHtml: () => (/* binding */ processHtml),\n/* harmony export */   stopPropagation: () => (/* binding */ stopPropagation)\n/* harmony export */ });\n/* harmony import */ var rehype__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rehype */ \"(ssr)/./node_modules/rehype/index.js\");\n\nvar processHtml = function processHtml(html, plugins) {\n  if (plugins === void 0) {\n    plugins = [];\n  }\n  return (0,rehype__WEBPACK_IMPORTED_MODULE_0__.rehype)().data('settings', {\n    fragment: true\n  }).use([...plugins]).processSync(\"\" + html).toString();\n};\nfunction htmlEncode(sHtml) {\n  return sHtml.replace(/```(tsx?|jsx?|html|xml)(.*)\\s+([\\s\\S]*?)(\\s.+)?```/g, str => {\n    return str.replace(/[<&\"]/g, c => ({\n      '<': '&lt;',\n      '>': '&gt;',\n      '&': '&amp;',\n      '\"': '&quot;'\n    })[c]);\n  }).replace(/[<&\"]/g, c => ({\n    '<': '&lt;',\n    '>': '&gt;',\n    '&': '&amp;',\n    '\"': '&quot;'\n  })[c]);\n}\nfunction stopPropagation(e) {\n  e.stopPropagation();\n  e.preventDefault();\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC10ZXh0YXJlYS1jb2RlLWVkaXRvci9lc20vdXRpbHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFnQztBQUN6QjtBQUNQO0FBQ0E7QUFDQTtBQUNBLFNBQVMsOENBQU07QUFDZjtBQUNBLEdBQUc7QUFDSDtBQUNPO0FBQ1A7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCLGtCQUFrQjtBQUNsQixLQUFLO0FBQ0wsR0FBRztBQUNILGNBQWM7QUFDZCxjQUFjO0FBQ2QsZUFBZTtBQUNmLGdCQUFnQjtBQUNoQixHQUFHO0FBQ0g7QUFDTztBQUNQO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXEN1cnNvciBQcm9qZWN0XFx3ZWJzaXRlXFxBdWdtZW50MlxccHJvbXB0LW1hbmFnZXJcXG5vZGVfbW9kdWxlc1xcQHVpd1xccmVhY3QtdGV4dGFyZWEtY29kZS1lZGl0b3JcXGVzbVxcdXRpbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcmVoeXBlIH0gZnJvbSAncmVoeXBlJztcbmV4cG9ydCB2YXIgcHJvY2Vzc0h0bWwgPSBmdW5jdGlvbiBwcm9jZXNzSHRtbChodG1sLCBwbHVnaW5zKSB7XG4gIGlmIChwbHVnaW5zID09PSB2b2lkIDApIHtcbiAgICBwbHVnaW5zID0gW107XG4gIH1cbiAgcmV0dXJuIHJlaHlwZSgpLmRhdGEoJ3NldHRpbmdzJywge1xuICAgIGZyYWdtZW50OiB0cnVlXG4gIH0pLnVzZShbLi4ucGx1Z2luc10pLnByb2Nlc3NTeW5jKFwiXCIgKyBodG1sKS50b1N0cmluZygpO1xufTtcbmV4cG9ydCBmdW5jdGlvbiBodG1sRW5jb2RlKHNIdG1sKSB7XG4gIHJldHVybiBzSHRtbC5yZXBsYWNlKC9gYGAodHN4P3xqc3g/fGh0bWx8eG1sKSguKilcXHMrKFtcXHNcXFNdKj8pKFxccy4rKT9gYGAvZywgc3RyID0+IHtcbiAgICByZXR1cm4gc3RyLnJlcGxhY2UoL1s8JlwiXS9nLCBjID0+ICh7XG4gICAgICAnPCc6ICcmbHQ7JyxcbiAgICAgICc+JzogJyZndDsnLFxuICAgICAgJyYnOiAnJmFtcDsnLFxuICAgICAgJ1wiJzogJyZxdW90OydcbiAgICB9KVtjXSk7XG4gIH0pLnJlcGxhY2UoL1s8JlwiXS9nLCBjID0+ICh7XG4gICAgJzwnOiAnJmx0OycsXG4gICAgJz4nOiAnJmd0OycsXG4gICAgJyYnOiAnJmFtcDsnLFxuICAgICdcIic6ICcmcXVvdDsnXG4gIH0pW2NdKTtcbn1cbmV4cG9ydCBmdW5jdGlvbiBzdG9wUHJvcGFnYXRpb24oZSkge1xuICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICBlLnByZXZlbnREZWZhdWx0KCk7XG59Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-textarea-code-editor/esm/utils.js\n");

/***/ })

};
;