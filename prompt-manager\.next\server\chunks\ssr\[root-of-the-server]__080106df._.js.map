{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/store/index.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { devtools, persist } from 'zustand/middleware'\n\n// 定义类型\nexport interface Category {\n  id: string\n  name: string\n  description?: string\n  color: string\n  icon?: string\n  createdAt: Date\n  updatedAt: Date\n  createdById: string\n  _count?: {\n    prompts: number\n  }\n}\n\nexport interface Prompt {\n  id: string\n  title: string\n  content: string\n  description?: string\n  tags: string[]\n  usageCount: number\n  isPublic: boolean\n  createdAt: Date\n  updatedAt: Date\n  categoryId?: string\n  createdById: string\n  category?: {\n    id: string\n    name: string\n    color: string\n    icon?: string\n  }\n  createdBy?: {\n    id: string\n    name?: string\n    image?: string\n  }\n}\n\n// UI状态接口\ninterface UIState {\n  // 侧边栏状态\n  sidebarOpen: boolean\n  setSidebarOpen: (open: boolean) => void\n  \n  // 搜索状态\n  searchQuery: string\n  setSearchQuery: (query: string) => void\n  \n  // 当前选中的分类\n  selectedCategoryId?: string\n  setSelectedCategoryId: (categoryId?: string) => void\n  \n  // 视图模式\n  viewMode: 'grid' | 'list'\n  setViewMode: (mode: 'grid' | 'list') => void\n  \n  // 排序方式\n  sortBy: 'createdAt' | 'updatedAt' | 'usageCount' | 'title'\n  setSortBy: (sortBy: 'createdAt' | 'updatedAt' | 'usageCount' | 'title') => void\n  \n  sortOrder: 'asc' | 'desc'\n  setSortOrder: (order: 'asc' | 'desc') => void\n  \n  // 模态框状态\n  modals: {\n    createPrompt: boolean\n    editPrompt: boolean\n    createCategory: boolean\n    editCategory: boolean\n    promptDetail: boolean\n  }\n  setModal: (modal: keyof UIState['modals'], open: boolean) => void\n  \n  // 当前编辑的项目\n  currentEditingPrompt?: Prompt\n  setCurrentEditingPrompt: (prompt?: Prompt) => void\n  \n  currentEditingCategory?: Category\n  setCurrentEditingCategory: (category?: Category) => void\n  \n  currentViewingPrompt?: Prompt\n  setCurrentViewingPrompt: (prompt?: Prompt) => void\n}\n\n// 搜索历史状态接口\ninterface SearchHistoryState {\n  searchHistory: string[]\n  addSearchHistory: (query: string) => void\n  clearSearchHistory: () => void\n  removeSearchHistory: (query: string) => void\n}\n\n// 用户偏好设置接口\ninterface UserPreferencesState {\n  // 主题设置\n  theme: 'light' | 'dark' | 'system'\n  setTheme: (theme: 'light' | 'dark' | 'system') => void\n  \n  // 语言设置\n  language: 'zh-CN' | 'en-US'\n  setLanguage: (language: 'zh-CN' | 'en-US') => void\n  \n  // 每页显示数量\n  pageSize: number\n  setPageSize: (size: number) => void\n  \n  // 是否显示描述\n  showDescription: boolean\n  setShowDescription: (show: boolean) => void\n  \n  // 是否显示标签\n  showTags: boolean\n  setShowTags: (show: boolean) => void\n  \n  // 是否显示使用次数\n  showUsageCount: boolean\n  setShowUsageCount: (show: boolean) => void\n}\n\n// 创建UI状态store\nexport const useUIStore = create<UIState>()(\n  devtools(\n    persist(\n      (set) => ({\n        sidebarOpen: true,\n        setSidebarOpen: (open) => set({ sidebarOpen: open }),\n        \n        searchQuery: '',\n        setSearchQuery: (query) => set({ searchQuery: query }),\n        \n        selectedCategoryId: undefined,\n        setSelectedCategoryId: (categoryId) => set({ selectedCategoryId: categoryId }),\n        \n        viewMode: 'grid',\n        setViewMode: (mode) => set({ viewMode: mode }),\n        \n        sortBy: 'createdAt',\n        setSortBy: (sortBy) => set({ sortBy }),\n        \n        sortOrder: 'desc',\n        setSortOrder: (order) => set({ sortOrder: order }),\n        \n        modals: {\n          createPrompt: false,\n          editPrompt: false,\n          createCategory: false,\n          editCategory: false,\n          promptDetail: false,\n        },\n        setModal: (modal, open) => \n          set((state) => ({\n            modals: { ...state.modals, [modal]: open }\n          })),\n        \n        currentEditingPrompt: undefined,\n        setCurrentEditingPrompt: (prompt) => set({ currentEditingPrompt: prompt }),\n        \n        currentEditingCategory: undefined,\n        setCurrentEditingCategory: (category) => set({ currentEditingCategory: category }),\n        \n        currentViewingPrompt: undefined,\n        setCurrentViewingPrompt: (prompt) => set({ currentViewingPrompt: prompt }),\n      }),\n      {\n        name: 'ui-store',\n        partialize: (state) => ({\n          sidebarOpen: state.sidebarOpen,\n          viewMode: state.viewMode,\n          sortBy: state.sortBy,\n          sortOrder: state.sortOrder,\n        }),\n      }\n    ),\n    { name: 'ui-store' }\n  )\n)\n\n// 创建搜索历史store\nexport const useSearchHistoryStore = create<SearchHistoryState>()(\n  devtools(\n    persist(\n      (set, get) => ({\n        searchHistory: [],\n        addSearchHistory: (query) => {\n          if (!query.trim()) return\n          \n          const { searchHistory } = get()\n          const newHistory = [query, ...searchHistory.filter(h => h !== query)].slice(0, 10)\n          set({ searchHistory: newHistory })\n        },\n        clearSearchHistory: () => set({ searchHistory: [] }),\n        removeSearchHistory: (query) => \n          set((state) => ({\n            searchHistory: state.searchHistory.filter(h => h !== query)\n          })),\n      }),\n      {\n        name: 'search-history-store',\n      }\n    ),\n    { name: 'search-history-store' }\n  )\n)\n\n// 创建用户偏好设置store\nexport const useUserPreferencesStore = create<UserPreferencesState>()(\n  devtools(\n    persist(\n      (set) => ({\n        theme: 'system',\n        setTheme: (theme) => set({ theme }),\n        \n        language: 'zh-CN',\n        setLanguage: (language) => set({ language }),\n        \n        pageSize: 20,\n        setPageSize: (size) => set({ pageSize: size }),\n        \n        showDescription: true,\n        setShowDescription: (show) => set({ showDescription: show }),\n        \n        showTags: true,\n        setShowTags: (show) => set({ showTags: show }),\n        \n        showUsageCount: true,\n        setShowUsageCount: (show) => set({ showUsageCount: show }),\n      }),\n      {\n        name: 'user-preferences-store',\n      }\n    ),\n    { name: 'user-preferences-store' }\n  )\n)\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AA4HO,MAAM,aAAa,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC7B,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,MAAQ,CAAC;QACR,aAAa;QACb,gBAAgB,CAAC,OAAS,IAAI;gBAAE,aAAa;YAAK;QAElD,aAAa;QACb,gBAAgB,CAAC,QAAU,IAAI;gBAAE,aAAa;YAAM;QAEpD,oBAAoB;QACpB,uBAAuB,CAAC,aAAe,IAAI;gBAAE,oBAAoB;YAAW;QAE5E,UAAU;QACV,aAAa,CAAC,OAAS,IAAI;gBAAE,UAAU;YAAK;QAE5C,QAAQ;QACR,WAAW,CAAC,SAAW,IAAI;gBAAE;YAAO;QAEpC,WAAW;QACX,cAAc,CAAC,QAAU,IAAI;gBAAE,WAAW;YAAM;QAEhD,QAAQ;YACN,cAAc;YACd,YAAY;YACZ,gBAAgB;YAChB,cAAc;YACd,cAAc;QAChB;QACA,UAAU,CAAC,OAAO,OAChB,IAAI,CAAC,QAAU,CAAC;oBACd,QAAQ;wBAAE,GAAG,MAAM,MAAM;wBAAE,CAAC,MAAM,EAAE;oBAAK;gBAC3C,CAAC;QAEH,sBAAsB;QACtB,yBAAyB,CAAC,SAAW,IAAI;gBAAE,sBAAsB;YAAO;QAExE,wBAAwB;QACxB,2BAA2B,CAAC,WAAa,IAAI;gBAAE,wBAAwB;YAAS;QAEhF,sBAAsB;QACtB,yBAAyB,CAAC,SAAW,IAAI;gBAAE,sBAAsB;YAAO;IAC1E,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,aAAa,MAAM,WAAW;YAC9B,UAAU,MAAM,QAAQ;YACxB,QAAQ,MAAM,MAAM;YACpB,WAAW,MAAM,SAAS;QAC5B,CAAC;AACH,IAEF;IAAE,MAAM;AAAW;AAKhB,MAAM,wBAAwB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IACxC,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,eAAe,EAAE;QACjB,kBAAkB,CAAC;YACjB,IAAI,CAAC,MAAM,IAAI,IAAI;YAEnB,MAAM,EAAE,aAAa,EAAE,GAAG;YAC1B,MAAM,aAAa;gBAAC;mBAAU,cAAc,MAAM,CAAC,CAAA,IAAK,MAAM;aAAO,CAAC,KAAK,CAAC,GAAG;YAC/E,IAAI;gBAAE,eAAe;YAAW;QAClC;QACA,oBAAoB,IAAM,IAAI;gBAAE,eAAe,EAAE;YAAC;QAClD,qBAAqB,CAAC,QACpB,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe,MAAM,aAAa,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;gBACvD,CAAC;IACL,CAAC,GACD;IACE,MAAM;AACR,IAEF;IAAE,MAAM;AAAuB;AAK5B,MAAM,0BAA0B,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC1C,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,MAAQ,CAAC;QACR,OAAO;QACP,UAAU,CAAC,QAAU,IAAI;gBAAE;YAAM;QAEjC,UAAU;QACV,aAAa,CAAC,WAAa,IAAI;gBAAE;YAAS;QAE1C,UAAU;QACV,aAAa,CAAC,OAAS,IAAI;gBAAE,UAAU;YAAK;QAE5C,iBAAiB;QACjB,oBAAoB,CAAC,OAAS,IAAI;gBAAE,iBAAiB;YAAK;QAE1D,UAAU;QACV,aAAa,CAAC,OAAS,IAAI;gBAAE,UAAU;YAAK;QAE5C,gBAAgB;QAChB,mBAAmB,CAAC,OAAS,IAAI;gBAAE,gBAAgB;YAAK;IAC1D,CAAC,GACD;IACE,MAAM;AACR,IAEF;IAAE,MAAM;AAAyB", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/hooks/useStore.ts"], "sourcesContent": ["import { useCallback } from 'react'\nimport { \n  useUIStore, \n  useSearchHistoryStore, \n  useUserPreferencesStore,\n  type Prompt,\n  type Category \n} from '~/store'\n\n// UI相关的hooks\nexport const useUI = () => {\n  const store = useUIStore()\n  \n  return {\n    // 侧边栏\n    sidebarOpen: store.sidebarOpen,\n    toggleSidebar: useCallback(() => store.setSidebarOpen(!store.sidebarOpen), [store]),\n    openSidebar: useCallback(() => store.setSidebarOpen(true), [store]),\n    closeSidebar: useCallback(() => store.setSidebarOpen(false), [store]),\n    \n    // 搜索\n    searchQuery: store.searchQuery,\n    setSearchQuery: store.setSearchQuery,\n    clearSearch: useCallback(() => store.setSearchQuery(''), [store]),\n    \n    // 分类筛选\n    selectedCategoryId: store.selectedCategoryId,\n    setSelectedCategoryId: store.setSelectedCategoryId,\n    clearCategoryFilter: useCallback(() => store.setSelectedCategoryId(undefined), [store]),\n    \n    // 视图模式\n    viewMode: store.viewMode,\n    setViewMode: store.setViewMode,\n    toggleViewMode: useCallback(() => {\n      store.setViewMode(store.viewMode === 'grid' ? 'list' : 'grid')\n    }, [store]),\n    \n    // 排序\n    sortBy: store.sortBy,\n    setSortBy: store.setSortBy,\n    sortOrder: store.sortOrder,\n    setSortOrder: store.setSortOrder,\n    toggleSortOrder: useCallback(() => {\n      store.setSortOrder(store.sortOrder === 'asc' ? 'desc' : 'asc')\n    }, [store]),\n  }\n}\n\n// 模态框相关的hooks\nexport const useModals = () => {\n  const store = useUIStore()\n  \n  return {\n    modals: store.modals,\n    \n    // 提示词相关模态框\n    openCreatePrompt: useCallback(() => store.setModal('createPrompt', true), [store]),\n    closeCreatePrompt: useCallback(() => store.setModal('createPrompt', false), [store]),\n    \n    openEditPrompt: useCallback((prompt: Prompt) => {\n      store.setCurrentEditingPrompt(prompt)\n      store.setModal('editPrompt', true)\n    }, [store]),\n    closeEditPrompt: useCallback(() => {\n      store.setCurrentEditingPrompt(undefined)\n      store.setModal('editPrompt', false)\n    }, [store]),\n    \n    openPromptDetail: useCallback((prompt: Prompt) => {\n      store.setCurrentViewingPrompt(prompt)\n      store.setModal('promptDetail', true)\n    }, [store]),\n    closePromptDetail: useCallback(() => {\n      store.setCurrentViewingPrompt(undefined)\n      store.setModal('promptDetail', false)\n    }, [store]),\n    \n    // 分类相关模态框\n    openCreateCategory: useCallback(() => store.setModal('createCategory', true), [store]),\n    closeCreateCategory: useCallback(() => store.setModal('createCategory', false), [store]),\n    \n    openEditCategory: useCallback((category: Category) => {\n      store.setCurrentEditingCategory(category)\n      store.setModal('editCategory', true)\n    }, [store]),\n    closeEditCategory: useCallback(() => {\n      store.setCurrentEditingCategory(undefined)\n      store.setModal('editCategory', false)\n    }, [store]),\n    \n    // 当前编辑的项目\n    currentEditingPrompt: store.currentEditingPrompt,\n    currentEditingCategory: store.currentEditingCategory,\n    currentViewingPrompt: store.currentViewingPrompt,\n  }\n}\n\n// 搜索历史相关的hooks\nexport const useSearchHistory = () => {\n  const store = useSearchHistoryStore()\n  \n  return {\n    searchHistory: store.searchHistory,\n    addSearchHistory: store.addSearchHistory,\n    clearSearchHistory: store.clearSearchHistory,\n    removeSearchHistory: store.removeSearchHistory,\n    \n    // 便捷方法\n    hasHistory: store.searchHistory.length > 0,\n    recentSearches: store.searchHistory.slice(0, 5), // 最近5个搜索\n  }\n}\n\n// 用户偏好设置相关的hooks\nexport const useUserPreferences = () => {\n  const store = useUserPreferencesStore()\n  \n  return {\n    // 主题\n    theme: store.theme,\n    setTheme: store.setTheme,\n    isDarkMode: store.theme === 'dark',\n    isLightMode: store.theme === 'light',\n    isSystemMode: store.theme === 'system',\n    \n    // 语言\n    language: store.language,\n    setLanguage: store.setLanguage,\n    isChineseMode: store.language === 'zh-CN',\n    isEnglishMode: store.language === 'en-US',\n    \n    // 显示设置\n    pageSize: store.pageSize,\n    setPageSize: store.setPageSize,\n    showDescription: store.showDescription,\n    setShowDescription: store.setShowDescription,\n    showTags: store.showTags,\n    setShowTags: store.setShowTags,\n    showUsageCount: store.showUsageCount,\n    setShowUsageCount: store.setShowUsageCount,\n    \n    // 便捷方法\n    toggleDescription: useCallback(() => store.setShowDescription(!store.showDescription), [store]),\n    toggleTags: useCallback(() => store.setShowTags(!store.showTags), [store]),\n    toggleUsageCount: useCallback(() => store.setShowUsageCount(!store.showUsageCount), [store]),\n  }\n}\n\n// 组合hook，提供常用的状态和操作\nexport const useAppState = () => {\n  const ui = useUI()\n  const modals = useModals()\n  const searchHistory = useSearchHistory()\n  const preferences = useUserPreferences()\n  \n  return {\n    ui,\n    modals,\n    searchHistory,\n    preferences,\n    \n    // 全局重置方法\n    resetFilters: useCallback(() => {\n      ui.clearSearch()\n      ui.clearCategoryFilter()\n      ui.setSortBy('createdAt')\n      ui.setSortOrder('desc')\n    }, [ui]),\n    \n    // 搜索相关的组合操作\n    performSearch: useCallback((query: string) => {\n      if (query.trim()) {\n        searchHistory.addSearchHistory(query.trim())\n        ui.setSearchQuery(query.trim())\n      }\n    }, [ui, searchHistory]),\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AASO,MAAM,QAAQ;IACnB,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,aAAU,AAAD;IAEvB,OAAO;QACL,MAAM;QACN,aAAa,MAAM,WAAW;QAC9B,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAM,MAAM,cAAc,CAAC,CAAC,MAAM,WAAW,GAAG;YAAC;SAAM;QAClF,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAM,MAAM,cAAc,CAAC,OAAO;YAAC;SAAM;QAClE,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAM,MAAM,cAAc,CAAC,QAAQ;YAAC;SAAM;QAEpE,KAAK;QACL,aAAa,MAAM,WAAW;QAC9B,gBAAgB,MAAM,cAAc;QACpC,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAM,MAAM,cAAc,CAAC,KAAK;YAAC;SAAM;QAEhE,OAAO;QACP,oBAAoB,MAAM,kBAAkB;QAC5C,uBAAuB,MAAM,qBAAqB;QAClD,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAM,MAAM,qBAAqB,CAAC,YAAY;YAAC;SAAM;QAEtF,OAAO;QACP,UAAU,MAAM,QAAQ;QACxB,aAAa,MAAM,WAAW;QAC9B,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;YAC1B,MAAM,WAAW,CAAC,MAAM,QAAQ,KAAK,SAAS,SAAS;QACzD,GAAG;YAAC;SAAM;QAEV,KAAK;QACL,QAAQ,MAAM,MAAM;QACpB,WAAW,MAAM,SAAS;QAC1B,WAAW,MAAM,SAAS;QAC1B,cAAc,MAAM,YAAY;QAChC,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;YAC3B,MAAM,YAAY,CAAC,MAAM,SAAS,KAAK,QAAQ,SAAS;QAC1D,GAAG;YAAC;SAAM;IACZ;AACF;AAGO,MAAM,YAAY;IACvB,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,aAAU,AAAD;IAEvB,OAAO;QACL,QAAQ,MAAM,MAAM;QAEpB,WAAW;QACX,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAM,MAAM,QAAQ,CAAC,gBAAgB,OAAO;YAAC;SAAM;QACjF,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAM,MAAM,QAAQ,CAAC,gBAAgB,QAAQ;YAAC;SAAM;QAEnF,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;YAC3B,MAAM,uBAAuB,CAAC;YAC9B,MAAM,QAAQ,CAAC,cAAc;QAC/B,GAAG;YAAC;SAAM;QACV,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;YAC3B,MAAM,uBAAuB,CAAC;YAC9B,MAAM,QAAQ,CAAC,cAAc;QAC/B,GAAG;YAAC;SAAM;QAEV,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;YAC7B,MAAM,uBAAuB,CAAC;YAC9B,MAAM,QAAQ,CAAC,gBAAgB;QACjC,GAAG;YAAC;SAAM;QACV,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;YAC7B,MAAM,uBAAuB,CAAC;YAC9B,MAAM,QAAQ,CAAC,gBAAgB;QACjC,GAAG;YAAC;SAAM;QAEV,UAAU;QACV,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAM,MAAM,QAAQ,CAAC,kBAAkB,OAAO;YAAC;SAAM;QACrF,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAM,MAAM,QAAQ,CAAC,kBAAkB,QAAQ;YAAC;SAAM;QAEvF,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;YAC7B,MAAM,yBAAyB,CAAC;YAChC,MAAM,QAAQ,CAAC,gBAAgB;QACjC,GAAG;YAAC;SAAM;QACV,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;YAC7B,MAAM,yBAAyB,CAAC;YAChC,MAAM,QAAQ,CAAC,gBAAgB;QACjC,GAAG;YAAC;SAAM;QAEV,UAAU;QACV,sBAAsB,MAAM,oBAAoB;QAChD,wBAAwB,MAAM,sBAAsB;QACpD,sBAAsB,MAAM,oBAAoB;IAClD;AACF;AAGO,MAAM,mBAAmB;IAC9B,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD;IAElC,OAAO;QACL,eAAe,MAAM,aAAa;QAClC,kBAAkB,MAAM,gBAAgB;QACxC,oBAAoB,MAAM,kBAAkB;QAC5C,qBAAqB,MAAM,mBAAmB;QAE9C,OAAO;QACP,YAAY,MAAM,aAAa,CAAC,MAAM,GAAG;QACzC,gBAAgB,MAAM,aAAa,CAAC,KAAK,CAAC,GAAG;IAC/C;AACF;AAGO,MAAM,qBAAqB;IAChC,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,0BAAuB,AAAD;IAEpC,OAAO;QACL,KAAK;QACL,OAAO,MAAM,KAAK;QAClB,UAAU,MAAM,QAAQ;QACxB,YAAY,MAAM,KAAK,KAAK;QAC5B,aAAa,MAAM,KAAK,KAAK;QAC7B,cAAc,MAAM,KAAK,KAAK;QAE9B,KAAK;QACL,UAAU,MAAM,QAAQ;QACxB,aAAa,MAAM,WAAW;QAC9B,eAAe,MAAM,QAAQ,KAAK;QAClC,eAAe,MAAM,QAAQ,KAAK;QAElC,OAAO;QACP,UAAU,MAAM,QAAQ;QACxB,aAAa,MAAM,WAAW;QAC9B,iBAAiB,MAAM,eAAe;QACtC,oBAAoB,MAAM,kBAAkB;QAC5C,UAAU,MAAM,QAAQ;QACxB,aAAa,MAAM,WAAW;QAC9B,gBAAgB,MAAM,cAAc;QACpC,mBAAmB,MAAM,iBAAiB;QAE1C,OAAO;QACP,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAM,MAAM,kBAAkB,CAAC,CAAC,MAAM,eAAe,GAAG;YAAC;SAAM;QAC9F,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAM,MAAM,WAAW,CAAC,CAAC,MAAM,QAAQ,GAAG;YAAC;SAAM;QACzE,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAM,MAAM,iBAAiB,CAAC,CAAC,MAAM,cAAc,GAAG;YAAC;SAAM;IAC7F;AACF;AAGO,MAAM,cAAc;IACzB,MAAM,KAAK;IACX,MAAM,SAAS;IACf,MAAM,gBAAgB;IACtB,MAAM,cAAc;IAEpB,OAAO;QACL;QACA;QACA;QACA;QAEA,SAAS;QACT,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;YACxB,GAAG,WAAW;YACd,GAAG,mBAAmB;YACtB,GAAG,SAAS,CAAC;YACb,GAAG,YAAY,CAAC;QAClB,GAAG;YAAC;SAAG;QAEP,YAAY;QACZ,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;YAC1B,IAAI,MAAM,IAAI,IAAI;gBAChB,cAAc,gBAAgB,CAAC,MAAM,IAAI;gBACzC,GAAG,cAAc,CAAC,MAAM,IAAI;YAC9B;QACF,GAAG;YAAC;YAAI;SAAc;IACxB;AACF", "debugId": null}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\n/**\n * 合并 Tailwind CSS 类名\n */\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n/**\n * 复制文本到剪贴板\n */\nexport async function copyToClipboard(text: string): Promise<boolean> {\n  try {\n    if (navigator.clipboard && window.isSecureContext) {\n      await navigator.clipboard.writeText(text)\n      return true\n    } else {\n      // 降级方案\n      const textArea = document.createElement('textarea')\n      textArea.value = text\n      textArea.style.position = 'fixed'\n      textArea.style.left = '-999999px'\n      textArea.style.top = '-999999px'\n      document.body.appendChild(textArea)\n      textArea.focus()\n      textArea.select()\n      \n      const success = document.execCommand('copy')\n      document.body.removeChild(textArea)\n      return success\n    }\n  } catch (error) {\n    console.error('复制失败:', error)\n    return false\n  }\n}\n\n/**\n * 格式化日期\n */\nexport function formatDate(date: Date | string, options?: Intl.DateTimeFormatOptions): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date\n  \n  const defaultOptions: Intl.DateTimeFormatOptions = {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }\n  \n  return dateObj.toLocaleDateString('zh-CN', { ...defaultOptions, ...options })\n}\n\n/**\n * 格式化相对时间\n */\nexport function formatRelativeTime(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date\n  const now = new Date()\n  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000)\n  \n  if (diffInSeconds < 60) {\n    return '刚刚'\n  }\n  \n  const diffInMinutes = Math.floor(diffInSeconds / 60)\n  if (diffInMinutes < 60) {\n    return `${diffInMinutes}分钟前`\n  }\n  \n  const diffInHours = Math.floor(diffInMinutes / 60)\n  if (diffInHours < 24) {\n    return `${diffInHours}小时前`\n  }\n  \n  const diffInDays = Math.floor(diffInHours / 24)\n  if (diffInDays < 7) {\n    return `${diffInDays}天前`\n  }\n  \n  const diffInWeeks = Math.floor(diffInDays / 7)\n  if (diffInWeeks < 4) {\n    return `${diffInWeeks}周前`\n  }\n  \n  const diffInMonths = Math.floor(diffInDays / 30)\n  if (diffInMonths < 12) {\n    return `${diffInMonths}个月前`\n  }\n  \n  const diffInYears = Math.floor(diffInDays / 365)\n  return `${diffInYears}年前`\n}\n\n/**\n * 截断文本\n */\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) {\n    return text\n  }\n  return text.slice(0, maxLength) + '...'\n}\n\n/**\n * 高亮搜索关键词\n */\nexport function highlightText(text: string, query: string): string {\n  if (!query.trim()) {\n    return text\n  }\n  \n  const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&')})`, 'gi')\n  return text.replace(regex, '<mark class=\"bg-yellow-200 dark:bg-yellow-800\">$1</mark>')\n}\n\n/**\n * 生成随机颜色\n */\nexport function generateRandomColor(): string {\n  const colors = [\n    '#EF4444', // red-500\n    '#F97316', // orange-500\n    '#F59E0B', // amber-500\n    '#EAB308', // yellow-500\n    '#84CC16', // lime-500\n    '#22C55E', // green-500\n    '#10B981', // emerald-500\n    '#14B8A6', // teal-500\n    '#06B6D4', // cyan-500\n    '#0EA5E9', // sky-500\n    '#3B82F6', // blue-500\n    '#6366F1', // indigo-500\n    '#8B5CF6', // violet-500\n    '#A855F7', // purple-500\n    '#D946EF', // fuchsia-500\n    '#EC4899', // pink-500\n    '#F43F5E', // rose-500\n  ]\n  \n  return colors[Math.floor(Math.random() * colors.length)]!\n}\n\n/**\n * 防抖函数\n */\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout | null = null\n  \n  return (...args: Parameters<T>) => {\n    if (timeout) {\n      clearTimeout(timeout)\n    }\n    \n    timeout = setTimeout(() => {\n      func(...args)\n    }, wait)\n  }\n}\n\n/**\n * 节流函数\n */\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let inThrottle = false\n  \n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args)\n      inThrottle = true\n      setTimeout(() => {\n        inThrottle = false\n      }, wait)\n    }\n  }\n}\n\n/**\n * 格式化文件大小\n */\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n  \n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\n/**\n * 验证邮箱格式\n */\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\n/**\n * 生成唯一ID\n */\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36)\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;;;AAKO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAKO,eAAe,gBAAgB,IAAY;IAChD,IAAI;QACF,IAAI,UAAU,SAAS,IAAI,OAAO,eAAe,EAAE;YACjD,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,OAAO;QACT,OAAO;YACL,OAAO;YACP,MAAM,WAAW,SAAS,aAAa,CAAC;YACxC,SAAS,KAAK,GAAG;YACjB,SAAS,KAAK,CAAC,QAAQ,GAAG;YAC1B,SAAS,KAAK,CAAC,IAAI,GAAG;YACtB,SAAS,KAAK,CAAC,GAAG,GAAG;YACrB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,SAAS,KAAK;YACd,SAAS,MAAM;YAEf,MAAM,UAAU,SAAS,WAAW,CAAC;YACrC,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,SAAS;QACvB,OAAO;IACT;AACF;AAKO,SAAS,WAAW,IAAmB,EAAE,OAAoC;IAClF,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAE5D,MAAM,iBAA6C;QACjD,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;IAEA,OAAO,QAAQ,kBAAkB,CAAC,SAAS;QAAE,GAAG,cAAc;QAAE,GAAG,OAAO;IAAC;AAC7E;AAKO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,QAAQ,OAAO,EAAE,IAAI;IAEvE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,gBAAgB;IACjD,IAAI,gBAAgB,IAAI;QACtB,OAAO,GAAG,cAAc,GAAG,CAAC;IAC9B;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI;QACpB,OAAO,GAAG,YAAY,GAAG,CAAC;IAC5B;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,GAAG;QAClB,OAAO,GAAG,WAAW,EAAE,CAAC;IAC1B;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,IAAI,cAAc,GAAG;QACnB,OAAO,GAAG,YAAY,EAAE,CAAC;IAC3B;IAEA,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa;IAC7C,IAAI,eAAe,IAAI;QACrB,OAAO,GAAG,aAAa,GAAG,CAAC;IAC7B;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,OAAO,GAAG,YAAY,EAAE,CAAC;AAC3B;AAKO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW;QAC5B,OAAO;IACT;IACA,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAKO,SAAS,cAAc,IAAY,EAAE,KAAa;IACvD,IAAI,CAAC,MAAM,IAAI,IAAI;QACjB,OAAO;IACT;IAEA,MAAM,QAAQ,IAAI,OAAO,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,uBAAuB,QAAQ,CAAC,CAAC,EAAE;IAC9E,OAAO,KAAK,OAAO,CAAC,OAAO;AAC7B;AAKO,SAAS;IACd,MAAM,SAAS;QACb;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;AAC1D;AAKO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI,UAAiC;IAErC,OAAO,CAAC,GAAG;QACT,IAAI,SAAS;YACX,aAAa;QACf;QAEA,UAAU,WAAW;YACnB,QAAQ;QACV,GAAG;IACL;AACF;AAKO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI,aAAa;IAEjB,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW;gBACT,aAAa;YACf,GAAG;QACL;IACF;AACF;AAKO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAKO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAKO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE", "debugId": null}}, {"offset": {"line": 502, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/components/PromptCard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion } from 'framer-motion'\nimport { toast } from 'react-hot-toast'\nimport { Card, CardContent, CardFooter, CardHeader, CardTitle } from '~/components/ui/Card'\nimport { Badge } from '~/components/ui/Badge'\nimport { Button } from '~/components/ui/Button'\nimport { useModals } from '~/hooks/useStore'\nimport { copyToClipboard, formatRelativeTime, truncateText } from '~/lib/utils'\nimport type { Prompt } from '~/store'\n\ninterface PromptCardProps {\n  prompt: Prompt\n  onCopy?: (promptId: string) => void\n  showActions?: boolean\n  className?: string\n}\n\nexport function PromptCard({ \n  prompt, \n  onCopy, \n  showActions = true,\n  className \n}: PromptCardProps) {\n  const [isHovered, setIsHovered] = useState(false)\n  const { openPromptDetail, openEditPrompt } = useModals()\n\n  const handleCopy = async (e: React.MouseEvent) => {\n    e.stopPropagation()\n    \n    const success = await copyToClipboard(prompt.content)\n    if (success) {\n      toast.success('提示词已复制到剪贴板')\n      onCopy?.(prompt.id)\n    } else {\n      toast.error('复制失败，请重试')\n    }\n  }\n\n  const handleEdit = (e: React.MouseEvent) => {\n    e.stopPropagation()\n    openEditPrompt(prompt)\n  }\n\n  const handleCardClick = () => {\n    openPromptDetail(prompt)\n  }\n\n  return (\n    <div className={`card bg-base-100 shadow-xl hover:shadow-2xl transition-all duration-300 cursor-pointer ${className}`}>\n      <div\n        className=\"card-body\"\n        onMouseEnter={() => setIsHovered(true)}\n        onMouseLeave={() => setIsHovered(false)}\n        onClick={handleCardClick}\n      >\n        {/* 标题和使用次数 */}\n        <div className=\"flex items-start justify-between mb-3\">\n          <h2 className=\"card-title text-lg line-clamp-2 flex-1\">\n            {prompt.title}\n          </h2>\n          <div className=\"badge badge-primary ml-2 shrink-0\">\n            <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n            </svg>\n            {prompt.usageCount}次\n          </div>\n        </div>\n\n        {/* 分类标签 */}\n        {prompt.category && (\n          <div className=\"flex items-center gap-2 mb-3\">\n            <div\n              className=\"w-3 h-3 rounded-full\"\n              style={{ backgroundColor: prompt.category.color }}\n            />\n            <span className=\"badge badge-outline badge-sm\">\n              {prompt.category.name}\n            </span>\n          </div>\n        )}\n\n        {/* 描述 */}\n        {prompt.description && (\n          <p className=\"text-sm opacity-70 mb-3 line-clamp-2\">\n            {prompt.description}\n          </p>\n        )}\n\n        {/* 内容预览 */}\n        <div className=\"bg-base-200 rounded-lg p-3 mb-4\">\n          <p className=\"text-sm line-clamp-3 leading-relaxed\">\n            {truncateText(prompt.content, 150)}\n          </p>\n        </div>\n\n        {/* 标签 */}\n        {prompt.tags.length > 0 && (\n          <div className=\"flex flex-wrap gap-1 mb-4\">\n            {prompt.tags.slice(0, 3).map((tag, index) => (\n              <div key={index} className=\"badge badge-ghost badge-sm\">\n                {tag}\n              </div>\n            ))}\n            {prompt.tags.length > 3 && (\n              <div className=\"badge badge-neutral badge-sm\">\n                +{prompt.tags.length - 3}\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* 底部信息和操作 */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-2 text-xs opacity-60\">\n            <span>{formatRelativeTime(prompt.createdAt)}</span>\n            {prompt.createdBy?.name && (\n              <>\n                <span>•</span>\n                <span>{prompt.createdBy.name}</span>\n              </>\n            )}\n          </div>\n\n          <div className=\"card-actions\">\n            {isHovered && (\n              <button\n                onClick={handleEdit}\n                className=\"btn btn-ghost btn-sm btn-square\"\n              >\n                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                </svg>\n              </button>\n            )}\n\n            <button\n              onClick={handleCopy}\n              className=\"btn btn-ghost btn-sm btn-square\"\n            >\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAIA;AACA;AATA;;;;;;AAmBO,SAAS,WAAW,EACzB,MAAM,EACN,MAAM,EACN,cAAc,IAAI,EAClB,SAAS,EACO;IAChB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,gBAAgB,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,YAAS,AAAD;IAErD,MAAM,aAAa,OAAO;QACxB,EAAE,eAAe;QAEjB,MAAM,UAAU,MAAM,CAAA,GAAA,mHAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,OAAO;QACpD,IAAI,SAAS;YACX,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,SAAS,OAAO,EAAE;QACpB,OAAO;YACL,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,eAAe;QACjB,eAAe;IACjB;IAEA,MAAM,kBAAkB;QACtB,iBAAiB;IACnB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,uFAAuF,EAAE,WAAW;kBACnH,cAAA,8OAAC;YACC,WAAU;YACV,cAAc,IAAM,aAAa;YACjC,cAAc,IAAM,aAAa;YACjC,SAAS;;8BAGT,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,OAAO,KAAK;;;;;;sCAEf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;oCAAe,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACtE,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;gCAEtE,OAAO,UAAU;gCAAC;;;;;;;;;;;;;gBAKtB,OAAO,QAAQ,kBACd,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,iBAAiB,OAAO,QAAQ,CAAC,KAAK;4BAAC;;;;;;sCAElD,8OAAC;4BAAK,WAAU;sCACb,OAAO,QAAQ,CAAC,IAAI;;;;;;;;;;;;gBAM1B,OAAO,WAAW,kBACjB,8OAAC;oBAAE,WAAU;8BACV,OAAO,WAAW;;;;;;8BAKvB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCACV,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,OAAO,OAAO,EAAE;;;;;;;;;;;gBAKjC,OAAO,IAAI,CAAC,MAAM,GAAG,mBACpB,8OAAC;oBAAI,WAAU;;wBACZ,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBACjC,8OAAC;gCAAgB,WAAU;0CACxB;+BADO;;;;;wBAIX,OAAO,IAAI,CAAC,MAAM,GAAG,mBACpB,8OAAC;4BAAI,WAAU;;gCAA+B;gCAC1C,OAAO,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;8BAO/B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAM,CAAA,GAAA,mHAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO,SAAS;;;;;;gCACzC,OAAO,SAAS,EAAE,sBACjB;;sDACE,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAM,OAAO,SAAS,CAAC,IAAI;;;;;;;;;;;;;;sCAKlC,8OAAC;4BAAI,WAAU;;gCACZ,2BACC,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAK3E,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrF", "debugId": null}}, {"offset": {"line": 794, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/components/ui/Button.tsx"], "sourcesContent": ["import { forwardRef, type ButtonHTMLAttributes } from 'react'\nimport { cn } from '~/lib/utils'\n\nexport interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'\n  size?: 'default' | 'sm' | 'lg' | 'icon'\n  loading?: boolean\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'default', size = 'default', loading = false, disabled, children, ...props }, ref) => {\n    return (\n      <button\n        className={cn(\n          // 基础样式\n          'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50',\n          \n          // 变体样式\n          {\n            // 默认样式\n            'bg-primary text-primary-foreground shadow hover:bg-primary/90': variant === 'default',\n            \n            // 危险操作样式\n            'bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90': variant === 'destructive',\n            \n            // 轮廓样式\n            'border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground': variant === 'outline',\n            \n            // 次要样式\n            'bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80': variant === 'secondary',\n            \n            // 幽灵样式\n            'hover:bg-accent hover:text-accent-foreground': variant === 'ghost',\n            \n            // 链接样式\n            'text-primary underline-offset-4 hover:underline': variant === 'link',\n          },\n          \n          // 尺寸样式\n          {\n            'h-9 px-4 py-2': size === 'default',\n            'h-8 rounded-md px-3 text-xs': size === 'sm',\n            'h-10 rounded-md px-8': size === 'lg',\n            'h-9 w-9': size === 'icon',\n          },\n          \n          className\n        )}\n        disabled={disabled || loading}\n        ref={ref}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"mr-2 h-4 w-4 animate-spin\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\n\nButton.displayName = 'Button'\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACtB,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACpG,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OAAO;QACP,uOAEA,OAAO;QACP;YACE,OAAO;YACP,iEAAiE,YAAY;YAE7E,SAAS;YACT,gFAAgF,YAAY;YAE5F,OAAO;YACP,4FAA4F,YAAY;YAExG,OAAO;YACP,0EAA0E,YAAY;YAEtF,OAAO;YACP,gDAAgD,YAAY;YAE5D,OAAO;YACP,mDAAmD,YAAY;QACjE,GAEA,OAAO;QACP;YACE,iBAAiB,SAAS;YAC1B,+BAA+B,SAAS;YACxC,wBAAwB,SAAS;YACjC,WAAW,SAAS;QACtB,GAEA;QAEF,UAAU,YAAY;QACtB,KAAK;QACJ,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 878, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/components/LoadingSpinner.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { cn } from '~/lib/utils'\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg'\n  className?: string\n  text?: string\n}\n\nexport function LoadingSpinner({ size = 'md', className, text }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-6 h-6',\n    lg: 'w-8 h-8',\n  }\n\n  return (\n    <div className={cn('flex flex-col items-center justify-center gap-2', className)}>\n      <motion.div\n        className={cn('border-2 border-slate-200 dark:border-slate-700 border-t-blue-500 rounded-full', sizeClasses[size])}\n        animate={{ rotate: 360 }}\n        transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}\n      />\n      {text && (\n        <motion.p\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.2 }}\n          className=\"text-sm text-slate-600 dark:text-slate-400\"\n        >\n          {text}\n        </motion.p>\n      )}\n    </div>\n  )\n}\n\n// 脉冲加载动画\nexport function PulseLoader({ className }: { className?: string }) {\n  return (\n    <div className={cn('flex space-x-1', className)}>\n      {[0, 1, 2].map((index) => (\n        <motion.div\n          key={index}\n          className=\"w-2 h-2 bg-blue-500 rounded-full\"\n          animate={{\n            scale: [1, 1.2, 1],\n            opacity: [0.7, 1, 0.7],\n          }}\n          transition={{\n            duration: 1,\n            repeat: Infinity,\n            delay: index * 0.2,\n          }}\n        />\n      ))}\n    </div>\n  )\n}\n\n// 骨架屏加载\nexport function SkeletonLoader({ \n  lines = 3, \n  className \n}: { \n  lines?: number\n  className?: string \n}) {\n  return (\n    <div className={cn('space-y-3', className)}>\n      {Array.from({ length: lines }).map((_, index) => (\n        <motion.div\n          key={index}\n          className=\"h-4 bg-slate-200 dark:bg-slate-700 rounded\"\n          style={{ width: `${Math.random() * 40 + 60}%` }}\n          animate={{\n            opacity: [0.5, 1, 0.5],\n          }}\n          transition={{\n            duration: 1.5,\n            repeat: Infinity,\n            delay: index * 0.1,\n          }}\n        />\n      ))}\n    </div>\n  )\n}\n\n// 卡片骨架屏\nexport function CardSkeleton({ className }: { className?: string }) {\n  return (\n    <div className={cn('p-6 border border-slate-200 dark:border-slate-700 rounded-lg', className)}>\n      <div className=\"space-y-4\">\n        {/* 标题 */}\n        <motion.div\n          className=\"h-6 bg-slate-200 dark:bg-slate-700 rounded w-3/4\"\n          animate={{ opacity: [0.5, 1, 0.5] }}\n          transition={{ duration: 1.5, repeat: Infinity }}\n        />\n        \n        {/* 内容行 */}\n        <div className=\"space-y-2\">\n          <motion.div\n            className=\"h-4 bg-slate-200 dark:bg-slate-700 rounded\"\n            animate={{ opacity: [0.5, 1, 0.5] }}\n            transition={{ duration: 1.5, repeat: Infinity, delay: 0.1 }}\n          />\n          <motion.div\n            className=\"h-4 bg-slate-200 dark:bg-slate-700 rounded w-5/6\"\n            animate={{ opacity: [0.5, 1, 0.5] }}\n            transition={{ duration: 1.5, repeat: Infinity, delay: 0.2 }}\n          />\n          <motion.div\n            className=\"h-4 bg-slate-200 dark:bg-slate-700 rounded w-4/6\"\n            animate={{ opacity: [0.5, 1, 0.5] }}\n            transition={{ duration: 1.5, repeat: Infinity, delay: 0.3 }}\n          />\n        </div>\n        \n        {/* 底部按钮区域 */}\n        <div className=\"flex justify-between items-center pt-2\">\n          <motion.div\n            className=\"h-4 bg-slate-200 dark:bg-slate-700 rounded w-1/4\"\n            animate={{ opacity: [0.5, 1, 0.5] }}\n            transition={{ duration: 1.5, repeat: Infinity, delay: 0.4 }}\n          />\n          <motion.div\n            className=\"h-8 bg-slate-200 dark:bg-slate-700 rounded w-20\"\n            animate={{ opacity: [0.5, 1, 0.5] }}\n            transition={{ duration: 1.5, repeat: Infinity, delay: 0.5 }}\n          />\n        </div>\n      </div>\n    </div>\n  )\n}\n\n// 波浪加载动画\nexport function WaveLoader({ className }: { className?: string }) {\n  return (\n    <div className={cn('flex items-center space-x-1', className)}>\n      {[0, 1, 2, 3, 4].map((index) => (\n        <motion.div\n          key={index}\n          className=\"w-1 h-8 bg-blue-500 rounded-full\"\n          animate={{\n            scaleY: [1, 2, 1],\n          }}\n          transition={{\n            duration: 1,\n            repeat: Infinity,\n            delay: index * 0.1,\n          }}\n        />\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AAHA;;;;AAWO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,IAAI,EAAuB;IAClF,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mDAAmD;;0BACpE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kFAAkF,WAAW,CAAC,KAAK;gBACjH,SAAS;oBAAE,QAAQ;gBAAI;gBACvB,YAAY;oBAAE,UAAU;oBAAG,QAAQ;oBAAU,MAAM;gBAAS;;;;;;YAE7D,sBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gBACP,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;0BAET;;;;;;;;;;;;AAKX;AAGO,SAAS,YAAY,EAAE,SAAS,EAA0B;IAC/D,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;kBAClC;YAAC;YAAG;YAAG;SAAE,CAAC,GAAG,CAAC,CAAC,sBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,WAAU;gBACV,SAAS;oBACP,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;oBAClB,SAAS;wBAAC;wBAAK;wBAAG;qBAAI;gBACxB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,OAAO,QAAQ;gBACjB;eAVK;;;;;;;;;;AAef;AAGO,SAAS,eAAe,EAC7B,QAAQ,CAAC,EACT,SAAS,EAIV;IACC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;kBAC7B,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,WAAU;gBACV,OAAO;oBAAE,OAAO,GAAG,KAAK,MAAM,KAAK,KAAK,GAAG,CAAC,CAAC;gBAAC;gBAC9C,SAAS;oBACP,SAAS;wBAAC;wBAAK;wBAAG;qBAAI;gBACxB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,OAAO,QAAQ;gBACjB;eAVK;;;;;;;;;;AAef;AAGO,SAAS,aAAa,EAAE,SAAS,EAA0B;IAChE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gEAAgE;kBACjF,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;4BAAC;4BAAK;4BAAG;yBAAI;oBAAC;oBAClC,YAAY;wBAAE,UAAU;wBAAK,QAAQ;oBAAS;;;;;;8BAIhD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;oCAAC;oCAAK;oCAAG;iCAAI;4BAAC;4BAClC,YAAY;gCAAE,UAAU;gCAAK,QAAQ;gCAAU,OAAO;4BAAI;;;;;;sCAE5D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;oCAAC;oCAAK;oCAAG;iCAAI;4BAAC;4BAClC,YAAY;gCAAE,UAAU;gCAAK,QAAQ;gCAAU,OAAO;4BAAI;;;;;;sCAE5D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;oCAAC;oCAAK;oCAAG;iCAAI;4BAAC;4BAClC,YAAY;gCAAE,UAAU;gCAAK,QAAQ;gCAAU,OAAO;4BAAI;;;;;;;;;;;;8BAK9D,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;oCAAC;oCAAK;oCAAG;iCAAI;4BAAC;4BAClC,YAAY;gCAAE,UAAU;gCAAK,QAAQ;gCAAU,OAAO;4BAAI;;;;;;sCAE5D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;oCAAC;oCAAK;oCAAG;iCAAI;4BAAC;4BAClC,YAAY;gCAAE,UAAU;gCAAK,QAAQ;gCAAU,OAAO;4BAAI;;;;;;;;;;;;;;;;;;;;;;;AAMtE;AAGO,SAAS,WAAW,EAAE,SAAS,EAA0B;IAC9D,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;kBAC/C;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE,CAAC,GAAG,CAAC,CAAC,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,WAAU;gBACV,SAAS;oBACP,QAAQ;wBAAC;wBAAG;wBAAG;qBAAE;gBACnB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,OAAO,QAAQ;gBACjB;eATK;;;;;;;;;;AAcf", "debugId": null}}, {"offset": {"line": 1197, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/components/PromptList.tsx"], "sourcesContent": ["'use client'\n\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { PromptCard } from '~/components/PromptCard'\nimport { But<PERSON> } from '~/components/ui/Button'\nimport { CardSkeleton } from '~/components/LoadingSpinner'\nimport { useUI } from '~/hooks/useStore'\nimport { api } from '~/trpc/react'\nimport type { Prompt } from '~/store'\n\ninterface PromptListProps {\n  prompts?: Prompt[]\n  isLoading?: boolean\n  onCopy?: (promptId: string) => void\n  showPagination?: boolean\n  currentPage?: number\n  totalPages?: number\n  onPageChange?: (page: number) => void\n}\n\nexport function PromptList({\n  prompts = [],\n  isLoading = false,\n  onCopy,\n  showPagination = false,\n  currentPage = 1,\n  totalPages = 1,\n  onPageChange,\n}: PromptListProps) {\n  const { viewMode, toggleViewMode } = useUI()\n\n  // 加载状态\n  if (isLoading) {\n    return (\n      <div className=\"space-y-4\">\n        {/* 视图切换按钮骨架 */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"h-4 bg-slate-200 dark:bg-slate-700 rounded w-32 animate-pulse\" />\n          <div className=\"h-9 bg-slate-200 dark:bg-slate-700 rounded w-24 animate-pulse\" />\n        </div>\n        \n        {/* 内容骨架 */}\n        <div className={\n          viewMode === 'grid'\n            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4'\n            : 'space-y-4'\n        }>\n          {Array.from({ length: 8 }).map((_, index) => (\n            <motion.div\n              key={index}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: index * 0.1 }}\n            >\n              <CardSkeleton />\n            </motion.div>\n          ))}\n        </div>\n      </div>\n    )\n  }\n\n  // 空状态\n  if (prompts.length === 0) {\n    return (\n      <div className=\"text-center py-16\">\n        <div className=\"hero\">\n          <div className=\"hero-content text-center\">\n            <div className=\"max-w-md\">\n              <div className=\"w-24 h-24 mx-auto mb-6 bg-base-300 rounded-full flex items-center justify-center\">\n                <svg\n                  className=\"w-12 h-12 opacity-50\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                  />\n                </svg>\n              </div>\n              <h1 className=\"text-3xl font-bold\">没有找到提示词</h1>\n              <p className=\"py-6\">\n                尝试调整搜索条件或创建新的提示词\n              </p>\n              <div className=\"flex justify-center gap-4\">\n                <button className=\"btn btn-primary\">\n                  创建提示词\n                </button>\n                <button className=\"btn btn-outline\">\n                  清除筛选\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      {/* 工具栏 */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"text-sm text-slate-600 dark:text-slate-400\">\n          共 {prompts.length} 个提示词\n        </div>\n        \n        <div className=\"flex items-center gap-2\">\n          {/* 视图切换 */}\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={toggleViewMode}\n            className=\"flex items-center gap-2\"\n          >\n            {viewMode === 'grid' ? (\n              <>\n                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 10h16M4 14h16M4 18h16\" />\n                </svg>\n                列表视图\n              </>\n            ) : (\n              <>\n                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z\" />\n                </svg>\n                网格视图\n              </>\n            )}\n          </Button>\n        </div>\n      </div>\n\n      {/* 提示词列表 */}\n      <AnimatePresence mode=\"wait\">\n        <motion.div\n          key={viewMode}\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          exit={{ opacity: 0, y: -20 }}\n          transition={{ duration: 0.3, ease: \"easeOut\" }}\n          className={\n            viewMode === 'grid'\n              ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'\n              : 'space-y-6'\n          }\n        >\n          {prompts.map((prompt, index) => (\n            <motion.div\n              key={prompt.id}\n              initial={{ opacity: 0, y: 30, scale: 0.9 }}\n              animate={{ opacity: 1, y: 0, scale: 1 }}\n              transition={{\n                delay: index * 0.08,\n                duration: 0.4,\n                ease: \"easeOut\"\n              }}\n              className={viewMode === 'list' ? 'w-full' : ''}\n            >\n              <PromptCard\n                prompt={prompt}\n                onCopy={onCopy}\n                className={viewMode === 'list' ? 'w-full' : ''}\n              />\n            </motion.div>\n          ))}\n        </motion.div>\n      </AnimatePresence>\n\n      {/* 分页 */}\n      {showPagination && totalPages > 1 && (\n        <div className=\"flex justify-center mt-8\">\n          <div className=\"join\">\n            <button\n              onClick={() => onPageChange?.(currentPage - 1)}\n              disabled={currentPage <= 1}\n              className=\"join-item btn btn-outline\"\n            >\n            <svg className=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n            </svg>\n              上一页\n            </button>\n\n            {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {\n              let page;\n              if (totalPages <= 5) {\n                page = i + 1;\n              } else if (currentPage <= 3) {\n                page = i + 1;\n              } else if (currentPage >= totalPages - 2) {\n                page = totalPages - 4 + i;\n              } else {\n                page = currentPage - 2 + i;\n              }\n\n              return (\n                <button\n                  key={page}\n                  onClick={() => onPageChange?.(page)}\n                  className={`join-item btn ${\n                    currentPage === page ? 'btn-active' : 'btn-outline'\n                  }`}\n                >\n                  {page}\n                </button>\n              );\n            })}\n\n            <button\n              onClick={() => onPageChange?.(currentPage + 1)}\n              disabled={currentPage >= totalPages}\n              className=\"join-item btn btn-outline\"\n            >\n              下一页\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAoBO,SAAS,WAAW,EACzB,UAAU,EAAE,EACZ,YAAY,KAAK,EACjB,MAAM,EACN,iBAAiB,KAAK,EACtB,cAAc,CAAC,EACf,aAAa,CAAC,EACd,YAAY,EACI;IAChB,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,QAAK,AAAD;IAEzC,OAAO;IACP,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;8BAIjB,8OAAC;oBAAI,WACH,aAAa,SACT,wEACA;8BAEH,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO,QAAQ;4BAAI;sCAEjC,cAAA,8OAAC,oIAAA,CAAA,eAAY;;;;;2BALR;;;;;;;;;;;;;;;;IAWjB;IAEA,MAAM;IACN,IAAI,QAAQ,MAAM,KAAK,GAAG;QACxB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,WAAU;oCACV,MAAK;oCACL,QAAO;oCACP,SAAQ;8CAER,cAAA,8OAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,aAAa;wCACb,GAAE;;;;;;;;;;;;;;;;0CAIR,8OAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,8OAAC;gCAAE,WAAU;0CAAO;;;;;;0CAGpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAAkB;;;;;;kDAGpC,8OAAC;wCAAO,WAAU;kDAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASlD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;4BAA6C;4BACvD,QAAQ,MAAM;4BAAC;;;;;;;kCAGpB,8OAAC;wBAAI,WAAU;kCAEb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCAET,aAAa,uBACZ;;kDACE,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;oCACjE;;6DAIR;;kDACE,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;oCACjE;;;;;;;;;;;;;;;;;;;0BAShB,8OAAC,yLAAA,CAAA,kBAAe;gBAAC,MAAK;0BACpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAU;oBAC7C,WACE,aAAa,SACT,wEACA;8BAGL,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;gCAAI,OAAO;4BAAI;4BACzC,SAAS;gCAAE,SAAS;gCAAG,GAAG;gCAAG,OAAO;4BAAE;4BACtC,YAAY;gCACV,OAAO,QAAQ;gCACf,UAAU;gCACV,MAAM;4BACR;4BACA,WAAW,aAAa,SAAS,WAAW;sCAE5C,cAAA,8OAAC,gIAAA,CAAA,aAAU;gCACT,QAAQ;gCACR,QAAQ;gCACR,WAAW,aAAa,SAAS,WAAW;;;;;;2BAbzC,OAAO,EAAE;;;;;mBAbb;;;;;;;;;;YAkCR,kBAAkB,aAAa,mBAC9B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS,IAAM,eAAe,cAAc;4BAC5C,UAAU,eAAe;4BACzB,WAAU;;8CAEZ,8OAAC;oCAAI,WAAU;oCAAe,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACtE,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;gCACjE;;;;;;;wBAIL,MAAM,IAAI,CAAC;4BAAE,QAAQ,KAAK,GAAG,CAAC,YAAY;wBAAG,GAAG,CAAC,GAAG;4BACnD,IAAI;4BACJ,IAAI,cAAc,GAAG;gCACnB,OAAO,IAAI;4BACb,OAAO,IAAI,eAAe,GAAG;gCAC3B,OAAO,IAAI;4BACb,OAAO,IAAI,eAAe,aAAa,GAAG;gCACxC,OAAO,aAAa,IAAI;4BAC1B,OAAO;gCACL,OAAO,cAAc,IAAI;4BAC3B;4BAEA,qBACE,8OAAC;gCAEC,SAAS,IAAM,eAAe;gCAC9B,WAAW,CAAC,cAAc,EACxB,gBAAgB,OAAO,eAAe,eACtC;0CAED;+BANI;;;;;wBASX;sCAEA,8OAAC;4BACC,SAAS,IAAM,eAAe,cAAc;4BAC5C,UAAU,eAAe;4BACzB,WAAU;;gCACX;8CAEC,8OAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrF", "debugId": null}}, {"offset": {"line": 1648, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/components/ui/Modal.tsx"], "sourcesContent": ["'use client'\n\nimport { forwardRef, useEffect, type HTMLAttributes, type ReactNode } from 'react'\nimport { createPortal } from 'react-dom'\nimport { cn } from '~/lib/utils'\n\ninterface ModalProps extends HTMLAttributes<HTMLDivElement> {\n  open: boolean\n  onClose: () => void\n  children: ReactNode\n  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'\n  closeOnOverlayClick?: boolean\n  closeOnEscape?: boolean\n}\n\nconst Modal = forwardRef<HTMLDivElement, ModalProps>(\n  ({ \n    open, \n    onClose, \n    children, \n    className,\n    size = 'md',\n    closeOnOverlayClick = true,\n    closeOnEscape = true,\n    ...props \n  }, ref) => {\n    // 处理ESC键关闭\n    useEffect(() => {\n      if (!closeOnEscape || !open) return\n\n      const handleEscape = (e: KeyboardEvent) => {\n        if (e.key === 'Escape') {\n          onClose()\n        }\n      }\n\n      document.addEventListener('keydown', handleEscape)\n      return () => document.removeEventListener('keydown', handleEscape)\n    }, [open, onClose, closeOnEscape])\n\n    // 防止背景滚动\n    useEffect(() => {\n      if (open) {\n        document.body.style.overflow = 'hidden'\n      } else {\n        document.body.style.overflow = 'unset'\n      }\n\n      return () => {\n        document.body.style.overflow = 'unset'\n      }\n    }, [open])\n\n    if (!open) return null\n\n    const sizeClasses = {\n      sm: 'max-w-sm',\n      md: 'max-w-md',\n      lg: 'max-w-lg',\n      xl: 'max-w-xl',\n      full: 'max-w-full mx-4',\n    }\n\n    const modalContent = (\n      <div className=\"fixed inset-0 z-50 flex items-center justify-center\">\n        {/* 背景遮罩 */}\n        <div \n          className=\"fixed inset-0 bg-black/50 backdrop-blur-sm\"\n          onClick={closeOnOverlayClick ? onClose : undefined}\n        />\n        \n        {/* 模态框内容 */}\n        <div\n          ref={ref}\n          className={cn(\n            'relative w-full rounded-lg bg-background shadow-lg',\n            sizeClasses[size],\n            className\n          )}\n          onClick={(e) => e.stopPropagation()}\n          {...props}\n        >\n          {children}\n        </div>\n      </div>\n    )\n\n    // 使用 Portal 渲染到 body\n    return typeof document !== 'undefined' \n      ? createPortal(modalContent, document.body)\n      : null\n  }\n)\nModal.displayName = 'Modal'\n\nconst ModalHeader = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex flex-col space-y-1.5 p-6', className)}\n      {...props}\n    />\n  )\n)\nModalHeader.displayName = 'ModalHeader'\n\nconst ModalTitle = forwardRef<HTMLHeadingElement, HTMLAttributes<HTMLHeadingElement>>(\n  ({ className, ...props }, ref) => (\n    <h2\n      ref={ref}\n      className={cn('text-lg font-semibold leading-none tracking-tight', className)}\n      {...props}\n    />\n  )\n)\nModalTitle.displayName = 'ModalTitle'\n\nconst ModalDescription = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLParagraphElement>>(\n  ({ className, ...props }, ref) => (\n    <p\n      ref={ref}\n      className={cn('text-sm text-muted-foreground', className)}\n      {...props}\n    />\n  )\n)\nModalDescription.displayName = 'ModalDescription'\n\nconst ModalContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n  )\n)\nModalContent.displayName = 'ModalContent'\n\nconst ModalFooter = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 p-6 pt-0', className)}\n      {...props}\n    />\n  )\n)\nModalFooter.displayName = 'ModalFooter'\n\nconst ModalCloseButton = forwardRef<HTMLButtonElement, HTMLAttributes<HTMLButtonElement> & { onClose: () => void }>(\n  ({ className, onClose, ...props }, ref) => (\n    <button\n      ref={ref}\n      className={cn(\n        'absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none',\n        className\n      )}\n      onClick={onClose}\n      {...props}\n    >\n      <svg\n        className=\"h-4 w-4\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n        fill=\"none\"\n        viewBox=\"0 0 24 24\"\n        stroke=\"currentColor\"\n      >\n        <path\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n          strokeWidth={2}\n          d=\"M6 18L18 6M6 6l12 12\"\n        />\n      </svg>\n      <span className=\"sr-only\">关闭</span>\n    </button>\n  )\n)\nModalCloseButton.displayName = 'ModalCloseButton'\n\nexport { \n  Modal, \n  ModalHeader, \n  ModalTitle, \n  ModalDescription, \n  ModalContent, \n  ModalFooter,\n  ModalCloseButton \n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAeA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrB,CAAC,EACC,IAAI,EACJ,OAAO,EACP,QAAQ,EACR,SAAS,EACT,OAAO,IAAI,EACX,sBAAsB,IAAI,EAC1B,gBAAgB,IAAI,EACpB,GAAG,OACJ,EAAE;IACD,WAAW;IACX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,iBAAiB,CAAC,MAAM;QAE7B,MAAM,eAAe,CAAC;YACpB,IAAI,EAAE,GAAG,KAAK,UAAU;gBACtB;YACF;QACF;QAEA,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;IACvD,GAAG;QAAC;QAAM;QAAS;KAAc;IAEjC,SAAS;IACT,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;KAAK;IAET,IAAI,CAAC,MAAM,OAAO;IAElB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,MAAM,6BACJ,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,WAAU;gBACV,SAAS,sBAAsB,UAAU;;;;;;0BAI3C,8OAAC;gBACC,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA,WAAW,CAAC,KAAK,EACjB;gBAEF,SAAS,CAAC,IAAM,EAAE,eAAe;gBAChC,GAAG,KAAK;0BAER;;;;;;;;;;;;IAKP,qBAAqB;IACrB,OAAO,OAAO,aAAa,4BACvB,CAAA,GAAA,4MAAA,CAAA,eAAY,AAAD,EAAE,cAAc,SAAS,IAAI,IACxC;AACN;AAEF,MAAM,WAAW,GAAG;AAEpB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAIf,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,qDAAqD;QAClE,GAAG,KAAK;;;;;;AAIf,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAChC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAIf,iBAAiB,WAAW,GAAG;AAE/B,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAGlE,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0EAA0E;QACvF,GAAG,KAAK;;;;;;AAIf,YAAY,WAAW,GAAG;AAE1B,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAChC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6MACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;gBACR,QAAO;0BAEP,cAAA,8OAAC;oBACC,eAAc;oBACd,gBAAe;oBACf,aAAa;oBACb,GAAE;;;;;;;;;;;0BAGN,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAIhC,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1832, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/components/ui/Badge.tsx"], "sourcesContent": ["import { type VariantProps, cva } from 'class-variance-authority'\nimport { forwardRef, type HTMLAttributes } from 'react'\nimport { cn } from '~/lib/utils'\n\nconst badgeVariants = cva(\n  'inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',\n  {\n    variants: {\n      variant: {\n        default:\n          'border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80',\n        secondary:\n          'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80',\n        destructive:\n          'border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80',\n        outline: 'text-foreground',\n        success:\n          'border-transparent bg-green-500 text-white shadow hover:bg-green-500/80',\n        warning:\n          'border-transparent bg-yellow-500 text-white shadow hover:bg-yellow-500/80',\n        info:\n          'border-transparent bg-blue-500 text-white shadow hover:bg-blue-500/80',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nconst Badge = forwardRef<HTMLDivElement, BadgeProps>(\n  ({ className, variant, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(badgeVariants({ variant }), className)}\n        {...props}\n      />\n    )\n  }\n)\nBadge.displayName = 'Badge'\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,wKACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,SACE;YACF,SACE;YACF,MACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE;IACjC,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1879, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/components/PromptDetailModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { toast } from 'react-hot-toast'\nimport { \n  <PERSON><PERSON>, \n  <PERSON><PERSON><PERSON>eader, \n  ModalT<PERSON><PERSON>, \n  ModalContent, \n  Modal<PERSON>ooter,\n  ModalCloseButton \n} from '~/components/ui/Modal'\nimport { Button } from '~/components/ui/Button'\nimport { Badge } from '~/components/ui/Badge'\nimport { useModals } from '~/hooks/useStore'\nimport { copyToClipboard, formatRelativeTime } from '~/lib/utils'\nimport { api } from '~/trpc/react'\n\nexport function PromptDetailModal() {\n  const { modals, currentViewingPrompt, closePromptDetail, openEditPrompt } = useModals()\n  const [isExpanded, setIsExpanded] = useState(false)\n  \n  // 复制提示词的mutation\n  const copyPromptMutation = api.prompt.copy.useMutation()\n  \n  if (!currentViewingPrompt) return null\n\n  const handleCopy = async () => {\n    const success = await copyToClipboard(currentViewingPrompt.content)\n    if (success) {\n      toast.success('提示词已复制到剪贴板')\n      try {\n        await copyPromptMutation.mutateAsync({ id: currentViewingPrompt.id })\n      } catch (error) {\n        console.error('更新使用次数失败:', error)\n      }\n    } else {\n      toast.error('复制失败，请重试')\n    }\n  }\n\n  const handleEdit = () => {\n    closePromptDetail()\n    openEditPrompt(currentViewingPrompt)\n  }\n\n  const contentLines = currentViewingPrompt.content.split('\\n')\n  const shouldShowExpand = contentLines.length > 10\n  const displayContent = isExpanded || !shouldShowExpand \n    ? currentViewingPrompt.content \n    : contentLines.slice(0, 10).join('\\n') + '\\n...'\n\n  return (\n    <Modal\n      open={modals.promptDetail}\n      onClose={closePromptDetail}\n      size=\"lg\"\n    >\n      <ModalHeader>\n        <ModalTitle className=\"pr-8\">\n          {currentViewingPrompt.title}\n        </ModalTitle>\n        <ModalCloseButton onClose={closePromptDetail} />\n        \n        {/* 分类和使用次数 */}\n        <div className=\"flex items-center gap-3 mt-3\">\n          {currentViewingPrompt.category && (\n            <div className=\"flex items-center gap-2\">\n              <div \n                className=\"w-3 h-3 rounded-full\"\n                style={{ backgroundColor: currentViewingPrompt.category.color }}\n              />\n              <span className=\"text-sm text-muted-foreground\">\n                {currentViewingPrompt.category.name}\n              </span>\n            </div>\n          )}\n          \n          <Badge variant=\"secondary\">\n            使用 {currentViewingPrompt.usageCount} 次\n          </Badge>\n          \n          {!currentViewingPrompt.isPublic && (\n            <Badge variant=\"outline\">\n              私有\n            </Badge>\n          )}\n        </div>\n      </ModalHeader>\n\n      <ModalContent>\n        {/* 描述 */}\n        {currentViewingPrompt.description && (\n          <div className=\"mb-4\">\n            <h4 className=\"text-sm font-medium text-muted-foreground mb-2\">描述</h4>\n            <p className=\"text-sm\">{currentViewingPrompt.description}</p>\n          </div>\n        )}\n\n        {/* 标签 */}\n        {currentViewingPrompt.tags.length > 0 && (\n          <div className=\"mb-4\">\n            <h4 className=\"text-sm font-medium text-muted-foreground mb-2\">标签</h4>\n            <div className=\"flex flex-wrap gap-1\">\n              {currentViewingPrompt.tags.map((tag, index) => (\n                <Badge key={index} variant=\"outline\" className=\"text-xs\">\n                  {tag}\n                </Badge>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* 提示词内容 */}\n        <div className=\"mb-4\">\n          <div className=\"flex items-center justify-between mb-2\">\n            <h4 className=\"text-sm font-medium text-muted-foreground\">提示词内容</h4>\n            {shouldShowExpand && (\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => setIsExpanded(!isExpanded)}\n                className=\"text-xs\"\n              >\n                {isExpanded ? '收起' : '展开全部'}\n              </Button>\n            )}\n          </div>\n          \n          <div className=\"bg-muted/50 rounded-lg p-4 max-h-96 overflow-y-auto\">\n            <pre className=\"text-sm whitespace-pre-wrap font-mono\">\n              {displayContent}\n            </pre>\n          </div>\n        </div>\n\n        {/* 元信息 */}\n        <div className=\"grid grid-cols-2 gap-4 text-xs text-muted-foreground\">\n          <div>\n            <span className=\"font-medium\">创建时间：</span>\n            <span>{formatRelativeTime(currentViewingPrompt.createdAt)}</span>\n          </div>\n          <div>\n            <span className=\"font-medium\">更新时间：</span>\n            <span>{formatRelativeTime(currentViewingPrompt.updatedAt)}</span>\n          </div>\n          {currentViewingPrompt.createdBy?.name && (\n            <div className=\"col-span-2\">\n              <span className=\"font-medium\">创建者：</span>\n              <span>{currentViewingPrompt.createdBy.name}</span>\n            </div>\n          )}\n        </div>\n      </ModalContent>\n\n      <ModalFooter>\n        <div className=\"flex items-center gap-2 w-full sm:w-auto\">\n          <Button\n            variant=\"outline\"\n            onClick={handleEdit}\n            className=\"flex-1 sm:flex-none\"\n          >\n            <svg\n              className=\"w-4 h-4 mr-2\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n              />\n            </svg>\n            编辑\n          </Button>\n          \n          <Button\n            onClick={handleCopy}\n            loading={copyPromptMutation.isPending}\n            className=\"flex-1 sm:flex-none\"\n          >\n            <svg\n              className=\"w-4 h-4 mr-2\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d=\"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\"\n              />\n            </svg>\n            复制内容\n          </Button>\n        </div>\n      </ModalFooter>\n    </Modal>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAQA;AACA;AACA;AACA;AACA;AAhBA;;;;;;;;;;AAkBO,SAAS;IACd,MAAM,EAAE,MAAM,EAAE,oBAAoB,EAAE,iBAAiB,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,YAAS,AAAD;IACpF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,iBAAiB;IACjB,MAAM,qBAAqB,qHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW;IAEtD,IAAI,CAAC,sBAAsB,OAAO;IAElC,MAAM,aAAa;QACjB,MAAM,UAAU,MAAM,CAAA,GAAA,mHAAA,CAAA,kBAAe,AAAD,EAAE,qBAAqB,OAAO;QAClE,IAAI,SAAS;YACX,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,IAAI;gBACF,MAAM,mBAAmB,WAAW,CAAC;oBAAE,IAAI,qBAAqB,EAAE;gBAAC;YACrE,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;YAC7B;QACF,OAAO;YACL,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,aAAa;QACjB;QACA,eAAe;IACjB;IAEA,MAAM,eAAe,qBAAqB,OAAO,CAAC,KAAK,CAAC;IACxD,MAAM,mBAAmB,aAAa,MAAM,GAAG;IAC/C,MAAM,iBAAiB,cAAc,CAAC,mBAClC,qBAAqB,OAAO,GAC5B,aAAa,KAAK,CAAC,GAAG,IAAI,IAAI,CAAC,QAAQ;IAE3C,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,MAAM,OAAO,YAAY;QACzB,SAAS;QACT,MAAK;;0BAEL,8OAAC,iIAAA,CAAA,cAAW;;kCACV,8OAAC,iIAAA,CAAA,aAAU;wBAAC,WAAU;kCACnB,qBAAqB,KAAK;;;;;;kCAE7B,8OAAC,iIAAA,CAAA,mBAAgB;wBAAC,SAAS;;;;;;kCAG3B,8OAAC;wBAAI,WAAU;;4BACZ,qBAAqB,QAAQ,kBAC5B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB,qBAAqB,QAAQ,CAAC,KAAK;wCAAC;;;;;;kDAEhE,8OAAC;wCAAK,WAAU;kDACb,qBAAqB,QAAQ,CAAC,IAAI;;;;;;;;;;;;0CAKzC,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;;oCAAY;oCACrB,qBAAqB,UAAU;oCAAC;;;;;;;4BAGrC,CAAC,qBAAqB,QAAQ,kBAC7B,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAU;;;;;;;;;;;;;;;;;;0BAO/B,8OAAC,iIAAA,CAAA,eAAY;;oBAEV,qBAAqB,WAAW,kBAC/B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiD;;;;;;0CAC/D,8OAAC;gCAAE,WAAU;0CAAW,qBAAqB,WAAW;;;;;;;;;;;;oBAK3D,qBAAqB,IAAI,CAAC,MAAM,GAAG,mBAClC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiD;;;;;;0CAC/D,8OAAC;gCAAI,WAAU;0CACZ,qBAAqB,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACnC,8OAAC,iIAAA,CAAA,QAAK;wCAAa,SAAQ;wCAAU,WAAU;kDAC5C;uCADS;;;;;;;;;;;;;;;;kCASpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA4C;;;;;;oCACzD,kCACC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,cAAc,CAAC;wCAC9B,WAAU;kDAET,aAAa,OAAO;;;;;;;;;;;;0CAK3B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACZ;;;;;;;;;;;;;;;;;kCAMP,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;kDAAc;;;;;;kDAC9B,8OAAC;kDAAM,CAAA,GAAA,mHAAA,CAAA,qBAAkB,AAAD,EAAE,qBAAqB,SAAS;;;;;;;;;;;;0CAE1D,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;kDAAc;;;;;;kDAC9B,8OAAC;kDAAM,CAAA,GAAA,mHAAA,CAAA,qBAAkB,AAAD,EAAE,qBAAqB,SAAS;;;;;;;;;;;;4BAEzD,qBAAqB,SAAS,EAAE,sBAC/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAc;;;;;;kDAC9B,8OAAC;kDAAM,qBAAqB,SAAS,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;;;0BAMlD,8OAAC,iIAAA,CAAA,cAAW;0BACV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,WAAU;;8CAEV,8OAAC;oCACC,WAAU;oCACV,MAAK;oCACL,QAAO;oCACP,SAAQ;8CAER,cAAA,8OAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,aAAa;wCACb,GAAE;;;;;;;;;;;gCAEA;;;;;;;sCAIR,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,SAAS,mBAAmB,SAAS;4BACrC,WAAU;;8CAEV,8OAAC;oCACC,WAAU;oCACV,MAAK;oCACL,QAAO;oCACP,SAAQ;8CAER,cAAA,8OAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,aAAa;wCACb,GAAE;;;;;;;;;;;gCAEA;;;;;;;;;;;;;;;;;;;;;;;;AAOlB", "debugId": null}}, {"offset": {"line": 2348, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/components/ui/Input.tsx"], "sourcesContent": ["import { forwardRef, type InputHTMLAttributes } from 'react'\nimport { cn } from '~/lib/utils'\n\nexport interface InputProps extends InputHTMLAttributes<HTMLInputElement> {\n  error?: string\n}\n\nconst Input = forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, error, ...props }, ref) => {\n    return (\n      <div className=\"w-full\">\n        <input\n          type={type}\n          className={cn(\n            'flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50',\n            error && 'border-destructive focus-visible:ring-destructive',\n            className\n          )}\n          ref={ref}\n          {...props}\n        />\n        {error && (\n          <p className=\"mt-1 text-sm text-destructive\">{error}</p>\n        )}\n      </div>\n    )\n  }\n)\nInput.displayName = 'Input'\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAMA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrB,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE;IACrC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,MAAM;gBACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yUACA,SAAS,qDACT;gBAEF,KAAK;gBACJ,GAAG,KAAK;;;;;;YAEV,uBACC,8OAAC;gBAAE,WAAU;0BAAiC;;;;;;;;;;;;AAItD;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2392, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/components/ui/Textarea.tsx"], "sourcesContent": ["import { forwardRef, type TextareaHTMLAttributes } from 'react'\nimport { cn } from '~/lib/utils'\n\nexport interface TextareaProps extends TextareaHTMLAttributes<HTMLTextAreaElement> {\n  error?: string\n}\n\nconst Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, error, ...props }, ref) => {\n    return (\n      <div className=\"w-full\">\n        <textarea\n          className={cn(\n            'flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50',\n            error && 'border-destructive focus-visible:ring-destructive',\n            className\n          )}\n          ref={ref}\n          {...props}\n        />\n        {error && (\n          <p className=\"mt-1 text-sm text-destructive\">{error}</p>\n        )}\n      </div>\n    )\n  }\n)\nTextarea.displayName = 'Textarea'\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAMA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACxB,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE;IAC/B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gQACA,SAAS,qDACT;gBAEF,KAAK;gBACJ,GAAG,KAAK;;;;;;YAEV,uBACC,8OAAC;gBAAE,WAAU;0BAAiC;;;;;;;;;;;;AAItD;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2435, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/components/PromptForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { toast } from 'react-hot-toast'\nimport CodeEditor from '@uiw/react-textarea-code-editor'\nimport { Button } from '~/components/ui/Button'\nimport { Input } from '~/components/ui/Input'\nimport { Textarea } from '~/components/ui/Textarea'\nimport { Badge } from '~/components/ui/Badge'\nimport { generateRandomColor } from '~/lib/utils'\nimport { api } from '~/trpc/react'\nimport type { Prompt, Category } from '~/store'\n\ninterface PromptFormProps {\n  prompt?: Prompt\n  onSubmit?: (data: PromptFormData) => void\n  onCancel?: () => void\n  isLoading?: boolean\n}\n\nexport interface PromptFormData {\n  title: string\n  content: string\n  description?: string\n  tags: string[]\n  categoryId?: string\n  isPublic: boolean\n}\n\nexport function PromptForm({ prompt, onSubmit, onCancel, isLoading = false }: PromptFormProps) {\n  const [formData, setFormData] = useState<PromptFormData>({\n    title: prompt?.title || '',\n    content: prompt?.content || '',\n    description: prompt?.description || '',\n    tags: prompt?.tags || [],\n    categoryId: prompt?.categoryId || '',\n    isPublic: prompt?.isPublic ?? true,\n  })\n  \n  const [tagInput, setTagInput] = useState('')\n  const [errors, setErrors] = useState<Record<string, string>>({})\n  \n  // 获取分类数据\n  const { data: categories } = api.category.getAll.useQuery()\n  \n  // 表单验证\n  const validateForm = (): boolean => {\n    const newErrors: Record<string, string> = {}\n    \n    if (!formData.title.trim()) {\n      newErrors.title = '标题不能为空'\n    } else if (formData.title.length > 100) {\n      newErrors.title = '标题不能超过100个字符'\n    }\n    \n    if (!formData.content.trim()) {\n      newErrors.content = '内容不能为空'\n    }\n    \n    if (formData.description && formData.description.length > 200) {\n      newErrors.description = '描述不能超过200个字符'\n    }\n    \n    if (formData.tags.length > 10) {\n      newErrors.tags = '标签不能超过10个'\n    }\n    \n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n  \n  // 处理表单提交\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!validateForm()) {\n      toast.error('请检查表单输入')\n      return\n    }\n    \n    onSubmit?.(formData)\n  }\n  \n  // 处理标签输入\n  const handleTagInputKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' || e.key === ',') {\n      e.preventDefault()\n      addTag()\n    }\n  }\n  \n  const addTag = () => {\n    const tag = tagInput.trim()\n    if (tag && !formData.tags.includes(tag) && formData.tags.length < 10) {\n      setFormData(prev => ({\n        ...prev,\n        tags: [...prev.tags, tag]\n      }))\n      setTagInput('')\n    }\n  }\n  \n  const removeTag = (tagToRemove: string) => {\n    setFormData(prev => ({\n      ...prev,\n      tags: prev.tags.filter(tag => tag !== tagToRemove)\n    }))\n  }\n  \n  // 处理输入变化\n  const handleInputChange = (field: keyof PromptFormData, value: any) => {\n    setFormData(prev => ({ ...prev, [field]: value }))\n    // 清除对应字段的错误\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: '' }))\n    }\n  }\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\n      {/* 标题 */}\n      <div>\n        <label className=\"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\">\n          标题 <span className=\"text-red-500\">*</span>\n        </label>\n        <Input\n          value={formData.title}\n          onChange={(e) => handleInputChange('title', e.target.value)}\n          placeholder=\"输入提示词标题...\"\n          error={errors.title}\n          maxLength={100}\n        />\n        <div className=\"text-xs text-slate-500 mt-1\">\n          {formData.title.length}/100\n        </div>\n      </div>\n      \n      {/* 描述 */}\n      <div>\n        <label className=\"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\">\n          描述\n        </label>\n        <Textarea\n          value={formData.description}\n          onChange={(e) => handleInputChange('description', e.target.value)}\n          placeholder=\"简短描述这个提示词的用途...\"\n          rows={3}\n          error={errors.description}\n          maxLength={200}\n        />\n        <div className=\"text-xs text-slate-500 mt-1\">\n          {(formData.description || '').length}/200\n        </div>\n      </div>\n      \n      {/* 分类 */}\n      <div>\n        <label className=\"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\">\n          分类\n        </label>\n        <select\n          value={formData.categoryId}\n          onChange={(e) => handleInputChange('categoryId', e.target.value)}\n          className=\"w-full h-9 px-3 py-1 text-sm border border-input bg-transparent rounded-md shadow-sm focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring\"\n        >\n          <option value=\"\">选择分类</option>\n          {categories?.map((category) => (\n            <option key={category.id} value={category.id}>\n              {category.name}\n            </option>\n          ))}\n        </select>\n      </div>\n      \n      {/* 标签 */}\n      <div>\n        <label className=\"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\">\n          标签\n        </label>\n        <div className=\"space-y-2\">\n          <div className=\"flex gap-2\">\n            <Input\n              value={tagInput}\n              onChange={(e) => setTagInput(e.target.value)}\n              onKeyDown={handleTagInputKeyDown}\n              placeholder=\"输入标签后按回车添加...\"\n              className=\"flex-1\"\n            />\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={addTag}\n              disabled={!tagInput.trim() || formData.tags.length >= 10}\n            >\n              添加\n            </Button>\n          </div>\n          \n          {formData.tags.length > 0 && (\n            <div className=\"flex flex-wrap gap-1\">\n              {formData.tags.map((tag, index) => (\n                <Badge\n                  key={index}\n                  variant=\"secondary\"\n                  className=\"cursor-pointer hover:bg-destructive hover:text-destructive-foreground\"\n                  onClick={() => removeTag(tag)}\n                >\n                  {tag}\n                  <svg className=\"w-3 h-3 ml-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </Badge>\n              ))}\n            </div>\n          )}\n          \n          <div className=\"text-xs text-slate-500\">\n            {formData.tags.length}/10 个标签\n          </div>\n          {errors.tags && (\n            <p className=\"text-sm text-destructive\">{errors.tags}</p>\n          )}\n        </div>\n      </div>\n      \n      {/* 内容编辑器 */}\n      <div>\n        <label className=\"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\">\n          提示词内容 <span className=\"text-red-500\">*</span>\n        </label>\n        <div className=\"border border-input rounded-md overflow-hidden\">\n          <CodeEditor\n            value={formData.content}\n            language=\"text\"\n            placeholder=\"输入您的提示词内容...\"\n            onChange={(evn) => handleInputChange('content', evn.target.value)}\n            padding={15}\n            style={{\n              fontSize: 14,\n              backgroundColor: 'transparent',\n              fontFamily: 'ui-monospace, SFMono-Regular, \"SF Mono\", Consolas, \"Liberation Mono\", Menlo, monospace',\n              minHeight: '200px',\n            }}\n          />\n        </div>\n        {errors.content && (\n          <p className=\"text-sm text-destructive mt-1\">{errors.content}</p>\n        )}\n      </div>\n      \n      {/* 可见性设置 */}\n      <div>\n        <label className=\"flex items-center gap-2\">\n          <input\n            type=\"checkbox\"\n            checked={formData.isPublic}\n            onChange={(e) => handleInputChange('isPublic', e.target.checked)}\n            className=\"rounded border-input\"\n          />\n          <span className=\"text-sm text-slate-700 dark:text-slate-300\">\n            公开此提示词（其他用户可以查看和使用）\n          </span>\n        </label>\n      </div>\n      \n      {/* 操作按钮 */}\n      <div className=\"flex items-center gap-3 pt-4\">\n        <Button\n          type=\"submit\"\n          loading={isLoading}\n          className=\"flex-1 sm:flex-none\"\n        >\n          {prompt ? '更新提示词' : '创建提示词'}\n        </Button>\n        \n        <Button\n          type=\"button\"\n          variant=\"outline\"\n          onClick={onCancel}\n          disabled={isLoading}\n          className=\"flex-1 sm:flex-none\"\n        >\n          取消\n        </Button>\n      </div>\n    </form>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAEA;AAVA;;;;;;;;;;AA6BO,SAAS,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,KAAK,EAAmB;IAC3F,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;QACvD,OAAO,QAAQ,SAAS;QACxB,SAAS,QAAQ,WAAW;QAC5B,aAAa,QAAQ,eAAe;QACpC,MAAM,QAAQ,QAAQ,EAAE;QACxB,YAAY,QAAQ,cAAc;QAClC,UAAU,QAAQ,YAAY;IAChC;IAEA,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,SAAS;IACT,MAAM,EAAE,MAAM,UAAU,EAAE,GAAG,qHAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ;IAEzD,OAAO;IACP,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,UAAU,KAAK,GAAG;QACpB,OAAO,IAAI,SAAS,KAAK,CAAC,MAAM,GAAG,KAAK;YACtC,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,UAAU,OAAO,GAAG;QACtB;QAEA,IAAI,SAAS,WAAW,IAAI,SAAS,WAAW,CAAC,MAAM,GAAG,KAAK;YAC7D,UAAU,WAAW,GAAG;QAC1B;QAEA,IAAI,SAAS,IAAI,CAAC,MAAM,GAAG,IAAI;YAC7B,UAAU,IAAI,GAAG;QACnB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,SAAS;IACT,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,WAAW;IACb;IAEA,SAAS;IACT,MAAM,wBAAwB,CAAC;QAC7B,IAAI,EAAE,GAAG,KAAK,WAAW,EAAE,GAAG,KAAK,KAAK;YACtC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,MAAM,SAAS;QACb,MAAM,MAAM,SAAS,IAAI;QACzB,IAAI,OAAO,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,QAAQ,SAAS,IAAI,CAAC,MAAM,GAAG,IAAI;YACpE,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,MAAM;2BAAI,KAAK,IAAI;wBAAE;qBAAI;gBAC3B,CAAC;YACD,YAAY;QACd;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,CAAA,MAAO,QAAQ;YACxC,CAAC;IACH;IAEA,SAAS;IACT,MAAM,oBAAoB,CAAC,OAA6B;QACtD,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAChD,YAAY;QACZ,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAG,CAAC;QAC7C;IACF;IAEA,qBACE,8OAAC;QAAK,UAAU;QAAc,WAAU;;0BAEtC,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;;4BAAoE;0CAChF,8OAAC;gCAAK,WAAU;0CAAe;;;;;;;;;;;;kCAEpC,8OAAC,iIAAA,CAAA,QAAK;wBACJ,OAAO,SAAS,KAAK;wBACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wBAC1D,aAAY;wBACZ,OAAO,OAAO,KAAK;wBACnB,WAAW;;;;;;kCAEb,8OAAC;wBAAI,WAAU;;4BACZ,SAAS,KAAK,CAAC,MAAM;4BAAC;;;;;;;;;;;;;0BAK3B,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAAoE;;;;;;kCAGrF,8OAAC,oIAAA,CAAA,WAAQ;wBACP,OAAO,SAAS,WAAW;wBAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;wBAChE,aAAY;wBACZ,MAAM;wBACN,OAAO,OAAO,WAAW;wBACzB,WAAW;;;;;;kCAEb,8OAAC;wBAAI,WAAU;;4BACZ,CAAC,SAAS,WAAW,IAAI,EAAE,EAAE,MAAM;4BAAC;;;;;;;;;;;;;0BAKzC,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAAoE;;;;;;kCAGrF,8OAAC;wBACC,OAAO,SAAS,UAAU;wBAC1B,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;wBAC/D,WAAU;;0CAEV,8OAAC;gCAAO,OAAM;0CAAG;;;;;;4BAChB,YAAY,IAAI,CAAC,yBAChB,8OAAC;oCAAyB,OAAO,SAAS,EAAE;8CACzC,SAAS,IAAI;mCADH,SAAS,EAAE;;;;;;;;;;;;;;;;;0BAQ9B,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAAoE;;;;;;kCAGrF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCACJ,OAAO;wCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wCAC3C,WAAW;wCACX,aAAY;wCACZ,WAAU;;;;;;kDAEZ,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,SAAS;wCACT,UAAU,CAAC,SAAS,IAAI,MAAM,SAAS,IAAI,CAAC,MAAM,IAAI;kDACvD;;;;;;;;;;;;4BAKF,SAAS,IAAI,CAAC,MAAM,GAAG,mBACtB,8OAAC;gCAAI,WAAU;0CACZ,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACvB,8OAAC,iIAAA,CAAA,QAAK;wCAEJ,SAAQ;wCACR,WAAU;wCACV,SAAS,IAAM,UAAU;;4CAExB;0DACD,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;uCAPlE;;;;;;;;;;0CAcb,8OAAC;gCAAI,WAAU;;oCACZ,SAAS,IAAI,CAAC,MAAM;oCAAC;;;;;;;4BAEvB,OAAO,IAAI,kBACV,8OAAC;gCAAE,WAAU;0CAA4B,OAAO,IAAI;;;;;;;;;;;;;;;;;;0BAM1D,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;;4BAAoE;0CAC7E,8OAAC;gCAAK,WAAU;0CAAe;;;;;;;;;;;;kCAEvC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4LAAA,CAAA,UAAU;4BACT,OAAO,SAAS,OAAO;4BACvB,UAAS;4BACT,aAAY;4BACZ,UAAU,CAAC,MAAQ,kBAAkB,WAAW,IAAI,MAAM,CAAC,KAAK;4BAChE,SAAS;4BACT,OAAO;gCACL,UAAU;gCACV,iBAAiB;gCACjB,YAAY;gCACZ,WAAW;4BACb;;;;;;;;;;;oBAGH,OAAO,OAAO,kBACb,8OAAC;wBAAE,WAAU;kCAAiC,OAAO,OAAO;;;;;;;;;;;;0BAKhE,8OAAC;0BACC,cAAA,8OAAC;oBAAM,WAAU;;sCACf,8OAAC;4BACC,MAAK;4BACL,SAAS,SAAS,QAAQ;4BAC1B,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,OAAO;4BAC/D,WAAU;;;;;;sCAEZ,8OAAC;4BAAK,WAAU;sCAA6C;;;;;;;;;;;;;;;;;0BAOjE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAS;wBACT,WAAU;kCAET,SAAS,UAAU;;;;;;kCAGtB,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,SAAS;wBACT,UAAU;wBACV,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 2921, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/components/CreatePromptModal.tsx"], "sourcesContent": ["'use client'\n\nimport { toast } from 'react-hot-toast'\nimport { \n  <PERSON><PERSON>, \n  <PERSON><PERSON><PERSON><PERSON>er, \n  ModalTitle, \n  ModalContent,\n  ModalCloseButton \n} from '~/components/ui/Modal'\nimport { PromptForm, type PromptFormData } from '~/components/PromptForm'\nimport { useModals } from '~/hooks/useStore'\nimport { api } from '~/trpc/react'\n\nexport function CreatePromptModal() {\n  const { modals, closeCreatePrompt } = useModals()\n  \n  // 创建提示词的mutation\n  const createPromptMutation = api.prompt.create.useMutation({\n    onSuccess: () => {\n      toast.success('提示词创建成功！')\n      closeCreatePrompt()\n      // 刷新提示词列表\n      void utils.prompt.getAll.invalidate()\n      void utils.prompt.getLatest.invalidate()\n      void utils.prompt.getPopular.invalidate()\n      void utils.stats.getOverview.invalidate()\n    },\n    onError: (error) => {\n      toast.error(error.message || '创建失败，请重试')\n    },\n  })\n  \n  // 获取utils用于刷新数据\n  const utils = api.useUtils()\n  \n  const handleSubmit = async (data: PromptFormData) => {\n    try {\n      await createPromptMutation.mutateAsync({\n        title: data.title,\n        content: data.content,\n        description: data.description || undefined,\n        tags: data.tags,\n        categoryId: data.categoryId || undefined,\n        isPublic: data.isPublic,\n      })\n    } catch (error) {\n      // 错误已在onError中处理\n    }\n  }\n\n  return (\n    <Modal\n      open={modals.createPrompt}\n      onClose={closeCreatePrompt}\n      size=\"xl\"\n    >\n      <ModalHeader>\n        <ModalTitle>创建新提示词</ModalTitle>\n        <ModalCloseButton onClose={closeCreatePrompt} />\n      </ModalHeader>\n\n      <ModalContent>\n        <PromptForm\n          onSubmit={handleSubmit}\n          onCancel={closeCreatePrompt}\n          isLoading={createPromptMutation.isPending}\n        />\n      </ModalContent>\n    </Modal>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AACA;AACA;AAZA;;;;;;;AAcO,SAAS;IACd,MAAM,EAAE,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,YAAS,AAAD;IAE9C,iBAAiB;IACjB,MAAM,uBAAuB,qHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC;QACzD,WAAW;YACT,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd;YACA,UAAU;YACV,KAAK,MAAM,MAAM,CAAC,MAAM,CAAC,UAAU;YACnC,KAAK,MAAM,MAAM,CAAC,SAAS,CAAC,UAAU;YACtC,KAAK,MAAM,MAAM,CAAC,UAAU,CAAC,UAAU;YACvC,KAAK,MAAM,KAAK,CAAC,WAAW,CAAC,UAAU;QACzC;QACA,SAAS,CAAC;YACR,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;QAC/B;IACF;IAEA,gBAAgB;IAChB,MAAM,QAAQ,qHAAA,CAAA,MAAG,CAAC,QAAQ;IAE1B,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,qBAAqB,WAAW,CAAC;gBACrC,OAAO,KAAK,KAAK;gBACjB,SAAS,KAAK,OAAO;gBACrB,aAAa,KAAK,WAAW,IAAI;gBACjC,MAAM,KAAK,IAAI;gBACf,YAAY,KAAK,UAAU,IAAI;gBAC/B,UAAU,KAAK,QAAQ;YACzB;QACF,EAAE,OAAO,OAAO;QACd,iBAAiB;QACnB;IACF;IAEA,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,MAAM,OAAO,YAAY;QACzB,SAAS;QACT,MAAK;;0BAEL,8OAAC,iIAAA,CAAA,cAAW;;kCACV,8OAAC,iIAAA,CAAA,aAAU;kCAAC;;;;;;kCACZ,8OAAC,iIAAA,CAAA,mBAAgB;wBAAC,SAAS;;;;;;;;;;;;0BAG7B,8OAAC,iIAAA,CAAA,eAAY;0BACX,cAAA,8OAAC,gIAAA,CAAA,aAAU;oBACT,UAAU;oBACV,UAAU;oBACV,WAAW,qBAAqB,SAAS;;;;;;;;;;;;;;;;;AAKnD", "debugId": null}}, {"offset": {"line": 3023, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/components/EditPromptModal.tsx"], "sourcesContent": ["'use client'\n\nimport { toast } from 'react-hot-toast'\nimport { \n  <PERSON><PERSON>, \n  <PERSON><PERSON><PERSON><PERSON><PERSON>, \n  <PERSON>dalTitle, \n  ModalContent,\n  ModalCloseButton \n} from '~/components/ui/Modal'\nimport { PromptForm, type PromptFormData } from '~/components/PromptForm'\nimport { useModals } from '~/hooks/useStore'\nimport { api } from '~/trpc/react'\n\nexport function EditPromptModal() {\n  const { modals, currentEditingPrompt, closeEditPrompt } = useModals()\n  \n  // 更新提示词的mutation\n  const updatePromptMutation = api.prompt.update.useMutation({\n    onSuccess: () => {\n      toast.success('提示词更新成功！')\n      closeEditPrompt()\n      // 刷新提示词列表\n      void utils.prompt.getAll.invalidate()\n      void utils.prompt.getLatest.invalidate()\n      void utils.prompt.getPopular.invalidate()\n      void utils.prompt.getById.invalidate()\n    },\n    onError: (error) => {\n      toast.error(error.message || '更新失败，请重试')\n    },\n  })\n  \n  // 获取utils用于刷新数据\n  const utils = api.useUtils()\n  \n  const handleSubmit = async (data: PromptFormData) => {\n    if (!currentEditingPrompt) return\n    \n    try {\n      await updatePromptMutation.mutateAsync({\n        id: currentEditingPrompt.id,\n        title: data.title,\n        content: data.content,\n        description: data.description || undefined,\n        tags: data.tags,\n        categoryId: data.categoryId || undefined,\n        isPublic: data.isPublic,\n      })\n    } catch (error) {\n      // 错误已在onError中处理\n    }\n  }\n\n  if (!currentEditingPrompt) return null\n\n  return (\n    <Modal\n      open={modals.editPrompt}\n      onClose={closeEditPrompt}\n      size=\"xl\"\n    >\n      <ModalHeader>\n        <ModalTitle>编辑提示词</ModalTitle>\n        <ModalCloseButton onClose={closeEditPrompt} />\n      </ModalHeader>\n\n      <ModalContent>\n        <PromptForm\n          prompt={currentEditingPrompt}\n          onSubmit={handleSubmit}\n          onCancel={closeEditPrompt}\n          isLoading={updatePromptMutation.isPending}\n        />\n      </ModalContent>\n    </Modal>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AACA;AACA;AAZA;;;;;;;AAcO,SAAS;IACd,MAAM,EAAE,MAAM,EAAE,oBAAoB,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,YAAS,AAAD;IAElE,iBAAiB;IACjB,MAAM,uBAAuB,qHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC;QACzD,WAAW;YACT,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd;YACA,UAAU;YACV,KAAK,MAAM,MAAM,CAAC,MAAM,CAAC,UAAU;YACnC,KAAK,MAAM,MAAM,CAAC,SAAS,CAAC,UAAU;YACtC,KAAK,MAAM,MAAM,CAAC,UAAU,CAAC,UAAU;YACvC,KAAK,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU;QACtC;QACA,SAAS,CAAC;YACR,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;QAC/B;IACF;IAEA,gBAAgB;IAChB,MAAM,QAAQ,qHAAA,CAAA,MAAG,CAAC,QAAQ;IAE1B,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,sBAAsB;QAE3B,IAAI;YACF,MAAM,qBAAqB,WAAW,CAAC;gBACrC,IAAI,qBAAqB,EAAE;gBAC3B,OAAO,KAAK,KAAK;gBACjB,SAAS,KAAK,OAAO;gBACrB,aAAa,KAAK,WAAW,IAAI;gBACjC,MAAM,KAAK,IAAI;gBACf,YAAY,KAAK,UAAU,IAAI;gBAC/B,UAAU,KAAK,QAAQ;YACzB;QACF,EAAE,OAAO,OAAO;QACd,iBAAiB;QACnB;IACF;IAEA,IAAI,CAAC,sBAAsB,OAAO;IAElC,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,MAAM,OAAO,UAAU;QACvB,SAAS;QACT,MAAK;;0BAEL,8OAAC,iIAAA,CAAA,cAAW;;kCACV,8OAAC,iIAAA,CAAA,aAAU;kCAAC;;;;;;kCACZ,8OAAC,iIAAA,CAAA,mBAAgB;wBAAC,SAAS;;;;;;;;;;;;0BAG7B,8OAAC,iIAAA,CAAA,eAAY;0BACX,cAAA,8OAAC,gIAAA,CAAA,aAAU;oBACT,QAAQ;oBACR,UAAU;oBACV,UAAU;oBACV,WAAW,qBAAqB,SAAS;;;;;;;;;;;;;;;;;AAKnD", "debugId": null}}, {"offset": {"line": 3129, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/components/CategoryForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { toast } from 'react-hot-toast'\nimport { But<PERSON> } from '~/components/ui/Button'\nimport { Input } from '~/components/ui/Input'\nimport { Textarea } from '~/components/ui/Textarea'\nimport { generateRandomColor } from '~/lib/utils'\nimport type { Category } from '~/store'\n\ninterface CategoryFormProps {\n  category?: Category\n  onSubmit?: (data: CategoryFormData) => void\n  onCancel?: () => void\n  isLoading?: boolean\n}\n\nexport interface CategoryFormData {\n  name: string\n  description?: string\n  color: string\n  icon?: string\n}\n\nconst PRESET_COLORS = [\n  '#EF4444', // red-500\n  '#F97316', // orange-500\n  '#F59E0B', // amber-500\n  '#EAB308', // yellow-500\n  '#84CC16', // lime-500\n  '#22C55E', // green-500\n  '#10B981', // emerald-500\n  '#14B8A6', // teal-500\n  '#06B6D4', // cyan-500\n  '#0EA5E9', // sky-500\n  '#3B82F6', // blue-500\n  '#6366F1', // indigo-500\n  '#8B5CF6', // violet-500\n  '#A855F7', // purple-500\n  '#D946EF', // fuchsia-500\n  '#EC4899', // pink-500\n]\n\nconst PRESET_ICONS = [\n  'code', 'pen', 'book', 'star', 'heart', 'lightbulb', 'tag', 'folder',\n  'document', 'chat', 'puzzle', 'rocket', 'shield', 'globe', 'music', 'camera'\n]\n\nexport function CategoryForm({ category, onSubmit, onCancel, isLoading = false }: CategoryFormProps) {\n  const [formData, setFormData] = useState<CategoryFormData>({\n    name: category?.name || '',\n    description: category?.description || '',\n    color: category?.color || '#3B82F6',\n    icon: category?.icon || 'folder',\n  })\n  \n  const [errors, setErrors] = useState<Record<string, string>>({})\n  \n  // 表单验证\n  const validateForm = (): boolean => {\n    const newErrors: Record<string, string> = {}\n    \n    if (!formData.name.trim()) {\n      newErrors.name = '分类名称不能为空'\n    } else if (formData.name.length > 50) {\n      newErrors.name = '分类名称不能超过50个字符'\n    }\n    \n    if (formData.description && formData.description.length > 200) {\n      newErrors.description = '描述不能超过200个字符'\n    }\n    \n    if (!formData.color.match(/^#[0-9A-F]{6}$/i)) {\n      newErrors.color = '颜色格式不正确'\n    }\n    \n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n  \n  // 处理表单提交\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!validateForm()) {\n      toast.error('请检查表单输入')\n      return\n    }\n    \n    onSubmit?.(formData)\n  }\n  \n  // 处理输入变化\n  const handleInputChange = (field: keyof CategoryFormData, value: any) => {\n    setFormData(prev => ({ ...prev, [field]: value }))\n    // 清除对应字段的错误\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: '' }))\n    }\n  }\n  \n  // 随机生成颜色\n  const handleRandomColor = () => {\n    const randomColor = generateRandomColor()\n    handleInputChange('color', randomColor)\n  }\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\n      {/* 分类名称 */}\n      <div>\n        <label className=\"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\">\n          分类名称 <span className=\"text-red-500\">*</span>\n        </label>\n        <Input\n          value={formData.name}\n          onChange={(e) => handleInputChange('name', e.target.value)}\n          placeholder=\"输入分类名称...\"\n          error={errors.name}\n          maxLength={50}\n        />\n        <div className=\"text-xs text-slate-500 mt-1\">\n          {formData.name.length}/50\n        </div>\n      </div>\n      \n      {/* 描述 */}\n      <div>\n        <label className=\"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\">\n          描述\n        </label>\n        <Textarea\n          value={formData.description}\n          onChange={(e) => handleInputChange('description', e.target.value)}\n          placeholder=\"简短描述这个分类...\"\n          rows={3}\n          error={errors.description}\n          maxLength={200}\n        />\n        <div className=\"text-xs text-slate-500 mt-1\">\n          {(formData.description || '').length}/200\n        </div>\n      </div>\n      \n      {/* 颜色选择 */}\n      <div>\n        <label className=\"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\">\n          分类颜色\n        </label>\n        <div className=\"space-y-3\">\n          {/* 预设颜色 */}\n          <div className=\"grid grid-cols-8 gap-2\">\n            {PRESET_COLORS.map((color) => (\n              <button\n                key={color}\n                type=\"button\"\n                className={`w-8 h-8 rounded-full border-2 transition-all ${\n                  formData.color === color \n                    ? 'border-slate-900 dark:border-slate-100 scale-110' \n                    : 'border-slate-300 dark:border-slate-600 hover:scale-105'\n                }`}\n                style={{ backgroundColor: color }}\n                onClick={() => handleInputChange('color', color)}\n              />\n            ))}\n          </div>\n          \n          {/* 自定义颜色 */}\n          <div className=\"flex items-center gap-2\">\n            <input\n              type=\"color\"\n              value={formData.color}\n              onChange={(e) => handleInputChange('color', e.target.value)}\n              className=\"w-8 h-8 rounded border border-input\"\n            />\n            <Input\n              value={formData.color}\n              onChange={(e) => handleInputChange('color', e.target.value)}\n              placeholder=\"#3B82F6\"\n              className=\"flex-1\"\n              error={errors.color}\n            />\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={handleRandomColor}\n            >\n              随机\n            </Button>\n          </div>\n        </div>\n      </div>\n      \n      {/* 图标选择 */}\n      <div>\n        <label className=\"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\">\n          图标\n        </label>\n        <div className=\"grid grid-cols-8 gap-2\">\n          {PRESET_ICONS.map((icon) => (\n            <button\n              key={icon}\n              type=\"button\"\n              className={`p-2 rounded border text-sm transition-all ${\n                formData.icon === icon\n                  ? 'border-primary bg-primary text-primary-foreground'\n                  : 'border-input hover:bg-accent'\n              }`}\n              onClick={() => handleInputChange('icon', icon)}\n            >\n              {icon}\n            </button>\n          ))}\n        </div>\n      </div>\n      \n      {/* 预览 */}\n      <div>\n        <label className=\"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\">\n          预览\n        </label>\n        <div className=\"flex items-center gap-2 p-3 border border-input rounded-md\">\n          <div \n            className=\"w-4 h-4 rounded-full\"\n            style={{ backgroundColor: formData.color }}\n          />\n          <span className=\"font-medium\">{formData.name || '分类名称'}</span>\n          <span className=\"text-xs text-muted-foreground\">({formData.icon})</span>\n        </div>\n      </div>\n      \n      {/* 操作按钮 */}\n      <div className=\"flex items-center gap-3 pt-4\">\n        <Button\n          type=\"submit\"\n          loading={isLoading}\n          className=\"flex-1 sm:flex-none\"\n        >\n          {category ? '更新分类' : '创建分类'}\n        </Button>\n        \n        <Button\n          type=\"button\"\n          variant=\"outline\"\n          onClick={onCancel}\n          disabled={isLoading}\n          className=\"flex-1 sm:flex-none\"\n        >\n          取消\n        </Button>\n      </div>\n    </form>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAwBA,MAAM,gBAAgB;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,eAAe;IACnB;IAAQ;IAAO;IAAQ;IAAQ;IAAS;IAAa;IAAO;IAC5D;IAAY;IAAQ;IAAU;IAAU;IAAU;IAAS;IAAS;CACrE;AAEM,SAAS,aAAa,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,KAAK,EAAqB;IACjG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;QACzD,MAAM,UAAU,QAAQ;QACxB,aAAa,UAAU,eAAe;QACtC,OAAO,UAAU,SAAS;QAC1B,MAAM,UAAU,QAAQ;IAC1B;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,OAAO;IACP,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,UAAU,IAAI,GAAG;QACnB,OAAO,IAAI,SAAS,IAAI,CAAC,MAAM,GAAG,IAAI;YACpC,UAAU,IAAI,GAAG;QACnB;QAEA,IAAI,SAAS,WAAW,IAAI,SAAS,WAAW,CAAC,MAAM,GAAG,KAAK;YAC7D,UAAU,WAAW,GAAG;QAC1B;QAEA,IAAI,CAAC,SAAS,KAAK,CAAC,KAAK,CAAC,oBAAoB;YAC5C,UAAU,KAAK,GAAG;QACpB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,SAAS;IACT,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,WAAW;IACb;IAEA,SAAS;IACT,MAAM,oBAAoB,CAAC,OAA+B;QACxD,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAChD,YAAY;QACZ,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAG,CAAC;QAC7C;IACF;IAEA,SAAS;IACT,MAAM,oBAAoB;QACxB,MAAM,cAAc,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD;QACtC,kBAAkB,SAAS;IAC7B;IAEA,qBACE,8OAAC;QAAK,UAAU;QAAc,WAAU;;0BAEtC,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;;4BAAoE;0CAC9E,8OAAC;gCAAK,WAAU;0CAAe;;;;;;;;;;;;kCAEtC,8OAAC,iIAAA,CAAA,QAAK;wBACJ,OAAO,SAAS,IAAI;wBACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;wBACzD,aAAY;wBACZ,OAAO,OAAO,IAAI;wBAClB,WAAW;;;;;;kCAEb,8OAAC;wBAAI,WAAU;;4BACZ,SAAS,IAAI,CAAC,MAAM;4BAAC;;;;;;;;;;;;;0BAK1B,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAAoE;;;;;;kCAGrF,8OAAC,oIAAA,CAAA,WAAQ;wBACP,OAAO,SAAS,WAAW;wBAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;wBAChE,aAAY;wBACZ,MAAM;wBACN,OAAO,OAAO,WAAW;wBACzB,WAAW;;;;;;kCAEb,8OAAC;wBAAI,WAAU;;4BACZ,CAAC,SAAS,WAAW,IAAI,EAAE,EAAE,MAAM;4BAAC;;;;;;;;;;;;;0BAKzC,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAAoE;;;;;;kCAGrF,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,sBAClB,8OAAC;wCAEC,MAAK;wCACL,WAAW,CAAC,6CAA6C,EACvD,SAAS,KAAK,KAAK,QACf,qDACA,0DACJ;wCACF,OAAO;4CAAE,iBAAiB;wCAAM;wCAChC,SAAS,IAAM,kBAAkB,SAAS;uCARrC;;;;;;;;;;0CAcX,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,OAAO,SAAS,KAAK;wCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wCAC1D,WAAU;;;;;;kDAEZ,8OAAC,iIAAA,CAAA,QAAK;wCACJ,OAAO,SAAS,KAAK;wCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wCAC1D,aAAY;wCACZ,WAAU;wCACV,OAAO,OAAO,KAAK;;;;;;kDAErB,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;kDACV;;;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAAoE;;;;;;kCAGrF,8OAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC;gCAEC,MAAK;gCACL,WAAW,CAAC,0CAA0C,EACpD,SAAS,IAAI,KAAK,OACd,sDACA,gCACJ;gCACF,SAAS,IAAM,kBAAkB,QAAQ;0CAExC;+BATI;;;;;;;;;;;;;;;;0BAgBb,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAAoE;;;;;;kCAGrF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,iBAAiB,SAAS,KAAK;gCAAC;;;;;;0CAE3C,8OAAC;gCAAK,WAAU;0CAAe,SAAS,IAAI,IAAI;;;;;;0CAChD,8OAAC;gCAAK,WAAU;;oCAAgC;oCAAE,SAAS,IAAI;oCAAC;;;;;;;;;;;;;;;;;;;0BAKpE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAS;wBACT,WAAU;kCAET,WAAW,SAAS;;;;;;kCAGvB,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,SAAS;wBACT,UAAU;wBACV,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 3542, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/components/CreateCategoryModal.tsx"], "sourcesContent": ["'use client'\n\nimport { toast } from 'react-hot-toast'\nimport { \n  <PERSON><PERSON>, \n  <PERSON><PERSON><PERSON><PERSON>er, \n  ModalTitle, \n  ModalContent,\n  ModalCloseButton \n} from '~/components/ui/Modal'\nimport { CategoryForm, type CategoryFormData } from '~/components/CategoryForm'\nimport { useModals } from '~/hooks/useStore'\nimport { api } from '~/trpc/react'\n\nexport function CreateCategoryModal() {\n  const { modals, closeCreateCategory } = useModals()\n  \n  // 创建分类的mutation\n  const createCategoryMutation = api.category.create.useMutation({\n    onSuccess: () => {\n      toast.success('分类创建成功！')\n      closeCreateCategory()\n      // 刷新分类列表\n      void utils.category.getAll.invalidate()\n      void utils.stats.getCategoryStats.invalidate()\n    },\n    onError: (error) => {\n      toast.error(error.message || '创建失败，请重试')\n    },\n  })\n  \n  // 获取utils用于刷新数据\n  const utils = api.useUtils()\n  \n  const handleSubmit = async (data: CategoryFormData) => {\n    try {\n      await createCategoryMutation.mutateAsync({\n        name: data.name,\n        description: data.description || undefined,\n        color: data.color,\n        icon: data.icon || undefined,\n      })\n    } catch (error) {\n      // 错误已在onError中处理\n    }\n  }\n\n  return (\n    <Modal\n      open={modals.createCategory}\n      onClose={closeCreateCategory}\n      size=\"lg\"\n    >\n      <ModalHeader>\n        <ModalTitle>创建新分类</ModalTitle>\n        <ModalCloseButton onClose={closeCreateCategory} />\n      </ModalHeader>\n\n      <ModalContent>\n        <CategoryForm\n          onSubmit={handleSubmit}\n          onCancel={closeCreateCategory}\n          isLoading={createCategoryMutation.isPending}\n        />\n      </ModalContent>\n    </Modal>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AACA;AACA;AAZA;;;;;;;AAcO,SAAS;IACd,MAAM,EAAE,MAAM,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,YAAS,AAAD;IAEhD,gBAAgB;IAChB,MAAM,yBAAyB,qHAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC;QAC7D,WAAW;YACT,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd;YACA,SAAS;YACT,KAAK,MAAM,QAAQ,CAAC,MAAM,CAAC,UAAU;YACrC,KAAK,MAAM,KAAK,CAAC,gBAAgB,CAAC,UAAU;QAC9C;QACA,SAAS,CAAC;YACR,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;QAC/B;IACF;IAEA,gBAAgB;IAChB,MAAM,QAAQ,qHAAA,CAAA,MAAG,CAAC,QAAQ;IAE1B,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,uBAAuB,WAAW,CAAC;gBACvC,MAAM,KAAK,IAAI;gBACf,aAAa,KAAK,WAAW,IAAI;gBACjC,OAAO,KAAK,KAAK;gBACjB,MAAM,KAAK,IAAI,IAAI;YACrB;QACF,EAAE,OAAO,OAAO;QACd,iBAAiB;QACnB;IACF;IAEA,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,MAAM,OAAO,cAAc;QAC3B,SAAS;QACT,MAAK;;0BAEL,8OAAC,iIAAA,CAAA,cAAW;;kCACV,8OAAC,iIAAA,CAAA,aAAU;kCAAC;;;;;;kCACZ,8OAAC,iIAAA,CAAA,mBAAgB;wBAAC,SAAS;;;;;;;;;;;;0BAG7B,8OAAC,iIAAA,CAAA,eAAY;0BACX,cAAA,8OAAC,kIAAA,CAAA,eAAY;oBACX,UAAU;oBACV,UAAU;oBACV,WAAW,uBAAuB,SAAS;;;;;;;;;;;;;;;;;AAKrD", "debugId": null}}, {"offset": {"line": 3640, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/components/EditCategoryModal.tsx"], "sourcesContent": ["'use client'\n\nimport { toast } from 'react-hot-toast'\nimport { \n  <PERSON><PERSON>, \n  <PERSON>dal<PERSON>eader, \n  ModalTitle, \n  ModalContent,\n  ModalCloseButton \n} from '~/components/ui/Modal'\nimport { CategoryForm, type CategoryFormData } from '~/components/CategoryForm'\nimport { useModals } from '~/hooks/useStore'\nimport { api } from '~/trpc/react'\n\nexport function EditCategoryModal() {\n  const { modals, currentEditingCategory, closeEditCategory } = useModals()\n  \n  // 更新分类的mutation\n  const updateCategoryMutation = api.category.update.useMutation({\n    onSuccess: () => {\n      toast.success('分类更新成功！')\n      closeEditCategory()\n      // 刷新分类列表\n      void utils.category.getAll.invalidate()\n      void utils.category.getById.invalidate()\n      void utils.stats.getCategoryStats.invalidate()\n    },\n    onError: (error) => {\n      toast.error(error.message || '更新失败，请重试')\n    },\n  })\n  \n  // 获取utils用于刷新数据\n  const utils = api.useUtils()\n  \n  const handleSubmit = async (data: CategoryFormData) => {\n    if (!currentEditingCategory) return\n    \n    try {\n      await updateCategoryMutation.mutateAsync({\n        id: currentEditingCategory.id,\n        name: data.name,\n        description: data.description || undefined,\n        color: data.color,\n        icon: data.icon || undefined,\n      })\n    } catch (error) {\n      // 错误已在onError中处理\n    }\n  }\n\n  if (!currentEditingCategory) return null\n\n  return (\n    <Modal\n      open={modals.editCategory}\n      onClose={closeEditCategory}\n      size=\"lg\"\n    >\n      <ModalHeader>\n        <ModalTitle>编辑分类</ModalTitle>\n        <ModalCloseButton onClose={closeEditCategory} />\n      </ModalHeader>\n\n      <ModalContent>\n        <CategoryForm\n          category={currentEditingCategory}\n          onSubmit={handleSubmit}\n          onCancel={closeEditCategory}\n          isLoading={updateCategoryMutation.isPending}\n        />\n      </ModalContent>\n    </Modal>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AACA;AACA;AAZA;;;;;;;AAcO,SAAS;IACd,MAAM,EAAE,MAAM,EAAE,sBAAsB,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,YAAS,AAAD;IAEtE,gBAAgB;IAChB,MAAM,yBAAyB,qHAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC;QAC7D,WAAW;YACT,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd;YACA,SAAS;YACT,KAAK,MAAM,QAAQ,CAAC,MAAM,CAAC,UAAU;YACrC,KAAK,MAAM,QAAQ,CAAC,OAAO,CAAC,UAAU;YACtC,KAAK,MAAM,KAAK,CAAC,gBAAgB,CAAC,UAAU;QAC9C;QACA,SAAS,CAAC;YACR,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;QAC/B;IACF;IAEA,gBAAgB;IAChB,MAAM,QAAQ,qHAAA,CAAA,MAAG,CAAC,QAAQ;IAE1B,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,wBAAwB;QAE7B,IAAI;YACF,MAAM,uBAAuB,WAAW,CAAC;gBACvC,IAAI,uBAAuB,EAAE;gBAC7B,MAAM,KAAK,IAAI;gBACf,aAAa,KAAK,WAAW,IAAI;gBACjC,OAAO,KAAK,KAAK;gBACjB,MAAM,KAAK,IAAI,IAAI;YACrB;QACF,EAAE,OAAO,OAAO;QACd,iBAAiB;QACnB;IACF;IAEA,IAAI,CAAC,wBAAwB,OAAO;IAEpC,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,MAAM,OAAO,YAAY;QACzB,SAAS;QACT,MAAK;;0BAEL,8OAAC,iIAAA,CAAA,cAAW;;kCACV,8OAAC,iIAAA,CAAA,aAAU;kCAAC;;;;;;kCACZ,8OAAC,iIAAA,CAAA,mBAAgB;wBAAC,SAAS;;;;;;;;;;;;0BAG7B,8OAAC,iIAAA,CAAA,eAAY;0BACX,cAAA,8OAAC,kIAAA,CAAA,eAAY;oBACX,UAAU;oBACV,UAAU;oBACV,UAAU;oBACV,WAAW,uBAAuB,SAAS;;;;;;;;;;;;;;;;;AAKrD", "debugId": null}}, {"offset": {"line": 3743, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/components/ui/Card.tsx"], "sourcesContent": ["import { forwardRef, type HTMLAttributes } from 'react'\nimport { cn } from '~/lib/utils'\n\nconst Card = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-xl border bg-card text-card-foreground shadow',\n        className\n      )}\n      {...props}\n    />\n  )\n)\nCard.displayName = 'Card'\n\nconst CardHeader = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex flex-col space-y-1.5 p-6', className)}\n      {...props}\n    />\n  )\n)\nCardHeader.displayName = 'CardHeader'\n\nconst CardTitle = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLHeadingElement>>(\n  ({ className, ...props }, ref) => (\n    <h3\n      ref={ref}\n      className={cn('font-semibold leading-none tracking-tight', className)}\n      {...props}\n    />\n  )\n)\nCardTitle.displayName = 'CardTitle'\n\nconst CardDescription = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLParagraphElement>>(\n  ({ className, ...props }, ref) => (\n    <p\n      ref={ref}\n      className={cn('text-sm text-muted-foreground', className)}\n      {...props}\n    />\n  )\n)\nCardDescription.displayName = 'CardDescription'\n\nconst CardContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n  )\n)\nCardContent.displayName = 'CardContent'\n\nconst CardFooter = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex items-center p-6 pt-0', className)}\n      {...props}\n    />\n  )\n)\nCardFooter.displayName = 'CardFooter'\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACpB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAIf,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACzB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAIf,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAGlE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAIf,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3822, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/components/StatsDashboard.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/Card'\nimport { Badge } from '~/components/ui/Badge'\nimport { api } from '~/trpc/react'\n\nexport function StatsDashboard() {\n  // 获取统计数据\n  const { data: overview } = api.stats.getOverview.useQuery()\n  const { data: categoryStats } = api.stats.getCategoryStats.useQuery()\n  const { data: usageTrend } = api.stats.getUsageTrend.useQuery()\n  const { data: popularTags } = api.stats.getPopularTags.useQuery({ limit: 10 })\n  const { data: leaderboard } = api.stats.getUsageLeaderboard.useQuery({ limit: 10 })\n\n  return (\n    <div className=\"space-y-6\">\n      {/* 头部 */}\n      <div>\n        <h2 className=\"text-xl font-semibold text-slate-900 dark:text-white\">\n          数据统计\n        </h2>\n        <p className=\"text-sm text-slate-600 dark:text-slate-400 mt-1\">\n          查看提示词使用情况和平台统计数据\n        </p>\n      </div>\n\n      {/* 总览统计 */}\n      {overview && (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0 }}\n          >\n            <Card>\n              <CardHeader className=\"pb-2\">\n                <CardTitle className=\"text-sm font-medium text-muted-foreground\">\n                  总提示词数\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold text-slate-900 dark:text-white\">\n                  {overview.totalPrompts}\n                </div>\n                <div className=\"flex items-center mt-1\">\n                  <svg className=\"w-4 h-4 text-green-500 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                  </svg>\n                  <span className=\"text-xs text-muted-foreground\">个提示词</span>\n                </div>\n              </CardContent>\n            </Card>\n          </motion.div>\n\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.1 }}\n          >\n            <Card>\n              <CardHeader className=\"pb-2\">\n                <CardTitle className=\"text-sm font-medium text-muted-foreground\">\n                  分类数量\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold text-slate-900 dark:text-white\">\n                  {overview.totalCategories}\n                </div>\n                <div className=\"flex items-center mt-1\">\n                  <svg className=\"w-4 h-4 text-blue-500 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\" />\n                  </svg>\n                  <span className=\"text-xs text-muted-foreground\">个分类</span>\n                </div>\n              </CardContent>\n            </Card>\n          </motion.div>\n\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.2 }}\n          >\n            <Card>\n              <CardHeader className=\"pb-2\">\n                <CardTitle className=\"text-sm font-medium text-muted-foreground\">\n                  总使用次数\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold text-slate-900 dark:text-white\">\n                  {overview.totalUsages}\n                </div>\n                <div className=\"flex items-center mt-1\">\n                  <svg className=\"w-4 h-4 text-purple-500 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\" />\n                  </svg>\n                  <span className=\"text-xs text-muted-foreground\">次复制</span>\n                </div>\n              </CardContent>\n            </Card>\n          </motion.div>\n\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3 }}\n          >\n            <Card>\n              <CardHeader className=\"pb-2\">\n                <CardTitle className=\"text-sm font-medium text-muted-foreground\">\n                  用户数量\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold text-slate-900 dark:text-white\">\n                  {overview.totalUsers}\n                </div>\n                <div className=\"flex items-center mt-1\">\n                  <svg className=\"w-4 h-4 text-orange-500 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\" />\n                  </svg>\n                  <span className=\"text-xs text-muted-foreground\">个用户</span>\n                </div>\n              </CardContent>\n            </Card>\n          </motion.div>\n        </div>\n      )}\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* 分类统计 */}\n        {categoryStats && categoryStats.length > 0 && (\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.4 }}\n          >\n            <Card>\n              <CardHeader>\n                <CardTitle>分类统计</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-3\">\n                  {categoryStats.slice(0, 8).map((category, index) => (\n                    <div key={category.id} className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center gap-2\">\n                        <div \n                          className=\"w-3 h-3 rounded-full\"\n                          style={{ backgroundColor: category.color }}\n                        />\n                        <span className=\"text-sm font-medium\">{category.name}</span>\n                      </div>\n                      <Badge variant=\"secondary\">\n                        {category.promptCount} 个\n                      </Badge>\n                    </div>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n          </motion.div>\n        )}\n\n        {/* 热门标签 */}\n        {popularTags && popularTags.length > 0 && (\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.5 }}\n          >\n            <Card>\n              <CardHeader>\n                <CardTitle>热门标签</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"flex flex-wrap gap-2\">\n                  {popularTags.map((tag, index) => (\n                    <Badge\n                      key={tag.tag}\n                      variant=\"outline\"\n                      className=\"text-xs\"\n                    >\n                      {tag.tag}\n                      <span className=\"ml-1 text-xs text-muted-foreground\">\n                        ({tag.count})\n                      </span>\n                    </Badge>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n          </motion.div>\n        )}\n      </div>\n\n      {/* 使用排行榜 */}\n      {leaderboard && leaderboard.length > 0 && (\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.6 }}\n        >\n          <Card>\n            <CardHeader>\n              <CardTitle>使用排行榜</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {leaderboard.map((prompt, index) => (\n                  <div key={prompt.id} className=\"flex items-center gap-4\">\n                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${\n                      index === 0 ? 'bg-yellow-500 text-white' :\n                      index === 1 ? 'bg-gray-400 text-white' :\n                      index === 2 ? 'bg-amber-600 text-white' :\n                      'bg-slate-100 dark:bg-slate-800 text-slate-600 dark:text-slate-400'\n                    }`}>\n                      {index + 1}\n                    </div>\n                    \n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"flex items-center gap-2\">\n                        <h4 className=\"font-medium text-sm truncate\">{prompt.title}</h4>\n                        {prompt.category && (\n                          <div className=\"flex items-center gap-1\">\n                            <div \n                              className=\"w-2 h-2 rounded-full\"\n                              style={{ backgroundColor: prompt.category.color }}\n                            />\n                            <span className=\"text-xs text-muted-foreground\">\n                              {prompt.category.name}\n                            </span>\n                          </div>\n                        )}\n                      </div>\n                      {prompt.description && (\n                        <p className=\"text-xs text-muted-foreground truncate mt-1\">\n                          {prompt.description}\n                        </p>\n                      )}\n                    </div>\n                    \n                    <div className=\"text-right\">\n                      <div className=\"text-lg font-bold text-slate-900 dark:text-white\">\n                        {prompt.usageCount}\n                      </div>\n                      <div className=\"text-xs text-muted-foreground\">次使用</div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </motion.div>\n      )}\n\n      {/* 使用趋势图（简化版本） */}\n      {usageTrend && usageTrend.length > 0 && (\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.7 }}\n        >\n          <Card>\n            <CardHeader>\n              <CardTitle>使用趋势（最近30天）</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"h-32 flex items-end justify-between gap-1\">\n                {usageTrend.slice(-14).map((data, index) => {\n                  const maxCount = Math.max(...usageTrend.map(d => d.count))\n                  const height = maxCount > 0 ? (data.count / maxCount) * 100 : 0\n                  \n                  return (\n                    <div\n                      key={data.date}\n                      className=\"flex-1 bg-blue-500 rounded-t opacity-70 hover:opacity-100 transition-opacity\"\n                      style={{ height: `${Math.max(height, 2)}%` }}\n                      title={`${data.date}: ${data.count} 次使用`}\n                    />\n                  )\n                })}\n              </div>\n              <div className=\"text-xs text-muted-foreground mt-2 text-center\">\n                最近14天的使用情况\n              </div>\n            </CardContent>\n          </Card>\n        </motion.div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOO,SAAS;IACd,SAAS;IACT,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,qHAAA,CAAA,MAAG,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ;IACzD,MAAM,EAAE,MAAM,aAAa,EAAE,GAAG,qHAAA,CAAA,MAAG,CAAC,KAAK,CAAC,gBAAgB,CAAC,QAAQ;IACnE,MAAM,EAAE,MAAM,UAAU,EAAE,GAAG,qHAAA,CAAA,MAAG,CAAC,KAAK,CAAC,aAAa,CAAC,QAAQ;IAC7D,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,qHAAA,CAAA,MAAG,CAAC,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC;QAAE,OAAO;IAAG;IAC5E,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,qHAAA,CAAA,MAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,QAAQ,CAAC;QAAE,OAAO;IAAG;IAEjF,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAGrE,8OAAC;wBAAE,WAAU;kCAAkD;;;;;;;;;;;;YAMhE,0BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAE;kCAEvB,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAA4C;;;;;;;;;;;8CAInE,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDACZ,SAAS,YAAY;;;;;;sDAExB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;oDAA8B,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACrF,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;8DAEvE,8OAAC;oDAAK,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMxD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;kCAEzB,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAA4C;;;;;;;;;;;8CAInE,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDACZ,SAAS,eAAe;;;;;;sDAE3B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;oDAA6B,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACpF,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;8DAEvE,8OAAC;oDAAK,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMxD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;kCAEzB,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAA4C;;;;;;;;;;;8CAInE,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDACZ,SAAS,WAAW;;;;;;sDAEvB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;oDAA+B,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACtF,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;8DAEvE,8OAAC;oDAAK,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMxD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;kCAEzB,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAA4C;;;;;;;;;;;8CAInE,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDACZ,SAAS,UAAU;;;;;;sDAEtB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;oDAA+B,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACtF,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;8DAEvE,8OAAC;oDAAK,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ5D,8OAAC;gBAAI,WAAU;;oBAEZ,iBAAiB,cAAc,MAAM,GAAG,mBACvC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;kCAEzB,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;8CAEb,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;kDACZ,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,UAAU,sBACxC,8OAAC;gDAAsB,WAAU;;kEAC/B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,iBAAiB,SAAS,KAAK;gEAAC;;;;;;0EAE3C,8OAAC;gEAAK,WAAU;0EAAuB,SAAS,IAAI;;;;;;;;;;;;kEAEtD,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;;4DACZ,SAAS,WAAW;4DAAC;;;;;;;;+CAThB,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;oBAoBhC,eAAe,YAAY,MAAM,GAAG,mBACnC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;kCAEzB,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;8CAEb,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,KAAK,sBACrB,8OAAC,iIAAA,CAAA,QAAK;gDAEJ,SAAQ;gDACR,WAAU;;oDAET,IAAI,GAAG;kEACR,8OAAC;wDAAK,WAAU;;4DAAqC;4DACjD,IAAI,KAAK;4DAAC;;;;;;;;+CANT,IAAI,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAkB3B,eAAe,YAAY,MAAM,GAAG,mBACnC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;0BAEzB,cAAA,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,8OAAC;wCAAoB,WAAU;;0DAC7B,8OAAC;gDAAI,WAAW,CAAC,wEAAwE,EACvF,UAAU,IAAI,6BACd,UAAU,IAAI,2BACd,UAAU,IAAI,4BACd,qEACA;0DACC,QAAQ;;;;;;0DAGX,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAgC,OAAO,KAAK;;;;;;4DACzD,OAAO,QAAQ,kBACd,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEACC,WAAU;wEACV,OAAO;4EAAE,iBAAiB,OAAO,QAAQ,CAAC,KAAK;wEAAC;;;;;;kFAElD,8OAAC;wEAAK,WAAU;kFACb,OAAO,QAAQ,CAAC,IAAI;;;;;;;;;;;;;;;;;;oDAK5B,OAAO,WAAW,kBACjB,8OAAC;wDAAE,WAAU;kEACV,OAAO,WAAW;;;;;;;;;;;;0DAKzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,OAAO,UAAU;;;;;;kEAEpB,8OAAC;wDAAI,WAAU;kEAAgC;;;;;;;;;;;;;uCApCzC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;YA+C9B,cAAc,WAAW,MAAM,GAAG,mBACjC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;0BAEzB,cAAA,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,8OAAC,gIAAA,CAAA,cAAW;;8CACV,8OAAC;oCAAI,WAAU;8CACZ,WAAW,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,MAAM;wCAChC,MAAM,WAAW,KAAK,GAAG,IAAI,WAAW,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;wCACxD,MAAM,SAAS,WAAW,IAAI,AAAC,KAAK,KAAK,GAAG,WAAY,MAAM;wCAE9D,qBACE,8OAAC;4CAEC,WAAU;4CACV,OAAO;gDAAE,QAAQ,GAAG,KAAK,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC;4CAAC;4CAC3C,OAAO,GAAG,KAAK,IAAI,CAAC,EAAE,EAAE,KAAK,KAAK,CAAC,IAAI,CAAC;2CAHnC,KAAK,IAAI;;;;;oCAMpB;;;;;;8CAEF,8OAAC;oCAAI,WAAU;8CAAiD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9E", "debugId": null}}, {"offset": {"line": 4677, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/components/CategorySidebarNew.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { cn } from '~/lib/utils'\nimport { api } from '~/trpc/react'\nimport { useUI } from '~/hooks/useStore'\n\ninterface CategorySidebarProps {\n  selectedCategoryId?: string | null\n  onCategorySelect: (categoryId: string | null) => void\n  className?: string\n}\n\nexport function CategorySidebar({ \n  selectedCategoryId, \n  onCategorySelect, \n  className \n}: CategorySidebarProps) {\n  const [hoveredCategoryId, setHoveredCategoryId] = useState<string | null>(null)\n  \n  const { openCreateCategoryModal, openEditCategoryModal } = useUI()\n  const { data: categories, isLoading } = api.category.getAll.useQuery()\n\n  const handleCategoryClick = (categoryId: string | null) => {\n    onCategorySelect(categoryId)\n  }\n\n  if (isLoading) {\n    return (\n      <div className={cn('h-full flex flex-col p-4', className)}>\n        <div className=\"skeleton h-6 w-24 mb-4\"></div>\n        <div className=\"space-y-2\">\n          <div className=\"skeleton h-10 w-full\"></div>\n          <div className=\"skeleton h-10 w-full\"></div>\n          <div className=\"skeleton h-10 w-full\"></div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className={cn('h-full flex flex-col', className)}>\n      {/* 头部 */}\n      <div className=\"p-4 border-b border-base-300\">\n        <h3 className=\"text-lg font-bold\">分类目录</h3>\n      </div>\n\n      {/* 分类列表 */}\n      <div className=\"flex-1 overflow-y-auto p-4\">\n        <div className=\"space-y-2\">\n          {/* 全部分类选项 */}\n          <button\n            onClick={() => handleCategoryClick(null)}\n            className={`btn btn-ghost w-full justify-between ${\n              selectedCategoryId === null ? 'btn-active' : ''\n            }`}\n          >\n            <span>全部分类</span>\n            <div className=\"badge badge-neutral\">\n              {categories?.reduce((total, cat) => total + cat._count.prompts, 0) || 0}\n            </div>\n          </button>\n\n          {/* 分类列表 */}\n          {categories?.map((category) => (\n            <div\n              key={category.id}\n              className=\"relative\"\n              onMouseEnter={() => setHoveredCategoryId(category.id)}\n              onMouseLeave={() => setHoveredCategoryId(null)}\n            >\n              <button\n                onClick={() => handleCategoryClick(category.id)}\n                className={`btn btn-ghost w-full justify-between ${\n                  selectedCategoryId === category.id ? 'btn-active' : ''\n                }`}\n              >\n                <div className=\"flex items-center gap-2\">\n                  <div \n                    className=\"w-3 h-3 rounded-full\"\n                    style={{ backgroundColor: category.color }}\n                  />\n                  <span>{category.name}</span>\n                </div>\n                <div className=\"badge badge-neutral\">\n                  {category._count.prompts}\n                </div>\n              </button>\n              \n              {/* 编辑按钮 */}\n              {hoveredCategoryId === category.id && (\n                <button\n                  onClick={(e) => {\n                    e.stopPropagation()\n                    openEditCategoryModal(category)\n                  }}\n                  className=\"btn btn-ghost btn-xs absolute right-2 top-1/2 transform -translate-y-1/2\"\n                >\n                  <svg className=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                  </svg>\n                </button>\n              )}\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* 底部新建按钮 */}\n      <div className=\"p-4 border-t border-base-300\">\n        <button\n          onClick={openCreateCategoryModal}\n          className=\"btn btn-primary btn-block\"\n        >\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4v16m8-8H4\" />\n          </svg>\n          新建分类\n        </button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAaO,SAAS,gBAAgB,EAC9B,kBAAkB,EAClB,gBAAgB,EAChB,SAAS,EACY;IACrB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1E,MAAM,EAAE,uBAAuB,EAAE,qBAAqB,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,QAAK,AAAD;IAC/D,MAAM,EAAE,MAAM,UAAU,EAAE,SAAS,EAAE,GAAG,qHAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ;IAEpE,MAAM,sBAAsB,CAAC;QAC3B,iBAAiB;IACnB;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;;8BAC7C,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB;;0BAEzC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;8BAAoB;;;;;;;;;;;0BAIpC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BACC,SAAS,IAAM,oBAAoB;4BACnC,WAAW,CAAC,qCAAqC,EAC/C,uBAAuB,OAAO,eAAe,IAC7C;;8CAEF,8OAAC;8CAAK;;;;;;8CACN,8OAAC;oCAAI,WAAU;8CACZ,YAAY,OAAO,CAAC,OAAO,MAAQ,QAAQ,IAAI,MAAM,CAAC,OAAO,EAAE,MAAM;;;;;;;;;;;;wBAKzE,YAAY,IAAI,CAAC,yBAChB,8OAAC;gCAEC,WAAU;gCACV,cAAc,IAAM,qBAAqB,SAAS,EAAE;gCACpD,cAAc,IAAM,qBAAqB;;kDAEzC,8OAAC;wCACC,SAAS,IAAM,oBAAoB,SAAS,EAAE;wCAC9C,WAAW,CAAC,qCAAqC,EAC/C,uBAAuB,SAAS,EAAE,GAAG,eAAe,IACpD;;0DAEF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,iBAAiB,SAAS,KAAK;wDAAC;;;;;;kEAE3C,8OAAC;kEAAM,SAAS,IAAI;;;;;;;;;;;;0DAEtB,8OAAC;gDAAI,WAAU;0DACZ,SAAS,MAAM,CAAC,OAAO;;;;;;;;;;;;oCAK3B,sBAAsB,SAAS,EAAE,kBAChC,8OAAC;wCACC,SAAS,CAAC;4CACR,EAAE,eAAe;4CACjB,sBAAsB;wCACxB;wCACA,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;+BAjCtE,SAAS,EAAE;;;;;;;;;;;;;;;;0BA2CxB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAS;oBACT,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;wBACjE;;;;;;;;;;;;;;;;;;AAMhB", "debugId": null}}, {"offset": {"line": 4940, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/components/ClientOnly.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\n\ninterface ClientOnlyProps {\n  children: React.ReactNode\n  fallback?: React.ReactNode\n}\n\nexport function ClientOnly({ children, fallback = null }: ClientOnlyProps) {\n  const [hasMounted, setHasMounted] = useState(false)\n\n  useEffect(() => {\n    setHasMounted(true)\n  }, [])\n\n  if (!hasMounted) {\n    return <>{fallback}</>\n  }\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASO,SAAS,WAAW,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAmB;IACvE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;IAChB,GAAG,EAAE;IAEL,IAAI,CAAC,YAAY;QACf,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 4966, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/components/PageTransition.tsx"], "sourcesContent": ["'use client'\n\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { ReactNode } from 'react'\n\ninterface PageTransitionProps {\n  children: ReactNode\n  transitionKey: string\n  className?: string\n}\n\nconst pageVariants = {\n  initial: {\n    opacity: 0,\n    y: 20,\n    scale: 0.98,\n  },\n  in: {\n    opacity: 1,\n    y: 0,\n    scale: 1,\n  },\n  out: {\n    opacity: 0,\n    y: -20,\n    scale: 1.02,\n  },\n}\n\nconst pageTransition = {\n  type: 'tween',\n  ease: 'anticipate',\n  duration: 0.4,\n}\n\nexport function PageTransition({ children, transitionKey, className }: PageTransitionProps) {\n  return (\n    <AnimatePresence mode=\"wait\">\n      <motion.div\n        key={transitionKey}\n        initial=\"initial\"\n        animate=\"in\"\n        exit=\"out\"\n        variants={pageVariants}\n        transition={pageTransition}\n        className={className}\n      >\n        {children}\n      </motion.div>\n    </AnimatePresence>\n  )\n}\n\n// 淡入淡出动画\nexport function FadeTransition({ children, transitionKey, className }: PageTransitionProps) {\n  return (\n    <AnimatePresence mode=\"wait\">\n      <motion.div\n        key={transitionKey}\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        exit={{ opacity: 0 }}\n        transition={{ duration: 0.3 }}\n        className={className}\n      >\n        {children}\n      </motion.div>\n    </AnimatePresence>\n  )\n}\n\n// 滑动动画\nexport function SlideTransition({\n  children,\n  transitionKey,\n  className,\n  direction = 'right'\n}: PageTransitionProps & { direction?: 'left' | 'right' | 'up' | 'down' }) {\n  const slideVariants = {\n    initial: {\n      opacity: 0,\n      x: direction === 'left' ? -100 : direction === 'right' ? 100 : 0,\n      y: direction === 'up' ? -100 : direction === 'down' ? 100 : 0,\n    },\n    in: {\n      opacity: 1,\n      x: 0,\n      y: 0,\n    },\n    out: {\n      opacity: 0,\n      x: direction === 'left' ? 100 : direction === 'right' ? -100 : 0,\n      y: direction === 'up' ? 100 : direction === 'down' ? -100 : 0,\n    },\n  }\n\n  return (\n    <AnimatePresence mode=\"wait\">\n      <motion.div\n        key={transitionKey}\n        initial=\"initial\"\n        animate=\"in\"\n        exit=\"out\"\n        variants={slideVariants}\n        transition={{ type: 'tween', ease: 'easeInOut', duration: 0.3 }}\n        className={className}\n      >\n        {children}\n      </motion.div>\n    </AnimatePresence>\n  )\n}\n\n// 缩放动画\nexport function ScaleTransition({ children, transitionKey, className }: PageTransitionProps) {\n  return (\n    <AnimatePresence mode=\"wait\">\n      <motion.div\n        key={transitionKey}\n        initial={{ opacity: 0, scale: 0.9 }}\n        animate={{ opacity: 1, scale: 1 }}\n        exit={{ opacity: 0, scale: 1.1 }}\n        transition={{ type: 'tween', ease: 'easeInOut', duration: 0.3 }}\n        className={className}\n      >\n        {children}\n      </motion.div>\n    </AnimatePresence>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AAAA;AAFA;;;AAWA,MAAM,eAAe;IACnB,SAAS;QACP,SAAS;QACT,GAAG;QACH,OAAO;IACT;IACA,IAAI;QACF,SAAS;QACT,GAAG;QACH,OAAO;IACT;IACA,KAAK;QACH,SAAS;QACT,GAAG,CAAC;QACJ,OAAO;IACT;AACF;AAEA,MAAM,iBAAiB;IACrB,MAAM;IACN,MAAM;IACN,UAAU;AACZ;AAEO,SAAS,eAAe,EAAE,QAAQ,EAAE,aAAa,EAAE,SAAS,EAAuB;IACxF,qBACE,8OAAC,yLAAA,CAAA,kBAAe;QAAC,MAAK;kBACpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YAET,SAAQ;YACR,SAAQ;YACR,MAAK;YACL,UAAU;YACV,YAAY;YACZ,WAAW;sBAEV;WARI;;;;;;;;;;AAYb;AAGO,SAAS,eAAe,EAAE,QAAQ,EAAE,aAAa,EAAE,SAAS,EAAuB;IACxF,qBACE,8OAAC,yLAAA,CAAA,kBAAe;QAAC,MAAK;kBACpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YAET,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,YAAY;gBAAE,UAAU;YAAI;YAC5B,WAAW;sBAEV;WAPI;;;;;;;;;;AAWb;AAGO,SAAS,gBAAgB,EAC9B,QAAQ,EACR,aAAa,EACb,SAAS,EACT,YAAY,OAAO,EACoD;IACvE,MAAM,gBAAgB;QACpB,SAAS;YACP,SAAS;YACT,GAAG,cAAc,SAAS,CAAC,MAAM,cAAc,UAAU,MAAM;YAC/D,GAAG,cAAc,OAAO,CAAC,MAAM,cAAc,SAAS,MAAM;QAC9D;QACA,IAAI;YACF,SAAS;YACT,GAAG;YACH,GAAG;QACL;QACA,KAAK;YACH,SAAS;YACT,GAAG,cAAc,SAAS,MAAM,cAAc,UAAU,CAAC,MAAM;YAC/D,GAAG,cAAc,OAAO,MAAM,cAAc,SAAS,CAAC,MAAM;QAC9D;IACF;IAEA,qBACE,8OAAC,yLAAA,CAAA,kBAAe;QAAC,MAAK;kBACpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YAET,SAAQ;YACR,SAAQ;YACR,MAAK;YACL,UAAU;YACV,YAAY;gBAAE,MAAM;gBAAS,MAAM;gBAAa,UAAU;YAAI;YAC9D,WAAW;sBAEV;WARI;;;;;;;;;;AAYb;AAGO,SAAS,gBAAgB,EAAE,QAAQ,EAAE,aAAa,EAAE,SAAS,EAAuB;IACzF,qBACE,8OAAC,yLAAA,CAAA,kBAAe;QAAC,MAAK;kBACpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YAET,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAClC,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAChC,MAAM;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAC/B,YAAY;gBAAE,MAAM;gBAAS,MAAM;gBAAa,UAAU;YAAI;YAC9D,WAAW;sBAEV;WAPI;;;;;;;;;;AAWb", "debugId": null}}, {"offset": {"line": 5132, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/components/AnimatedButton.tsx"], "sourcesContent": ["'use client'\n\nimport { motion, MotionProps } from 'framer-motion'\nimport { forwardRef, ReactNode } from 'react'\nimport { Button, ButtonProps } from '~/components/ui/Button'\nimport { cn } from '~/lib/utils'\n\ninterface AnimatedButtonProps extends ButtonProps {\n  children: ReactNode\n  animationType?: 'bounce' | 'scale' | 'shake' | 'pulse' | 'rotate'\n  className?: string\n}\n\nconst animations = {\n  bounce: {\n    whileHover: { y: -2 },\n    whileTap: { y: 0 },\n    transition: { type: 'spring', stiffness: 400, damping: 10 }\n  },\n  scale: {\n    whileHover: { scale: 1.05 },\n    whileTap: { scale: 0.95 },\n    transition: { type: 'spring', stiffness: 400, damping: 10 }\n  },\n  shake: {\n    whileHover: { x: [-1, 1, -1, 1, 0] },\n    transition: { duration: 0.4 }\n  },\n  pulse: {\n    whileHover: { scale: [1, 1.05, 1] },\n    transition: { duration: 0.3, repeat: Infinity }\n  },\n  rotate: {\n    whileHover: { rotate: [0, -5, 5, 0] },\n    transition: { duration: 0.3 }\n  }\n}\n\nexport const AnimatedButton = forwardRef<HTMLButtonElement, AnimatedButtonProps>(\n  ({ children, animationType = 'scale', className, ...props }, ref) => {\n    const animation = animations[animationType]\n\n    return (\n      <motion.button\n        ref={ref}\n        className={cn('cursor-pointer inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2', className)}\n        {...animation}\n        {...props}\n      >\n        {children}\n      </motion.button>\n    )\n  }\n)\n\nAnimatedButton.displayName = 'AnimatedButton'\n\n// 浮动动作按钮\nexport function FloatingActionButton({ \n  children, \n  onClick, \n  className \n}: { \n  children: ReactNode\n  onClick?: () => void\n  className?: string \n}) {\n  return (\n    <motion.button\n      className={cn(\n        'fixed bottom-6 right-6 w-14 h-14 bg-blue-500 hover:bg-blue-600 text-white rounded-full shadow-lg flex items-center justify-center z-50',\n        className\n      )}\n      onClick={onClick}\n      whileHover={{ scale: 1.1 }}\n      whileTap={{ scale: 0.9 }}\n      initial={{ scale: 0 }}\n      animate={{ scale: 1 }}\n      transition={{ type: 'spring', stiffness: 260, damping: 20 }}\n    >\n      {children}\n    </motion.button>\n  )\n}\n\n// 成功动画按钮\nexport function SuccessButton({ \n  children, \n  isSuccess, \n  onClick, \n  className,\n  ...props \n}: ButtonProps & { \n  isSuccess?: boolean\n}) {\n  return (\n    <motion.div\n      animate={isSuccess ? { scale: [1, 1.2, 1] } : { scale: 1 }}\n      transition={{ duration: 0.3 }}\n    >\n      <Button\n        className={cn(\n          'relative overflow-hidden',\n          isSuccess && 'bg-green-500 hover:bg-green-600',\n          className\n        )}\n        onClick={onClick}\n        {...props}\n      >\n        <motion.div\n          initial={{ x: '-100%' }}\n          animate={isSuccess ? { x: '0%' } : { x: '-100%' }}\n          transition={{ duration: 0.3 }}\n          className=\"absolute inset-0 bg-green-400\"\n        />\n        <span className=\"relative z-10\">\n          {isSuccess ? '成功!' : children}\n        </span>\n      </Button>\n    </motion.div>\n  )\n}\n\n// 加载按钮\nexport function LoadingButton({ \n  children, \n  isLoading, \n  onClick, \n  className,\n  ...props \n}: ButtonProps & { \n  isLoading?: boolean\n}) {\n  return (\n    <Button\n      className={cn('relative', className)}\n      onClick={onClick}\n      disabled={isLoading}\n      {...props}\n    >\n      <motion.span\n        animate={{ opacity: isLoading ? 0 : 1 }}\n        transition={{ duration: 0.2 }}\n      >\n        {children}\n      </motion.span>\n      \n      {isLoading && (\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ duration: 0.2 }}\n          className=\"absolute inset-0 flex items-center justify-center\"\n        >\n          <motion.div\n            className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full\"\n            animate={{ rotate: 360 }}\n            transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}\n          />\n        </motion.div>\n      )}\n    </Button>\n  )\n}\n\n// 点击波纹效果按钮\nexport function RippleButton({ \n  children, \n  onClick, \n  className,\n  ...props \n}: ButtonProps) {\n  return (\n    <motion.div className=\"relative overflow-hidden rounded-md\">\n      <Button\n        className={cn('relative z-10', className)}\n        onClick={onClick}\n        {...props}\n      >\n        {children}\n      </Button>\n      \n      <motion.div\n        className=\"absolute inset-0 bg-white opacity-20 rounded-full\"\n        initial={{ scale: 0, opacity: 0.5 }}\n        whileTap={{ scale: 4, opacity: 0 }}\n        transition={{ duration: 0.4 }}\n      />\n    </motion.div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAaA,MAAM,aAAa;IACjB,QAAQ;QACN,YAAY;YAAE,GAAG,CAAC;QAAE;QACpB,UAAU;YAAE,GAAG;QAAE;QACjB,YAAY;YAAE,MAAM;YAAU,WAAW;YAAK,SAAS;QAAG;IAC5D;IACA,OAAO;QACL,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;QACxB,YAAY;YAAE,MAAM;YAAU,WAAW;YAAK,SAAS;QAAG;IAC5D;IACA,OAAO;QACL,YAAY;YAAE,GAAG;gBAAC,CAAC;gBAAG;gBAAG,CAAC;gBAAG;gBAAG;aAAE;QAAC;QACnC,YAAY;YAAE,UAAU;QAAI;IAC9B;IACA,OAAO;QACL,YAAY;YAAE,OAAO;gBAAC;gBAAG;gBAAM;aAAE;QAAC;QAClC,YAAY;YAAE,UAAU;YAAK,QAAQ;QAAS;IAChD;IACA,QAAQ;QACN,YAAY;YAAE,QAAQ;gBAAC;gBAAG,CAAC;gBAAG;gBAAG;aAAE;QAAC;QACpC,YAAY;YAAE,UAAU;QAAI;IAC9B;AACF;AAEO,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrC,CAAC,EAAE,QAAQ,EAAE,gBAAgB,OAAO,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC3D,MAAM,YAAY,UAAU,CAAC,cAAc;IAE3C,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+WAA+W;QAC5X,GAAG,SAAS;QACZ,GAAG,KAAK;kBAER;;;;;;AAGP;AAGF,eAAe,WAAW,GAAG;AAGtB,SAAS,qBAAqB,EACnC,QAAQ,EACR,OAAO,EACP,SAAS,EAKV;IACC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0IACA;QAEF,SAAS;QACT,YAAY;YAAE,OAAO;QAAI;QACzB,UAAU;YAAE,OAAO;QAAI;QACvB,SAAS;YAAE,OAAO;QAAE;QACpB,SAAS;YAAE,OAAO;QAAE;QACpB,YAAY;YAAE,MAAM;YAAU,WAAW;YAAK,SAAS;QAAG;kBAEzD;;;;;;AAGP;AAGO,SAAS,cAAc,EAC5B,QAAQ,EACR,SAAS,EACT,OAAO,EACP,SAAS,EACT,GAAG,OAGJ;IACC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS,YAAY;YAAE,OAAO;gBAAC;gBAAG;gBAAK;aAAE;QAAC,IAAI;YAAE,OAAO;QAAE;QACzD,YAAY;YAAE,UAAU;QAAI;kBAE5B,cAAA,8OAAC,kIAAA,CAAA,SAAM;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4BACA,aAAa,mCACb;YAEF,SAAS;YACR,GAAG,KAAK;;8BAET,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;oBAAQ;oBACtB,SAAS,YAAY;wBAAE,GAAG;oBAAK,IAAI;wBAAE,GAAG;oBAAQ;oBAChD,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;;;;;8BAEZ,8OAAC;oBAAK,WAAU;8BACb,YAAY,QAAQ;;;;;;;;;;;;;;;;;AAK/B;AAGO,SAAS,cAAc,EAC5B,QAAQ,EACR,SAAS,EACT,OAAO,EACP,SAAS,EACT,GAAG,OAGJ;IACC,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAC1B,SAAS;QACT,UAAU;QACT,GAAG,KAAK;;0BAET,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gBACV,SAAS;oBAAE,SAAS,YAAY,IAAI;gBAAE;gBACtC,YAAY;oBAAE,UAAU;gBAAI;0BAE3B;;;;;;YAGF,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;0BAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,QAAQ;oBAAI;oBACvB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;wBAAU,MAAM;oBAAS;;;;;;;;;;;;;;;;;AAMxE;AAGO,SAAS,aAAa,EAC3B,QAAQ,EACR,OAAO,EACP,SAAS,EACT,GAAG,OACS;IACZ,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QAAC,WAAU;;0BACpB,8OAAC,kIAAA,CAAA,SAAM;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;gBAC/B,SAAS;gBACR,GAAG,KAAK;0BAER;;;;;;0BAGH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,OAAO;oBAAG,SAAS;gBAAI;gBAClC,UAAU;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBACjC,YAAY;oBAAE,UAAU;gBAAI;;;;;;;;;;;;AAIpC", "debugId": null}}, {"offset": {"line": 5419, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/components/SearchAndFilter.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { Input } from '~/components/ui/Input'\nimport { Button } from '~/components/ui/Button'\nimport { Badge } from '~/components/ui/Badge'\nimport { useUI, useSearchHistory, useModals } from '~/hooks/useStore'\nimport { debounce } from '~/lib/utils'\nimport { api } from '~/trpc/react'\n\ninterface SearchAndFilterProps {\n  onSearch?: (query: string) => void\n  onSortChange?: (sortBy: string, sortOrder: 'asc' | 'desc') => void\n  hasActiveFilters?: boolean\n  onClearFilters?: () => void\n}\n\nexport function SearchAndFilter({\n  onSearch,\n  onSortChange,\n  hasActiveFilters = false,\n  onClearFilters,\n}: SearchAndFilterProps) {\n  const {\n    searchQuery,\n    setSearchQuery,\n    sortBy,\n    setSortBy,\n    sortOrder,\n    setSortOrder,\n  } = useUI()\n  \n  const {\n    searchHistory,\n    addSearchHistory,\n    removeSearchHistory,\n    recentSearches,\n  } = useSearchHistory()\n\n  const [showSearchHistory, setShowSearchHistory] = useState(false)\n  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery)\n  \n  // 防抖搜索\n  const debouncedSearch = debounce((query: string) => {\n    setSearchQuery(query)\n    onSearch?.(query)\n    if (query.trim()) {\n      addSearchHistory(query.trim())\n    }\n  }, 300)\n  \n  // 处理搜索输入\n  const handleSearchChange = (value: string) => {\n    setLocalSearchQuery(value)\n    debouncedSearch(value)\n  }\n  \n  // 处理搜索历史点击\n  const handleHistoryClick = (query: string) => {\n    setLocalSearchQuery(query)\n    setSearchQuery(query)\n    onSearch?.(query)\n    setShowSearchHistory(false)\n  }\n  \n\n  \n  // 处理排序\n  const handleSortChange = (newSortBy: string) => {\n    if (newSortBy === sortBy) {\n      // 如果是同一个字段，切换排序方向\n      const newSortOrder = sortOrder === 'asc' ? 'desc' : 'asc'\n      setSortOrder(newSortOrder)\n      onSortChange?.(sortBy, newSortOrder)\n    } else {\n      // 如果是不同字段，使用默认排序方向\n      setSortBy(newSortBy as any)\n      setSortOrder('desc')\n      onSortChange?.(newSortBy, 'desc')\n    }\n  }\n  \n  // 清除所有筛选\n  const handleClearFilters = () => {\n    setLocalSearchQuery('')\n    setSearchQuery('')\n    setSortBy('createdAt')\n    setSortOrder('desc')\n    onSearch?.('')\n    onSortChange?.('createdAt', 'desc')\n    onClearFilters?.()\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* 搜索框 */}\n      <div className=\"relative\">\n        <div className=\"form-control\">\n          <div className=\"input-group\">\n            <input\n              type=\"text\"\n              placeholder=\"搜索提示词标题、内容或标签...\"\n              value={localSearchQuery}\n              onChange={(e) => handleSearchChange(e.target.value)}\n              onFocus={() => setShowSearchHistory(true)}\n              onBlur={() => setTimeout(() => setShowSearchHistory(false), 200)}\n              className=\"input input-bordered w-full\"\n            />\n            <button className=\"btn btn-square\">\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n        \n        {/* 搜索历史下拉 */}\n        {showSearchHistory && recentSearches.length > 0 && (\n          <div className=\"dropdown dropdown-open w-full\">\n            <div className=\"dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-full mt-1\">\n              <div className=\"menu-title\">\n                <span>最近搜索</span>\n              </div>\n              {recentSearches.map((query, index) => (\n                <li key={index}>\n                  <div className=\"flex items-center justify-between\">\n                    <button\n                      onClick={() => handleHistoryClick(query)}\n                      className=\"flex-1 text-left\"\n                    >\n                      {query}\n                    </button>\n                    <button\n                      onClick={(e) => {\n                        e.stopPropagation()\n                        removeSearchHistory(query)\n                      }}\n                      className=\"btn btn-ghost btn-xs\"\n                    >\n                      <svg className=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                      </svg>\n                    </button>\n                  </div>\n                </li>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n      \n      {/* 排序 */}\n      <div className=\"flex flex-wrap items-center gap-4\">\n        <span className=\"text-sm font-medium\">排序：</span>\n        <div className=\"join\">\n          {[\n            { key: 'createdAt', label: '创建时间' },\n            { key: 'updatedAt', label: '更新时间' },\n            { key: 'usageCount', label: '使用次数' },\n            { key: 'title', label: '标题' },\n          ].map((sort) => (\n            <button\n              key={sort.key}\n              onClick={() => handleSortChange(sort.key)}\n              className={`btn join-item btn-sm ${\n                sortBy === sort.key ? 'btn-active' : 'btn-outline'\n              }`}\n            >\n              {sort.label}\n              {sortBy === sort.key && (\n                <svg\n                  className={`w-3 h-3 ml-1 transition-transform ${\n                    sortOrder === 'asc' ? 'rotate-180' : ''\n                  }`}\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                </svg>\n              )}\n            </button>\n          ))}\n        </div>\n        \n        {/* 清除筛选 */}\n        {hasActiveFilters && (\n          <button\n            onClick={handleClearFilters}\n            className=\"btn btn-outline btn-error btn-sm\"\n          >\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n            </svg>\n            清除筛选\n          </button>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAKA;AACA;AARA;;;;;AAkBO,SAAS,gBAAgB,EAC9B,QAAQ,EACR,YAAY,EACZ,mBAAmB,KAAK,EACxB,cAAc,EACO;IACrB,MAAM,EACJ,WAAW,EACX,cAAc,EACd,MAAM,EACN,SAAS,EACT,SAAS,EACT,YAAY,EACb,GAAG,CAAA,GAAA,wHAAA,CAAA,QAAK,AAAD;IAER,MAAM,EACJ,aAAa,EACb,gBAAgB,EAChB,mBAAmB,EACnB,cAAc,EACf,GAAG,CAAA,GAAA,wHAAA,CAAA,mBAAgB,AAAD;IAEnB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,OAAO;IACP,MAAM,kBAAkB,CAAA,GAAA,mHAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QAChC,eAAe;QACf,WAAW;QACX,IAAI,MAAM,IAAI,IAAI;YAChB,iBAAiB,MAAM,IAAI;QAC7B;IACF,GAAG;IAEH,SAAS;IACT,MAAM,qBAAqB,CAAC;QAC1B,oBAAoB;QACpB,gBAAgB;IAClB;IAEA,WAAW;IACX,MAAM,qBAAqB,CAAC;QAC1B,oBAAoB;QACpB,eAAe;QACf,WAAW;QACX,qBAAqB;IACvB;IAIA,OAAO;IACP,MAAM,mBAAmB,CAAC;QACxB,IAAI,cAAc,QAAQ;YACxB,kBAAkB;YAClB,MAAM,eAAe,cAAc,QAAQ,SAAS;YACpD,aAAa;YACb,eAAe,QAAQ;QACzB,OAAO;YACL,mBAAmB;YACnB,UAAU;YACV,aAAa;YACb,eAAe,WAAW;QAC5B;IACF;IAEA,SAAS;IACT,MAAM,qBAAqB;QACzB,oBAAoB;QACpB,eAAe;QACf,UAAU;QACV,aAAa;QACb,WAAW;QACX,eAAe,aAAa;QAC5B;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oCAClD,SAAS,IAAM,qBAAqB;oCACpC,QAAQ,IAAM,WAAW,IAAM,qBAAqB,QAAQ;oCAC5D,WAAU;;;;;;8CAEZ,8OAAC;oCAAO,WAAU;8CAChB,cAAA,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAO5E,qBAAqB,eAAe,MAAM,GAAG,mBAC5C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;kDAAK;;;;;;;;;;;gCAEP,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,8OAAC;kDACC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,mBAAmB;oDAClC,WAAU;8DAET;;;;;;8DAEH,8OAAC;oDACC,SAAS,CAAC;wDACR,EAAE,eAAe;wDACjB,oBAAoB;oDACtB;oDACA,WAAU;8DAEV,cAAA,8OAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACjE,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;uCAhBpE;;;;;;;;;;;;;;;;;;;;;;0BA4BnB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAAsB;;;;;;kCACtC,8OAAC;wBAAI,WAAU;kCACZ;4BACC;gCAAE,KAAK;gCAAa,OAAO;4BAAO;4BAClC;gCAAE,KAAK;gCAAa,OAAO;4BAAO;4BAClC;gCAAE,KAAK;gCAAc,OAAO;4BAAO;4BACnC;gCAAE,KAAK;gCAAS,OAAO;4BAAK;yBAC7B,CAAC,GAAG,CAAC,CAAC,qBACL,8OAAC;gCAEC,SAAS,IAAM,iBAAiB,KAAK,GAAG;gCACxC,WAAW,CAAC,qBAAqB,EAC/B,WAAW,KAAK,GAAG,GAAG,eAAe,eACrC;;oCAED,KAAK,KAAK;oCACV,WAAW,KAAK,GAAG,kBAClB,8OAAC;wCACC,WAAW,CAAC,kCAAkC,EAC5C,cAAc,QAAQ,eAAe,IACrC;wCACF,MAAK;wCACL,QAAO;wCACP,SAAQ;kDAER,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;+BAhBpE,KAAK,GAAG;;;;;;;;;;oBAwBlB,kCACC,8OAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;4BACjE;;;;;;;;;;;;;;;;;;;AAOlB", "debugId": null}}, {"offset": {"line": 5747, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/app/_components/HomePage.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion } from 'framer-motion'\nimport { api } from '~/trpc/react'\nimport { PromptList } from '~/components/PromptList'\nimport { PromptDetailModal } from '~/components/PromptDetailModal'\nimport { CreatePromptModal } from '~/components/CreatePromptModal'\nimport { EditPromptModal } from '~/components/EditPromptModal'\nimport { CreateCategoryModal } from '~/components/CreateCategoryModal'\nimport { EditCategoryModal } from '~/components/EditCategoryModal'\nimport { StatsDashboard } from '~/components/StatsDashboard'\nimport { CategorySidebar } from '~/components/CategorySidebarNew'\nimport { ClientOnly } from '~/components/ClientOnly'\nimport { PageTransition } from '~/components/PageTransition'\nimport { AnimatedButton, FloatingActionButton } from '~/components/AnimatedButton'\nimport { SearchAndFilter } from '~/components/SearchAndFilter'\nimport { But<PERSON> } from '~/components/ui/Button'\nimport { Badge } from '~/components/ui/Badge'\nimport { useUI, useModals, useUserPreferences } from '~/hooks/useStore'\n\nexport function HomePage() {\n  const [currentPage, setCurrentPage] = useState(1)\n  const [currentView, setCurrentView] = useState<'prompts' | 'stats'>('prompts')\n  const { openCreatePrompt } = useModals()\n  const {\n    searchQuery,\n    selectedCategoryId,\n    setSelectedCategoryId,\n    sortBy,\n    sortOrder\n  } = useUI()\n  const { pageSize } = useUserPreferences()\n\n  // 获取提示词列表数据\n  const { data: promptsData, isLoading: isLoadingPrompts } = api.prompt.getAll.useQuery({\n    page: currentPage,\n    limit: pageSize,\n    search: searchQuery || undefined,\n    categoryId: selectedCategoryId || undefined,\n    sortBy,\n    sortOrder,\n  })\n\n  // 获取其他数据\n  const { data: categories } = api.category.getAll.useQuery()\n  const { data: stats } = api.stats.getOverview.useQuery()\n\n  // 复制提示词的mutation\n  const copyPromptMutation = api.prompt.copy.useMutation()\n\n  const handleCopyPrompt = async (promptId: string) => {\n    try {\n      await copyPromptMutation.mutateAsync({ id: promptId })\n    } catch (error) {\n      console.error('更新使用次数失败:', error)\n    }\n  }\n\n  // 处理搜索\n  const handleSearch = (query: string) => {\n    setCurrentPage(1) // 重置到第一页\n  }\n\n  // 处理分类筛选\n  const handleCategoryFilter = (categoryId: string | null) => {\n    setSelectedCategoryId(categoryId || undefined)\n    setCurrentPage(1) // 重置到第一页\n  }\n\n  // 清除所有筛选\n  const handleClearFilters = () => {\n    setSelectedCategoryId(undefined)\n    setCurrentPage(1)\n  }\n\n  // 处理排序\n  const handleSortChange = (newSortBy: string, newSortOrder: 'asc' | 'desc') => {\n    setCurrentPage(1) // 重置到第一页\n  }\n\n  // 处理分页\n  const handlePageChange = (page: number) => {\n    setCurrentPage(page)\n  }\n\n  return (\n    <div className=\"min-h-screen bg-base-200\">\n      {/* 头部导航栏 */}\n      <div className=\"navbar bg-base-100 shadow-lg\">\n        <div className=\"navbar-start\">\n          <div className=\"flex items-center gap-4\">\n            <h1 className=\"text-xl font-bold text-primary\">\n              提示词管理工具\n            </h1>\n            {stats && (\n              <div className=\"hidden lg:flex items-center gap-4\">\n                <div className=\"stats stats-horizontal shadow\">\n                  <div className=\"stat\">\n                    <div className=\"stat-title\">提示词</div>\n                    <div className=\"stat-value text-primary text-lg\">{stats.totalPrompts}</div>\n                  </div>\n                  <div className=\"stat\">\n                    <div className=\"stat-title\">分类</div>\n                    <div className=\"stat-value text-secondary text-lg\">{stats.totalCategories}</div>\n                  </div>\n                  <div className=\"stat\">\n                    <div className=\"stat-title\">使用次数</div>\n                    <div className=\"stat-value text-accent text-lg\">{stats.totalUsages}</div>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n            \n        <div className=\"navbar-end\">\n          <div className=\"flex items-center gap-4\">\n            {/* 视图切换 */}\n            <div className=\"tabs tabs-boxed\">\n              <button\n                onClick={() => setCurrentView('prompts')}\n                className={`tab ${currentView === 'prompts' ? 'tab-active' : ''}`}\n              >\n                <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n                提示词\n              </button>\n              <button\n                onClick={() => setCurrentView('stats')}\n                className={`tab ${currentView === 'stats' ? 'tab-active' : ''}`}\n              >\n                <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                </svg>\n                数据统计\n              </button>\n            </div>\n\n            {/* 新建提示词按钮 */}\n            <button\n              onClick={openCreatePrompt}\n              className=\"btn btn-primary\"\n            >\n              <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4v16m8-8H4\" />\n              </svg>\n              新建提示词\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* 主要内容区域 */}\n      <div className=\"drawer lg:drawer-open\">\n        <input id=\"drawer-toggle\" type=\"checkbox\" className=\"drawer-toggle\" />\n\n        {/* 主内容区域 */}\n        <div className=\"drawer-content flex flex-col\">\n          {/* 移动端菜单按钮 */}\n          <div className=\"navbar lg:hidden\">\n            <div className=\"flex-none\">\n              <label htmlFor=\"drawer-toggle\" className=\"btn btn-square btn-ghost\">\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                </svg>\n              </label>\n            </div>\n          </div>\n\n          {/* 主内容区域 */}\n          <main className=\"flex-1 p-6\">\n            <PageTransition transitionKey={currentView}>\n              {currentView === 'prompts' ? (\n                <>\n                  {/* 搜索和筛选 */}\n                  <div className=\"card bg-base-100 shadow-xl mb-6\">\n                    <div className=\"card-body\">\n                      <SearchAndFilter\n                        onSearch={handleSearch}\n                        onSortChange={handleSortChange}\n                        hasActiveFilters={!!searchQuery || !!selectedCategoryId}\n                        onClearFilters={handleClearFilters}\n                      />\n                    </div>\n                  </div>\n\n                  {/* 提示词列表 */}\n                  <div className=\"card bg-base-100 shadow-xl\">\n                    <div className=\"card-body\">\n                      <div className=\"flex items-center justify-between mb-6\">\n                        <h2 className=\"card-title text-2xl\">\n                          {searchQuery || selectedCategoryId ? '搜索结果' : '所有提示词'}\n                        </h2>\n                        <div className=\"badge badge-neutral\">\n                          共 {promptsData?.prompts?.length || 0} 个提示词\n                        </div>\n                      </div>\n\n                      <PromptList\n                        prompts={promptsData?.prompts || []}\n                        isLoading={isLoadingPrompts}\n                        onCopy={handleCopyPrompt}\n                        showPagination={true}\n                        currentPage={currentPage}\n                        totalPages={promptsData?.pagination.totalPages || 1}\n                        onPageChange={handlePageChange}\n                      />\n                    </div>\n                  </div>\n                </>\n              ) : (\n                /* 统计视图 */\n                <StatsDashboard />\n              )}\n            </PageTransition>\n          </main>\n        </div>\n\n        {/* 侧边栏 */}\n        <div className=\"drawer-side\">\n          <label htmlFor=\"drawer-toggle\" className=\"drawer-overlay\"></label>\n          <aside className=\"min-h-full w-64 bg-base-100\">\n            <ClientOnly\n              fallback={\n                <div className=\"p-4\">\n                  <h3 className=\"text-lg font-semibold mb-4\">分类目录</h3>\n                  <div className=\"space-y-2\">\n                    <div className=\"btn btn-ghost btn-block justify-start\">\n                      <span className=\"badge badge-primary\">0</span>\n                      全部分类\n                    </div>\n                  </div>\n                </div>\n              }\n            >\n              <CategorySidebar\n                selectedCategoryId={selectedCategoryId}\n                onCategorySelect={handleCategoryFilter}\n              />\n            </ClientOnly>\n          </aside>\n        </div>\n      </div>\n\n      {/* 浮动操作按钮 */}\n      <FloatingActionButton onClick={openCreatePrompt}>\n        <svg\n          className=\"w-6 h-6\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n        >\n          <path\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth={2}\n            d=\"M12 4v16m8-8H4\"\n          />\n        </svg>\n      </FloatingActionButton>\n\n      {/* 模态框 */}\n      <PromptDetailModal />\n      <CreatePromptModal />\n      <EditPromptModal />\n      <CreateCategoryModal />\n      <EditCategoryModal />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAnBA;;;;;;;;;;;;;;;;;AAqBO,SAAS;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACpE,MAAM,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,YAAS,AAAD;IACrC,MAAM,EACJ,WAAW,EACX,kBAAkB,EAClB,qBAAqB,EACrB,MAAM,EACN,SAAS,EACV,GAAG,CAAA,GAAA,wHAAA,CAAA,QAAK,AAAD;IACR,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,qBAAkB,AAAD;IAEtC,YAAY;IACZ,MAAM,EAAE,MAAM,WAAW,EAAE,WAAW,gBAAgB,EAAE,GAAG,qHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;QACpF,MAAM;QACN,OAAO;QACP,QAAQ,eAAe;QACvB,YAAY,sBAAsB;QAClC;QACA;IACF;IAEA,SAAS;IACT,MAAM,EAAE,MAAM,UAAU,EAAE,GAAG,qHAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ;IACzD,MAAM,EAAE,MAAM,KAAK,EAAE,GAAG,qHAAA,CAAA,MAAG,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ;IAEtD,iBAAiB;IACjB,MAAM,qBAAqB,qHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW;IAEtD,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,mBAAmB,WAAW,CAAC;gBAAE,IAAI;YAAS;QACtD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,OAAO;IACP,MAAM,eAAe,CAAC;QACpB,eAAe,IAAG,SAAS;IAC7B;IAEA,SAAS;IACT,MAAM,uBAAuB,CAAC;QAC5B,sBAAsB,cAAc;QACpC,eAAe,IAAG,SAAS;IAC7B;IAEA,SAAS;IACT,MAAM,qBAAqB;QACzB,sBAAsB;QACtB,eAAe;IACjB;IAEA,OAAO;IACP,MAAM,mBAAmB,CAAC,WAAmB;QAC3C,eAAe,IAAG,SAAS;IAC7B;IAEA,OAAO;IACP,MAAM,mBAAmB,CAAC;QACxB,eAAe;IACjB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiC;;;;;;gCAG9C,uBACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAa;;;;;;kEAC5B,8OAAC;wDAAI,WAAU;kEAAmC,MAAM,YAAY;;;;;;;;;;;;0DAEtE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAa;;;;;;kEAC5B,8OAAC;wDAAI,WAAU;kEAAqC,MAAM,eAAe;;;;;;;;;;;;0DAE3E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAa;;;;;;kEAC5B,8OAAC;wDAAI,WAAU;kEAAkC,MAAM,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ9E,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAW,CAAC,IAAI,EAAE,gBAAgB,YAAY,eAAe,IAAI;;8DAEjE,8OAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACtE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;gDACjE;;;;;;;sDAGR,8OAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAW,CAAC,IAAI,EAAE,gBAAgB,UAAU,eAAe,IAAI;;8DAE/D,8OAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACtE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;gDACjE;;;;;;;;;;;;;8CAMV,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCACjE;;;;;;;;;;;;;;;;;;;;;;;;0BAQd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,IAAG;wBAAgB,MAAK;wBAAW,WAAU;;;;;;kCAGpD,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAM,SAAQ;wCAAgB,WAAU;kDACvC,cAAA,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO7E,8OAAC;gCAAK,WAAU;0CACd,cAAA,8OAAC,oIAAA,CAAA,iBAAc;oCAAC,eAAe;8CAC5B,gBAAgB,0BACf;;0DAEE,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,qIAAA,CAAA,kBAAe;wDACd,UAAU;wDACV,cAAc;wDACd,kBAAkB,CAAC,CAAC,eAAe,CAAC,CAAC;wDACrC,gBAAgB;;;;;;;;;;;;;;;;0DAMtB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EACX,eAAe,qBAAqB,SAAS;;;;;;8EAEhD,8OAAC;oEAAI,WAAU;;wEAAsB;wEAChC,aAAa,SAAS,UAAU;wEAAE;;;;;;;;;;;;;sEAIzC,8OAAC,gIAAA,CAAA,aAAU;4DACT,SAAS,aAAa,WAAW,EAAE;4DACnC,WAAW;4DACX,QAAQ;4DACR,gBAAgB;4DAChB,aAAa;4DACb,YAAY,aAAa,WAAW,cAAc;4DAClD,cAAc;;;;;;;;;;;;;;;;;;uDAMtB,QAAQ,iBACR,8OAAC,oIAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;;;;;;kCAOvB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,SAAQ;gCAAgB,WAAU;;;;;;0CACzC,8OAAC;gCAAM,WAAU;0CACf,cAAA,8OAAC,gIAAA,CAAA,aAAU;oCACT,wBACE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAsB;;;;;;wDAAQ;;;;;;;;;;;;;;;;;;8CAOtD,cAAA,8OAAC,wIAAA,CAAA,kBAAe;wCACd,oBAAoB;wCACpB,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ5B,8OAAC,oIAAA,CAAA,uBAAoB;gBAAC,SAAS;0BAC7B,cAAA,8OAAC;oBACC,WAAU;oBACV,MAAK;oBACL,QAAO;oBACP,SAAQ;8BAER,cAAA,8OAAC;wBACC,eAAc;wBACd,gBAAe;wBACf,aAAa;wBACb,GAAE;;;;;;;;;;;;;;;;0BAMR,8OAAC,uIAAA,CAAA,oBAAiB;;;;;0BAClB,8OAAC,uIAAA,CAAA,oBAAiB;;;;;0BAClB,8OAAC,qIAAA,CAAA,kBAAe;;;;;0BAChB,8OAAC,yIAAA,CAAA,sBAAmB;;;;;0BACpB,8OAAC,uIAAA,CAAA,oBAAiB;;;;;;;;;;;AAGxB", "debugId": null}}]}