"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/character-reference-invalid";
exports.ids = ["vendor-chunks/character-reference-invalid"];
exports.modules = {

/***/ "(ssr)/./node_modules/character-reference-invalid/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/character-reference-invalid/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   characterReferenceInvalid: () => (/* binding */ characterReferenceInvalid)\n/* harmony export */ });\n/**\n * Map of invalid numeric character references to their replacements, according to HTML.\n *\n * @type {Record<number, string>}\n */\nconst characterReferenceInvalid = {\n  0: '�',\n  128: '€',\n  130: '‚',\n  131: 'ƒ',\n  132: '„',\n  133: '…',\n  134: '†',\n  135: '‡',\n  136: 'ˆ',\n  137: '‰',\n  138: 'Š',\n  139: '‹',\n  140: 'Œ',\n  142: 'Ž',\n  145: '‘',\n  146: '’',\n  147: '“',\n  148: '”',\n  149: '•',\n  150: '–',\n  151: '—',\n  152: '˜',\n  153: '™',\n  154: 'š',\n  155: '›',\n  156: 'œ',\n  158: 'ž',\n  159: 'Ÿ'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvY2hhcmFjdGVyLXJlZmVyZW5jZS1pbnZhbGlkL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxDdXJzb3IgUHJvamVjdFxcd2Vic2l0ZVxcQXVnbWVudDJcXHByb21wdC1tYW5hZ2VyXFxub2RlX21vZHVsZXNcXGNoYXJhY3Rlci1yZWZlcmVuY2UtaW52YWxpZFxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBNYXAgb2YgaW52YWxpZCBudW1lcmljIGNoYXJhY3RlciByZWZlcmVuY2VzIHRvIHRoZWlyIHJlcGxhY2VtZW50cywgYWNjb3JkaW5nIHRvIEhUTUwuXG4gKlxuICogQHR5cGUge1JlY29yZDxudW1iZXIsIHN0cmluZz59XG4gKi9cbmV4cG9ydCBjb25zdCBjaGFyYWN0ZXJSZWZlcmVuY2VJbnZhbGlkID0ge1xuICAwOiAn77+9JyxcbiAgMTI4OiAn4oKsJyxcbiAgMTMwOiAn4oCaJyxcbiAgMTMxOiAnxpInLFxuICAxMzI6ICfigJ4nLFxuICAxMzM6ICfigKYnLFxuICAxMzQ6ICfigKAnLFxuICAxMzU6ICfigKEnLFxuICAxMzY6ICfLhicsXG4gIDEzNzogJ+KAsCcsXG4gIDEzODogJ8WgJyxcbiAgMTM5OiAn4oC5JyxcbiAgMTQwOiAnxZInLFxuICAxNDI6ICfFvScsXG4gIDE0NTogJ+KAmCcsXG4gIDE0NjogJ+KAmScsXG4gIDE0NzogJ+KAnCcsXG4gIDE0ODogJ+KAnScsXG4gIDE0OTogJ+KAoicsXG4gIDE1MDogJ+KAkycsXG4gIDE1MTogJ+KAlCcsXG4gIDE1MjogJ8ucJyxcbiAgMTUzOiAn4oSiJyxcbiAgMTU0OiAnxaEnLFxuICAxNTU6ICfigLonLFxuICAxNTY6ICfFkycsXG4gIDE1ODogJ8W+JyxcbiAgMTU5OiAnxbgnXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/character-reference-invalid/index.js\n");

/***/ })

};
;