import _extends from "@babel/runtime/helpers/extends";
import _objectWithoutPropertiesLoose from "@babel/runtime/helpers/objectWithoutPropertiesLoose";
var _excluded = ["prefixCls", "value", "padding", "minHeight", "placeholder", "language", "data-color-mode", "className", "style", "rehypePlugins", "onChange", "indentWidth"];
import React, { useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { processHtml, htmlEncode } from "./utils.js";
import shortcuts from "./shortcuts.js";
import * as styles from "./styles.js";
import "./style/index.css";
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
export * from "./SelectionText.js";
export default /*#__PURE__*/React.forwardRef((props, ref) => {
  var {
      prefixCls = 'w-tc-editor',
      padding = 10,
      minHeight = 16,
      placeholder,
      language,
      'data-color-mode': dataColorMode,
      className,
      style,
      rehypePlugins,
      onChange,
      indentWidth = 2
    } = props,
    other = _objectWithoutPropertiesLoose(props, _excluded);
  var [value, setValue] = useState(props.value || '');
  useEffect(() => setValue(props.value || ''), [props.value]);
  var textRef = useRef(null);
  useImperativeHandle(ref, () => textRef.current, [textRef]);
  var contentStyle = {
    paddingTop: padding,
    paddingRight: padding,
    paddingBottom: padding,
    paddingLeft: padding
  };
  var htmlStr = useMemo(() => processHtml("<pre aria-hidden=true><code " + (language && value ? "class=\"language-" + language + "\"" : '') + " >" + htmlEncode(String(value || '')) + "</code><br /></pre>", rehypePlugins), [value, language, rehypePlugins]);
  var preView = useMemo(() => /*#__PURE__*/_jsx("div", {
    style: _extends({}, styles.editor, contentStyle, {
      minHeight
    }),
    className: prefixCls + "-preview " + (language ? "language-" + language : ''),
    dangerouslySetInnerHTML: {
      __html: htmlStr
    }
  }),
  // eslint-disable-next-line react-hooks/exhaustive-deps
  [prefixCls, language, htmlStr]);
  var change = evn => {
    setValue(evn.target.value);
    onChange && onChange(evn);
  };
  var keyDown = evn => {
    if (other.readOnly) return;
    if (!other.onKeyDown || other.onKeyDown(evn) !== false) {
      shortcuts(evn, indentWidth);
    }
  };
  var textareaProps = _extends({
    autoComplete: 'off',
    autoCorrect: 'off',
    spellCheck: 'false',
    autoCapitalize: 'off'
  }, other, {
    placeholder,
    onKeyDown: keyDown,
    style: _extends({}, styles.editor, styles.textarea, contentStyle, {
      minHeight
    }, placeholder && !value ? {
      WebkitTextFillColor: 'inherit'
    } : {}),
    onChange: change,
    className: prefixCls + "-text",
    value: value
  });
  return /*#__PURE__*/_jsxs("div", {
    style: _extends({}, styles.container, style),
    className: prefixCls + " " + (className || ''),
    "data-color-mode": dataColorMode,
    children: [/*#__PURE__*/_jsx("textarea", _extends({}, textareaProps, {
      ref: textRef
    })), preView]
  });
});