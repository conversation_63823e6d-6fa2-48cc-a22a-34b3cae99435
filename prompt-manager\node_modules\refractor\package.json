{"author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "bugs": "https://github.com/wooorm/refractor/issues", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <daniel.s<PERSON><PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>> (https://wooorm.com)"], "dependencies": {"@types/hast": "^2.0.0", "@types/prismjs": "^1.0.0", "hastscript": "^7.0.0", "parse-entities": "^4.0.0"}, "description": "Lightweight, robust, elegant virtual syntax highlighting using Prism", "devDependencies": {"@babel/core": "^7.0.0", "@types/babel__core": "^7.0.0", "@types/mdast": "^4.0.0", "@types/node": "^22.0.0", "alpha-sort": "^5.0.0", "c8": "^10.0.0", "chalk": "^5.0.0", "detab": "^3.0.0", "hast-util-from-html": "^2.0.0", "hast-util-to-html": "^9.0.0", "is-keyword": "^1.0.0", "mdast-zone": "^6.0.0", "patch-package": "^8.0.0", "prettier": "^3.0.0", "prismjs": "~1.30.0", "remark-cli": "^12.0.0", "remark-preset-wooorm": "^11.0.0", "trim-lines": "^3.0.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "xo": "^0.60.0"}, "files": ["index.d.ts.map", "index.d.ts", "index.js", "lang/", "lib/"], "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}, "keywords": ["ast", "code", "dom", "highlighting", "highlight", "prism", "syntax", "virtual"], "license": "MIT", "main": "index.js", "name": "refractor", "prettier": {"bracketSpacing": false, "semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm", ["remark-toc", {"skip": "license"}], "./script/count.js", "./script/support.js"]}, "repository": "wooorm/refractor", "scripts": {"build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark --frail --output --quiet -- . && prettier --log-level warn --write -- . && xo --fix", "generate-core": "patch-package && cp node_modules/prismjs/components/prism-core.js lib/prism-core.js", "generate-fixtures": "node --conditions development script/fixtures.js", "generate-languages": "node --conditions development script/languages.js", "generate-list": "node --conditions development script/list.js", "generate": "npm run generate-core && npm run generate-languages && npm run generate-list && npm run generate-fixtures", "test-api": "node --conditions development test/index.js", "test-coverage": "c8 --100 --check-coverage --exclude \"lang/\" --exclude \"test/\" --exclude \"lib/prism-core.js\" --reporter lcov -- npm run test-api", "test": "npm run generate && npm run build && npm run format && npm run test-coverage"}, "sideEffects": ["lib/all.js", "lib/common.js"], "typeCoverage": {"atLeast": 100, "ignoreFiles": ["lang/*.d.ts", "lib/prism-core.d.ts", "lib/prism-core.js"], "strict": true}, "types": "index.d.ts", "type": "module", "version": "4.9.0", "xo": {"ignores": ["lang/*.js", "lib/prism-core.js"], "overrides": [{"files": ["script/**/*.js", "test/**/*.js"], "rules": {"no-await-in-loop": "off", "unicorn/prefer-top-level-await": "off"}}], "prettier": true, "rules": {"unicorn/prefer-string-replace-all": "off"}}}