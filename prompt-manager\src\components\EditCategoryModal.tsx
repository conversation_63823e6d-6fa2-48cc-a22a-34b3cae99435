'use client'

import { toast } from 'react-hot-toast'
import { 
  <PERSON><PERSON>, 
  <PERSON>dal<PERSON>eader, 
  ModalTitle, 
  ModalContent,
  ModalCloseButton 
} from '~/components/ui/Modal'
import { CategoryForm, type CategoryFormData } from '~/components/CategoryForm'
import { useModals } from '~/hooks/useStore'
import { api } from '~/trpc/react'

export function EditCategoryModal() {
  const { modals, currentEditingCategory, closeEditCategory } = useModals()
  
  // 更新分类的mutation
  const updateCategoryMutation = api.category.update.useMutation({
    onSuccess: () => {
      toast.success('分类更新成功！')
      closeEditCategory()
      // 刷新分类列表
      void utils.category.getAll.invalidate()
      void utils.category.getById.invalidate()
      void utils.stats.getCategoryStats.invalidate()
    },
    onError: (error) => {
      toast.error(error.message || '更新失败，请重试')
    },
  })
  
  // 获取utils用于刷新数据
  const utils = api.useUtils()
  
  const handleSubmit = async (data: CategoryFormData) => {
    if (!currentEditingCategory) return
    
    try {
      await updateCategoryMutation.mutateAsync({
        id: currentEditingCategory.id,
        name: data.name,
        description: data.description || undefined,
        color: data.color,
        icon: data.icon || undefined,
      })
    } catch (error) {
      // 错误已在onError中处理
    }
  }

  if (!currentEditingCategory) return null

  return (
    <Modal
      open={modals.editCategory}
      onClose={closeEditCategory}
      size="lg"
    >
      <ModalHeader>
        <ModalTitle>编辑分类</ModalTitle>
        <ModalCloseButton onClose={closeEditCategory} />
      </ModalHeader>

      <ModalContent>
        <CategoryForm
          category={currentEditingCategory}
          onSubmit={handleSubmit}
          onCancel={closeEditCategory}
          isLoading={updateCategoryMutation.isPending}
        />
      </ModalContent>
    </Modal>
  )
}
