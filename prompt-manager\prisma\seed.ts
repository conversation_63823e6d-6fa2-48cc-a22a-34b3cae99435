import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('开始创建种子数据...')

  // 创建测试用户
  const user = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '测试用户',
    },
  })

  console.log('创建用户:', user)

  // 创建分类
  const categories = await Promise.all([
    prisma.category.upsert({
      where: { name: '编程助手' },
      update: {},
      create: {
        name: '编程助手',
        description: '用于编程开发的提示词',
        color: '#10B981',
        icon: 'code',
        createdById: user.id,
      },
    }),
    prisma.category.upsert({
      where: { name: '写作助手' },
      update: {},
      create: {
        name: '写作助手',
        description: '用于写作和文案的提示词',
        color: '#8B5CF6',
        icon: 'pen',
        createdById: user.id,
      },
    }),
    prisma.category.upsert({
      where: { name: '学习助手' },
      update: {},
      create: {
        name: '学习助手',
        description: '用于学习和教育的提示词',
        color: '#F59E0B',
        icon: 'book',
        createdById: user.id,
      },
    }),
  ])

  console.log('创建分类:', categories)

  // 创建示例提示词
  const prompts = await Promise.all([
    prisma.prompt.create({
      data: {
        title: 'React组件开发助手',
        content: `你是一个专业的React开发专家。请帮我创建一个React组件，要求：

1. 使用TypeScript
2. 遵循最佳实践
3. 包含适当的类型定义
4. 添加必要的注释
5. 考虑性能优化

组件需求：{组件需求描述}`,
        description: '帮助开发React组件的专业提示词',
        tags: ['React', 'TypeScript', '前端开发'],
        categoryId: categories[0].id,
        createdById: user.id,
        usageCount: 15,
      },
    }),
    prisma.prompt.create({
      data: {
        title: '代码审查助手',
        content: `请作为一个资深的代码审查专家，对以下代码进行详细审查：

审查要点：
1. 代码质量和可读性
2. 性能优化建议
3. 安全性问题
4. 最佳实践遵循情况
5. 潜在的bug或问题

代码：
{代码内容}

请提供具体的改进建议和修改方案。`,
        description: '专业的代码审查和优化建议',
        tags: ['代码审查', '优化', '最佳实践'],
        categoryId: categories[0].id,
        createdById: user.id,
        usageCount: 23,
      },
    }),
    prisma.prompt.create({
      data: {
        title: '技术文档写作',
        content: `你是一个专业的技术文档写作专家。请帮我写一份清晰、详细的技术文档：

文档要求：
1. 结构清晰，层次分明
2. 语言简洁明了
3. 包含必要的示例代码
4. 添加适当的图表说明
5. 考虑读者的技术水平

主题：{文档主题}
目标读者：{目标读者}
具体要求：{具体要求}`,
        description: '专业的技术文档写作助手',
        tags: ['技术文档', '写作', '说明文档'],
        categoryId: categories[1].id,
        createdById: user.id,
        usageCount: 8,
      },
    }),
    prisma.prompt.create({
      data: {
        title: '学习计划制定',
        content: `你是一个专业的学习规划师。请为我制定一个详细的学习计划：

学习目标：{学习目标}
当前水平：{当前水平}
可用时间：{每天/每周可用时间}
学习期限：{预期完成时间}

请提供：
1. 详细的学习路径
2. 每个阶段的学习重点
3. 推荐的学习资源
4. 实践项目建议
5. 进度检查节点`,
        description: '个性化学习计划制定助手',
        tags: ['学习计划', '教育', '技能提升'],
        categoryId: categories[2].id,
        createdById: user.id,
        usageCount: 12,
      },
    }),
  ])

  console.log('创建提示词:', prompts)

  // 创建一些使用记录
  for (const prompt of prompts) {
    const usageCount = Math.floor(Math.random() * 5) + 1
    for (let i = 0; i < usageCount; i++) {
      await prisma.promptUsage.create({
        data: {
          promptId: prompt.id,
          userId: user.id,
          createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000), // 随机过去30天内的时间
        },
      })
    }
  }

  console.log('种子数据创建完成！')
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error(e)
    await prisma.$disconnect()
    process.exit(1)
  })
