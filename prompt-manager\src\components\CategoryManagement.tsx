'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { toast } from 'react-hot-toast'
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/Card'
import { Button } from '~/components/ui/Button'
import { Badge } from '~/components/ui/Badge'
import { useModals } from '~/hooks/useStore'
import { formatRelativeTime } from '~/lib/utils'
import { api } from '~/trpc/react'

export function CategoryManagement() {
  const { openCreateCategory, openEditCategory } = useModals()
  
  // 获取分类数据
  const { data: categories, isLoading } = api.category.getAll.useQuery()
  
  // 删除分类的mutation
  const deleteCategoryMutation = api.category.delete.useMutation({
    onSuccess: () => {
      toast.success('分类删除成功！')
      void utils.category.getAll.invalidate()
      void utils.stats.getCategoryStats.invalidate()
    },
    onError: (error) => {
      toast.error(error.message || '删除失败，请重试')
    },
  })
  
  const utils = api.useUtils()
  
  const handleDeleteCategory = async (categoryId: string, categoryName: string) => {
    if (!confirm(`确定要删除分类"${categoryName}"吗？此操作不可撤销。`)) {
      return
    }
    
    try {
      await deleteCategoryMutation.mutateAsync({ id: categoryId })
    } catch (error) {
      // 错误已在onError中处理
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold">分类管理</h2>
          <div className="h-9 bg-slate-200 dark:bg-slate-700 rounded w-24 animate-pulse" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Array.from({ length: 6 }).map((_, index) => (
            <div
              key={index}
              className="h-32 bg-slate-200 dark:bg-slate-700 rounded-lg animate-pulse"
            />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-slate-900 dark:text-white">
            分类管理
          </h2>
          <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
            管理您的提示词分类，创建、编辑或删除分类
          </p>
        </div>
        
        <Button onClick={openCreateCategory}>
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
          新建分类
        </Button>
      </div>

      {/* 分类列表 */}
      {categories && categories.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {categories.map((category, index) => (
            <motion.div
              key={category.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="h-full hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <div 
                        className="w-4 h-4 rounded-full"
                        style={{ backgroundColor: category.color }}
                      />
                      <div>
                        <CardTitle className="text-lg">{category.name}</CardTitle>
                        {category.icon && (
                          <span className="text-xs text-muted-foreground">
                            图标: {category.icon}
                          </span>
                        )}
                      </div>
                    </div>
                    
                    <Badge variant="secondary">
                      {category._count?.prompts || 0} 个提示词
                    </Badge>
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* 描述 */}
                  {category.description && (
                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {category.description}
                    </p>
                  )}
                  
                  {/* 元信息 */}
                  <div className="text-xs text-muted-foreground space-y-1">
                    <div>创建时间: {formatRelativeTime(category.createdAt)}</div>
                    <div>更新时间: {formatRelativeTime(category.updatedAt)}</div>
                  </div>
                  
                  {/* 操作按钮 */}
                  <div className="flex items-center gap-2 pt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openEditCategory(category)}
                      className="flex-1"
                    >
                      <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                      编辑
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteCategory(category.id, category.name)}
                      disabled={deleteCategoryMutation.isPending || (category._count?.prompts || 0) > 0}
                      className="flex-1 hover:bg-destructive hover:text-destructive-foreground"
                    >
                      <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                      删除
                    </Button>
                  </div>
                  
                  {/* 删除提示 */}
                  {(category._count?.prompts || 0) > 0 && (
                    <p className="text-xs text-amber-600 dark:text-amber-400">
                      该分类下还有提示词，无法删除
                    </p>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-slate-400 dark:text-slate-600 mb-4">
            <svg
              className="w-16 h-16 mx-auto mb-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-slate-900 dark:text-white mb-2">
            还没有分类
          </h3>
          <p className="text-slate-600 dark:text-slate-400 mb-4">
            创建您的第一个分类，开始组织您的提示词
          </p>
          <Button onClick={openCreateCategory}>
            创建第一个分类
          </Button>
        </div>
      )}
    </div>
  )
}
