"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/CreateCategoryModal.tsx":
/*!************************************************!*\
  !*** ./src/components/CreateCategoryModal.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreateCategoryModal: () => (/* binding */ CreateCategoryModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_ui_Modal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ~/components/ui/Modal */ \"(app-pages-browser)/./src/components/ui/Modal.tsx\");\n/* harmony import */ var _components_CategoryForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ~/components/CategoryForm */ \"(app-pages-browser)/./src/components/CategoryForm.tsx\");\n/* harmony import */ var _hooks_useStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ~/hooks/useStore */ \"(app-pages-browser)/./src/hooks/useStore.ts\");\n/* harmony import */ var _trpc_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ~/trpc/react */ \"(app-pages-browser)/./src/trpc/react.tsx\");\n/* __next_internal_client_entry_do_not_use__ CreateCategoryModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction CreateCategoryModal() {\n    _s();\n    const { modals, closeCreateCategory } = (0,_hooks_useStore__WEBPACK_IMPORTED_MODULE_4__.useModals)();\n    // 创建分类的mutation\n    const createCategoryMutation = _trpc_react__WEBPACK_IMPORTED_MODULE_5__.api.category.create.useMutation({\n        onSuccess: {\n            \"CreateCategoryModal.useMutation[createCategoryMutation]\": ()=>{\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.toast.success('分类创建成功！');\n                closeCreateCategory();\n                // 刷新分类列表\n                void utils.category.getAll.invalidate();\n                void utils.stats.getCategoryStats.invalidate();\n            }\n        }[\"CreateCategoryModal.useMutation[createCategoryMutation]\"],\n        onError: {\n            \"CreateCategoryModal.useMutation[createCategoryMutation]\": (error)=>{\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.toast.error(error.message || '创建失败，请重试');\n            }\n        }[\"CreateCategoryModal.useMutation[createCategoryMutation]\"]\n    });\n    // 获取utils用于刷新数据\n    const utils = _trpc_react__WEBPACK_IMPORTED_MODULE_5__.api.useUtils();\n    const handleSubmit = async (data)=>{\n        try {\n            await createCategoryMutation.mutateAsync({\n                name: data.name,\n                description: data.description || undefined,\n                color: data.color,\n                icon: data.icon || undefined\n            });\n        } catch (error) {\n        // 错误已在onError中处理\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Modal__WEBPACK_IMPORTED_MODULE_2__.Modal, {\n        open: modals.createCategory,\n        onClose: closeCreateCategory,\n        size: \"lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Modal__WEBPACK_IMPORTED_MODULE_2__.ModalHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Modal__WEBPACK_IMPORTED_MODULE_2__.ModalTitle, {\n                        children: \"创建新分类\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CreateCategoryModal.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Modal__WEBPACK_IMPORTED_MODULE_2__.ModalCloseButton, {\n                        onClose: closeCreateCategory\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CreateCategoryModal.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CreateCategoryModal.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Modal__WEBPACK_IMPORTED_MODULE_2__.ModalContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CategoryForm__WEBPACK_IMPORTED_MODULE_3__.CategoryForm, {\n                    onSubmit: handleSubmit,\n                    onCancel: closeCreateCategory,\n                    isLoading: createCategoryMutation.isPending\n                }, void 0, false, {\n                    fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CreateCategoryModal.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CreateCategoryModal.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\components\\\\CreateCategoryModal.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateCategoryModal, \"xkIs4Og0vF8O2R5TnHm08iywWJw=\", false, function() {\n    return [\n        _hooks_useStore__WEBPACK_IMPORTED_MODULE_4__.useModals,\n        _trpc_react__WEBPACK_IMPORTED_MODULE_5__.api.useUtils\n    ];\n});\n_c = CreateCategoryModal;\nvar _c;\n$RefreshReg$(_c, \"CreateCategoryModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CreateCategoryModal.tsx\n"));

/***/ })

});