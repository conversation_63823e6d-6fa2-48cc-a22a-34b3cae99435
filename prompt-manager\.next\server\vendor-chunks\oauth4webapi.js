"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/oauth4webapi";
exports.ids = ["vendor-chunks/oauth4webapi"];
exports.modules = {

/***/ "(rsc)/./node_modules/oauth4webapi/build/index.js":
/*!**************************************************!*\
  !*** ./node_modules/oauth4webapi/build/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AUTHORIZATION_RESPONSE_ERROR: () => (/* binding */ AUTHORIZATION_RESPONSE_ERROR),\n/* harmony export */   AuthorizationResponseError: () => (/* binding */ AuthorizationResponseError),\n/* harmony export */   ClientSecretBasic: () => (/* binding */ ClientSecretBasic),\n/* harmony export */   ClientSecretJwt: () => (/* binding */ ClientSecretJwt),\n/* harmony export */   ClientSecretPost: () => (/* binding */ ClientSecretPost),\n/* harmony export */   DPoP: () => (/* binding */ DPoP),\n/* harmony export */   HTTP_REQUEST_FORBIDDEN: () => (/* binding */ HTTP_REQUEST_FORBIDDEN),\n/* harmony export */   INVALID_REQUEST: () => (/* binding */ INVALID_REQUEST),\n/* harmony export */   INVALID_RESPONSE: () => (/* binding */ INVALID_RESPONSE),\n/* harmony export */   INVALID_SERVER_METADATA: () => (/* binding */ INVALID_SERVER_METADATA),\n/* harmony export */   JSON_ATTRIBUTE_COMPARISON: () => (/* binding */ JSON_ATTRIBUTE_COMPARISON),\n/* harmony export */   JWT_CLAIM_COMPARISON: () => (/* binding */ JWT_CLAIM_COMPARISON),\n/* harmony export */   JWT_TIMESTAMP_CHECK: () => (/* binding */ JWT_TIMESTAMP_CHECK),\n/* harmony export */   JWT_USERINFO_EXPECTED: () => (/* binding */ JWT_USERINFO_EXPECTED),\n/* harmony export */   KEY_SELECTION: () => (/* binding */ KEY_SELECTION),\n/* harmony export */   MISSING_SERVER_METADATA: () => (/* binding */ MISSING_SERVER_METADATA),\n/* harmony export */   None: () => (/* binding */ None),\n/* harmony export */   OperationProcessingError: () => (/* binding */ OperationProcessingError),\n/* harmony export */   PARSE_ERROR: () => (/* binding */ PARSE_ERROR),\n/* harmony export */   PrivateKeyJwt: () => (/* binding */ PrivateKeyJwt),\n/* harmony export */   REQUEST_PROTOCOL_FORBIDDEN: () => (/* binding */ REQUEST_PROTOCOL_FORBIDDEN),\n/* harmony export */   RESPONSE_BODY_ERROR: () => (/* binding */ RESPONSE_BODY_ERROR),\n/* harmony export */   RESPONSE_IS_NOT_CONFORM: () => (/* binding */ RESPONSE_IS_NOT_CONFORM),\n/* harmony export */   RESPONSE_IS_NOT_JSON: () => (/* binding */ RESPONSE_IS_NOT_JSON),\n/* harmony export */   ResponseBodyError: () => (/* binding */ ResponseBodyError),\n/* harmony export */   TlsClientAuth: () => (/* binding */ TlsClientAuth),\n/* harmony export */   UNSUPPORTED_OPERATION: () => (/* binding */ UNSUPPORTED_OPERATION),\n/* harmony export */   UnsupportedOperationError: () => (/* binding */ UnsupportedOperationError),\n/* harmony export */   WWWAuthenticateChallengeError: () => (/* binding */ WWWAuthenticateChallengeError),\n/* harmony export */   WWW_AUTHENTICATE_CHALLENGE: () => (/* binding */ WWW_AUTHENTICATE_CHALLENGE),\n/* harmony export */   _expectedIssuer: () => (/* binding */ _expectedIssuer),\n/* harmony export */   _nodiscoverycheck: () => (/* binding */ _nodiscoverycheck),\n/* harmony export */   _nopkce: () => (/* binding */ _nopkce),\n/* harmony export */   allowInsecureRequests: () => (/* binding */ allowInsecureRequests),\n/* harmony export */   authorizationCodeGrantRequest: () => (/* binding */ authorizationCodeGrantRequest),\n/* harmony export */   backchannelAuthenticationGrantRequest: () => (/* binding */ backchannelAuthenticationGrantRequest),\n/* harmony export */   backchannelAuthenticationRequest: () => (/* binding */ backchannelAuthenticationRequest),\n/* harmony export */   calculatePKCECodeChallenge: () => (/* binding */ calculatePKCECodeChallenge),\n/* harmony export */   checkProtocol: () => (/* binding */ checkProtocol),\n/* harmony export */   clientCredentialsGrantRequest: () => (/* binding */ clientCredentialsGrantRequest),\n/* harmony export */   clockSkew: () => (/* binding */ clockSkew),\n/* harmony export */   clockTolerance: () => (/* binding */ clockTolerance),\n/* harmony export */   customFetch: () => (/* binding */ customFetch),\n/* harmony export */   deviceAuthorizationRequest: () => (/* binding */ deviceAuthorizationRequest),\n/* harmony export */   deviceCodeGrantRequest: () => (/* binding */ deviceCodeGrantRequest),\n/* harmony export */   discoveryRequest: () => (/* binding */ discoveryRequest),\n/* harmony export */   dynamicClientRegistrationRequest: () => (/* binding */ dynamicClientRegistrationRequest),\n/* harmony export */   expectNoNonce: () => (/* binding */ expectNoNonce),\n/* harmony export */   expectNoState: () => (/* binding */ expectNoState),\n/* harmony export */   formPostResponse: () => (/* binding */ formPostResponse),\n/* harmony export */   generateKeyPair: () => (/* binding */ generateKeyPair),\n/* harmony export */   generateRandomCodeVerifier: () => (/* binding */ generateRandomCodeVerifier),\n/* harmony export */   generateRandomNonce: () => (/* binding */ generateRandomNonce),\n/* harmony export */   generateRandomState: () => (/* binding */ generateRandomState),\n/* harmony export */   genericTokenEndpointRequest: () => (/* binding */ genericTokenEndpointRequest),\n/* harmony export */   getContentType: () => (/* binding */ getContentType),\n/* harmony export */   getValidatedIdTokenClaims: () => (/* binding */ getValidatedIdTokenClaims),\n/* harmony export */   introspectionRequest: () => (/* binding */ introspectionRequest),\n/* harmony export */   isDPoPNonceError: () => (/* binding */ isDPoPNonceError),\n/* harmony export */   issueRequestObject: () => (/* binding */ issueRequestObject),\n/* harmony export */   jweDecrypt: () => (/* binding */ jweDecrypt),\n/* harmony export */   jwksCache: () => (/* binding */ jwksCache),\n/* harmony export */   modifyAssertion: () => (/* binding */ modifyAssertion),\n/* harmony export */   nopkce: () => (/* binding */ nopkce),\n/* harmony export */   processAuthorizationCodeResponse: () => (/* binding */ processAuthorizationCodeResponse),\n/* harmony export */   processBackchannelAuthenticationGrantResponse: () => (/* binding */ processBackchannelAuthenticationGrantResponse),\n/* harmony export */   processBackchannelAuthenticationResponse: () => (/* binding */ processBackchannelAuthenticationResponse),\n/* harmony export */   processClientCredentialsResponse: () => (/* binding */ processClientCredentialsResponse),\n/* harmony export */   processDeviceAuthorizationResponse: () => (/* binding */ processDeviceAuthorizationResponse),\n/* harmony export */   processDeviceCodeResponse: () => (/* binding */ processDeviceCodeResponse),\n/* harmony export */   processDiscoveryResponse: () => (/* binding */ processDiscoveryResponse),\n/* harmony export */   processDynamicClientRegistrationResponse: () => (/* binding */ processDynamicClientRegistrationResponse),\n/* harmony export */   processGenericTokenEndpointResponse: () => (/* binding */ processGenericTokenEndpointResponse),\n/* harmony export */   processIntrospectionResponse: () => (/* binding */ processIntrospectionResponse),\n/* harmony export */   processPushedAuthorizationResponse: () => (/* binding */ processPushedAuthorizationResponse),\n/* harmony export */   processRefreshTokenResponse: () => (/* binding */ processRefreshTokenResponse),\n/* harmony export */   processResourceDiscoveryResponse: () => (/* binding */ processResourceDiscoveryResponse),\n/* harmony export */   processRevocationResponse: () => (/* binding */ processRevocationResponse),\n/* harmony export */   processUserInfoResponse: () => (/* binding */ processUserInfoResponse),\n/* harmony export */   protectedResourceRequest: () => (/* binding */ protectedResourceRequest),\n/* harmony export */   pushedAuthorizationRequest: () => (/* binding */ pushedAuthorizationRequest),\n/* harmony export */   refreshTokenGrantRequest: () => (/* binding */ refreshTokenGrantRequest),\n/* harmony export */   resolveEndpoint: () => (/* binding */ resolveEndpoint),\n/* harmony export */   resourceDiscoveryRequest: () => (/* binding */ resourceDiscoveryRequest),\n/* harmony export */   revocationRequest: () => (/* binding */ revocationRequest),\n/* harmony export */   skipAuthTimeCheck: () => (/* binding */ skipAuthTimeCheck),\n/* harmony export */   skipStateCheck: () => (/* binding */ skipStateCheck),\n/* harmony export */   skipSubjectCheck: () => (/* binding */ skipSubjectCheck),\n/* harmony export */   userInfoRequest: () => (/* binding */ userInfoRequest),\n/* harmony export */   validateApplicationLevelSignature: () => (/* binding */ validateApplicationLevelSignature),\n/* harmony export */   validateAuthResponse: () => (/* binding */ validateAuthResponse),\n/* harmony export */   validateCodeIdTokenResponse: () => (/* binding */ validateCodeIdTokenResponse),\n/* harmony export */   validateDetachedSignatureResponse: () => (/* binding */ validateDetachedSignatureResponse),\n/* harmony export */   validateJwtAccessToken: () => (/* binding */ validateJwtAccessToken),\n/* harmony export */   validateJwtAuthResponse: () => (/* binding */ validateJwtAuthResponse)\n/* harmony export */ });\nlet USER_AGENT;\nif (typeof navigator === 'undefined' || !navigator.userAgent?.startsWith?.('Mozilla/5.0 ')) {\n    const NAME = 'oauth4webapi';\n    const VERSION = 'v3.6.0';\n    USER_AGENT = `${NAME}/${VERSION}`;\n}\nfunction looseInstanceOf(input, expected) {\n    if (input == null) {\n        return false;\n    }\n    try {\n        return (input instanceof expected ||\n            Object.getPrototypeOf(input)[Symbol.toStringTag] === expected.prototype[Symbol.toStringTag]);\n    }\n    catch {\n        return false;\n    }\n}\nconst ERR_INVALID_ARG_VALUE = 'ERR_INVALID_ARG_VALUE';\nconst ERR_INVALID_ARG_TYPE = 'ERR_INVALID_ARG_TYPE';\nfunction CodedTypeError(message, code, cause) {\n    const err = new TypeError(message, { cause });\n    Object.assign(err, { code });\n    return err;\n}\nconst allowInsecureRequests = Symbol();\nconst clockSkew = Symbol();\nconst clockTolerance = Symbol();\nconst customFetch = Symbol();\nconst modifyAssertion = Symbol();\nconst jweDecrypt = Symbol();\nconst jwksCache = Symbol();\nconst encoder = new TextEncoder();\nconst decoder = new TextDecoder();\nfunction buf(input) {\n    if (typeof input === 'string') {\n        return encoder.encode(input);\n    }\n    return decoder.decode(input);\n}\nlet encodeBase64Url;\nif (Uint8Array.prototype.toBase64) {\n    encodeBase64Url = (input) => {\n        if (input instanceof ArrayBuffer) {\n            input = new Uint8Array(input);\n        }\n        return input.toBase64({ alphabet: 'base64url', omitPadding: true });\n    };\n}\nelse {\n    const CHUNK_SIZE = 0x8000;\n    encodeBase64Url = (input) => {\n        if (input instanceof ArrayBuffer) {\n            input = new Uint8Array(input);\n        }\n        const arr = [];\n        for (let i = 0; i < input.byteLength; i += CHUNK_SIZE) {\n            arr.push(String.fromCharCode.apply(null, input.subarray(i, i + CHUNK_SIZE)));\n        }\n        return btoa(arr.join('')).replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n    };\n}\nlet decodeBase64Url;\nif (Uint8Array.fromBase64) {\n    decodeBase64Url = (input) => {\n        try {\n            return Uint8Array.fromBase64(input, { alphabet: 'base64url' });\n        }\n        catch (cause) {\n            throw CodedTypeError('The input to be decoded is not correctly encoded.', ERR_INVALID_ARG_VALUE, cause);\n        }\n    };\n}\nelse {\n    decodeBase64Url = (input) => {\n        try {\n            const binary = atob(input.replace(/-/g, '+').replace(/_/g, '/').replace(/\\s/g, ''));\n            const bytes = new Uint8Array(binary.length);\n            for (let i = 0; i < binary.length; i++) {\n                bytes[i] = binary.charCodeAt(i);\n            }\n            return bytes;\n        }\n        catch (cause) {\n            throw CodedTypeError('The input to be decoded is not correctly encoded.', ERR_INVALID_ARG_VALUE, cause);\n        }\n    };\n}\nfunction b64u(input) {\n    if (typeof input === 'string') {\n        return decodeBase64Url(input);\n    }\n    return encodeBase64Url(input);\n}\nclass UnsupportedOperationError extends Error {\n    code;\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        this.code = UNSUPPORTED_OPERATION;\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nclass OperationProcessingError extends Error {\n    code;\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        if (options?.code) {\n            this.code = options?.code;\n        }\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nfunction OPE(message, code, cause) {\n    return new OperationProcessingError(message, { code, cause });\n}\nfunction assertCryptoKey(key, it) {\n    if (!(key instanceof CryptoKey)) {\n        throw CodedTypeError(`${it} must be a CryptoKey`, ERR_INVALID_ARG_TYPE);\n    }\n}\nfunction assertPrivateKey(key, it) {\n    assertCryptoKey(key, it);\n    if (key.type !== 'private') {\n        throw CodedTypeError(`${it} must be a private CryptoKey`, ERR_INVALID_ARG_VALUE);\n    }\n}\nfunction assertPublicKey(key, it) {\n    assertCryptoKey(key, it);\n    if (key.type !== 'public') {\n        throw CodedTypeError(`${it} must be a public CryptoKey`, ERR_INVALID_ARG_VALUE);\n    }\n}\nfunction normalizeTyp(value) {\n    return value.toLowerCase().replace(/^application\\//, '');\n}\nfunction isJsonObject(input) {\n    if (input === null || typeof input !== 'object' || Array.isArray(input)) {\n        return false;\n    }\n    return true;\n}\nfunction prepareHeaders(input) {\n    if (looseInstanceOf(input, Headers)) {\n        input = Object.fromEntries(input.entries());\n    }\n    const headers = new Headers(input ?? {});\n    if (USER_AGENT && !headers.has('user-agent')) {\n        headers.set('user-agent', USER_AGENT);\n    }\n    if (headers.has('authorization')) {\n        throw CodedTypeError('\"options.headers\" must not include the \"authorization\" header name', ERR_INVALID_ARG_VALUE);\n    }\n    return headers;\n}\nfunction signal(url, value) {\n    if (value !== undefined) {\n        if (typeof value === 'function') {\n            value = value(url.href);\n        }\n        if (!(value instanceof AbortSignal)) {\n            throw CodedTypeError('\"options.signal\" must return or be an instance of AbortSignal', ERR_INVALID_ARG_TYPE);\n        }\n        return value;\n    }\n    return undefined;\n}\nfunction replaceDoubleSlash(pathname) {\n    if (pathname.includes('//')) {\n        return pathname.replace('//', '/');\n    }\n    return pathname;\n}\nfunction prependWellKnown(url, wellKnown, allowTerminatingSlash = false) {\n    if (url.pathname === '/') {\n        url.pathname = wellKnown;\n    }\n    else {\n        url.pathname = replaceDoubleSlash(`${wellKnown}/${allowTerminatingSlash ? url.pathname : url.pathname.replace(/(\\/)$/, '')}`);\n    }\n    return url;\n}\nfunction appendWellKnown(url, wellKnown) {\n    url.pathname = replaceDoubleSlash(`${url.pathname}/${wellKnown}`);\n    return url;\n}\nasync function performDiscovery(input, urlName, transform, options) {\n    if (!(input instanceof URL)) {\n        throw CodedTypeError(`\"${urlName}\" must be an instance of URL`, ERR_INVALID_ARG_TYPE);\n    }\n    checkProtocol(input, options?.[allowInsecureRequests] !== true);\n    const url = transform(new URL(input.href));\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    return (options?.[customFetch] || fetch)(url.href, {\n        body: undefined,\n        headers: Object.fromEntries(headers.entries()),\n        method: 'GET',\n        redirect: 'manual',\n        signal: signal(url, options?.signal),\n    });\n}\nasync function discoveryRequest(issuerIdentifier, options) {\n    return performDiscovery(issuerIdentifier, 'issuerIdentifier', (url) => {\n        switch (options?.algorithm) {\n            case undefined:\n            case 'oidc':\n                appendWellKnown(url, '.well-known/openid-configuration');\n                break;\n            case 'oauth2':\n                prependWellKnown(url, '.well-known/oauth-authorization-server');\n                break;\n            default:\n                throw CodedTypeError('\"options.algorithm\" must be \"oidc\" (default), or \"oauth2\"', ERR_INVALID_ARG_VALUE);\n        }\n        return url;\n    }, options);\n}\nfunction assertNumber(input, allow0, it, code, cause) {\n    try {\n        if (typeof input !== 'number' || !Number.isFinite(input)) {\n            throw CodedTypeError(`${it} must be a number`, ERR_INVALID_ARG_TYPE, cause);\n        }\n        if (input > 0)\n            return;\n        if (allow0) {\n            if (input !== 0) {\n                throw CodedTypeError(`${it} must be a non-negative number`, ERR_INVALID_ARG_VALUE, cause);\n            }\n            return;\n        }\n        throw CodedTypeError(`${it} must be a positive number`, ERR_INVALID_ARG_VALUE, cause);\n    }\n    catch (err) {\n        if (code) {\n            throw OPE(err.message, code, cause);\n        }\n        throw err;\n    }\n}\nfunction assertString(input, it, code, cause) {\n    try {\n        if (typeof input !== 'string') {\n            throw CodedTypeError(`${it} must be a string`, ERR_INVALID_ARG_TYPE, cause);\n        }\n        if (input.length === 0) {\n            throw CodedTypeError(`${it} must not be empty`, ERR_INVALID_ARG_VALUE, cause);\n        }\n    }\n    catch (err) {\n        if (code) {\n            throw OPE(err.message, code, cause);\n        }\n        throw err;\n    }\n}\nasync function processDiscoveryResponse(expectedIssuerIdentifier, response) {\n    const expected = expectedIssuerIdentifier;\n    if (!(expected instanceof URL) && expected !== _nodiscoverycheck) {\n        throw CodedTypeError('\"expectedIssuerIdentifier\" must be an instance of URL', ERR_INVALID_ARG_TYPE);\n    }\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    if (response.status !== 200) {\n        throw OPE('\"response\" is not a conform Authorization Server Metadata response (unexpected HTTP status code)', RESPONSE_IS_NOT_CONFORM, response);\n    }\n    assertReadableResponse(response);\n    const json = await getResponseJsonBody(response);\n    assertString(json.issuer, '\"response\" body \"issuer\" property', INVALID_RESPONSE, { body: json });\n    if (expected !== _nodiscoverycheck && new URL(json.issuer).href !== expected.href) {\n        throw OPE('\"response\" body \"issuer\" property does not match the expected value', JSON_ATTRIBUTE_COMPARISON, { expected: expected.href, body: json, attribute: 'issuer' });\n    }\n    return json;\n}\nfunction assertApplicationJson(response) {\n    assertContentType(response, 'application/json');\n}\nfunction notJson(response, ...types) {\n    let msg = '\"response\" content-type must be ';\n    if (types.length > 2) {\n        const last = types.pop();\n        msg += `${types.join(', ')}, or ${last}`;\n    }\n    else if (types.length === 2) {\n        msg += `${types[0]} or ${types[1]}`;\n    }\n    else {\n        msg += types[0];\n    }\n    return OPE(msg, RESPONSE_IS_NOT_JSON, response);\n}\nfunction assertContentTypes(response, ...types) {\n    if (!types.includes(getContentType(response))) {\n        throw notJson(response, ...types);\n    }\n}\nfunction assertContentType(response, contentType) {\n    if (getContentType(response) !== contentType) {\n        throw notJson(response, contentType);\n    }\n}\nfunction randomBytes() {\n    return b64u(crypto.getRandomValues(new Uint8Array(32)));\n}\nfunction generateRandomCodeVerifier() {\n    return randomBytes();\n}\nfunction generateRandomState() {\n    return randomBytes();\n}\nfunction generateRandomNonce() {\n    return randomBytes();\n}\nasync function calculatePKCECodeChallenge(codeVerifier) {\n    assertString(codeVerifier, 'codeVerifier');\n    return b64u(await crypto.subtle.digest('SHA-256', buf(codeVerifier)));\n}\nfunction getKeyAndKid(input) {\n    if (input instanceof CryptoKey) {\n        return { key: input };\n    }\n    if (!(input?.key instanceof CryptoKey)) {\n        return {};\n    }\n    if (input.kid !== undefined) {\n        assertString(input.kid, '\"kid\"');\n    }\n    return {\n        key: input.key,\n        kid: input.kid,\n    };\n}\nfunction psAlg(key) {\n    switch (key.algorithm.hash.name) {\n        case 'SHA-256':\n            return 'PS256';\n        case 'SHA-384':\n            return 'PS384';\n        case 'SHA-512':\n            return 'PS512';\n        default:\n            throw new UnsupportedOperationError('unsupported RsaHashedKeyAlgorithm hash name', {\n                cause: key,\n            });\n    }\n}\nfunction rsAlg(key) {\n    switch (key.algorithm.hash.name) {\n        case 'SHA-256':\n            return 'RS256';\n        case 'SHA-384':\n            return 'RS384';\n        case 'SHA-512':\n            return 'RS512';\n        default:\n            throw new UnsupportedOperationError('unsupported RsaHashedKeyAlgorithm hash name', {\n                cause: key,\n            });\n    }\n}\nfunction esAlg(key) {\n    switch (key.algorithm.namedCurve) {\n        case 'P-256':\n            return 'ES256';\n        case 'P-384':\n            return 'ES384';\n        case 'P-521':\n            return 'ES512';\n        default:\n            throw new UnsupportedOperationError('unsupported EcKeyAlgorithm namedCurve', { cause: key });\n    }\n}\nfunction keyToJws(key) {\n    switch (key.algorithm.name) {\n        case 'RSA-PSS':\n            return psAlg(key);\n        case 'RSASSA-PKCS1-v1_5':\n            return rsAlg(key);\n        case 'ECDSA':\n            return esAlg(key);\n        case 'Ed25519':\n        case 'EdDSA':\n            return 'Ed25519';\n        default:\n            throw new UnsupportedOperationError('unsupported CryptoKey algorithm name', { cause: key });\n    }\n}\nfunction getClockSkew(client) {\n    const skew = client?.[clockSkew];\n    return typeof skew === 'number' && Number.isFinite(skew) ? skew : 0;\n}\nfunction getClockTolerance(client) {\n    const tolerance = client?.[clockTolerance];\n    return typeof tolerance === 'number' && Number.isFinite(tolerance) && Math.sign(tolerance) !== -1\n        ? tolerance\n        : 30;\n}\nfunction epochTime() {\n    return Math.floor(Date.now() / 1000);\n}\nfunction assertAs(as) {\n    if (typeof as !== 'object' || as === null) {\n        throw CodedTypeError('\"as\" must be an object', ERR_INVALID_ARG_TYPE);\n    }\n    assertString(as.issuer, '\"as.issuer\"');\n}\nfunction assertClient(client) {\n    if (typeof client !== 'object' || client === null) {\n        throw CodedTypeError('\"client\" must be an object', ERR_INVALID_ARG_TYPE);\n    }\n    assertString(client.client_id, '\"client.client_id\"');\n}\nfunction formUrlEncode(token) {\n    return encodeURIComponent(token).replace(/(?:[-_.!~*'()]|%20)/g, (substring) => {\n        switch (substring) {\n            case '-':\n            case '_':\n            case '.':\n            case '!':\n            case '~':\n            case '*':\n            case \"'\":\n            case '(':\n            case ')':\n                return `%${substring.charCodeAt(0).toString(16).toUpperCase()}`;\n            case '%20':\n                return '+';\n            default:\n                throw new Error();\n        }\n    });\n}\nfunction ClientSecretPost(clientSecret) {\n    assertString(clientSecret, '\"clientSecret\"');\n    return (_as, client, body, _headers) => {\n        body.set('client_id', client.client_id);\n        body.set('client_secret', clientSecret);\n    };\n}\nfunction ClientSecretBasic(clientSecret) {\n    assertString(clientSecret, '\"clientSecret\"');\n    return (_as, client, _body, headers) => {\n        const username = formUrlEncode(client.client_id);\n        const password = formUrlEncode(clientSecret);\n        const credentials = btoa(`${username}:${password}`);\n        headers.set('authorization', `Basic ${credentials}`);\n    };\n}\nfunction clientAssertionPayload(as, client) {\n    const now = epochTime() + getClockSkew(client);\n    return {\n        jti: randomBytes(),\n        aud: as.issuer,\n        exp: now + 60,\n        iat: now,\n        nbf: now,\n        iss: client.client_id,\n        sub: client.client_id,\n    };\n}\nfunction PrivateKeyJwt(clientPrivateKey, options) {\n    const { key, kid } = getKeyAndKid(clientPrivateKey);\n    assertPrivateKey(key, '\"clientPrivateKey.key\"');\n    return async (as, client, body, _headers) => {\n        const header = { alg: keyToJws(key), kid };\n        const payload = clientAssertionPayload(as, client);\n        options?.[modifyAssertion]?.(header, payload);\n        body.set('client_id', client.client_id);\n        body.set('client_assertion_type', 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer');\n        body.set('client_assertion', await signJwt(header, payload, key));\n    };\n}\nfunction ClientSecretJwt(clientSecret, options) {\n    assertString(clientSecret, '\"clientSecret\"');\n    const modify = options?.[modifyAssertion];\n    let key;\n    return async (as, client, body, _headers) => {\n        key ||= await crypto.subtle.importKey('raw', buf(clientSecret), { hash: 'SHA-256', name: 'HMAC' }, false, ['sign']);\n        const header = { alg: 'HS256' };\n        const payload = clientAssertionPayload(as, client);\n        modify?.(header, payload);\n        const data = `${b64u(buf(JSON.stringify(header)))}.${b64u(buf(JSON.stringify(payload)))}`;\n        const hmac = await crypto.subtle.sign(key.algorithm, key, buf(data));\n        body.set('client_id', client.client_id);\n        body.set('client_assertion_type', 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer');\n        body.set('client_assertion', `${data}.${b64u(new Uint8Array(hmac))}`);\n    };\n}\nfunction None() {\n    return (_as, client, body, _headers) => {\n        body.set('client_id', client.client_id);\n    };\n}\nfunction TlsClientAuth() {\n    return None();\n}\nasync function signJwt(header, payload, key) {\n    if (!key.usages.includes('sign')) {\n        throw CodedTypeError('CryptoKey instances used for signing assertions must include \"sign\" in their \"usages\"', ERR_INVALID_ARG_VALUE);\n    }\n    const input = `${b64u(buf(JSON.stringify(header)))}.${b64u(buf(JSON.stringify(payload)))}`;\n    const signature = b64u(await crypto.subtle.sign(keyToSubtle(key), key, buf(input)));\n    return `${input}.${signature}`;\n}\nasync function issueRequestObject(as, client, parameters, privateKey, options) {\n    assertAs(as);\n    assertClient(client);\n    parameters = new URLSearchParams(parameters);\n    const { key, kid } = getKeyAndKid(privateKey);\n    assertPrivateKey(key, '\"privateKey.key\"');\n    parameters.set('client_id', client.client_id);\n    const now = epochTime() + getClockSkew(client);\n    const claims = {\n        ...Object.fromEntries(parameters.entries()),\n        jti: randomBytes(),\n        aud: as.issuer,\n        exp: now + 60,\n        iat: now,\n        nbf: now,\n        iss: client.client_id,\n    };\n    let resource;\n    if (parameters.has('resource') &&\n        (resource = parameters.getAll('resource')) &&\n        resource.length > 1) {\n        claims.resource = resource;\n    }\n    {\n        let value = parameters.get('max_age');\n        if (value !== null) {\n            claims.max_age = parseInt(value, 10);\n            assertNumber(claims.max_age, true, '\"max_age\" parameter');\n        }\n    }\n    {\n        let value = parameters.get('claims');\n        if (value !== null) {\n            try {\n                claims.claims = JSON.parse(value);\n            }\n            catch (cause) {\n                throw OPE('failed to parse the \"claims\" parameter as JSON', PARSE_ERROR, cause);\n            }\n            if (!isJsonObject(claims.claims)) {\n                throw CodedTypeError('\"claims\" parameter must be a JSON with a top level object', ERR_INVALID_ARG_VALUE);\n            }\n        }\n    }\n    {\n        let value = parameters.get('authorization_details');\n        if (value !== null) {\n            try {\n                claims.authorization_details = JSON.parse(value);\n            }\n            catch (cause) {\n                throw OPE('failed to parse the \"authorization_details\" parameter as JSON', PARSE_ERROR, cause);\n            }\n            if (!Array.isArray(claims.authorization_details)) {\n                throw CodedTypeError('\"authorization_details\" parameter must be a JSON with a top level array', ERR_INVALID_ARG_VALUE);\n            }\n        }\n    }\n    const header = {\n        alg: keyToJws(key),\n        typ: 'oauth-authz-req+jwt',\n        kid,\n    };\n    options?.[modifyAssertion]?.(header, claims);\n    return signJwt(header, claims, key);\n}\nlet jwkCache;\nasync function getSetPublicJwkCache(key) {\n    const { kty, e, n, x, y, crv } = await crypto.subtle.exportKey('jwk', key);\n    const jwk = { kty, e, n, x, y, crv };\n    jwkCache.set(key, jwk);\n    return jwk;\n}\nasync function publicJwk(key) {\n    jwkCache ||= new WeakMap();\n    return jwkCache.get(key) || getSetPublicJwkCache(key);\n}\nconst URLParse = URL.parse\n    ?\n        (url, base) => URL.parse(url, base)\n    : (url, base) => {\n        try {\n            return new URL(url, base);\n        }\n        catch {\n            return null;\n        }\n    };\nfunction checkProtocol(url, enforceHttps) {\n    if (enforceHttps && url.protocol !== 'https:') {\n        throw OPE('only requests to HTTPS are allowed', HTTP_REQUEST_FORBIDDEN, url);\n    }\n    if (url.protocol !== 'https:' && url.protocol !== 'http:') {\n        throw OPE('only HTTP and HTTPS requests are allowed', REQUEST_PROTOCOL_FORBIDDEN, url);\n    }\n}\nfunction validateEndpoint(value, endpoint, useMtlsAlias, enforceHttps) {\n    let url;\n    if (typeof value !== 'string' || !(url = URLParse(value))) {\n        throw OPE(`authorization server metadata does not contain a valid ${useMtlsAlias ? `\"as.mtls_endpoint_aliases.${endpoint}\"` : `\"as.${endpoint}\"`}`, value === undefined ? MISSING_SERVER_METADATA : INVALID_SERVER_METADATA, { attribute: useMtlsAlias ? `mtls_endpoint_aliases.${endpoint}` : endpoint });\n    }\n    checkProtocol(url, enforceHttps);\n    return url;\n}\nfunction resolveEndpoint(as, endpoint, useMtlsAlias, enforceHttps) {\n    if (useMtlsAlias && as.mtls_endpoint_aliases && endpoint in as.mtls_endpoint_aliases) {\n        return validateEndpoint(as.mtls_endpoint_aliases[endpoint], endpoint, useMtlsAlias, enforceHttps);\n    }\n    return validateEndpoint(as[endpoint], endpoint, useMtlsAlias, enforceHttps);\n}\nasync function pushedAuthorizationRequest(as, client, clientAuthentication, parameters, options) {\n    assertAs(as);\n    assertClient(client);\n    const url = resolveEndpoint(as, 'pushed_authorization_request_endpoint', client.use_mtls_endpoint_aliases, options?.[allowInsecureRequests] !== true);\n    const body = new URLSearchParams(parameters);\n    body.set('client_id', client.client_id);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    if (options?.DPoP !== undefined) {\n        assertDPoP(options.DPoP);\n        await options.DPoP.addProof(url, headers, 'POST');\n    }\n    const response = await authenticatedRequest(as, client, clientAuthentication, url, body, headers, options);\n    options?.DPoP?.cacheNonce(response);\n    return response;\n}\nclass DPoPHandler {\n    #header;\n    #privateKey;\n    #publicKey;\n    #clockSkew;\n    #modifyAssertion;\n    #map;\n    #jkt;\n    constructor(client, keyPair, options) {\n        assertPrivateKey(keyPair?.privateKey, '\"DPoP.privateKey\"');\n        assertPublicKey(keyPair?.publicKey, '\"DPoP.publicKey\"');\n        if (!keyPair.publicKey.extractable) {\n            throw CodedTypeError('\"DPoP.publicKey.extractable\" must be true', ERR_INVALID_ARG_VALUE);\n        }\n        this.#modifyAssertion = options?.[modifyAssertion];\n        this.#clockSkew = getClockSkew(client);\n        this.#privateKey = keyPair.privateKey;\n        this.#publicKey = keyPair.publicKey;\n        branded.add(this);\n    }\n    #get(key) {\n        this.#map ||= new Map();\n        let item = this.#map.get(key);\n        if (item) {\n            this.#map.delete(key);\n            this.#map.set(key, item);\n        }\n        return item;\n    }\n    #set(key, val) {\n        this.#map ||= new Map();\n        this.#map.delete(key);\n        if (this.#map.size === 100) {\n            this.#map.delete(this.#map.keys().next().value);\n        }\n        this.#map.set(key, val);\n    }\n    async calculateThumbprint() {\n        if (!this.#jkt) {\n            const jwk = await crypto.subtle.exportKey('jwk', this.#publicKey);\n            let components;\n            switch (jwk.kty) {\n                case 'EC':\n                    components = { crv: jwk.crv, kty: jwk.kty, x: jwk.x, y: jwk.y };\n                    break;\n                case 'OKP':\n                    components = { crv: jwk.crv, kty: jwk.kty, x: jwk.x };\n                    break;\n                case 'RSA':\n                    components = { e: jwk.e, kty: jwk.kty, n: jwk.n };\n                    break;\n                default:\n                    throw new UnsupportedOperationError('unsupported JWK', { cause: { jwk } });\n            }\n            this.#jkt ||= b64u(await crypto.subtle.digest({ name: 'SHA-256' }, buf(JSON.stringify(components))));\n        }\n        return this.#jkt;\n    }\n    async addProof(url, headers, htm, accessToken) {\n        this.#header ||= {\n            alg: keyToJws(this.#privateKey),\n            typ: 'dpop+jwt',\n            jwk: await publicJwk(this.#publicKey),\n        };\n        const nonce = this.#get(url.origin);\n        const now = epochTime() + this.#clockSkew;\n        const payload = {\n            iat: now,\n            jti: randomBytes(),\n            htm,\n            nonce,\n            htu: `${url.origin}${url.pathname}`,\n            ath: accessToken ? b64u(await crypto.subtle.digest('SHA-256', buf(accessToken))) : undefined,\n        };\n        this.#modifyAssertion?.(this.#header, payload);\n        headers.set('dpop', await signJwt(this.#header, payload, this.#privateKey));\n    }\n    cacheNonce(response) {\n        try {\n            const nonce = response.headers.get('dpop-nonce');\n            if (nonce) {\n                this.#set(new URL(response.url).origin, nonce);\n            }\n        }\n        catch { }\n    }\n}\nfunction isDPoPNonceError(err) {\n    if (err instanceof WWWAuthenticateChallengeError) {\n        const { 0: challenge, length } = err.cause;\n        return (length === 1 && challenge.scheme === 'dpop' && challenge.parameters.error === 'use_dpop_nonce');\n    }\n    if (err instanceof ResponseBodyError) {\n        return err.error === 'use_dpop_nonce';\n    }\n    return false;\n}\nfunction DPoP(client, keyPair, options) {\n    return new DPoPHandler(client, keyPair, options);\n}\nclass ResponseBodyError extends Error {\n    cause;\n    code;\n    error;\n    status;\n    error_description;\n    response;\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        this.code = RESPONSE_BODY_ERROR;\n        this.cause = options.cause;\n        this.error = options.cause.error;\n        this.status = options.response.status;\n        this.error_description = options.cause.error_description;\n        Object.defineProperty(this, 'response', { enumerable: false, value: options.response });\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nclass AuthorizationResponseError extends Error {\n    cause;\n    code;\n    error;\n    error_description;\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        this.code = AUTHORIZATION_RESPONSE_ERROR;\n        this.cause = options.cause;\n        this.error = options.cause.get('error');\n        this.error_description = options.cause.get('error_description') ?? undefined;\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nclass WWWAuthenticateChallengeError extends Error {\n    cause;\n    code;\n    response;\n    status;\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        this.code = WWW_AUTHENTICATE_CHALLENGE;\n        this.cause = options.cause;\n        this.status = options.response.status;\n        this.response = options.response;\n        Object.defineProperty(this, 'response', { enumerable: false });\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nconst tokenMatch = \"[a-zA-Z0-9!#$%&\\\\'\\\\*\\\\+\\\\-\\\\.\\\\^_`\\\\|~]+\";\nconst token68Match = '[a-zA-Z0-9\\\\-\\\\._\\\\~\\\\+\\\\/]+[=]{0,2}';\nconst quotedMatch = '\"((?:[^\"\\\\\\\\]|\\\\\\\\.)*)\"';\nconst quotedParamMatcher = '(' + tokenMatch + ')\\\\s*=\\\\s*' + quotedMatch;\nconst paramMatcher = '(' + tokenMatch + ')\\\\s*=\\\\s*(' + tokenMatch + ')';\nconst schemeRE = new RegExp('^[,\\\\s]*(' + tokenMatch + ')\\\\s(.*)');\nconst quotedParamRE = new RegExp('^[,\\\\s]*' + quotedParamMatcher + '[,\\\\s]*(.*)');\nconst unquotedParamRE = new RegExp('^[,\\\\s]*' + paramMatcher + '[,\\\\s]*(.*)');\nconst token68ParamRE = new RegExp('^(' + token68Match + ')(?:$|[,\\\\s])(.*)');\nfunction parseWwwAuthenticateChallenges(response) {\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    const header = response.headers.get('www-authenticate');\n    if (header === null) {\n        return undefined;\n    }\n    const challenges = [];\n    let rest = header;\n    while (rest) {\n        let match = rest.match(schemeRE);\n        const scheme = match?.['1'].toLowerCase();\n        rest = match?.['2'];\n        if (!scheme) {\n            return undefined;\n        }\n        const parameters = {};\n        let token68;\n        while (rest) {\n            let key;\n            let value;\n            if ((match = rest.match(quotedParamRE))) {\n                ;\n                [, key, value, rest] = match;\n                if (value.includes('\\\\')) {\n                    try {\n                        value = JSON.parse(`\"${value}\"`);\n                    }\n                    catch { }\n                }\n                parameters[key.toLowerCase()] = value;\n                continue;\n            }\n            if ((match = rest.match(unquotedParamRE))) {\n                ;\n                [, key, value, rest] = match;\n                parameters[key.toLowerCase()] = value;\n                continue;\n            }\n            if ((match = rest.match(token68ParamRE))) {\n                if (Object.keys(parameters).length) {\n                    break;\n                }\n                ;\n                [, token68, rest] = match;\n                break;\n            }\n            return undefined;\n        }\n        const challenge = { scheme, parameters };\n        if (token68) {\n            challenge.token68 = token68;\n        }\n        challenges.push(challenge);\n    }\n    if (!challenges.length) {\n        return undefined;\n    }\n    return challenges;\n}\nasync function processPushedAuthorizationResponse(as, client, response) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    checkAuthenticationChallenges(response);\n    await checkOAuthBodyError(response, 201, 'Pushed Authorization Request Endpoint');\n    assertReadableResponse(response);\n    const json = await getResponseJsonBody(response);\n    assertString(json.request_uri, '\"response\" body \"request_uri\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    let expiresIn = typeof json.expires_in !== 'number' ? parseFloat(json.expires_in) : json.expires_in;\n    assertNumber(expiresIn, true, '\"response\" body \"expires_in\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    json.expires_in = expiresIn;\n    return json;\n}\nasync function parseOAuthResponseErrorBody(response) {\n    if (response.status > 399 && response.status < 500) {\n        assertReadableResponse(response);\n        assertApplicationJson(response);\n        try {\n            const json = await response.clone().json();\n            if (isJsonObject(json) && typeof json.error === 'string' && json.error.length) {\n                return json;\n            }\n        }\n        catch { }\n    }\n    return undefined;\n}\nasync function checkOAuthBodyError(response, expected, label) {\n    if (response.status !== expected) {\n        let err;\n        if ((err = await parseOAuthResponseErrorBody(response))) {\n            await response.body?.cancel();\n            throw new ResponseBodyError('server responded with an error in the response body', {\n                cause: err,\n                response,\n            });\n        }\n        throw OPE(`\"response\" is not a conform ${label} response (unexpected HTTP status code)`, RESPONSE_IS_NOT_CONFORM, response);\n    }\n}\nfunction assertDPoP(option) {\n    if (!branded.has(option)) {\n        throw CodedTypeError('\"options.DPoP\" is not a valid DPoPHandle', ERR_INVALID_ARG_VALUE);\n    }\n}\nasync function resourceRequest(accessToken, method, url, headers, body, options) {\n    assertString(accessToken, '\"accessToken\"');\n    if (!(url instanceof URL)) {\n        throw CodedTypeError('\"url\" must be an instance of URL', ERR_INVALID_ARG_TYPE);\n    }\n    checkProtocol(url, options?.[allowInsecureRequests] !== true);\n    headers = prepareHeaders(headers);\n    if (options?.DPoP) {\n        assertDPoP(options.DPoP);\n        await options.DPoP.addProof(url, headers, method.toUpperCase(), accessToken);\n    }\n    headers.set('authorization', `${headers.has('dpop') ? 'DPoP' : 'Bearer'} ${accessToken}`);\n    const response = await (options?.[customFetch] || fetch)(url.href, {\n        body,\n        headers: Object.fromEntries(headers.entries()),\n        method,\n        redirect: 'manual',\n        signal: signal(url, options?.signal),\n    });\n    options?.DPoP?.cacheNonce(response);\n    return response;\n}\nasync function protectedResourceRequest(accessToken, method, url, headers, body, options) {\n    const response = await resourceRequest(accessToken, method, url, headers, body, options);\n    checkAuthenticationChallenges(response);\n    return response;\n}\nasync function userInfoRequest(as, client, accessToken, options) {\n    assertAs(as);\n    assertClient(client);\n    const url = resolveEndpoint(as, 'userinfo_endpoint', client.use_mtls_endpoint_aliases, options?.[allowInsecureRequests] !== true);\n    const headers = prepareHeaders(options?.headers);\n    if (client.userinfo_signed_response_alg) {\n        headers.set('accept', 'application/jwt');\n    }\n    else {\n        headers.set('accept', 'application/json');\n        headers.append('accept', 'application/jwt');\n    }\n    return resourceRequest(accessToken, 'GET', url, headers, null, {\n        ...options,\n        [clockSkew]: getClockSkew(client),\n    });\n}\nlet jwksMap;\nfunction setJwksCache(as, jwks, uat, cache) {\n    jwksMap ||= new WeakMap();\n    jwksMap.set(as, {\n        jwks,\n        uat,\n        get age() {\n            return epochTime() - this.uat;\n        },\n    });\n    if (cache) {\n        Object.assign(cache, { jwks: structuredClone(jwks), uat });\n    }\n}\nfunction isFreshJwksCache(input) {\n    if (typeof input !== 'object' || input === null) {\n        return false;\n    }\n    if (!('uat' in input) || typeof input.uat !== 'number' || epochTime() - input.uat >= 300) {\n        return false;\n    }\n    if (!('jwks' in input) ||\n        !isJsonObject(input.jwks) ||\n        !Array.isArray(input.jwks.keys) ||\n        !Array.prototype.every.call(input.jwks.keys, isJsonObject)) {\n        return false;\n    }\n    return true;\n}\nfunction clearJwksCache(as, cache) {\n    jwksMap?.delete(as);\n    delete cache?.jwks;\n    delete cache?.uat;\n}\nasync function getPublicSigKeyFromIssuerJwksUri(as, options, header) {\n    const { alg, kid } = header;\n    checkSupportedJwsAlg(header);\n    if (!jwksMap?.has(as) && isFreshJwksCache(options?.[jwksCache])) {\n        setJwksCache(as, options?.[jwksCache].jwks, options?.[jwksCache].uat);\n    }\n    let jwks;\n    let age;\n    if (jwksMap?.has(as)) {\n        ;\n        ({ jwks, age } = jwksMap.get(as));\n        if (age >= 300) {\n            clearJwksCache(as, options?.[jwksCache]);\n            return getPublicSigKeyFromIssuerJwksUri(as, options, header);\n        }\n    }\n    else {\n        jwks = await jwksRequest(as, options).then(processJwksResponse);\n        age = 0;\n        setJwksCache(as, jwks, epochTime(), options?.[jwksCache]);\n    }\n    let kty;\n    switch (alg.slice(0, 2)) {\n        case 'RS':\n        case 'PS':\n            kty = 'RSA';\n            break;\n        case 'ES':\n            kty = 'EC';\n            break;\n        case 'Ed':\n            kty = 'OKP';\n            break;\n        default:\n            throw new UnsupportedOperationError('unsupported JWS algorithm', { cause: { alg } });\n    }\n    const candidates = jwks.keys.filter((jwk) => {\n        if (jwk.kty !== kty) {\n            return false;\n        }\n        if (kid !== undefined && kid !== jwk.kid) {\n            return false;\n        }\n        if (jwk.alg !== undefined && alg !== jwk.alg) {\n            return false;\n        }\n        if (jwk.use !== undefined && jwk.use !== 'sig') {\n            return false;\n        }\n        if (jwk.key_ops?.includes('verify') === false) {\n            return false;\n        }\n        switch (true) {\n            case alg === 'ES256' && jwk.crv !== 'P-256':\n            case alg === 'ES384' && jwk.crv !== 'P-384':\n            case alg === 'ES512' && jwk.crv !== 'P-521':\n            case alg === 'Ed25519' && jwk.crv !== 'Ed25519':\n            case alg === 'EdDSA' && jwk.crv !== 'Ed25519':\n                return false;\n        }\n        return true;\n    });\n    const { 0: jwk, length } = candidates;\n    if (!length) {\n        if (age >= 60) {\n            clearJwksCache(as, options?.[jwksCache]);\n            return getPublicSigKeyFromIssuerJwksUri(as, options, header);\n        }\n        throw OPE('error when selecting a JWT verification key, no applicable keys found', KEY_SELECTION, { header, candidates, jwks_uri: new URL(as.jwks_uri) });\n    }\n    if (length !== 1) {\n        throw OPE('error when selecting a JWT verification key, multiple applicable keys found, a \"kid\" JWT Header Parameter is required', KEY_SELECTION, { header, candidates, jwks_uri: new URL(as.jwks_uri) });\n    }\n    return importJwk(alg, jwk);\n}\nconst skipSubjectCheck = Symbol();\nfunction getContentType(input) {\n    return input.headers.get('content-type')?.split(';')[0];\n}\nasync function processUserInfoResponse(as, client, expectedSubject, response, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    checkAuthenticationChallenges(response);\n    if (response.status !== 200) {\n        throw OPE('\"response\" is not a conform UserInfo Endpoint response (unexpected HTTP status code)', RESPONSE_IS_NOT_CONFORM, response);\n    }\n    assertReadableResponse(response);\n    let json;\n    if (getContentType(response) === 'application/jwt') {\n        const { claims, jwt } = await validateJwt(await response.text(), checkSigningAlgorithm.bind(undefined, client.userinfo_signed_response_alg, as.userinfo_signing_alg_values_supported, undefined), getClockSkew(client), getClockTolerance(client), options?.[jweDecrypt])\n            .then(validateOptionalAudience.bind(undefined, client.client_id))\n            .then(validateOptionalIssuer.bind(undefined, as));\n        jwtRefs.set(response, jwt);\n        json = claims;\n    }\n    else {\n        if (client.userinfo_signed_response_alg) {\n            throw OPE('JWT UserInfo Response expected', JWT_USERINFO_EXPECTED, response);\n        }\n        json = await getResponseJsonBody(response);\n    }\n    assertString(json.sub, '\"response\" body \"sub\" property', INVALID_RESPONSE, { body: json });\n    switch (expectedSubject) {\n        case skipSubjectCheck:\n            break;\n        default:\n            assertString(expectedSubject, '\"expectedSubject\"');\n            if (json.sub !== expectedSubject) {\n                throw OPE('unexpected \"response\" body \"sub\" property value', JSON_ATTRIBUTE_COMPARISON, {\n                    expected: expectedSubject,\n                    body: json,\n                    attribute: 'sub',\n                });\n            }\n    }\n    return json;\n}\nasync function authenticatedRequest(as, client, clientAuthentication, url, body, headers, options) {\n    await clientAuthentication(as, client, body, headers);\n    headers.set('content-type', 'application/x-www-form-urlencoded;charset=UTF-8');\n    return (options?.[customFetch] || fetch)(url.href, {\n        body,\n        headers: Object.fromEntries(headers.entries()),\n        method: 'POST',\n        redirect: 'manual',\n        signal: signal(url, options?.signal),\n    });\n}\nasync function tokenEndpointRequest(as, client, clientAuthentication, grantType, parameters, options) {\n    const url = resolveEndpoint(as, 'token_endpoint', client.use_mtls_endpoint_aliases, options?.[allowInsecureRequests] !== true);\n    parameters.set('grant_type', grantType);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    if (options?.DPoP !== undefined) {\n        assertDPoP(options.DPoP);\n        await options.DPoP.addProof(url, headers, 'POST');\n    }\n    const response = await authenticatedRequest(as, client, clientAuthentication, url, parameters, headers, options);\n    options?.DPoP?.cacheNonce(response);\n    return response;\n}\nasync function refreshTokenGrantRequest(as, client, clientAuthentication, refreshToken, options) {\n    assertAs(as);\n    assertClient(client);\n    assertString(refreshToken, '\"refreshToken\"');\n    const parameters = new URLSearchParams(options?.additionalParameters);\n    parameters.set('refresh_token', refreshToken);\n    return tokenEndpointRequest(as, client, clientAuthentication, 'refresh_token', parameters, options);\n}\nconst idTokenClaims = new WeakMap();\nconst jwtRefs = new WeakMap();\nfunction getValidatedIdTokenClaims(ref) {\n    if (!ref.id_token) {\n        return undefined;\n    }\n    const claims = idTokenClaims.get(ref);\n    if (!claims) {\n        throw CodedTypeError('\"ref\" was already garbage collected or did not resolve from the proper sources', ERR_INVALID_ARG_VALUE);\n    }\n    return claims;\n}\nasync function validateApplicationLevelSignature(as, ref, options) {\n    assertAs(as);\n    if (!jwtRefs.has(ref)) {\n        throw CodedTypeError('\"ref\" does not contain a processed JWT Response to verify the signature of', ERR_INVALID_ARG_VALUE);\n    }\n    const { 0: protectedHeader, 1: payload, 2: encodedSignature } = jwtRefs.get(ref).split('.');\n    const header = JSON.parse(buf(b64u(protectedHeader)));\n    if (header.alg.startsWith('HS')) {\n        throw new UnsupportedOperationError('unsupported JWS algorithm', { cause: { alg: header.alg } });\n    }\n    let key;\n    key = await getPublicSigKeyFromIssuerJwksUri(as, options, header);\n    await validateJwsSignature(protectedHeader, payload, key, b64u(encodedSignature));\n}\nasync function processGenericAccessTokenResponse(as, client, response, additionalRequiredIdTokenClaims, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    checkAuthenticationChallenges(response);\n    await checkOAuthBodyError(response, 200, 'Token Endpoint');\n    assertReadableResponse(response);\n    const json = await getResponseJsonBody(response);\n    assertString(json.access_token, '\"response\" body \"access_token\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    assertString(json.token_type, '\"response\" body \"token_type\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    json.token_type = json.token_type.toLowerCase();\n    if (json.token_type !== 'dpop' && json.token_type !== 'bearer') {\n        throw new UnsupportedOperationError('unsupported `token_type` value', { cause: { body: json } });\n    }\n    if (json.expires_in !== undefined) {\n        let expiresIn = typeof json.expires_in !== 'number' ? parseFloat(json.expires_in) : json.expires_in;\n        assertNumber(expiresIn, true, '\"response\" body \"expires_in\" property', INVALID_RESPONSE, {\n            body: json,\n        });\n        json.expires_in = expiresIn;\n    }\n    if (json.refresh_token !== undefined) {\n        assertString(json.refresh_token, '\"response\" body \"refresh_token\" property', INVALID_RESPONSE, {\n            body: json,\n        });\n    }\n    if (json.scope !== undefined && typeof json.scope !== 'string') {\n        throw OPE('\"response\" body \"scope\" property must be a string', INVALID_RESPONSE, { body: json });\n    }\n    if (json.id_token !== undefined) {\n        assertString(json.id_token, '\"response\" body \"id_token\" property', INVALID_RESPONSE, {\n            body: json,\n        });\n        const requiredClaims = ['aud', 'exp', 'iat', 'iss', 'sub'];\n        if (client.require_auth_time === true) {\n            requiredClaims.push('auth_time');\n        }\n        if (client.default_max_age !== undefined) {\n            assertNumber(client.default_max_age, true, '\"client.default_max_age\"');\n            requiredClaims.push('auth_time');\n        }\n        if (additionalRequiredIdTokenClaims?.length) {\n            requiredClaims.push(...additionalRequiredIdTokenClaims);\n        }\n        const { claims, jwt } = await validateJwt(json.id_token, checkSigningAlgorithm.bind(undefined, client.id_token_signed_response_alg, as.id_token_signing_alg_values_supported, 'RS256'), getClockSkew(client), getClockTolerance(client), options?.[jweDecrypt])\n            .then(validatePresence.bind(undefined, requiredClaims))\n            .then(validateIssuer.bind(undefined, as))\n            .then(validateAudience.bind(undefined, client.client_id));\n        if (Array.isArray(claims.aud) && claims.aud.length !== 1) {\n            if (claims.azp === undefined) {\n                throw OPE('ID Token \"aud\" (audience) claim includes additional untrusted audiences', JWT_CLAIM_COMPARISON, { claims, claim: 'aud' });\n            }\n            if (claims.azp !== client.client_id) {\n                throw OPE('unexpected ID Token \"azp\" (authorized party) claim value', JWT_CLAIM_COMPARISON, { expected: client.client_id, claims, claim: 'azp' });\n            }\n        }\n        if (claims.auth_time !== undefined) {\n            assertNumber(claims.auth_time, false, 'ID Token \"auth_time\" (authentication time)', INVALID_RESPONSE, { claims });\n        }\n        jwtRefs.set(response, jwt);\n        idTokenClaims.set(json, claims);\n    }\n    return json;\n}\nfunction checkAuthenticationChallenges(response) {\n    let challenges;\n    if ((challenges = parseWwwAuthenticateChallenges(response))) {\n        throw new WWWAuthenticateChallengeError('server responded with a challenge in the WWW-Authenticate HTTP Header', { cause: challenges, response });\n    }\n}\nasync function processRefreshTokenResponse(as, client, response, options) {\n    return processGenericAccessTokenResponse(as, client, response, undefined, options);\n}\nfunction validateOptionalAudience(expected, result) {\n    if (result.claims.aud !== undefined) {\n        return validateAudience(expected, result);\n    }\n    return result;\n}\nfunction validateAudience(expected, result) {\n    if (Array.isArray(result.claims.aud)) {\n        if (!result.claims.aud.includes(expected)) {\n            throw OPE('unexpected JWT \"aud\" (audience) claim value', JWT_CLAIM_COMPARISON, {\n                expected,\n                claims: result.claims,\n                claim: 'aud',\n            });\n        }\n    }\n    else if (result.claims.aud !== expected) {\n        throw OPE('unexpected JWT \"aud\" (audience) claim value', JWT_CLAIM_COMPARISON, {\n            expected,\n            claims: result.claims,\n            claim: 'aud',\n        });\n    }\n    return result;\n}\nfunction validateOptionalIssuer(as, result) {\n    if (result.claims.iss !== undefined) {\n        return validateIssuer(as, result);\n    }\n    return result;\n}\nfunction validateIssuer(as, result) {\n    const expected = as[_expectedIssuer]?.(result) ?? as.issuer;\n    if (result.claims.iss !== expected) {\n        throw OPE('unexpected JWT \"iss\" (issuer) claim value', JWT_CLAIM_COMPARISON, {\n            expected,\n            claims: result.claims,\n            claim: 'iss',\n        });\n    }\n    return result;\n}\nconst branded = new WeakSet();\nfunction brand(searchParams) {\n    branded.add(searchParams);\n    return searchParams;\n}\nconst nopkce = Symbol();\nasync function authorizationCodeGrantRequest(as, client, clientAuthentication, callbackParameters, redirectUri, codeVerifier, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!branded.has(callbackParameters)) {\n        throw CodedTypeError('\"callbackParameters\" must be an instance of URLSearchParams obtained from \"validateAuthResponse()\", or \"validateJwtAuthResponse()', ERR_INVALID_ARG_VALUE);\n    }\n    assertString(redirectUri, '\"redirectUri\"');\n    const code = getURLSearchParameter(callbackParameters, 'code');\n    if (!code) {\n        throw OPE('no authorization code in \"callbackParameters\"', INVALID_RESPONSE);\n    }\n    const parameters = new URLSearchParams(options?.additionalParameters);\n    parameters.set('redirect_uri', redirectUri);\n    parameters.set('code', code);\n    if (codeVerifier !== nopkce) {\n        assertString(codeVerifier, '\"codeVerifier\"');\n        parameters.set('code_verifier', codeVerifier);\n    }\n    return tokenEndpointRequest(as, client, clientAuthentication, 'authorization_code', parameters, options);\n}\nconst jwtClaimNames = {\n    aud: 'audience',\n    c_hash: 'code hash',\n    client_id: 'client id',\n    exp: 'expiration time',\n    iat: 'issued at',\n    iss: 'issuer',\n    jti: 'jwt id',\n    nonce: 'nonce',\n    s_hash: 'state hash',\n    sub: 'subject',\n    ath: 'access token hash',\n    htm: 'http method',\n    htu: 'http uri',\n    cnf: 'confirmation',\n    auth_time: 'authentication time',\n};\nfunction validatePresence(required, result) {\n    for (const claim of required) {\n        if (result.claims[claim] === undefined) {\n            throw OPE(`JWT \"${claim}\" (${jwtClaimNames[claim]}) claim missing`, INVALID_RESPONSE, {\n                claims: result.claims,\n            });\n        }\n    }\n    return result;\n}\nconst expectNoNonce = Symbol();\nconst skipAuthTimeCheck = Symbol();\nasync function processAuthorizationCodeResponse(as, client, response, options) {\n    if (typeof options?.expectedNonce === 'string' ||\n        typeof options?.maxAge === 'number' ||\n        options?.requireIdToken) {\n        return processAuthorizationCodeOpenIDResponse(as, client, response, options.expectedNonce, options.maxAge, {\n            [jweDecrypt]: options[jweDecrypt],\n        });\n    }\n    return processAuthorizationCodeOAuth2Response(as, client, response, options);\n}\nasync function processAuthorizationCodeOpenIDResponse(as, client, response, expectedNonce, maxAge, options) {\n    const additionalRequiredClaims = [];\n    switch (expectedNonce) {\n        case undefined:\n            expectedNonce = expectNoNonce;\n            break;\n        case expectNoNonce:\n            break;\n        default:\n            assertString(expectedNonce, '\"expectedNonce\" argument');\n            additionalRequiredClaims.push('nonce');\n    }\n    maxAge ??= client.default_max_age;\n    switch (maxAge) {\n        case undefined:\n            maxAge = skipAuthTimeCheck;\n            break;\n        case skipAuthTimeCheck:\n            break;\n        default:\n            assertNumber(maxAge, true, '\"maxAge\" argument');\n            additionalRequiredClaims.push('auth_time');\n    }\n    const result = await processGenericAccessTokenResponse(as, client, response, additionalRequiredClaims, options);\n    assertString(result.id_token, '\"response\" body \"id_token\" property', INVALID_RESPONSE, {\n        body: result,\n    });\n    const claims = getValidatedIdTokenClaims(result);\n    if (maxAge !== skipAuthTimeCheck) {\n        const now = epochTime() + getClockSkew(client);\n        const tolerance = getClockTolerance(client);\n        if (claims.auth_time + maxAge < now - tolerance) {\n            throw OPE('too much time has elapsed since the last End-User authentication', JWT_TIMESTAMP_CHECK, { claims, now, tolerance, claim: 'auth_time' });\n        }\n    }\n    if (expectedNonce === expectNoNonce) {\n        if (claims.nonce !== undefined) {\n            throw OPE('unexpected ID Token \"nonce\" claim value', JWT_CLAIM_COMPARISON, {\n                expected: undefined,\n                claims,\n                claim: 'nonce',\n            });\n        }\n    }\n    else if (claims.nonce !== expectedNonce) {\n        throw OPE('unexpected ID Token \"nonce\" claim value', JWT_CLAIM_COMPARISON, {\n            expected: expectedNonce,\n            claims,\n            claim: 'nonce',\n        });\n    }\n    return result;\n}\nasync function processAuthorizationCodeOAuth2Response(as, client, response, options) {\n    const result = await processGenericAccessTokenResponse(as, client, response, undefined, options);\n    const claims = getValidatedIdTokenClaims(result);\n    if (claims) {\n        if (client.default_max_age !== undefined) {\n            assertNumber(client.default_max_age, true, '\"client.default_max_age\"');\n            const now = epochTime() + getClockSkew(client);\n            const tolerance = getClockTolerance(client);\n            if (claims.auth_time + client.default_max_age < now - tolerance) {\n                throw OPE('too much time has elapsed since the last End-User authentication', JWT_TIMESTAMP_CHECK, { claims, now, tolerance, claim: 'auth_time' });\n            }\n        }\n        if (claims.nonce !== undefined) {\n            throw OPE('unexpected ID Token \"nonce\" claim value', JWT_CLAIM_COMPARISON, {\n                expected: undefined,\n                claims,\n                claim: 'nonce',\n            });\n        }\n    }\n    return result;\n}\nconst WWW_AUTHENTICATE_CHALLENGE = 'OAUTH_WWW_AUTHENTICATE_CHALLENGE';\nconst RESPONSE_BODY_ERROR = 'OAUTH_RESPONSE_BODY_ERROR';\nconst UNSUPPORTED_OPERATION = 'OAUTH_UNSUPPORTED_OPERATION';\nconst AUTHORIZATION_RESPONSE_ERROR = 'OAUTH_AUTHORIZATION_RESPONSE_ERROR';\nconst JWT_USERINFO_EXPECTED = 'OAUTH_JWT_USERINFO_EXPECTED';\nconst PARSE_ERROR = 'OAUTH_PARSE_ERROR';\nconst INVALID_RESPONSE = 'OAUTH_INVALID_RESPONSE';\nconst INVALID_REQUEST = 'OAUTH_INVALID_REQUEST';\nconst RESPONSE_IS_NOT_JSON = 'OAUTH_RESPONSE_IS_NOT_JSON';\nconst RESPONSE_IS_NOT_CONFORM = 'OAUTH_RESPONSE_IS_NOT_CONFORM';\nconst HTTP_REQUEST_FORBIDDEN = 'OAUTH_HTTP_REQUEST_FORBIDDEN';\nconst REQUEST_PROTOCOL_FORBIDDEN = 'OAUTH_REQUEST_PROTOCOL_FORBIDDEN';\nconst JWT_TIMESTAMP_CHECK = 'OAUTH_JWT_TIMESTAMP_CHECK_FAILED';\nconst JWT_CLAIM_COMPARISON = 'OAUTH_JWT_CLAIM_COMPARISON_FAILED';\nconst JSON_ATTRIBUTE_COMPARISON = 'OAUTH_JSON_ATTRIBUTE_COMPARISON_FAILED';\nconst KEY_SELECTION = 'OAUTH_KEY_SELECTION_FAILED';\nconst MISSING_SERVER_METADATA = 'OAUTH_MISSING_SERVER_METADATA';\nconst INVALID_SERVER_METADATA = 'OAUTH_INVALID_SERVER_METADATA';\nfunction checkJwtType(expected, result) {\n    if (typeof result.header.typ !== 'string' || normalizeTyp(result.header.typ) !== expected) {\n        throw OPE('unexpected JWT \"typ\" header parameter value', INVALID_RESPONSE, {\n            header: result.header,\n        });\n    }\n    return result;\n}\nasync function clientCredentialsGrantRequest(as, client, clientAuthentication, parameters, options) {\n    assertAs(as);\n    assertClient(client);\n    return tokenEndpointRequest(as, client, clientAuthentication, 'client_credentials', new URLSearchParams(parameters), options);\n}\nasync function genericTokenEndpointRequest(as, client, clientAuthentication, grantType, parameters, options) {\n    assertAs(as);\n    assertClient(client);\n    assertString(grantType, '\"grantType\"');\n    return tokenEndpointRequest(as, client, clientAuthentication, grantType, new URLSearchParams(parameters), options);\n}\nasync function processGenericTokenEndpointResponse(as, client, response, options) {\n    return processGenericAccessTokenResponse(as, client, response, undefined, options);\n}\nasync function processClientCredentialsResponse(as, client, response, options) {\n    return processGenericAccessTokenResponse(as, client, response, undefined, options);\n}\nasync function revocationRequest(as, client, clientAuthentication, token, options) {\n    assertAs(as);\n    assertClient(client);\n    assertString(token, '\"token\"');\n    const url = resolveEndpoint(as, 'revocation_endpoint', client.use_mtls_endpoint_aliases, options?.[allowInsecureRequests] !== true);\n    const body = new URLSearchParams(options?.additionalParameters);\n    body.set('token', token);\n    const headers = prepareHeaders(options?.headers);\n    headers.delete('accept');\n    return authenticatedRequest(as, client, clientAuthentication, url, body, headers, options);\n}\nasync function processRevocationResponse(response) {\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    checkAuthenticationChallenges(response);\n    await checkOAuthBodyError(response, 200, 'Revocation Endpoint');\n    return undefined;\n}\nfunction assertReadableResponse(response) {\n    if (response.bodyUsed) {\n        throw CodedTypeError('\"response\" body has been used already', ERR_INVALID_ARG_VALUE);\n    }\n}\nasync function introspectionRequest(as, client, clientAuthentication, token, options) {\n    assertAs(as);\n    assertClient(client);\n    assertString(token, '\"token\"');\n    const url = resolveEndpoint(as, 'introspection_endpoint', client.use_mtls_endpoint_aliases, options?.[allowInsecureRequests] !== true);\n    const body = new URLSearchParams(options?.additionalParameters);\n    body.set('token', token);\n    const headers = prepareHeaders(options?.headers);\n    if (options?.requestJwtResponse ?? client.introspection_signed_response_alg) {\n        headers.set('accept', 'application/token-introspection+jwt');\n    }\n    else {\n        headers.set('accept', 'application/json');\n    }\n    return authenticatedRequest(as, client, clientAuthentication, url, body, headers, options);\n}\nasync function processIntrospectionResponse(as, client, response, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    checkAuthenticationChallenges(response);\n    await checkOAuthBodyError(response, 200, 'Introspection Endpoint');\n    let json;\n    if (getContentType(response) === 'application/token-introspection+jwt') {\n        assertReadableResponse(response);\n        const { claims, jwt } = await validateJwt(await response.text(), checkSigningAlgorithm.bind(undefined, client.introspection_signed_response_alg, as.introspection_signing_alg_values_supported, 'RS256'), getClockSkew(client), getClockTolerance(client), options?.[jweDecrypt])\n            .then(checkJwtType.bind(undefined, 'token-introspection+jwt'))\n            .then(validatePresence.bind(undefined, ['aud', 'iat', 'iss']))\n            .then(validateIssuer.bind(undefined, as))\n            .then(validateAudience.bind(undefined, client.client_id));\n        jwtRefs.set(response, jwt);\n        if (!isJsonObject(claims.token_introspection)) {\n            throw OPE('JWT \"token_introspection\" claim must be a JSON object', INVALID_RESPONSE, {\n                claims,\n            });\n        }\n        json = claims.token_introspection;\n    }\n    else {\n        assertReadableResponse(response);\n        json = await getResponseJsonBody(response);\n    }\n    if (typeof json.active !== 'boolean') {\n        throw OPE('\"response\" body \"active\" property must be a boolean', INVALID_RESPONSE, {\n            body: json,\n        });\n    }\n    return json;\n}\nasync function jwksRequest(as, options) {\n    assertAs(as);\n    const url = resolveEndpoint(as, 'jwks_uri', false, options?.[allowInsecureRequests] !== true);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    headers.append('accept', 'application/jwk-set+json');\n    return (options?.[customFetch] || fetch)(url.href, {\n        body: undefined,\n        headers: Object.fromEntries(headers.entries()),\n        method: 'GET',\n        redirect: 'manual',\n        signal: signal(url, options?.signal),\n    });\n}\nasync function processJwksResponse(response) {\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    if (response.status !== 200) {\n        throw OPE('\"response\" is not a conform JSON Web Key Set response (unexpected HTTP status code)', RESPONSE_IS_NOT_CONFORM, response);\n    }\n    assertReadableResponse(response);\n    const json = await getResponseJsonBody(response, (response) => assertContentTypes(response, 'application/json', 'application/jwk-set+json'));\n    if (!Array.isArray(json.keys)) {\n        throw OPE('\"response\" body \"keys\" property must be an array', INVALID_RESPONSE, { body: json });\n    }\n    if (!Array.prototype.every.call(json.keys, isJsonObject)) {\n        throw OPE('\"response\" body \"keys\" property members must be JWK formatted objects', INVALID_RESPONSE, { body: json });\n    }\n    return json;\n}\nfunction supported(alg) {\n    switch (alg) {\n        case 'PS256':\n        case 'ES256':\n        case 'RS256':\n        case 'PS384':\n        case 'ES384':\n        case 'RS384':\n        case 'PS512':\n        case 'ES512':\n        case 'RS512':\n        case 'Ed25519':\n        case 'EdDSA':\n            return true;\n        default:\n            return false;\n    }\n}\nfunction checkSupportedJwsAlg(header) {\n    if (!supported(header.alg)) {\n        throw new UnsupportedOperationError('unsupported JWS \"alg\" identifier', {\n            cause: { alg: header.alg },\n        });\n    }\n}\nfunction checkRsaKeyAlgorithm(key) {\n    const { algorithm } = key;\n    if (typeof algorithm.modulusLength !== 'number' || algorithm.modulusLength < 2048) {\n        throw new UnsupportedOperationError(`unsupported ${algorithm.name} modulusLength`, {\n            cause: key,\n        });\n    }\n}\nfunction ecdsaHashName(key) {\n    const { algorithm } = key;\n    switch (algorithm.namedCurve) {\n        case 'P-256':\n            return 'SHA-256';\n        case 'P-384':\n            return 'SHA-384';\n        case 'P-521':\n            return 'SHA-512';\n        default:\n            throw new UnsupportedOperationError('unsupported ECDSA namedCurve', { cause: key });\n    }\n}\nfunction keyToSubtle(key) {\n    switch (key.algorithm.name) {\n        case 'ECDSA':\n            return {\n                name: key.algorithm.name,\n                hash: ecdsaHashName(key),\n            };\n        case 'RSA-PSS': {\n            checkRsaKeyAlgorithm(key);\n            switch (key.algorithm.hash.name) {\n                case 'SHA-256':\n                case 'SHA-384':\n                case 'SHA-512':\n                    return {\n                        name: key.algorithm.name,\n                        saltLength: parseInt(key.algorithm.hash.name.slice(-3), 10) >> 3,\n                    };\n                default:\n                    throw new UnsupportedOperationError('unsupported RSA-PSS hash name', { cause: key });\n            }\n        }\n        case 'RSASSA-PKCS1-v1_5':\n            checkRsaKeyAlgorithm(key);\n            return key.algorithm.name;\n        case 'Ed25519':\n            return key.algorithm.name;\n    }\n    throw new UnsupportedOperationError('unsupported CryptoKey algorithm name', { cause: key });\n}\nasync function validateJwsSignature(protectedHeader, payload, key, signature) {\n    const data = buf(`${protectedHeader}.${payload}`);\n    const algorithm = keyToSubtle(key);\n    const verified = await crypto.subtle.verify(algorithm, key, signature, data);\n    if (!verified) {\n        throw OPE('JWT signature verification failed', INVALID_RESPONSE, {\n            key,\n            data,\n            signature,\n            algorithm,\n        });\n    }\n}\nasync function validateJwt(jws, checkAlg, clockSkew, clockTolerance, decryptJwt) {\n    let { 0: protectedHeader, 1: payload, length } = jws.split('.');\n    if (length === 5) {\n        if (decryptJwt !== undefined) {\n            jws = await decryptJwt(jws);\n            ({ 0: protectedHeader, 1: payload, length } = jws.split('.'));\n        }\n        else {\n            throw new UnsupportedOperationError('JWE decryption is not configured', { cause: jws });\n        }\n    }\n    if (length !== 3) {\n        throw OPE('Invalid JWT', INVALID_RESPONSE, jws);\n    }\n    let header;\n    try {\n        header = JSON.parse(buf(b64u(protectedHeader)));\n    }\n    catch (cause) {\n        throw OPE('failed to parse JWT Header body as base64url encoded JSON', PARSE_ERROR, cause);\n    }\n    if (!isJsonObject(header)) {\n        throw OPE('JWT Header must be a top level object', INVALID_RESPONSE, jws);\n    }\n    checkAlg(header);\n    if (header.crit !== undefined) {\n        throw new UnsupportedOperationError('no JWT \"crit\" header parameter extensions are supported', {\n            cause: { header },\n        });\n    }\n    let claims;\n    try {\n        claims = JSON.parse(buf(b64u(payload)));\n    }\n    catch (cause) {\n        throw OPE('failed to parse JWT Payload body as base64url encoded JSON', PARSE_ERROR, cause);\n    }\n    if (!isJsonObject(claims)) {\n        throw OPE('JWT Payload must be a top level object', INVALID_RESPONSE, jws);\n    }\n    const now = epochTime() + clockSkew;\n    if (claims.exp !== undefined) {\n        if (typeof claims.exp !== 'number') {\n            throw OPE('unexpected JWT \"exp\" (expiration time) claim type', INVALID_RESPONSE, { claims });\n        }\n        if (claims.exp <= now - clockTolerance) {\n            throw OPE('unexpected JWT \"exp\" (expiration time) claim value, expiration is past current timestamp', JWT_TIMESTAMP_CHECK, { claims, now, tolerance: clockTolerance, claim: 'exp' });\n        }\n    }\n    if (claims.iat !== undefined) {\n        if (typeof claims.iat !== 'number') {\n            throw OPE('unexpected JWT \"iat\" (issued at) claim type', INVALID_RESPONSE, { claims });\n        }\n    }\n    if (claims.iss !== undefined) {\n        if (typeof claims.iss !== 'string') {\n            throw OPE('unexpected JWT \"iss\" (issuer) claim type', INVALID_RESPONSE, { claims });\n        }\n    }\n    if (claims.nbf !== undefined) {\n        if (typeof claims.nbf !== 'number') {\n            throw OPE('unexpected JWT \"nbf\" (not before) claim type', INVALID_RESPONSE, { claims });\n        }\n        if (claims.nbf > now + clockTolerance) {\n            throw OPE('unexpected JWT \"nbf\" (not before) claim value', JWT_TIMESTAMP_CHECK, {\n                claims,\n                now,\n                tolerance: clockTolerance,\n                claim: 'nbf',\n            });\n        }\n    }\n    if (claims.aud !== undefined) {\n        if (typeof claims.aud !== 'string' && !Array.isArray(claims.aud)) {\n            throw OPE('unexpected JWT \"aud\" (audience) claim type', INVALID_RESPONSE, { claims });\n        }\n    }\n    return { header, claims, jwt: jws };\n}\nasync function validateJwtAuthResponse(as, client, parameters, expectedState, options) {\n    assertAs(as);\n    assertClient(client);\n    if (parameters instanceof URL) {\n        parameters = parameters.searchParams;\n    }\n    if (!(parameters instanceof URLSearchParams)) {\n        throw CodedTypeError('\"parameters\" must be an instance of URLSearchParams, or URL', ERR_INVALID_ARG_TYPE);\n    }\n    const response = getURLSearchParameter(parameters, 'response');\n    if (!response) {\n        throw OPE('\"parameters\" does not contain a JARM response', INVALID_RESPONSE);\n    }\n    const { claims, header, jwt } = await validateJwt(response, checkSigningAlgorithm.bind(undefined, client.authorization_signed_response_alg, as.authorization_signing_alg_values_supported, 'RS256'), getClockSkew(client), getClockTolerance(client), options?.[jweDecrypt])\n        .then(validatePresence.bind(undefined, ['aud', 'exp', 'iss']))\n        .then(validateIssuer.bind(undefined, as))\n        .then(validateAudience.bind(undefined, client.client_id));\n    const { 0: protectedHeader, 1: payload, 2: encodedSignature } = jwt.split('.');\n    const signature = b64u(encodedSignature);\n    const key = await getPublicSigKeyFromIssuerJwksUri(as, options, header);\n    await validateJwsSignature(protectedHeader, payload, key, signature);\n    const result = new URLSearchParams();\n    for (const [key, value] of Object.entries(claims)) {\n        if (typeof value === 'string' && key !== 'aud') {\n            result.set(key, value);\n        }\n    }\n    return validateAuthResponse(as, client, result, expectedState);\n}\nasync function idTokenHash(data, header, claimName) {\n    let algorithm;\n    switch (header.alg) {\n        case 'RS256':\n        case 'PS256':\n        case 'ES256':\n            algorithm = 'SHA-256';\n            break;\n        case 'RS384':\n        case 'PS384':\n        case 'ES384':\n            algorithm = 'SHA-384';\n            break;\n        case 'RS512':\n        case 'PS512':\n        case 'ES512':\n        case 'Ed25519':\n        case 'EdDSA':\n            algorithm = 'SHA-512';\n            break;\n        default:\n            throw new UnsupportedOperationError(`unsupported JWS algorithm for ${claimName} calculation`, { cause: { alg: header.alg } });\n    }\n    const digest = await crypto.subtle.digest(algorithm, buf(data));\n    return b64u(digest.slice(0, digest.byteLength / 2));\n}\nasync function idTokenHashMatches(data, actual, header, claimName) {\n    const expected = await idTokenHash(data, header, claimName);\n    return actual === expected;\n}\nasync function validateDetachedSignatureResponse(as, client, parameters, expectedNonce, expectedState, maxAge, options) {\n    return validateHybridResponse(as, client, parameters, expectedNonce, expectedState, maxAge, options, true);\n}\nasync function validateCodeIdTokenResponse(as, client, parameters, expectedNonce, expectedState, maxAge, options) {\n    return validateHybridResponse(as, client, parameters, expectedNonce, expectedState, maxAge, options, false);\n}\nasync function consumeStream(request) {\n    if (request.bodyUsed) {\n        throw CodedTypeError('form_post Request instances must contain a readable body', ERR_INVALID_ARG_VALUE, { cause: request });\n    }\n    return request.text();\n}\nasync function formPostResponse(request) {\n    if (request.method !== 'POST') {\n        throw CodedTypeError('form_post responses are expected to use the POST method', ERR_INVALID_ARG_VALUE, { cause: request });\n    }\n    if (getContentType(request) !== 'application/x-www-form-urlencoded') {\n        throw CodedTypeError('form_post responses are expected to use the application/x-www-form-urlencoded content-type', ERR_INVALID_ARG_VALUE, { cause: request });\n    }\n    return consumeStream(request);\n}\nasync function validateHybridResponse(as, client, parameters, expectedNonce, expectedState, maxAge, options, fapi) {\n    assertAs(as);\n    assertClient(client);\n    if (parameters instanceof URL) {\n        if (!parameters.hash.length) {\n            throw CodedTypeError('\"parameters\" as an instance of URL must contain a hash (fragment) with the Authorization Response parameters', ERR_INVALID_ARG_VALUE);\n        }\n        parameters = new URLSearchParams(parameters.hash.slice(1));\n    }\n    else if (looseInstanceOf(parameters, Request)) {\n        parameters = new URLSearchParams(await formPostResponse(parameters));\n    }\n    else if (parameters instanceof URLSearchParams) {\n        parameters = new URLSearchParams(parameters);\n    }\n    else {\n        throw CodedTypeError('\"parameters\" must be an instance of URLSearchParams, URL, or Response', ERR_INVALID_ARG_TYPE);\n    }\n    const id_token = getURLSearchParameter(parameters, 'id_token');\n    parameters.delete('id_token');\n    switch (expectedState) {\n        case undefined:\n        case expectNoState:\n            break;\n        default:\n            assertString(expectedState, '\"expectedState\" argument');\n    }\n    const result = validateAuthResponse({\n        ...as,\n        authorization_response_iss_parameter_supported: false,\n    }, client, parameters, expectedState);\n    if (!id_token) {\n        throw OPE('\"parameters\" does not contain an ID Token', INVALID_RESPONSE);\n    }\n    const code = getURLSearchParameter(parameters, 'code');\n    if (!code) {\n        throw OPE('\"parameters\" does not contain an Authorization Code', INVALID_RESPONSE);\n    }\n    const requiredClaims = [\n        'aud',\n        'exp',\n        'iat',\n        'iss',\n        'sub',\n        'nonce',\n        'c_hash',\n    ];\n    const state = parameters.get('state');\n    if (fapi && (typeof expectedState === 'string' || state !== null)) {\n        requiredClaims.push('s_hash');\n    }\n    if (maxAge !== undefined) {\n        assertNumber(maxAge, true, '\"maxAge\" argument');\n    }\n    else if (client.default_max_age !== undefined) {\n        assertNumber(client.default_max_age, true, '\"client.default_max_age\"');\n    }\n    maxAge ??= client.default_max_age ?? skipAuthTimeCheck;\n    if (client.require_auth_time || maxAge !== skipAuthTimeCheck) {\n        requiredClaims.push('auth_time');\n    }\n    const { claims, header, jwt } = await validateJwt(id_token, checkSigningAlgorithm.bind(undefined, client.id_token_signed_response_alg, as.id_token_signing_alg_values_supported, 'RS256'), getClockSkew(client), getClockTolerance(client), options?.[jweDecrypt])\n        .then(validatePresence.bind(undefined, requiredClaims))\n        .then(validateIssuer.bind(undefined, as))\n        .then(validateAudience.bind(undefined, client.client_id));\n    const clockSkew = getClockSkew(client);\n    const now = epochTime() + clockSkew;\n    if (claims.iat < now - 3600) {\n        throw OPE('unexpected JWT \"iat\" (issued at) claim value, it is too far in the past', JWT_TIMESTAMP_CHECK, { now, claims, claim: 'iat' });\n    }\n    assertString(claims.c_hash, 'ID Token \"c_hash\" (code hash) claim value', INVALID_RESPONSE, {\n        claims,\n    });\n    if (claims.auth_time !== undefined) {\n        assertNumber(claims.auth_time, false, 'ID Token \"auth_time\" (authentication time)', INVALID_RESPONSE, { claims });\n    }\n    if (maxAge !== skipAuthTimeCheck) {\n        const now = epochTime() + getClockSkew(client);\n        const tolerance = getClockTolerance(client);\n        if (claims.auth_time + maxAge < now - tolerance) {\n            throw OPE('too much time has elapsed since the last End-User authentication', JWT_TIMESTAMP_CHECK, { claims, now, tolerance, claim: 'auth_time' });\n        }\n    }\n    assertString(expectedNonce, '\"expectedNonce\" argument');\n    if (claims.nonce !== expectedNonce) {\n        throw OPE('unexpected ID Token \"nonce\" claim value', JWT_CLAIM_COMPARISON, {\n            expected: expectedNonce,\n            claims,\n            claim: 'nonce',\n        });\n    }\n    if (Array.isArray(claims.aud) && claims.aud.length !== 1) {\n        if (claims.azp === undefined) {\n            throw OPE('ID Token \"aud\" (audience) claim includes additional untrusted audiences', JWT_CLAIM_COMPARISON, { claims, claim: 'aud' });\n        }\n        if (claims.azp !== client.client_id) {\n            throw OPE('unexpected ID Token \"azp\" (authorized party) claim value', JWT_CLAIM_COMPARISON, {\n                expected: client.client_id,\n                claims,\n                claim: 'azp',\n            });\n        }\n    }\n    const { 0: protectedHeader, 1: payload, 2: encodedSignature } = jwt.split('.');\n    const signature = b64u(encodedSignature);\n    const key = await getPublicSigKeyFromIssuerJwksUri(as, options, header);\n    await validateJwsSignature(protectedHeader, payload, key, signature);\n    if ((await idTokenHashMatches(code, claims.c_hash, header, 'c_hash')) !== true) {\n        throw OPE('invalid ID Token \"c_hash\" (code hash) claim value', JWT_CLAIM_COMPARISON, {\n            code,\n            alg: header.alg,\n            claim: 'c_hash',\n            claims,\n        });\n    }\n    if ((fapi && state !== null) || claims.s_hash !== undefined) {\n        assertString(claims.s_hash, 'ID Token \"s_hash\" (state hash) claim value', INVALID_RESPONSE, {\n            claims,\n        });\n        assertString(state, '\"state\" response parameter', INVALID_RESPONSE, { parameters });\n        if ((await idTokenHashMatches(state, claims.s_hash, header, 's_hash')) !== true) {\n            throw OPE('invalid ID Token \"s_hash\" (state hash) claim value', JWT_CLAIM_COMPARISON, {\n                state,\n                alg: header.alg,\n                claim: 's_hash',\n                claims,\n            });\n        }\n    }\n    return result;\n}\nfunction checkSigningAlgorithm(client, issuer, fallback, header) {\n    if (client !== undefined) {\n        if (typeof client === 'string' ? header.alg !== client : !client.includes(header.alg)) {\n            throw OPE('unexpected JWT \"alg\" header parameter', INVALID_RESPONSE, {\n                header,\n                expected: client,\n                reason: 'client configuration',\n            });\n        }\n        return;\n    }\n    if (Array.isArray(issuer)) {\n        if (!issuer.includes(header.alg)) {\n            throw OPE('unexpected JWT \"alg\" header parameter', INVALID_RESPONSE, {\n                header,\n                expected: issuer,\n                reason: 'authorization server metadata',\n            });\n        }\n        return;\n    }\n    if (fallback !== undefined) {\n        if (typeof fallback === 'string'\n            ? header.alg !== fallback\n            : typeof fallback === 'function'\n                ? !fallback(header.alg)\n                : !fallback.includes(header.alg)) {\n            throw OPE('unexpected JWT \"alg\" header parameter', INVALID_RESPONSE, {\n                header,\n                expected: fallback,\n                reason: 'default value',\n            });\n        }\n        return;\n    }\n    throw OPE('missing client or server configuration to verify used JWT \"alg\" header parameter', undefined, { client, issuer, fallback });\n}\nfunction getURLSearchParameter(parameters, name) {\n    const { 0: value, length } = parameters.getAll(name);\n    if (length > 1) {\n        throw OPE(`\"${name}\" parameter must be provided only once`, INVALID_RESPONSE);\n    }\n    return value;\n}\nconst skipStateCheck = Symbol();\nconst expectNoState = Symbol();\nfunction validateAuthResponse(as, client, parameters, expectedState) {\n    assertAs(as);\n    assertClient(client);\n    if (parameters instanceof URL) {\n        parameters = parameters.searchParams;\n    }\n    if (!(parameters instanceof URLSearchParams)) {\n        throw CodedTypeError('\"parameters\" must be an instance of URLSearchParams, or URL', ERR_INVALID_ARG_TYPE);\n    }\n    if (getURLSearchParameter(parameters, 'response')) {\n        throw OPE('\"parameters\" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()', INVALID_RESPONSE, { parameters });\n    }\n    const iss = getURLSearchParameter(parameters, 'iss');\n    const state = getURLSearchParameter(parameters, 'state');\n    if (!iss && as.authorization_response_iss_parameter_supported) {\n        throw OPE('response parameter \"iss\" (issuer) missing', INVALID_RESPONSE, { parameters });\n    }\n    if (iss && iss !== as.issuer) {\n        throw OPE('unexpected \"iss\" (issuer) response parameter value', INVALID_RESPONSE, {\n            expected: as.issuer,\n            parameters,\n        });\n    }\n    switch (expectedState) {\n        case undefined:\n        case expectNoState:\n            if (state !== undefined) {\n                throw OPE('unexpected \"state\" response parameter encountered', INVALID_RESPONSE, {\n                    expected: undefined,\n                    parameters,\n                });\n            }\n            break;\n        case skipStateCheck:\n            break;\n        default:\n            assertString(expectedState, '\"expectedState\" argument');\n            if (state !== expectedState) {\n                throw OPE(state === undefined\n                    ? 'response parameter \"state\" missing'\n                    : 'unexpected \"state\" response parameter value', INVALID_RESPONSE, { expected: expectedState, parameters });\n            }\n    }\n    const error = getURLSearchParameter(parameters, 'error');\n    if (error) {\n        throw new AuthorizationResponseError('authorization response from the server is an error', {\n            cause: parameters,\n        });\n    }\n    const id_token = getURLSearchParameter(parameters, 'id_token');\n    const token = getURLSearchParameter(parameters, 'token');\n    if (id_token !== undefined || token !== undefined) {\n        throw new UnsupportedOperationError('implicit and hybrid flows are not supported');\n    }\n    return brand(new URLSearchParams(parameters));\n}\nfunction algToSubtle(alg) {\n    switch (alg) {\n        case 'PS256':\n        case 'PS384':\n        case 'PS512':\n            return { name: 'RSA-PSS', hash: `SHA-${alg.slice(-3)}` };\n        case 'RS256':\n        case 'RS384':\n        case 'RS512':\n            return { name: 'RSASSA-PKCS1-v1_5', hash: `SHA-${alg.slice(-3)}` };\n        case 'ES256':\n        case 'ES384':\n            return { name: 'ECDSA', namedCurve: `P-${alg.slice(-3)}` };\n        case 'ES512':\n            return { name: 'ECDSA', namedCurve: 'P-521' };\n        case 'Ed25519':\n        case 'EdDSA':\n            return 'Ed25519';\n        default:\n            throw new UnsupportedOperationError('unsupported JWS algorithm', { cause: { alg } });\n    }\n}\nasync function importJwk(alg, jwk) {\n    const { ext, key_ops, use, ...key } = jwk;\n    return crypto.subtle.importKey('jwk', key, algToSubtle(alg), true, ['verify']);\n}\nasync function deviceAuthorizationRequest(as, client, clientAuthentication, parameters, options) {\n    assertAs(as);\n    assertClient(client);\n    const url = resolveEndpoint(as, 'device_authorization_endpoint', client.use_mtls_endpoint_aliases, options?.[allowInsecureRequests] !== true);\n    const body = new URLSearchParams(parameters);\n    body.set('client_id', client.client_id);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    return authenticatedRequest(as, client, clientAuthentication, url, body, headers, options);\n}\nasync function processDeviceAuthorizationResponse(as, client, response) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    checkAuthenticationChallenges(response);\n    await checkOAuthBodyError(response, 200, 'Device Authorization Endpoint');\n    assertReadableResponse(response);\n    const json = await getResponseJsonBody(response);\n    assertString(json.device_code, '\"response\" body \"device_code\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    assertString(json.user_code, '\"response\" body \"user_code\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    assertString(json.verification_uri, '\"response\" body \"verification_uri\" property', INVALID_RESPONSE, { body: json });\n    let expiresIn = typeof json.expires_in !== 'number' ? parseFloat(json.expires_in) : json.expires_in;\n    assertNumber(expiresIn, true, '\"response\" body \"expires_in\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    json.expires_in = expiresIn;\n    if (json.verification_uri_complete !== undefined) {\n        assertString(json.verification_uri_complete, '\"response\" body \"verification_uri_complete\" property', INVALID_RESPONSE, { body: json });\n    }\n    if (json.interval !== undefined) {\n        assertNumber(json.interval, false, '\"response\" body \"interval\" property', INVALID_RESPONSE, {\n            body: json,\n        });\n    }\n    return json;\n}\nasync function deviceCodeGrantRequest(as, client, clientAuthentication, deviceCode, options) {\n    assertAs(as);\n    assertClient(client);\n    assertString(deviceCode, '\"deviceCode\"');\n    const parameters = new URLSearchParams(options?.additionalParameters);\n    parameters.set('device_code', deviceCode);\n    return tokenEndpointRequest(as, client, clientAuthentication, 'urn:ietf:params:oauth:grant-type:device_code', parameters, options);\n}\nasync function processDeviceCodeResponse(as, client, response, options) {\n    return processGenericAccessTokenResponse(as, client, response, undefined, options);\n}\nasync function generateKeyPair(alg, options) {\n    assertString(alg, '\"alg\"');\n    const algorithm = algToSubtle(alg);\n    if (alg.startsWith('PS') || alg.startsWith('RS')) {\n        Object.assign(algorithm, {\n            modulusLength: options?.modulusLength ?? 2048,\n            publicExponent: new Uint8Array([0x01, 0x00, 0x01]),\n        });\n    }\n    return crypto.subtle.generateKey(algorithm, options?.extractable ?? false, [\n        'sign',\n        'verify',\n    ]);\n}\nfunction normalizeHtu(htu) {\n    const url = new URL(htu);\n    url.search = '';\n    url.hash = '';\n    return url.href;\n}\nasync function validateDPoP(request, accessToken, accessTokenClaims, options) {\n    const headerValue = request.headers.get('dpop');\n    if (headerValue === null) {\n        throw OPE('operation indicated DPoP use but the request has no DPoP HTTP Header', INVALID_REQUEST, { headers: request.headers });\n    }\n    if (request.headers.get('authorization')?.toLowerCase().startsWith('dpop ') === false) {\n        throw OPE(`operation indicated DPoP use but the request's Authorization HTTP Header scheme is not DPoP`, INVALID_REQUEST, { headers: request.headers });\n    }\n    if (typeof accessTokenClaims.cnf?.jkt !== 'string') {\n        throw OPE('operation indicated DPoP use but the JWT Access Token has no jkt confirmation claim', INVALID_REQUEST, { claims: accessTokenClaims });\n    }\n    const clockSkew = getClockSkew(options);\n    const proof = await validateJwt(headerValue, checkSigningAlgorithm.bind(undefined, options?.signingAlgorithms, undefined, supported), clockSkew, getClockTolerance(options), undefined)\n        .then(checkJwtType.bind(undefined, 'dpop+jwt'))\n        .then(validatePresence.bind(undefined, ['iat', 'jti', 'ath', 'htm', 'htu']));\n    const now = epochTime() + clockSkew;\n    const diff = Math.abs(now - proof.claims.iat);\n    if (diff > 300) {\n        throw OPE('DPoP Proof iat is not recent enough', JWT_TIMESTAMP_CHECK, {\n            now,\n            claims: proof.claims,\n            claim: 'iat',\n        });\n    }\n    if (proof.claims.htm !== request.method) {\n        throw OPE('DPoP Proof htm mismatch', JWT_CLAIM_COMPARISON, {\n            expected: request.method,\n            claims: proof.claims,\n            claim: 'htm',\n        });\n    }\n    if (typeof proof.claims.htu !== 'string' ||\n        normalizeHtu(proof.claims.htu) !== normalizeHtu(request.url)) {\n        throw OPE('DPoP Proof htu mismatch', JWT_CLAIM_COMPARISON, {\n            expected: normalizeHtu(request.url),\n            claims: proof.claims,\n            claim: 'htu',\n        });\n    }\n    {\n        const expected = b64u(await crypto.subtle.digest('SHA-256', buf(accessToken)));\n        if (proof.claims.ath !== expected) {\n            throw OPE('DPoP Proof ath mismatch', JWT_CLAIM_COMPARISON, {\n                expected,\n                claims: proof.claims,\n                claim: 'ath',\n            });\n        }\n    }\n    {\n        let components;\n        switch (proof.header.jwk.kty) {\n            case 'EC':\n                components = {\n                    crv: proof.header.jwk.crv,\n                    kty: proof.header.jwk.kty,\n                    x: proof.header.jwk.x,\n                    y: proof.header.jwk.y,\n                };\n                break;\n            case 'OKP':\n                components = {\n                    crv: proof.header.jwk.crv,\n                    kty: proof.header.jwk.kty,\n                    x: proof.header.jwk.x,\n                };\n                break;\n            case 'RSA':\n                components = {\n                    e: proof.header.jwk.e,\n                    kty: proof.header.jwk.kty,\n                    n: proof.header.jwk.n,\n                };\n                break;\n            default:\n                throw new UnsupportedOperationError('unsupported JWK key type', { cause: proof.header.jwk });\n        }\n        const expected = b64u(await crypto.subtle.digest('SHA-256', buf(JSON.stringify(components))));\n        if (accessTokenClaims.cnf.jkt !== expected) {\n            throw OPE('JWT Access Token confirmation mismatch', JWT_CLAIM_COMPARISON, {\n                expected,\n                claims: accessTokenClaims,\n                claim: 'cnf.jkt',\n            });\n        }\n    }\n    const { 0: protectedHeader, 1: payload, 2: encodedSignature } = headerValue.split('.');\n    const signature = b64u(encodedSignature);\n    const { jwk, alg } = proof.header;\n    if (!jwk) {\n        throw OPE('DPoP Proof is missing the jwk header parameter', INVALID_REQUEST, {\n            header: proof.header,\n        });\n    }\n    const key = await importJwk(alg, jwk);\n    if (key.type !== 'public') {\n        throw OPE('DPoP Proof jwk header parameter must contain a public key', INVALID_REQUEST, {\n            header: proof.header,\n        });\n    }\n    await validateJwsSignature(protectedHeader, payload, key, signature);\n}\nasync function validateJwtAccessToken(as, request, expectedAudience, options) {\n    assertAs(as);\n    if (!looseInstanceOf(request, Request)) {\n        throw CodedTypeError('\"request\" must be an instance of Request', ERR_INVALID_ARG_TYPE);\n    }\n    assertString(expectedAudience, '\"expectedAudience\"');\n    const authorization = request.headers.get('authorization');\n    if (authorization === null) {\n        throw OPE('\"request\" is missing an Authorization HTTP Header', INVALID_REQUEST, {\n            headers: request.headers,\n        });\n    }\n    let { 0: scheme, 1: accessToken, length } = authorization.split(' ');\n    scheme = scheme.toLowerCase();\n    switch (scheme) {\n        case 'dpop':\n        case 'bearer':\n            break;\n        default:\n            throw new UnsupportedOperationError('unsupported Authorization HTTP Header scheme', {\n                cause: { headers: request.headers },\n            });\n    }\n    if (length !== 2) {\n        throw OPE('invalid Authorization HTTP Header format', INVALID_REQUEST, {\n            headers: request.headers,\n        });\n    }\n    const requiredClaims = [\n        'iss',\n        'exp',\n        'aud',\n        'sub',\n        'iat',\n        'jti',\n        'client_id',\n    ];\n    if (options?.requireDPoP || scheme === 'dpop' || request.headers.has('dpop')) {\n        requiredClaims.push('cnf');\n    }\n    const { claims, header } = await validateJwt(accessToken, checkSigningAlgorithm.bind(undefined, options?.signingAlgorithms, undefined, supported), getClockSkew(options), getClockTolerance(options), undefined)\n        .then(checkJwtType.bind(undefined, 'at+jwt'))\n        .then(validatePresence.bind(undefined, requiredClaims))\n        .then(validateIssuer.bind(undefined, as))\n        .then(validateAudience.bind(undefined, expectedAudience))\n        .catch(reassignRSCode);\n    for (const claim of ['client_id', 'jti', 'sub']) {\n        if (typeof claims[claim] !== 'string') {\n            throw OPE(`unexpected JWT \"${claim}\" claim type`, INVALID_REQUEST, { claims });\n        }\n    }\n    if ('cnf' in claims) {\n        if (!isJsonObject(claims.cnf)) {\n            throw OPE('unexpected JWT \"cnf\" (confirmation) claim value', INVALID_REQUEST, { claims });\n        }\n        const { 0: cnf, length } = Object.keys(claims.cnf);\n        if (length) {\n            if (length !== 1) {\n                throw new UnsupportedOperationError('multiple confirmation claims are not supported', {\n                    cause: { claims },\n                });\n            }\n            if (cnf !== 'jkt') {\n                throw new UnsupportedOperationError('unsupported JWT Confirmation method', {\n                    cause: { claims },\n                });\n            }\n        }\n    }\n    const { 0: protectedHeader, 1: payload, 2: encodedSignature } = accessToken.split('.');\n    const signature = b64u(encodedSignature);\n    const key = await getPublicSigKeyFromIssuerJwksUri(as, options, header);\n    await validateJwsSignature(protectedHeader, payload, key, signature);\n    if (options?.requireDPoP ||\n        scheme === 'dpop' ||\n        claims.cnf?.jkt !== undefined ||\n        request.headers.has('dpop')) {\n        await validateDPoP(request, accessToken, claims, options).catch(reassignRSCode);\n    }\n    return claims;\n}\nfunction reassignRSCode(err) {\n    if (err instanceof OperationProcessingError && err?.code === INVALID_REQUEST) {\n        err.code = INVALID_RESPONSE;\n    }\n    throw err;\n}\nasync function backchannelAuthenticationRequest(as, client, clientAuthentication, parameters, options) {\n    assertAs(as);\n    assertClient(client);\n    const url = resolveEndpoint(as, 'backchannel_authentication_endpoint', client.use_mtls_endpoint_aliases, options?.[allowInsecureRequests] !== true);\n    const body = new URLSearchParams(parameters);\n    body.set('client_id', client.client_id);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    return authenticatedRequest(as, client, clientAuthentication, url, body, headers, options);\n}\nasync function processBackchannelAuthenticationResponse(as, client, response) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    checkAuthenticationChallenges(response);\n    await checkOAuthBodyError(response, 200, 'Backchannel Authentication Endpoint');\n    assertReadableResponse(response);\n    const json = await getResponseJsonBody(response);\n    assertString(json.auth_req_id, '\"response\" body \"auth_req_id\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    let expiresIn = typeof json.expires_in !== 'number' ? parseFloat(json.expires_in) : json.expires_in;\n    assertNumber(expiresIn, true, '\"response\" body \"expires_in\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    json.expires_in = expiresIn;\n    if (json.interval !== undefined) {\n        assertNumber(json.interval, false, '\"response\" body \"interval\" property', INVALID_RESPONSE, {\n            body: json,\n        });\n    }\n    return json;\n}\nasync function backchannelAuthenticationGrantRequest(as, client, clientAuthentication, authReqId, options) {\n    assertAs(as);\n    assertClient(client);\n    assertString(authReqId, '\"authReqId\"');\n    const parameters = new URLSearchParams(options?.additionalParameters);\n    parameters.set('auth_req_id', authReqId);\n    return tokenEndpointRequest(as, client, clientAuthentication, 'urn:openid:params:grant-type:ciba', parameters, options);\n}\nasync function processBackchannelAuthenticationGrantResponse(as, client, response, options) {\n    return processGenericAccessTokenResponse(as, client, response, undefined, options);\n}\nasync function dynamicClientRegistrationRequest(as, metadata, options) {\n    assertAs(as);\n    const url = resolveEndpoint(as, 'registration_endpoint', metadata.use_mtls_endpoint_aliases, options?.[allowInsecureRequests] !== true);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    headers.set('content-type', 'application/json');\n    const method = 'POST';\n    if (options?.DPoP) {\n        assertDPoP(options.DPoP);\n        await options.DPoP.addProof(url, headers, method, options.initialAccessToken);\n    }\n    if (options?.initialAccessToken) {\n        headers.set('authorization', `${headers.has('dpop') ? 'DPoP' : 'Bearer'} ${options.initialAccessToken}`);\n    }\n    const response = await (options?.[customFetch] || fetch)(url.href, {\n        body: JSON.stringify(metadata),\n        headers: Object.fromEntries(headers.entries()),\n        method,\n        redirect: 'manual',\n        signal: signal(url, options?.signal),\n    });\n    options?.DPoP?.cacheNonce(response);\n    return response;\n}\nasync function processDynamicClientRegistrationResponse(response) {\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    checkAuthenticationChallenges(response);\n    await checkOAuthBodyError(response, 201, 'Dynamic Client Registration Endpoint');\n    assertReadableResponse(response);\n    const json = await getResponseJsonBody(response);\n    assertString(json.client_id, '\"response\" body \"client_id\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    if (json.client_secret !== undefined) {\n        assertString(json.client_secret, '\"response\" body \"client_secret\" property', INVALID_RESPONSE, {\n            body: json,\n        });\n    }\n    if (json.client_secret) {\n        assertNumber(json.client_secret_expires_at, true, '\"response\" body \"client_secret_expires_at\" property', INVALID_RESPONSE, {\n            body: json,\n        });\n    }\n    return json;\n}\nasync function resourceDiscoveryRequest(resourceIdentifier, options) {\n    return performDiscovery(resourceIdentifier, 'resourceIdentifier', (url) => {\n        prependWellKnown(url, '.well-known/oauth-protected-resource', true);\n        return url;\n    }, options);\n}\nasync function processResourceDiscoveryResponse(expectedResourceIdentifier, response) {\n    const expected = expectedResourceIdentifier;\n    if (!(expected instanceof URL) && expected !== _nodiscoverycheck) {\n        throw CodedTypeError('\"expectedResourceIdentifier\" must be an instance of URL', ERR_INVALID_ARG_TYPE);\n    }\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    if (response.status !== 200) {\n        throw OPE('\"response\" is not a conform Resource Server Metadata response (unexpected HTTP status code)', RESPONSE_IS_NOT_CONFORM, response);\n    }\n    assertReadableResponse(response);\n    const json = await getResponseJsonBody(response);\n    assertString(json.resource, '\"response\" body \"resource\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    if (expected !== _nodiscoverycheck && new URL(json.resource).href !== expected.href) {\n        throw OPE('\"response\" body \"resource\" property does not match the expected value', JSON_ATTRIBUTE_COMPARISON, { expected: expected.href, body: json, attribute: 'resource' });\n    }\n    return json;\n}\nasync function getResponseJsonBody(response, check = assertApplicationJson) {\n    let json;\n    try {\n        json = await response.json();\n    }\n    catch (cause) {\n        check(response);\n        throw OPE('failed to parse \"response\" body as JSON', PARSE_ERROR, cause);\n    }\n    if (!isJsonObject(json)) {\n        throw OPE('\"response\" body must be a top level object', INVALID_RESPONSE, { body: json });\n    }\n    return json;\n}\nconst _nopkce = nopkce;\nconst _nodiscoverycheck = Symbol();\nconst _expectedIssuer = Symbol();\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth4webapi/build/index.js\n");

/***/ })

};
;