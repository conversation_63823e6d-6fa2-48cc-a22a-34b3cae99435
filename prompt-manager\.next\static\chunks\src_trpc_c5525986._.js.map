{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/trpc/query-client.ts"], "sourcesContent": ["import {\n  defaultShouldDehydrateQuery,\n  QueryClient,\n} from \"@tanstack/react-query\";\nimport SuperJSON from \"superjson\";\n\nexport const createQueryClient = () =>\n  new QueryClient({\n    defaultOptions: {\n      queries: {\n        // With SSR, we usually want to set some default staleTime\n        // above 0 to avoid refetching immediately on the client\n        staleTime: 30 * 1000,\n      },\n      dehydrate: {\n        serializeData: SuperJSON.serialize,\n        shouldDehydrateQuery: (query) =>\n          defaultShouldDehydrateQuery(query) ||\n          query.state.status === \"pending\",\n      },\n      hydrate: {\n        deserializeData: SuperJSON.deserialize,\n      },\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAIA;;;AAEO,MAAM,oBAAoB,IAC/B,IAAI,gLAAA,CAAA,cAAW,CAAC;QACd,gBAAgB;YACd,SAAS;gBACP,0DAA0D;gBAC1D,wDAAwD;gBACxD,WAAW,KAAK;YAClB;YACA,WAAW;gBACT,eAAe,6IAAA,CAAA,UAAS,CAAC,SAAS;gBAClC,sBAAsB,CAAC,QACrB,CAAA,GAAA,8KAAA,CAAA,8BAA2B,AAAD,EAAE,UAC5B,MAAM,KAAK,CAAC,MAAM,KAAK;YAC3B;YACA,SAAS;gBACP,iBAAiB,6IAAA,CAAA,UAAS,CAAC,WAAW;YACxC;QACF;IACF", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/trpc/react.tsx"], "sourcesContent": ["\"use client\";\n\nimport { QueryClientProvider, type QueryClient } from \"@tanstack/react-query\";\nimport { httpBatchStreamLink, loggerLink } from \"@trpc/client\";\nimport { createTRPCReact } from \"@trpc/react-query\";\nimport { type inferRouterInputs, type inferRouterOutputs } from \"@trpc/server\";\nimport { useState } from \"react\";\nimport SuperJSON from \"superjson\";\n\nimport { type AppRouter } from \"~/server/api/root\";\nimport { createQueryClient } from \"./query-client\";\n\nlet clientQueryClientSingleton: QueryClient | undefined = undefined;\nconst getQueryClient = () => {\n  if (typeof window === \"undefined\") {\n    // Server: always make a new query client\n    return createQueryClient();\n  }\n  // Browser: use singleton pattern to keep the same query client\n  clientQueryClientSingleton ??= createQueryClient();\n\n  return clientQueryClientSingleton;\n};\n\nexport const api = createTRPCReact<AppRouter>();\n\n/**\n * Inference helper for inputs.\n *\n * @example type HelloInput = RouterInputs['example']['hello']\n */\nexport type RouterInputs = inferRouterInputs<AppRouter>;\n\n/**\n * Inference helper for outputs.\n *\n * @example type HelloOutput = RouterOutputs['example']['hello']\n */\nexport type RouterOutputs = inferRouterOutputs<AppRouter>;\n\nexport function TRPCReactProvider(props: { children: React.ReactNode }) {\n  const queryClient = getQueryClient();\n\n  const [trpcClient] = useState(() =>\n    api.createClient({\n      links: [\n        loggerLink({\n          enabled: (op) =>\n            process.env.NODE_ENV === \"development\" ||\n            (op.direction === \"down\" && op.result instanceof Error),\n        }),\n        httpBatchStreamLink({\n          transformer: SuperJSON,\n          url: getBaseUrl() + \"/api/trpc\",\n          headers: () => {\n            const headers = new Headers();\n            headers.set(\"x-trpc-source\", \"nextjs-react\");\n            return headers;\n          },\n        }),\n      ],\n    }),\n  );\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      <api.Provider client={trpcClient} queryClient={queryClient}>\n        {props.children}\n      </api.Provider>\n    </QueryClientProvider>\n  );\n}\n\nfunction getBaseUrl() {\n  if (typeof window !== \"undefined\") return window.location.origin;\n  if (process.env.VERCEL_URL) return `https://${process.env.VERCEL_URL}`;\n  return `http://localhost:${process.env.PORT ?? 3000}`;\n}\n"], "names": [], "mappings": ";;;;AAgDY;;AA9CZ;AACA;AAAA;AAAA;AACA;AAAA;AAEA;AACA;AAGA;;;AAVA;;;;;;;AAYA,IAAI,6BAAsD;AAC1D,MAAM,iBAAiB;IACrB;;IAIA,+DAA+D;IAC/D,uCAAA,wCAAA,6BAAA,6BAA+B,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD;IAE/C,OAAO;AACT;AAEO,MAAM,MAAM,CAAA,GAAA,6KAAA,CAAA,kBAAe,AAAD;AAgB1B,SAAS,kBAAkB,KAAoC;;IACpE,MAAM,cAAc;IAEpB,MAAM,CAAC,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;sCAAE,IAC5B,IAAI,YAAY,CAAC;gBACf,OAAO;oBACL,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE;wBACT,OAAO;0DAAE,CAAC,KACR,oDAAyB,iBACxB,GAAG,SAAS,KAAK,UAAU,GAAG,MAAM,YAAY;;oBACrD;oBACA,CAAA,GAAA,qKAAA,CAAA,sBAAmB,AAAD,EAAE;wBAClB,aAAa,6IAAA,CAAA,UAAS;wBACtB,KAAK,eAAe;wBACpB,OAAO;0DAAE;gCACP,MAAM,UAAU,IAAI;gCACpB,QAAQ,GAAG,CAAC,iBAAiB;gCAC7B,OAAO;4BACT;;oBACF;iBACD;YACH;;IAGF,qBACE,6LAAC,yLAAA,CAAA,sBAAmB;QAAC,QAAQ;kBAC3B,cAAA,6LAAC,IAAI,QAAQ;YAAC,QAAQ;YAAY,aAAa;sBAC5C,MAAM,QAAQ;;;;;;;;;;;AAIvB;GA/BgB;KAAA;AAiChB,SAAS;IACP,wCAAmC,OAAO,OAAO,QAAQ,CAAC,MAAM;;;QAErC;AAC7B", "debugId": null}}]}