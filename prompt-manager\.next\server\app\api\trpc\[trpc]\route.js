const CHUNK_PUBLIC_PATH = "server/app/api/trpc/[trpc]/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_next_98aff0b9._.js");
runtime.loadChunk("server/chunks/node_modules_@trpc_server_dist_db6083d7._.js");
runtime.loadChunk("server/chunks/node_modules_zod_v3_cc34a201._.js");
runtime.loadChunk("server/chunks/e044d_@auth_core_66f24a4e._.js");
runtime.loadChunk("server/chunks/e044d_jose_dist_node_esm_e9323528._.js");
runtime.loadChunk("server/chunks/node_modules_297f9262._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__1672de43._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/trpc/[trpc]/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/trpc/[trpc]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/trpc/[trpc]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
