/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/trpc/[trpc]/route";
exports.ids = ["app/api/trpc/[trpc]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute&page=%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute.ts&appDir=D%3A%5CCursor%20Project%5Cwebsite%5CAugment2%5Cprompt-manager%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCursor%20Project%5Cwebsite%5CAugment2%5Cprompt-manager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute&page=%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute.ts&appDir=D%3A%5CCursor%20Project%5Cwebsite%5CAugment2%5Cprompt-manager%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCursor%20Project%5Cwebsite%5CAugment2%5Cprompt-manager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var D_Cursor_Project_website_Augment2_prompt_manager_src_app_api_trpc_trpc_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/trpc/[trpc]/route.ts */ \"(rsc)/./src/app/api/trpc/[trpc]/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/trpc/[trpc]/route\",\n        pathname: \"/api/trpc/[trpc]\",\n        filename: \"route\",\n        bundlePath: \"app/api/trpc/[trpc]/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"D:\\\\Cursor Project\\\\website\\\\Augment2\\\\prompt-manager\\\\src\\\\app\\\\api\\\\trpc\\\\[trpc]\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Cursor_Project_website_Augment2_prompt_manager_src_app_api_trpc_trpc_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/trpc/[trpc]/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZ0cnBjJTJGJTVCdHJwYyU1RCUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGdHJwYyUyRiU1QnRycGMlNUQlMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZ0cnBjJTJGJTVCdHJwYyU1RCUyRnJvdXRlLnRzJmFwcERpcj1EJTNBJTVDQ3Vyc29yJTIwUHJvamVjdCU1Q3dlYnNpdGUlNUNBdWdtZW50MiU1Q3Byb21wdC1tYW5hZ2VyJTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1EJTNBJTVDQ3Vyc29yJTIwUHJvamVjdCU1Q3dlYnNpdGUlNUNBdWdtZW50MiU1Q3Byb21wdC1tYW5hZ2VyJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEJmlzR2xvYmFsTm90Rm91bmRFbmFibGVkPSEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDcUI7QUFDZDtBQUNTO0FBQ087QUFDSztBQUNtQztBQUNqRDtBQUNPO0FBQ2Y7QUFDc0M7QUFDekI7QUFDTTtBQUNDO0FBQ2hCO0FBQ3VEO0FBQ3pIO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix5R0FBbUI7QUFDM0M7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLGFBQWEsT0FBb0MsSUFBSSxDQUFFO0FBQ3ZELGdCQUFnQixNQUF1QztBQUN2RDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLHNEQUFzRDtBQUM5RDtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUMwRjtBQUNuRjtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLEtBQXFCLEVBQUUsRUFFMUIsQ0FBQztBQUNOO0FBQ0E7QUFDQTtBQUNBLCtCQUErQixPQUF3QztBQUN2RTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxvSkFBb0o7QUFDaEssOEJBQThCLDZGQUFnQjtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsNkZBQWU7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsNEVBQVM7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBLDhCQUE4Qiw2RUFBYztBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsNEVBQWU7QUFDM0MsNEJBQTRCLDZFQUFnQjtBQUM1QyxvQkFBb0IseUdBQWtCLGtDQUFrQyxpSEFBc0I7QUFDOUY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlFQUFpRSxnRkFBYztBQUMvRSwrREFBK0QseUNBQXlDO0FBQ3hHO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLFFBQVEsRUFBRSxNQUFNO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0Esa0JBQWtCO0FBQ2xCLHVDQUF1QyxRQUFRLEVBQUUsUUFBUTtBQUN6RDtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSwrQ0FBK0Msb0JBQW9CO0FBQ25FO0FBQ0EseUJBQXlCLDZFQUFjO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3Q0FBd0Msc0ZBQXlCO0FBQ2pFO0FBQ0Esb0NBQW9DLDRFQUFzQjtBQUMxRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNKQUFzSixvRUFBYztBQUNwSywwSUFBMEksb0VBQWM7QUFDeEo7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDLDZFQUFlO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QjtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0I7QUFDdEI7QUFDQSw4QkFBOEIsNkVBQVk7QUFDMUM7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE4QywyRkFBbUI7QUFDakU7QUFDQTtBQUNBLDZCQUE2QjtBQUM3Qix5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQixrRUFBUztBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFJQUFxSSw2RUFBZTtBQUNwSjtBQUNBLDJHQUEyRyxpSEFBaUg7QUFDNU47QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0EsaUJBQWlCLDZFQUFjO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0Qix3RkFBMkI7QUFDdkQsa0JBQWtCLDZFQUFjO0FBQ2hDLCtCQUErQiw0RUFBc0I7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2Q0FBNkMsMEZBQXFCO0FBQ2xFO0FBQ0Esa0JBQWtCLDZFQUFZO0FBQzlCO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWLDZFQUE2RSxnRkFBYztBQUMzRixpQ0FBaUMsUUFBUSxFQUFFLFFBQVE7QUFDbkQsMEJBQTBCLHVFQUFRO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQywyRkFBbUI7QUFDckQ7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsNkVBQVk7QUFDMUI7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0IHsgZ2V0UmVxdWVzdE1ldGEgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yZXF1ZXN0LW1ldGFcIjtcbmltcG9ydCB7IGdldFRyYWNlciwgU3BhbktpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvdHJhY2UvdHJhY2VyXCI7XG5pbXBvcnQgeyBub3JtYWxpemVBcHBQYXRoIH0gZnJvbSBcIm5leHQvZGlzdC9zaGFyZWQvbGliL3JvdXRlci91dGlscy9hcHAtcGF0aHNcIjtcbmltcG9ydCB7IE5vZGVOZXh0UmVxdWVzdCwgTm9kZU5leHRSZXNwb25zZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Jhc2UtaHR0cC9ub2RlXCI7XG5pbXBvcnQgeyBOZXh0UmVxdWVzdEFkYXB0ZXIsIHNpZ25hbEZyb21Ob2RlUmVzcG9uc2UgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci93ZWIvc3BlYy1leHRlbnNpb24vYWRhcHRlcnMvbmV4dC1yZXF1ZXN0XCI7XG5pbXBvcnQgeyBCYXNlU2VydmVyU3BhbiB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi90cmFjZS9jb25zdGFudHNcIjtcbmltcG9ydCB7IGdldFJldmFsaWRhdGVSZWFzb24gfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9pbnN0cnVtZW50YXRpb24vdXRpbHNcIjtcbmltcG9ydCB7IHNlbmRSZXNwb25zZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3NlbmQtcmVzcG9uc2VcIjtcbmltcG9ydCB7IGZyb21Ob2RlT3V0Z29pbmdIdHRwSGVhZGVycywgdG9Ob2RlT3V0Z29pbmdIdHRwSGVhZGVycyB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3dlYi91dGlsc1wiO1xuaW1wb3J0IHsgZ2V0Q2FjaGVDb250cm9sSGVhZGVyIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL2NhY2hlLWNvbnRyb2xcIjtcbmltcG9ydCB7IElORklOSVRFX0NBQ0hFLCBORVhUX0NBQ0hFX1RBR1NfSEVBREVSIH0gZnJvbSBcIm5leHQvZGlzdC9saWIvY29uc3RhbnRzXCI7XG5pbXBvcnQgeyBOb0ZhbGxiYWNrRXJyb3IgfSBmcm9tIFwibmV4dC9kaXN0L3NoYXJlZC9saWIvbm8tZmFsbGJhY2stZXJyb3IuZXh0ZXJuYWxcIjtcbmltcG9ydCB7IENhY2hlZFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3Jlc3BvbnNlLWNhY2hlXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiRDpcXFxcQ3Vyc29yIFByb2plY3RcXFxcd2Vic2l0ZVxcXFxBdWdtZW50MlxcXFxwcm9tcHQtbWFuYWdlclxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFx0cnBjXFxcXFt0cnBjXVxcXFxyb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvdHJwYy9bdHJwY10vcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS90cnBjL1t0cnBjXVwiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvdHJwYy9bdHJwY10vcm91dGVcIlxuICAgIH0sXG4gICAgZGlzdERpcjogcHJvY2Vzcy5lbnYuX19ORVhUX1JFTEFUSVZFX0RJU1RfRElSIHx8ICcnLFxuICAgIHByb2plY3REaXI6IHByb2Nlc3MuZW52Ll9fTkVYVF9SRUxBVElWRV9QUk9KRUNUX0RJUiB8fCAnJyxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIkQ6XFxcXEN1cnNvciBQcm9qZWN0XFxcXHdlYnNpdGVcXFxcQXVnbWVudDJcXFxccHJvbXB0LW1hbmFnZXJcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcdHJwY1xcXFxbdHJwY11cXFxccm91dGUudHNcIixcbiAgICBuZXh0Q29uZmlnT3V0cHV0LFxuICAgIHVzZXJsYW5kXG59KTtcbi8vIFB1bGwgb3V0IHRoZSBleHBvcnRzIHRoYXQgd2UgbmVlZCB0byBleHBvc2UgZnJvbSB0aGUgbW9kdWxlLiBUaGlzIHNob3VsZFxuLy8gYmUgZWxpbWluYXRlZCB3aGVuIHdlJ3ZlIG1vdmVkIHRoZSBvdGhlciByb3V0ZXMgdG8gdGhlIG5ldyBmb3JtYXQuIFRoZXNlXG4vLyBhcmUgdXNlZCB0byBob29rIGludG8gdGhlIHJvdXRlLlxuY29uc3QgeyB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MgfSA9IHJvdXRlTW9kdWxlO1xuZnVuY3Rpb24gcGF0Y2hGZXRjaCgpIHtcbiAgICByZXR1cm4gX3BhdGNoRmV0Y2goe1xuICAgICAgICB3b3JrQXN5bmNTdG9yYWdlLFxuICAgICAgICB3b3JrVW5pdEFzeW5jU3RvcmFnZVxuICAgIH0pO1xufVxuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgcGF0Y2hGZXRjaCwgIH07XG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gaGFuZGxlcihyZXEsIHJlcywgY3R4KSB7XG4gICAgdmFyIF9uZXh0Q29uZmlnX2V4cGVyaW1lbnRhbDtcbiAgICBsZXQgc3JjUGFnZSA9IFwiL2FwaS90cnBjL1t0cnBjXS9yb3V0ZVwiO1xuICAgIC8vIHR1cmJvcGFjayBkb2Vzbid0IG5vcm1hbGl6ZSBgL2luZGV4YCBpbiB0aGUgcGFnZSBuYW1lXG4gICAgLy8gc28gd2UgbmVlZCB0byB0byBwcm9jZXNzIGR5bmFtaWMgcm91dGVzIHByb3Blcmx5XG4gICAgLy8gVE9ETzogZml4IHR1cmJvcGFjayBwcm92aWRpbmcgZGlmZmVyaW5nIHZhbHVlIGZyb20gd2VicGFja1xuICAgIGlmIChwcm9jZXNzLmVudi5UVVJCT1BBQ0spIHtcbiAgICAgICAgc3JjUGFnZSA9IHNyY1BhZ2UucmVwbGFjZSgvXFwvaW5kZXgkLywgJycpIHx8ICcvJztcbiAgICB9IGVsc2UgaWYgKHNyY1BhZ2UgPT09ICcvaW5kZXgnKSB7XG4gICAgICAgIC8vIHdlIGFsd2F5cyBub3JtYWxpemUgL2luZGV4IHNwZWNpZmljYWxseVxuICAgICAgICBzcmNQYWdlID0gJy8nO1xuICAgIH1cbiAgICBjb25zdCBtdWx0aVpvbmVEcmFmdE1vZGUgPSBwcm9jZXNzLmVudi5fX05FWFRfTVVMVElfWk9ORV9EUkFGVF9NT0RFO1xuICAgIGNvbnN0IHByZXBhcmVSZXN1bHQgPSBhd2FpdCByb3V0ZU1vZHVsZS5wcmVwYXJlKHJlcSwgcmVzLCB7XG4gICAgICAgIHNyY1BhZ2UsXG4gICAgICAgIG11bHRpWm9uZURyYWZ0TW9kZVxuICAgIH0pO1xuICAgIGlmICghcHJlcGFyZVJlc3VsdCkge1xuICAgICAgICByZXMuc3RhdHVzQ29kZSA9IDQwMDtcbiAgICAgICAgcmVzLmVuZCgnQmFkIFJlcXVlc3QnKTtcbiAgICAgICAgY3R4LndhaXRVbnRpbCA9PSBudWxsID8gdm9pZCAwIDogY3R4LndhaXRVbnRpbC5jYWxsKGN0eCwgUHJvbWlzZS5yZXNvbHZlKCkpO1xuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG4gICAgY29uc3QgeyBidWlsZElkLCBwYXJhbXMsIG5leHRDb25maWcsIGlzRHJhZnRNb2RlLCBwcmVyZW5kZXJNYW5pZmVzdCwgcm91dGVyU2VydmVyQ29udGV4dCwgaXNPbkRlbWFuZFJldmFsaWRhdGUsIHJldmFsaWRhdGVPbmx5R2VuZXJhdGVkLCByZXNvbHZlZFBhdGhuYW1lIH0gPSBwcmVwYXJlUmVzdWx0O1xuICAgIGNvbnN0IG5vcm1hbGl6ZWRTcmNQYWdlID0gbm9ybWFsaXplQXBwUGF0aChzcmNQYWdlKTtcbiAgICBsZXQgaXNJc3IgPSBCb29sZWFuKHByZXJlbmRlck1hbmlmZXN0LmR5bmFtaWNSb3V0ZXNbbm9ybWFsaXplZFNyY1BhZ2VdIHx8IHByZXJlbmRlck1hbmlmZXN0LnJvdXRlc1tyZXNvbHZlZFBhdGhuYW1lXSk7XG4gICAgaWYgKGlzSXNyICYmICFpc0RyYWZ0TW9kZSkge1xuICAgICAgICBjb25zdCBpc1ByZXJlbmRlcmVkID0gQm9vbGVhbihwcmVyZW5kZXJNYW5pZmVzdC5yb3V0ZXNbcmVzb2x2ZWRQYXRobmFtZV0pO1xuICAgICAgICBjb25zdCBwcmVyZW5kZXJJbmZvID0gcHJlcmVuZGVyTWFuaWZlc3QuZHluYW1pY1JvdXRlc1tub3JtYWxpemVkU3JjUGFnZV07XG4gICAgICAgIGlmIChwcmVyZW5kZXJJbmZvKSB7XG4gICAgICAgICAgICBpZiAocHJlcmVuZGVySW5mby5mYWxsYmFjayA9PT0gZmFsc2UgJiYgIWlzUHJlcmVuZGVyZWQpIHtcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgTm9GYWxsYmFja0Vycm9yKCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgbGV0IGNhY2hlS2V5ID0gbnVsbDtcbiAgICBpZiAoaXNJc3IgJiYgIXJvdXRlTW9kdWxlLmlzRGV2ICYmICFpc0RyYWZ0TW9kZSkge1xuICAgICAgICBjYWNoZUtleSA9IHJlc29sdmVkUGF0aG5hbWU7XG4gICAgICAgIC8vIGVuc3VyZSAvaW5kZXggYW5kIC8gaXMgbm9ybWFsaXplZCB0byBvbmUga2V5XG4gICAgICAgIGNhY2hlS2V5ID0gY2FjaGVLZXkgPT09ICcvaW5kZXgnID8gJy8nIDogY2FjaGVLZXk7XG4gICAgfVxuICAgIGNvbnN0IHN1cHBvcnRzRHluYW1pY1Jlc3BvbnNlID0gLy8gSWYgd2UncmUgaW4gZGV2ZWxvcG1lbnQsIHdlIGFsd2F5cyBzdXBwb3J0IGR5bmFtaWMgSFRNTFxuICAgIHJvdXRlTW9kdWxlLmlzRGV2ID09PSB0cnVlIHx8IC8vIElmIHRoaXMgaXMgbm90IFNTRyBvciBkb2VzIG5vdCBoYXZlIHN0YXRpYyBwYXRocywgdGhlbiBpdCBzdXBwb3J0c1xuICAgIC8vIGR5bmFtaWMgSFRNTC5cbiAgICAhaXNJc3I7XG4gICAgLy8gVGhpcyBpcyBhIHJldmFsaWRhdGlvbiByZXF1ZXN0IGlmIHRoZSByZXF1ZXN0IGlzIGZvciBhIHN0YXRpY1xuICAgIC8vIHBhZ2UgYW5kIGl0IGlzIG5vdCBiZWluZyByZXN1bWVkIGZyb20gYSBwb3N0cG9uZWQgcmVuZGVyIGFuZFxuICAgIC8vIGl0IGlzIG5vdCBhIGR5bmFtaWMgUlNDIHJlcXVlc3QgdGhlbiBpdCBpcyBhIHJldmFsaWRhdGlvblxuICAgIC8vIHJlcXVlc3QuXG4gICAgY29uc3QgaXNSZXZhbGlkYXRlID0gaXNJc3IgJiYgIXN1cHBvcnRzRHluYW1pY1Jlc3BvbnNlO1xuICAgIGNvbnN0IG1ldGhvZCA9IHJlcS5tZXRob2QgfHwgJ0dFVCc7XG4gICAgY29uc3QgdHJhY2VyID0gZ2V0VHJhY2VyKCk7XG4gICAgY29uc3QgYWN0aXZlU3BhbiA9IHRyYWNlci5nZXRBY3RpdmVTY29wZVNwYW4oKTtcbiAgICBjb25zdCBjb250ZXh0ID0ge1xuICAgICAgICBwYXJhbXMsXG4gICAgICAgIHByZXJlbmRlck1hbmlmZXN0LFxuICAgICAgICByZW5kZXJPcHRzOiB7XG4gICAgICAgICAgICBleHBlcmltZW50YWw6IHtcbiAgICAgICAgICAgICAgICBkeW5hbWljSU86IEJvb2xlYW4obmV4dENvbmZpZy5leHBlcmltZW50YWwuZHluYW1pY0lPKSxcbiAgICAgICAgICAgICAgICBhdXRoSW50ZXJydXB0czogQm9vbGVhbihuZXh0Q29uZmlnLmV4cGVyaW1lbnRhbC5hdXRoSW50ZXJydXB0cylcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBzdXBwb3J0c0R5bmFtaWNSZXNwb25zZSxcbiAgICAgICAgICAgIGluY3JlbWVudGFsQ2FjaGU6IGdldFJlcXVlc3RNZXRhKHJlcSwgJ2luY3JlbWVudGFsQ2FjaGUnKSxcbiAgICAgICAgICAgIGNhY2hlTGlmZVByb2ZpbGVzOiAoX25leHRDb25maWdfZXhwZXJpbWVudGFsID0gbmV4dENvbmZpZy5leHBlcmltZW50YWwpID09IG51bGwgPyB2b2lkIDAgOiBfbmV4dENvbmZpZ19leHBlcmltZW50YWwuY2FjaGVMaWZlLFxuICAgICAgICAgICAgaXNSZXZhbGlkYXRlLFxuICAgICAgICAgICAgd2FpdFVudGlsOiBjdHgud2FpdFVudGlsLFxuICAgICAgICAgICAgb25DbG9zZTogKGNiKT0+e1xuICAgICAgICAgICAgICAgIHJlcy5vbignY2xvc2UnLCBjYik7XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgb25BZnRlclRhc2tFcnJvcjogdW5kZWZpbmVkLFxuICAgICAgICAgICAgb25JbnN0cnVtZW50YXRpb25SZXF1ZXN0RXJyb3I6IChlcnJvciwgX3JlcXVlc3QsIGVycm9yQ29udGV4dCk9PnJvdXRlTW9kdWxlLm9uUmVxdWVzdEVycm9yKHJlcSwgZXJyb3IsIGVycm9yQ29udGV4dCwgcm91dGVyU2VydmVyQ29udGV4dClcbiAgICAgICAgfSxcbiAgICAgICAgc2hhcmVkQ29udGV4dDoge1xuICAgICAgICAgICAgYnVpbGRJZFxuICAgICAgICB9XG4gICAgfTtcbiAgICBjb25zdCBub2RlTmV4dFJlcSA9IG5ldyBOb2RlTmV4dFJlcXVlc3QocmVxKTtcbiAgICBjb25zdCBub2RlTmV4dFJlcyA9IG5ldyBOb2RlTmV4dFJlc3BvbnNlKHJlcyk7XG4gICAgY29uc3QgbmV4dFJlcSA9IE5leHRSZXF1ZXN0QWRhcHRlci5mcm9tTm9kZU5leHRSZXF1ZXN0KG5vZGVOZXh0UmVxLCBzaWduYWxGcm9tTm9kZVJlc3BvbnNlKHJlcykpO1xuICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IGludm9rZVJvdXRlTW9kdWxlID0gYXN5bmMgKHNwYW4pPT57XG4gICAgICAgICAgICByZXR1cm4gcm91dGVNb2R1bGUuaGFuZGxlKG5leHRSZXEsIGNvbnRleHQpLmZpbmFsbHkoKCk9PntcbiAgICAgICAgICAgICAgICBpZiAoIXNwYW4pIHJldHVybjtcbiAgICAgICAgICAgICAgICBzcGFuLnNldEF0dHJpYnV0ZXMoe1xuICAgICAgICAgICAgICAgICAgICAnaHR0cC5zdGF0dXNfY29kZSc6IHJlcy5zdGF0dXNDb2RlLFxuICAgICAgICAgICAgICAgICAgICAnbmV4dC5yc2MnOiBmYWxzZVxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIGNvbnN0IHJvb3RTcGFuQXR0cmlidXRlcyA9IHRyYWNlci5nZXRSb290U3BhbkF0dHJpYnV0ZXMoKTtcbiAgICAgICAgICAgICAgICAvLyBXZSB3ZXJlIHVuYWJsZSB0byBnZXQgYXR0cmlidXRlcywgcHJvYmFibHkgT1RFTCBpcyBub3QgZW5hYmxlZFxuICAgICAgICAgICAgICAgIGlmICghcm9vdFNwYW5BdHRyaWJ1dGVzKSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKHJvb3RTcGFuQXR0cmlidXRlcy5nZXQoJ25leHQuc3Bhbl90eXBlJykgIT09IEJhc2VTZXJ2ZXJTcGFuLmhhbmRsZVJlcXVlc3QpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS53YXJuKGBVbmV4cGVjdGVkIHJvb3Qgc3BhbiB0eXBlICcke3Jvb3RTcGFuQXR0cmlidXRlcy5nZXQoJ25leHQuc3Bhbl90eXBlJyl9Jy4gUGxlYXNlIHJlcG9ydCB0aGlzIE5leHQuanMgaXNzdWUgaHR0cHM6Ly9naXRodWIuY29tL3ZlcmNlbC9uZXh0LmpzYCk7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY29uc3Qgcm91dGUgPSByb290U3BhbkF0dHJpYnV0ZXMuZ2V0KCduZXh0LnJvdXRlJyk7XG4gICAgICAgICAgICAgICAgaWYgKHJvdXRlKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG5hbWUgPSBgJHttZXRob2R9ICR7cm91dGV9YDtcbiAgICAgICAgICAgICAgICAgICAgc3Bhbi5zZXRBdHRyaWJ1dGVzKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICduZXh0LnJvdXRlJzogcm91dGUsXG4gICAgICAgICAgICAgICAgICAgICAgICAnaHR0cC5yb3V0ZSc6IHJvdXRlLFxuICAgICAgICAgICAgICAgICAgICAgICAgJ25leHQuc3Bhbl9uYW1lJzogbmFtZVxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgc3Bhbi51cGRhdGVOYW1lKG5hbWUpO1xuICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIHNwYW4udXBkYXRlTmFtZShgJHttZXRob2R9ICR7cmVxLnVybH1gKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfTtcbiAgICAgICAgY29uc3QgaGFuZGxlUmVzcG9uc2UgPSBhc3luYyAoY3VycmVudFNwYW4pPT57XG4gICAgICAgICAgICB2YXIgX2NhY2hlRW50cnlfdmFsdWU7XG4gICAgICAgICAgICBjb25zdCByZXNwb25zZUdlbmVyYXRvciA9IGFzeW5jICh7IHByZXZpb3VzQ2FjaGVFbnRyeSB9KT0+e1xuICAgICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgICAgIGlmICghZ2V0UmVxdWVzdE1ldGEocmVxLCAnbWluaW1hbE1vZGUnKSAmJiBpc09uRGVtYW5kUmV2YWxpZGF0ZSAmJiByZXZhbGlkYXRlT25seUdlbmVyYXRlZCAmJiAhcHJldmlvdXNDYWNoZUVudHJ5KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXMuc3RhdHVzQ29kZSA9IDQwNDtcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIG9uLWRlbWFuZCByZXZhbGlkYXRlIGFsd2F5cyBzZXRzIHRoaXMgaGVhZGVyXG4gICAgICAgICAgICAgICAgICAgICAgICByZXMuc2V0SGVhZGVyKCd4LW5leHRqcy1jYWNoZScsICdSRVZBTElEQVRFRCcpO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmVzLmVuZCgnVGhpcyBwYWdlIGNvdWxkIG5vdCBiZSBmb3VuZCcpO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBpbnZva2VSb3V0ZU1vZHVsZShjdXJyZW50U3Bhbik7XG4gICAgICAgICAgICAgICAgICAgIHJlcS5mZXRjaE1ldHJpY3MgPSBjb250ZXh0LnJlbmRlck9wdHMuZmV0Y2hNZXRyaWNzO1xuICAgICAgICAgICAgICAgICAgICBsZXQgcGVuZGluZ1dhaXRVbnRpbCA9IGNvbnRleHQucmVuZGVyT3B0cy5wZW5kaW5nV2FpdFVudGlsO1xuICAgICAgICAgICAgICAgICAgICAvLyBBdHRlbXB0IHVzaW5nIHByb3ZpZGVkIHdhaXRVbnRpbCBpZiBhdmFpbGFibGVcbiAgICAgICAgICAgICAgICAgICAgLy8gaWYgaXQncyBub3Qgd2UgZmFsbGJhY2sgdG8gc2VuZFJlc3BvbnNlJ3MgaGFuZGxpbmdcbiAgICAgICAgICAgICAgICAgICAgaWYgKHBlbmRpbmdXYWl0VW50aWwpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChjdHgud2FpdFVudGlsKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY3R4LndhaXRVbnRpbChwZW5kaW5nV2FpdFVudGlsKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwZW5kaW5nV2FpdFVudGlsID0gdW5kZWZpbmVkO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGNhY2hlVGFncyA9IGNvbnRleHQucmVuZGVyT3B0cy5jb2xsZWN0ZWRUYWdzO1xuICAgICAgICAgICAgICAgICAgICAvLyBJZiB0aGUgcmVxdWVzdCBpcyBmb3IgYSBzdGF0aWMgcmVzcG9uc2UsIHdlIGNhbiBjYWNoZSBpdCBzbyBsb25nXG4gICAgICAgICAgICAgICAgICAgIC8vIGFzIGl0J3Mgbm90IGVkZ2UuXG4gICAgICAgICAgICAgICAgICAgIGlmIChpc0lzcikge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgYmxvYiA9IGF3YWl0IHJlc3BvbnNlLmJsb2IoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIENvcHkgdGhlIGhlYWRlcnMgZnJvbSB0aGUgcmVzcG9uc2UuXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBoZWFkZXJzID0gdG9Ob2RlT3V0Z29pbmdIdHRwSGVhZGVycyhyZXNwb25zZS5oZWFkZXJzKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChjYWNoZVRhZ3MpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBoZWFkZXJzW05FWFRfQ0FDSEVfVEFHU19IRUFERVJdID0gY2FjaGVUYWdzO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCFoZWFkZXJzWydjb250ZW50LXR5cGUnXSAmJiBibG9iLnR5cGUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBoZWFkZXJzWydjb250ZW50LXR5cGUnXSA9IGJsb2IudHlwZTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHJldmFsaWRhdGUgPSB0eXBlb2YgY29udGV4dC5yZW5kZXJPcHRzLmNvbGxlY3RlZFJldmFsaWRhdGUgPT09ICd1bmRlZmluZWQnIHx8IGNvbnRleHQucmVuZGVyT3B0cy5jb2xsZWN0ZWRSZXZhbGlkYXRlID49IElORklOSVRFX0NBQ0hFID8gZmFsc2UgOiBjb250ZXh0LnJlbmRlck9wdHMuY29sbGVjdGVkUmV2YWxpZGF0ZTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGV4cGlyZSA9IHR5cGVvZiBjb250ZXh0LnJlbmRlck9wdHMuY29sbGVjdGVkRXhwaXJlID09PSAndW5kZWZpbmVkJyB8fCBjb250ZXh0LnJlbmRlck9wdHMuY29sbGVjdGVkRXhwaXJlID49IElORklOSVRFX0NBQ0hFID8gdW5kZWZpbmVkIDogY29udGV4dC5yZW5kZXJPcHRzLmNvbGxlY3RlZEV4cGlyZTtcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIENyZWF0ZSB0aGUgY2FjaGUgZW50cnkgZm9yIHRoZSByZXNwb25zZS5cbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGNhY2hlRW50cnkgPSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2luZDogQ2FjaGVkUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiByZXNwb25zZS5zdGF0dXMsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvZHk6IEJ1ZmZlci5mcm9tKGF3YWl0IGJsb2IuYXJyYXlCdWZmZXIoKSksXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhlYWRlcnNcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhY2hlQ29udHJvbDoge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXZhbGlkYXRlLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBleHBpcmVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGNhY2hlRW50cnk7XG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBzZW5kIHJlc3BvbnNlIHdpdGhvdXQgY2FjaGluZyBpZiBub3QgSVNSXG4gICAgICAgICAgICAgICAgICAgICAgICBhd2FpdCBzZW5kUmVzcG9uc2Uobm9kZU5leHRSZXEsIG5vZGVOZXh0UmVzLCByZXNwb25zZSwgY29udGV4dC5yZW5kZXJPcHRzLnBlbmRpbmdXYWl0VW50aWwpO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gaWYgdGhpcyBpcyBhIGJhY2tncm91bmQgcmV2YWxpZGF0ZSB3ZSBuZWVkIHRvIHJlcG9ydFxuICAgICAgICAgICAgICAgICAgICAvLyB0aGUgcmVxdWVzdCBlcnJvciBoZXJlIGFzIGl0IHdvbid0IGJlIGJ1YmJsZWRcbiAgICAgICAgICAgICAgICAgICAgaWYgKHByZXZpb3VzQ2FjaGVFbnRyeSA9PSBudWxsID8gdm9pZCAwIDogcHJldmlvdXNDYWNoZUVudHJ5LmlzU3RhbGUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGF3YWl0IHJvdXRlTW9kdWxlLm9uUmVxdWVzdEVycm9yKHJlcSwgZXJyLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcm91dGVyS2luZDogJ0FwcCBSb3V0ZXInLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJvdXRlUGF0aDogc3JjUGFnZSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByb3V0ZVR5cGU6ICdyb3V0ZScsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV2YWxpZGF0ZVJlYXNvbjogZ2V0UmV2YWxpZGF0ZVJlYXNvbih7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzUmV2YWxpZGF0ZSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNPbkRlbWFuZFJldmFsaWRhdGVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgICAgICAgICAgfSwgcm91dGVyU2VydmVyQ29udGV4dCk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgZXJyO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH07XG4gICAgICAgICAgICBjb25zdCBjYWNoZUVudHJ5ID0gYXdhaXQgcm91dGVNb2R1bGUuaGFuZGxlUmVzcG9uc2Uoe1xuICAgICAgICAgICAgICAgIHJlcSxcbiAgICAgICAgICAgICAgICBuZXh0Q29uZmlnLFxuICAgICAgICAgICAgICAgIGNhY2hlS2V5LFxuICAgICAgICAgICAgICAgIHJvdXRlS2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgICAgICAgICBpc0ZhbGxiYWNrOiBmYWxzZSxcbiAgICAgICAgICAgICAgICBwcmVyZW5kZXJNYW5pZmVzdCxcbiAgICAgICAgICAgICAgICBpc1JvdXRlUFBSRW5hYmxlZDogZmFsc2UsXG4gICAgICAgICAgICAgICAgaXNPbkRlbWFuZFJldmFsaWRhdGUsXG4gICAgICAgICAgICAgICAgcmV2YWxpZGF0ZU9ubHlHZW5lcmF0ZWQsXG4gICAgICAgICAgICAgICAgcmVzcG9uc2VHZW5lcmF0b3IsXG4gICAgICAgICAgICAgICAgd2FpdFVudGlsOiBjdHgud2FpdFVudGlsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIC8vIHdlIGRvbid0IGNyZWF0ZSBhIGNhY2hlRW50cnkgZm9yIElTUlxuICAgICAgICAgICAgaWYgKCFpc0lzcikge1xuICAgICAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKChjYWNoZUVudHJ5ID09IG51bGwgPyB2b2lkIDAgOiAoX2NhY2hlRW50cnlfdmFsdWUgPSBjYWNoZUVudHJ5LnZhbHVlKSA9PSBudWxsID8gdm9pZCAwIDogX2NhY2hlRW50cnlfdmFsdWUua2luZCkgIT09IENhY2hlZFJvdXRlS2luZC5BUFBfUk9VVEUpIHtcbiAgICAgICAgICAgICAgICB2YXIgX2NhY2hlRW50cnlfdmFsdWUxO1xuICAgICAgICAgICAgICAgIHRocm93IE9iamVjdC5kZWZpbmVQcm9wZXJ0eShuZXcgRXJyb3IoYEludmFyaWFudDogYXBwLXJvdXRlIHJlY2VpdmVkIGludmFsaWQgY2FjaGUgZW50cnkgJHtjYWNoZUVudHJ5ID09IG51bGwgPyB2b2lkIDAgOiAoX2NhY2hlRW50cnlfdmFsdWUxID0gY2FjaGVFbnRyeS52YWx1ZSkgPT0gbnVsbCA/IHZvaWQgMCA6IF9jYWNoZUVudHJ5X3ZhbHVlMS5raW5kfWApLCBcIl9fTkVYVF9FUlJPUl9DT0RFXCIsIHtcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU6IFwiRTcwMVwiLFxuICAgICAgICAgICAgICAgICAgICBlbnVtZXJhYmxlOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoIWdldFJlcXVlc3RNZXRhKHJlcSwgJ21pbmltYWxNb2RlJykpIHtcbiAgICAgICAgICAgICAgICByZXMuc2V0SGVhZGVyKCd4LW5leHRqcy1jYWNoZScsIGlzT25EZW1hbmRSZXZhbGlkYXRlID8gJ1JFVkFMSURBVEVEJyA6IGNhY2hlRW50cnkuaXNNaXNzID8gJ01JU1MnIDogY2FjaGVFbnRyeS5pc1N0YWxlID8gJ1NUQUxFJyA6ICdISVQnKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIERyYWZ0IG1vZGUgc2hvdWxkIG5ldmVyIGJlIGNhY2hlZFxuICAgICAgICAgICAgaWYgKGlzRHJhZnRNb2RlKSB7XG4gICAgICAgICAgICAgICAgcmVzLnNldEhlYWRlcignQ2FjaGUtQ29udHJvbCcsICdwcml2YXRlLCBuby1jYWNoZSwgbm8tc3RvcmUsIG1heC1hZ2U9MCwgbXVzdC1yZXZhbGlkYXRlJyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCBoZWFkZXJzID0gZnJvbU5vZGVPdXRnb2luZ0h0dHBIZWFkZXJzKGNhY2hlRW50cnkudmFsdWUuaGVhZGVycyk7XG4gICAgICAgICAgICBpZiAoIShnZXRSZXF1ZXN0TWV0YShyZXEsICdtaW5pbWFsTW9kZScpICYmIGlzSXNyKSkge1xuICAgICAgICAgICAgICAgIGhlYWRlcnMuZGVsZXRlKE5FWFRfQ0FDSEVfVEFHU19IRUFERVIpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gSWYgY2FjaGUgY29udHJvbCBpcyBhbHJlYWR5IHNldCBvbiB0aGUgcmVzcG9uc2Ugd2UgZG9uJ3RcbiAgICAgICAgICAgIC8vIG92ZXJyaWRlIGl0IHRvIGFsbG93IHVzZXJzIHRvIGN1c3RvbWl6ZSBpdCB2aWEgbmV4dC5jb25maWdcbiAgICAgICAgICAgIGlmIChjYWNoZUVudHJ5LmNhY2hlQ29udHJvbCAmJiAhcmVzLmdldEhlYWRlcignQ2FjaGUtQ29udHJvbCcpICYmICFoZWFkZXJzLmdldCgnQ2FjaGUtQ29udHJvbCcpKSB7XG4gICAgICAgICAgICAgICAgaGVhZGVycy5zZXQoJ0NhY2hlLUNvbnRyb2wnLCBnZXRDYWNoZUNvbnRyb2xIZWFkZXIoY2FjaGVFbnRyeS5jYWNoZUNvbnRyb2wpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGF3YWl0IHNlbmRSZXNwb25zZShub2RlTmV4dFJlcSwgbm9kZU5leHRSZXMsIG5ldyBSZXNwb25zZShjYWNoZUVudHJ5LnZhbHVlLmJvZHksIHtcbiAgICAgICAgICAgICAgICBoZWFkZXJzLFxuICAgICAgICAgICAgICAgIHN0YXR1czogY2FjaGVFbnRyeS52YWx1ZS5zdGF0dXMgfHwgMjAwXG4gICAgICAgICAgICB9KSk7XG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgfTtcbiAgICAgICAgLy8gVE9ETzogYWN0aXZlU3BhbiBjb2RlIHBhdGggaXMgZm9yIHdoZW4gd3JhcHBlZCBieVxuICAgICAgICAvLyBuZXh0LXNlcnZlciBjYW4gYmUgcmVtb3ZlZCB3aGVuIHRoaXMgaXMgbm8gbG9uZ2VyIHVzZWRcbiAgICAgICAgaWYgKGFjdGl2ZVNwYW4pIHtcbiAgICAgICAgICAgIGF3YWl0IGhhbmRsZVJlc3BvbnNlKGFjdGl2ZVNwYW4pO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgYXdhaXQgdHJhY2VyLndpdGhQcm9wYWdhdGVkQ29udGV4dChyZXEuaGVhZGVycywgKCk9PnRyYWNlci50cmFjZShCYXNlU2VydmVyU3Bhbi5oYW5kbGVSZXF1ZXN0LCB7XG4gICAgICAgICAgICAgICAgICAgIHNwYW5OYW1lOiBgJHttZXRob2R9ICR7cmVxLnVybH1gLFxuICAgICAgICAgICAgICAgICAgICBraW5kOiBTcGFuS2luZC5TRVJWRVIsXG4gICAgICAgICAgICAgICAgICAgIGF0dHJpYnV0ZXM6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICdodHRwLm1ldGhvZCc6IG1ldGhvZCxcbiAgICAgICAgICAgICAgICAgICAgICAgICdodHRwLnRhcmdldCc6IHJlcS51cmxcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0sIGhhbmRsZVJlc3BvbnNlKSk7XG4gICAgICAgIH1cbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgICAgLy8gaWYgd2UgYXJlbid0IHdyYXBwZWQgYnkgYmFzZS1zZXJ2ZXIgaGFuZGxlIGhlcmVcbiAgICAgICAgaWYgKCFhY3RpdmVTcGFuKSB7XG4gICAgICAgICAgICBhd2FpdCByb3V0ZU1vZHVsZS5vblJlcXVlc3RFcnJvcihyZXEsIGVyciwge1xuICAgICAgICAgICAgICAgIHJvdXRlcktpbmQ6ICdBcHAgUm91dGVyJyxcbiAgICAgICAgICAgICAgICByb3V0ZVBhdGg6IG5vcm1hbGl6ZWRTcmNQYWdlLFxuICAgICAgICAgICAgICAgIHJvdXRlVHlwZTogJ3JvdXRlJyxcbiAgICAgICAgICAgICAgICByZXZhbGlkYXRlUmVhc29uOiBnZXRSZXZhbGlkYXRlUmVhc29uKHtcbiAgICAgICAgICAgICAgICAgICAgaXNSZXZhbGlkYXRlLFxuICAgICAgICAgICAgICAgICAgICBpc09uRGVtYW5kUmV2YWxpZGF0ZVxuICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgICAvLyByZXRocm93IHNvIHRoYXQgd2UgY2FuIGhhbmRsZSBzZXJ2aW5nIGVycm9yIHBhZ2VcbiAgICAgICAgLy8gSWYgdGhpcyBpcyBkdXJpbmcgc3RhdGljIGdlbmVyYXRpb24sIHRocm93IHRoZSBlcnJvciBhZ2Fpbi5cbiAgICAgICAgaWYgKGlzSXNyKSB0aHJvdyBlcnI7XG4gICAgICAgIC8vIE90aGVyd2lzZSwgc2VuZCBhIDUwMCByZXNwb25zZS5cbiAgICAgICAgYXdhaXQgc2VuZFJlc3BvbnNlKG5vZGVOZXh0UmVxLCBub2RlTmV4dFJlcywgbmV3IFJlc3BvbnNlKG51bGwsIHtcbiAgICAgICAgICAgIHN0YXR1czogNTAwXG4gICAgICAgIH0pKTtcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute&page=%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute.ts&appDir=D%3A%5CCursor%20Project%5Cwebsite%5CAugment2%5Cprompt-manager%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCursor%20Project%5Cwebsite%5CAugment2%5Cprompt-manager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/trpc/[trpc]/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/trpc/[trpc]/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _trpc_server_adapters_fetch__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @trpc/server/adapters/fetch */ \"(rsc)/./node_modules/@trpc/server/dist/adapters/fetch/index.mjs\");\n/* harmony import */ var _env__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ~/env */ \"(rsc)/./src/env.js\");\n/* harmony import */ var _server_api_root__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ~/server/api/root */ \"(rsc)/./src/server/api/root.ts\");\n/* harmony import */ var _server_api_trpc__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ~/server/api/trpc */ \"(rsc)/./src/server/api/trpc.ts\");\n\n\n\n\n/**\n * This wraps the `createTRPCContext` helper and provides the required context for the tRPC API when\n * handling a HTTP request (e.g. when you make requests from Client Components).\n */ const createContext = async (req)=>{\n    return (0,_server_api_trpc__WEBPACK_IMPORTED_MODULE_2__.createTRPCContext)({\n        headers: req.headers\n    });\n};\nconst handler = (req)=>(0,_trpc_server_adapters_fetch__WEBPACK_IMPORTED_MODULE_3__.fetchRequestHandler)({\n        endpoint: \"/api/trpc\",\n        req,\n        router: _server_api_root__WEBPACK_IMPORTED_MODULE_1__.appRouter,\n        createContext: ()=>createContext(req),\n        onError: _env__WEBPACK_IMPORTED_MODULE_0__.env.NODE_ENV === \"development\" ? ({ path, error })=>{\n            console.error(`❌ tRPC failed on ${path ?? \"<no-path>\"}: ${error.message}`);\n        } : undefined\n    });\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/trpc/[trpc]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/env.js":
/*!********************!*\
  !*** ./src/env.js ***!
  \********************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   env: () => (/* binding */ env)\n/* harmony export */ });\n/* harmony import */ var _t3_oss_env_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @t3-oss/env-nextjs */ \"(rsc)/./node_modules/@t3-oss/env-nextjs/dist/index.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n\n\nconst env = (0,_t3_oss_env_nextjs__WEBPACK_IMPORTED_MODULE_0__.createEnv)({\n    /**\n   * Specify your server-side environment variables schema here. This way you can ensure the app\n   * isn't built with invalid env vars.\n   */ server: {\n        AUTH_SECRET:  false ? 0 : zod__WEBPACK_IMPORTED_MODULE_1__.string().optional(),\n        AUTH_DISCORD_ID: zod__WEBPACK_IMPORTED_MODULE_1__.string().optional(),\n        AUTH_DISCORD_SECRET: zod__WEBPACK_IMPORTED_MODULE_1__.string().optional(),\n        DATABASE_URL: zod__WEBPACK_IMPORTED_MODULE_1__.string().url(),\n        NODE_ENV: zod__WEBPACK_IMPORTED_MODULE_1__[\"enum\"]([\n            \"development\",\n            \"test\",\n            \"production\"\n        ]).default(\"development\")\n    },\n    /**\n   * Specify your client-side environment variables schema here. This way you can ensure the app\n   * isn't built with invalid env vars. To expose them to the client, prefix them with\n   * `NEXT_PUBLIC_`.\n   */ client: {\n    },\n    /**\n   * You can't destruct `process.env` as a regular object in the Next.js edge runtimes (e.g.\n   * middlewares) or client-side so we need to destruct manually.\n   */ runtimeEnv: {\n        AUTH_SECRET: process.env.AUTH_SECRET,\n        AUTH_DISCORD_ID: process.env.AUTH_DISCORD_ID,\n        AUTH_DISCORD_SECRET: process.env.AUTH_DISCORD_SECRET,\n        DATABASE_URL: process.env.DATABASE_URL,\n        NODE_ENV: \"development\"\n    },\n    /**\n   * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially\n   * useful for Docker builds.\n   */ skipValidation: !!process.env.SKIP_ENV_VALIDATION,\n    /**\n   * Makes it so that empty strings are treated as undefined. `SOME_VAR: z.string()` and\n   * `SOME_VAR=''` will throw an error.\n   */ emptyStringAsUndefined: true\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/env.js\n");

/***/ }),

/***/ "(rsc)/./src/server/api/root.ts":
/*!********************************!*\
  !*** ./src/server/api/root.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   appRouter: () => (/* binding */ appRouter),\n/* harmony export */   createCaller: () => (/* binding */ createCaller)\n/* harmony export */ });\n/* harmony import */ var _server_api_routers_category__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ~/server/api/routers/category */ \"(rsc)/./src/server/api/routers/category.ts\");\n/* harmony import */ var _server_api_routers_prompt__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ~/server/api/routers/prompt */ \"(rsc)/./src/server/api/routers/prompt.ts\");\n/* harmony import */ var _server_api_routers_stats__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ~/server/api/routers/stats */ \"(rsc)/./src/server/api/routers/stats.ts\");\n/* harmony import */ var _server_api_trpc__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ~/server/api/trpc */ \"(rsc)/./src/server/api/trpc.ts\");\n\n\n\n\n/**\n * This is the primary router for your server.\n *\n * All routers added in /api/routers should be manually added here.\n */ const appRouter = (0,_server_api_trpc__WEBPACK_IMPORTED_MODULE_3__.createTRPCRouter)({\n    category: _server_api_routers_category__WEBPACK_IMPORTED_MODULE_0__.categoryRouter,\n    prompt: _server_api_routers_prompt__WEBPACK_IMPORTED_MODULE_1__.promptRouter,\n    stats: _server_api_routers_stats__WEBPACK_IMPORTED_MODULE_2__.statsRouter\n});\n/**\n * Create a server-side caller for the tRPC API.\n * @example\n * const trpc = createCaller(createContext);\n * const res = await trpc.post.all();\n *       ^? Post[]\n */ const createCaller = (0,_server_api_trpc__WEBPACK_IMPORTED_MODULE_3__.createCallerFactory)(appRouter);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc2VydmVyL2FwaS9yb290LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUErRDtBQUNKO0FBQ0Y7QUFDaUI7QUFFMUU7Ozs7Q0FJQyxHQUNNLE1BQU1LLFlBQVlELGtFQUFnQkEsQ0FBQztJQUN4Q0UsVUFBVU4sd0VBQWNBO0lBQ3hCTyxRQUFRTixvRUFBWUE7SUFDcEJPLE9BQU9OLGtFQUFXQTtBQUNwQixHQUFHO0FBS0g7Ozs7OztDQU1DLEdBQ00sTUFBTU8sZUFBZU4scUVBQW1CQSxDQUFDRSxXQUFXIiwic291cmNlcyI6WyJEOlxcQ3Vyc29yIFByb2plY3RcXHdlYnNpdGVcXEF1Z21lbnQyXFxwcm9tcHQtbWFuYWdlclxcc3JjXFxzZXJ2ZXJcXGFwaVxccm9vdC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjYXRlZ29yeVJvdXRlciB9IGZyb20gXCJ+L3NlcnZlci9hcGkvcm91dGVycy9jYXRlZ29yeVwiO1xuaW1wb3J0IHsgcHJvbXB0Um91dGVyIH0gZnJvbSBcIn4vc2VydmVyL2FwaS9yb3V0ZXJzL3Byb21wdFwiO1xuaW1wb3J0IHsgc3RhdHNSb3V0ZXIgfSBmcm9tIFwifi9zZXJ2ZXIvYXBpL3JvdXRlcnMvc3RhdHNcIjtcbmltcG9ydCB7IGNyZWF0ZUNhbGxlckZhY3RvcnksIGNyZWF0ZVRSUENSb3V0ZXIgfSBmcm9tIFwifi9zZXJ2ZXIvYXBpL3RycGNcIjtcblxuLyoqXG4gKiBUaGlzIGlzIHRoZSBwcmltYXJ5IHJvdXRlciBmb3IgeW91ciBzZXJ2ZXIuXG4gKlxuICogQWxsIHJvdXRlcnMgYWRkZWQgaW4gL2FwaS9yb3V0ZXJzIHNob3VsZCBiZSBtYW51YWxseSBhZGRlZCBoZXJlLlxuICovXG5leHBvcnQgY29uc3QgYXBwUm91dGVyID0gY3JlYXRlVFJQQ1JvdXRlcih7XG4gIGNhdGVnb3J5OiBjYXRlZ29yeVJvdXRlcixcbiAgcHJvbXB0OiBwcm9tcHRSb3V0ZXIsXG4gIHN0YXRzOiBzdGF0c1JvdXRlcixcbn0pO1xuXG4vLyBleHBvcnQgdHlwZSBkZWZpbml0aW9uIG9mIEFQSVxuZXhwb3J0IHR5cGUgQXBwUm91dGVyID0gdHlwZW9mIGFwcFJvdXRlcjtcblxuLyoqXG4gKiBDcmVhdGUgYSBzZXJ2ZXItc2lkZSBjYWxsZXIgZm9yIHRoZSB0UlBDIEFQSS5cbiAqIEBleGFtcGxlXG4gKiBjb25zdCB0cnBjID0gY3JlYXRlQ2FsbGVyKGNyZWF0ZUNvbnRleHQpO1xuICogY29uc3QgcmVzID0gYXdhaXQgdHJwYy5wb3N0LmFsbCgpO1xuICogICAgICAgXj8gUG9zdFtdXG4gKi9cbmV4cG9ydCBjb25zdCBjcmVhdGVDYWxsZXIgPSBjcmVhdGVDYWxsZXJGYWN0b3J5KGFwcFJvdXRlcik7XG4iXSwibmFtZXMiOlsiY2F0ZWdvcnlSb3V0ZXIiLCJwcm9tcHRSb3V0ZXIiLCJzdGF0c1JvdXRlciIsImNyZWF0ZUNhbGxlckZhY3RvcnkiLCJjcmVhdGVUUlBDUm91dGVyIiwiYXBwUm91dGVyIiwiY2F0ZWdvcnkiLCJwcm9tcHQiLCJzdGF0cyIsImNyZWF0ZUNhbGxlciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/server/api/root.ts\n");

/***/ }),

/***/ "(rsc)/./src/server/api/routers/category.ts":
/*!********************************************!*\
  !*** ./src/server/api/routers/category.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   categoryRouter: () => (/* binding */ categoryRouter)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ~/server/api/trpc */ \"(rsc)/./src/server/api/trpc.ts\");\n\n\nconst categoryRouter = (0,_server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.createTRPCRouter)({\n    // 获取所有分类\n    getAll: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.publicProcedure.query(async ({ ctx })=>{\n        return ctx.db.category.findMany({\n            orderBy: {\n                createdAt: \"desc\"\n            },\n            include: {\n                _count: {\n                    select: {\n                        prompts: true\n                    }\n                }\n            }\n        });\n    }),\n    // 根据ID获取分类\n    getById: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.publicProcedure.input(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        id: zod__WEBPACK_IMPORTED_MODULE_1__.string()\n    })).query(async ({ ctx, input })=>{\n        return ctx.db.category.findUnique({\n            where: {\n                id: input.id\n            },\n            include: {\n                prompts: {\n                    orderBy: {\n                        createdAt: \"desc\"\n                    },\n                    take: 10\n                },\n                _count: {\n                    select: {\n                        prompts: true\n                    }\n                }\n            }\n        });\n    }),\n    // 创建分类\n    create: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.protectedProcedure.input(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        name: zod__WEBPACK_IMPORTED_MODULE_1__.string().min(1, \"分类名称不能为空\").max(50, \"分类名称不能超过50个字符\"),\n        description: zod__WEBPACK_IMPORTED_MODULE_1__.string().optional(),\n        color: zod__WEBPACK_IMPORTED_MODULE_1__.string().regex(/^#[0-9A-F]{6}$/i, \"颜色格式不正确\").default(\"#3B82F6\"),\n        icon: zod__WEBPACK_IMPORTED_MODULE_1__.string().optional()\n    })).mutation(async ({ ctx, input })=>{\n        return ctx.db.category.create({\n            data: {\n                ...input,\n                createdById: ctx.session.user.id\n            }\n        });\n    }),\n    // 更新分类\n    update: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.protectedProcedure.input(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        id: zod__WEBPACK_IMPORTED_MODULE_1__.string(),\n        name: zod__WEBPACK_IMPORTED_MODULE_1__.string().min(1, \"分类名称不能为空\").max(50, \"分类名称不能超过50个字符\").optional(),\n        description: zod__WEBPACK_IMPORTED_MODULE_1__.string().optional(),\n        color: zod__WEBPACK_IMPORTED_MODULE_1__.string().regex(/^#[0-9A-F]{6}$/i, \"颜色格式不正确\").optional(),\n        icon: zod__WEBPACK_IMPORTED_MODULE_1__.string().optional()\n    })).mutation(async ({ ctx, input })=>{\n        const { id, ...updateData } = input;\n        // 检查分类是否存在且属于当前用户\n        const category = await ctx.db.category.findUnique({\n            where: {\n                id\n            }\n        });\n        if (!category) {\n            throw new Error(\"分类不存在\");\n        }\n        if (category.createdById !== ctx.session.user.id) {\n            throw new Error(\"无权限修改此分类\");\n        }\n        return ctx.db.category.update({\n            where: {\n                id\n            },\n            data: updateData\n        });\n    }),\n    // 删除分类\n    delete: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.protectedProcedure.input(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        id: zod__WEBPACK_IMPORTED_MODULE_1__.string()\n    })).mutation(async ({ ctx, input })=>{\n        // 检查分类是否存在且属于当前用户\n        const category = await ctx.db.category.findUnique({\n            where: {\n                id: input.id\n            },\n            include: {\n                _count: {\n                    select: {\n                        prompts: true\n                    }\n                }\n            }\n        });\n        if (!category) {\n            throw new Error(\"分类不存在\");\n        }\n        if (category.createdById !== ctx.session.user.id) {\n            throw new Error(\"无权限删除此分类\");\n        }\n        if (category._count.prompts > 0) {\n            throw new Error(\"该分类下还有提示词，无法删除\");\n        }\n        return ctx.db.category.delete({\n            where: {\n                id: input.id\n            }\n        });\n    }),\n    // 获取分类统计信息\n    getStats: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.publicProcedure.query(async ({ ctx })=>{\n        const stats = await ctx.db.category.findMany({\n            select: {\n                id: true,\n                name: true,\n                color: true,\n                _count: {\n                    select: {\n                        prompts: true\n                    }\n                }\n            },\n            orderBy: {\n                prompts: {\n                    _count: \"desc\"\n                }\n            }\n        });\n        return stats;\n    })\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/server/api/routers/category.ts\n");

/***/ }),

/***/ "(rsc)/./src/server/api/routers/prompt.ts":
/*!******************************************!*\
  !*** ./src/server/api/routers/prompt.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   promptRouter: () => (/* binding */ promptRouter)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ~/server/api/trpc */ \"(rsc)/./src/server/api/trpc.ts\");\n\n\nconst promptRouter = (0,_server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.createTRPCRouter)({\n    // 获取所有提示词（支持分页、搜索、筛选）\n    getAll: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.publicProcedure.input(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        page: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(1).default(1),\n        limit: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(1).max(100).default(20),\n        search: zod__WEBPACK_IMPORTED_MODULE_1__.string().optional(),\n        categoryId: zod__WEBPACK_IMPORTED_MODULE_1__.string().optional(),\n        sortBy: zod__WEBPACK_IMPORTED_MODULE_1__[\"enum\"]([\n            \"createdAt\",\n            \"updatedAt\",\n            \"usageCount\",\n            \"title\"\n        ]).default(\"createdAt\"),\n        sortOrder: zod__WEBPACK_IMPORTED_MODULE_1__[\"enum\"]([\n            \"asc\",\n            \"desc\"\n        ]).default(\"desc\")\n    })).query(async ({ ctx, input })=>{\n        const { page, limit, search, categoryId, sortBy, sortOrder } = input;\n        const skip = (page - 1) * limit;\n        // 构建查询条件\n        const where = {\n            isPublic: true\n        };\n        if (search) {\n            where.OR = [\n                {\n                    title: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    description: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    content: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    tags: {\n                        hasSome: [\n                            search\n                        ]\n                    }\n                }\n            ];\n        }\n        if (categoryId) {\n            where.categoryId = categoryId;\n        }\n        // 获取总数\n        const total = await ctx.db.prompt.count({\n            where\n        });\n        // 获取数据\n        const prompts = await ctx.db.prompt.findMany({\n            where,\n            skip,\n            take: limit,\n            orderBy: {\n                [sortBy]: sortOrder\n            },\n            include: {\n                category: {\n                    select: {\n                        id: true,\n                        name: true,\n                        color: true,\n                        icon: true\n                    }\n                },\n                createdBy: {\n                    select: {\n                        id: true,\n                        name: true,\n                        image: true\n                    }\n                }\n            }\n        });\n        return {\n            prompts,\n            pagination: {\n                page,\n                limit,\n                total,\n                totalPages: Math.ceil(total / limit)\n            }\n        };\n    }),\n    // 根据ID获取提示词详情\n    getById: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.publicProcedure.input(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        id: zod__WEBPACK_IMPORTED_MODULE_1__.string()\n    })).query(async ({ ctx, input })=>{\n        return ctx.db.prompt.findUnique({\n            where: {\n                id: input.id\n            },\n            include: {\n                category: {\n                    select: {\n                        id: true,\n                        name: true,\n                        color: true,\n                        icon: true\n                    }\n                },\n                createdBy: {\n                    select: {\n                        id: true,\n                        name: true,\n                        image: true\n                    }\n                }\n            }\n        });\n    }),\n    // 创建提示词\n    create: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.protectedProcedure.input(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        title: zod__WEBPACK_IMPORTED_MODULE_1__.string().min(1, \"标题不能为空\").max(100, \"标题不能超过100个字符\"),\n        content: zod__WEBPACK_IMPORTED_MODULE_1__.string().min(1, \"内容不能为空\"),\n        description: zod__WEBPACK_IMPORTED_MODULE_1__.string().max(200, \"描述不能超过200个字符\").optional(),\n        tags: zod__WEBPACK_IMPORTED_MODULE_1__.array(zod__WEBPACK_IMPORTED_MODULE_1__.string()).max(10, \"标签不能超过10个\").default([]),\n        categoryId: zod__WEBPACK_IMPORTED_MODULE_1__.string().optional(),\n        isPublic: zod__WEBPACK_IMPORTED_MODULE_1__.boolean().default(true)\n    })).mutation(async ({ ctx, input })=>{\n        return ctx.db.prompt.create({\n            data: {\n                ...input,\n                createdById: ctx.session.user.id\n            },\n            include: {\n                category: {\n                    select: {\n                        id: true,\n                        name: true,\n                        color: true,\n                        icon: true\n                    }\n                }\n            }\n        });\n    }),\n    // 更新提示词\n    update: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.protectedProcedure.input(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        id: zod__WEBPACK_IMPORTED_MODULE_1__.string(),\n        title: zod__WEBPACK_IMPORTED_MODULE_1__.string().min(1, \"标题不能为空\").max(100, \"标题不能超过100个字符\").optional(),\n        content: zod__WEBPACK_IMPORTED_MODULE_1__.string().min(1, \"内容不能为空\").optional(),\n        description: zod__WEBPACK_IMPORTED_MODULE_1__.string().max(200, \"描述不能超过200个字符\").optional(),\n        tags: zod__WEBPACK_IMPORTED_MODULE_1__.array(zod__WEBPACK_IMPORTED_MODULE_1__.string()).max(10, \"标签不能超过10个\").optional(),\n        categoryId: zod__WEBPACK_IMPORTED_MODULE_1__.string().optional(),\n        isPublic: zod__WEBPACK_IMPORTED_MODULE_1__.boolean().optional()\n    })).mutation(async ({ ctx, input })=>{\n        const { id, ...updateData } = input;\n        // 检查提示词是否存在且属于当前用户\n        const prompt = await ctx.db.prompt.findUnique({\n            where: {\n                id\n            }\n        });\n        if (!prompt) {\n            throw new Error(\"提示词不存在\");\n        }\n        if (prompt.createdById !== ctx.session.user.id) {\n            throw new Error(\"无权限修改此提示词\");\n        }\n        return ctx.db.prompt.update({\n            where: {\n                id\n            },\n            data: updateData,\n            include: {\n                category: {\n                    select: {\n                        id: true,\n                        name: true,\n                        color: true,\n                        icon: true\n                    }\n                }\n            }\n        });\n    }),\n    // 删除提示词\n    delete: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.protectedProcedure.input(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        id: zod__WEBPACK_IMPORTED_MODULE_1__.string()\n    })).mutation(async ({ ctx, input })=>{\n        // 检查提示词是否存在且属于当前用户\n        const prompt = await ctx.db.prompt.findUnique({\n            where: {\n                id: input.id\n            }\n        });\n        if (!prompt) {\n            throw new Error(\"提示词不存在\");\n        }\n        if (prompt.createdById !== ctx.session.user.id) {\n            throw new Error(\"无权限删除此提示词\");\n        }\n        return ctx.db.prompt.delete({\n            where: {\n                id: input.id\n            }\n        });\n    }),\n    // 复制提示词（增加使用次数）\n    copy: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.publicProcedure.input(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        id: zod__WEBPACK_IMPORTED_MODULE_1__.string()\n    })).mutation(async ({ ctx, input })=>{\n        // 更新使用次数\n        const prompt = await ctx.db.prompt.update({\n            where: {\n                id: input.id\n            },\n            data: {\n                usageCount: {\n                    increment: 1\n                }\n            }\n        });\n        // 如果用户已登录，记录使用历史\n        if (ctx.session?.user?.id) {\n            await ctx.db.promptUsage.create({\n                data: {\n                    promptId: input.id,\n                    userId: ctx.session.user.id\n                }\n            });\n        }\n        return prompt;\n    }),\n    // 获取我的提示词\n    getMine: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.protectedProcedure.input(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        page: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(1).default(1),\n        limit: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(1).max(100).default(20),\n        search: zod__WEBPACK_IMPORTED_MODULE_1__.string().optional(),\n        categoryId: zod__WEBPACK_IMPORTED_MODULE_1__.string().optional()\n    })).query(async ({ ctx, input })=>{\n        const { page, limit, search, categoryId } = input;\n        const skip = (page - 1) * limit;\n        const where = {\n            createdById: ctx.session.user.id\n        };\n        if (search) {\n            where.OR = [\n                {\n                    title: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    description: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    content: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                }\n            ];\n        }\n        if (categoryId) {\n            where.categoryId = categoryId;\n        }\n        const total = await ctx.db.prompt.count({\n            where\n        });\n        const prompts = await ctx.db.prompt.findMany({\n            where,\n            skip,\n            take: limit,\n            orderBy: {\n                updatedAt: \"desc\"\n            },\n            include: {\n                category: {\n                    select: {\n                        id: true,\n                        name: true,\n                        color: true,\n                        icon: true\n                    }\n                }\n            }\n        });\n        return {\n            prompts,\n            pagination: {\n                page,\n                limit,\n                total,\n                totalPages: Math.ceil(total / limit)\n            }\n        };\n    }),\n    // 获取热门提示词\n    getPopular: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.publicProcedure.input(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        limit: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(1).max(50).default(10)\n    })).query(async ({ ctx, input })=>{\n        return ctx.db.prompt.findMany({\n            where: {\n                isPublic: true\n            },\n            take: input.limit,\n            orderBy: {\n                usageCount: \"desc\"\n            },\n            include: {\n                category: {\n                    select: {\n                        id: true,\n                        name: true,\n                        color: true,\n                        icon: true\n                    }\n                },\n                createdBy: {\n                    select: {\n                        id: true,\n                        name: true,\n                        image: true\n                    }\n                }\n            }\n        });\n    }),\n    // 获取最新提示词\n    getLatest: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.publicProcedure.input(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        limit: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(1).max(50).default(10)\n    })).query(async ({ ctx, input })=>{\n        return ctx.db.prompt.findMany({\n            where: {\n                isPublic: true\n            },\n            take: input.limit,\n            orderBy: {\n                createdAt: \"desc\"\n            },\n            include: {\n                category: {\n                    select: {\n                        id: true,\n                        name: true,\n                        color: true,\n                        icon: true\n                    }\n                },\n                createdBy: {\n                    select: {\n                        id: true,\n                        name: true,\n                        image: true\n                    }\n                }\n            }\n        });\n    })\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/server/api/routers/prompt.ts\n");

/***/ }),

/***/ "(rsc)/./src/server/api/routers/stats.ts":
/*!*****************************************!*\
  !*** ./src/server/api/routers/stats.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   statsRouter: () => (/* binding */ statsRouter)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ~/server/api/trpc */ \"(rsc)/./src/server/api/trpc.ts\");\n\n\nconst statsRouter = (0,_server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.createTRPCRouter)({\n    // 获取总体统计信息\n    getOverview: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.publicProcedure.query(async ({ ctx })=>{\n        const [totalPrompts, totalCategories, totalUsers, totalUsages] = await Promise.all([\n            ctx.db.prompt.count({\n                where: {\n                    isPublic: true\n                }\n            }),\n            ctx.db.category.count(),\n            ctx.db.user.count(),\n            ctx.db.promptUsage.count()\n        ]);\n        return {\n            totalPrompts,\n            totalCategories,\n            totalUsers,\n            totalUsages\n        };\n    }),\n    // 获取分类统计\n    getCategoryStats: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.publicProcedure.query(async ({ ctx })=>{\n        const categoryStats = await ctx.db.category.findMany({\n            select: {\n                id: true,\n                name: true,\n                color: true,\n                icon: true,\n                _count: {\n                    select: {\n                        prompts: true\n                    }\n                }\n            },\n            orderBy: {\n                prompts: {\n                    _count: \"desc\"\n                }\n            }\n        });\n        return categoryStats.map((category)=>({\n                id: category.id,\n                name: category.name,\n                color: category.color,\n                icon: category.icon,\n                promptCount: category._count.prompts\n            }));\n    }),\n    // 获取使用趋势（最近30天）\n    getUsageTrend: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.publicProcedure.query(async ({ ctx })=>{\n        const thirtyDaysAgo = new Date();\n        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n        const usageData = await ctx.db.promptUsage.findMany({\n            where: {\n                createdAt: {\n                    gte: thirtyDaysAgo\n                }\n            },\n            select: {\n                createdAt: true\n            },\n            orderBy: {\n                createdAt: \"asc\"\n            }\n        });\n        // 按日期分组统计\n        const dailyUsage = new Map();\n        // 初始化最近30天的数据\n        for(let i = 29; i >= 0; i--){\n            const date = new Date();\n            date.setDate(date.getDate() - i);\n            const dateStr = date.toISOString().split('T')[0];\n            dailyUsage.set(dateStr, 0);\n        }\n        // 统计实际使用数据\n        usageData.forEach((usage)=>{\n            const dateStr = usage.createdAt.toISOString().split('T')[0];\n            if (dateStr) {\n                dailyUsage.set(dateStr, (dailyUsage.get(dateStr) || 0) + 1);\n            }\n        });\n        return Array.from(dailyUsage.entries()).map(([date, count])=>({\n                date,\n                count\n            }));\n    }),\n    // 获取热门标签\n    getPopularTags: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.publicProcedure.input(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        limit: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(1).max(50).default(20)\n    })).query(async ({ ctx, input })=>{\n        const prompts = await ctx.db.prompt.findMany({\n            where: {\n                isPublic: true\n            },\n            select: {\n                tags: true\n            }\n        });\n        // 统计标签使用频率\n        const tagCount = new Map();\n        prompts.forEach((prompt)=>{\n            prompt.tags.forEach((tag)=>{\n                tagCount.set(tag, (tagCount.get(tag) || 0) + 1);\n            });\n        });\n        // 排序并返回前N个\n        return Array.from(tagCount.entries()).sort((a, b)=>b[1] - a[1]).slice(0, input.limit).map(([tag, count])=>({\n                tag,\n                count\n            }));\n    }),\n    // 获取用户个人统计（需要登录）\n    getMyStats: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.protectedProcedure.query(async ({ ctx })=>{\n        const userId = ctx.session.user.id;\n        const [myPrompts, myCategories, myUsages, myTotalUsageCount] = await Promise.all([\n            ctx.db.prompt.count({\n                where: {\n                    createdById: userId\n                }\n            }),\n            ctx.db.category.count({\n                where: {\n                    createdById: userId\n                }\n            }),\n            ctx.db.promptUsage.count({\n                where: {\n                    userId\n                }\n            }),\n            ctx.db.prompt.aggregate({\n                where: {\n                    createdById: userId\n                },\n                _sum: {\n                    usageCount: true\n                }\n            })\n        ]);\n        // 获取我最常用的分类\n        const myTopCategories = await ctx.db.category.findMany({\n            where: {\n                createdById: userId\n            },\n            select: {\n                id: true,\n                name: true,\n                color: true,\n                _count: {\n                    select: {\n                        prompts: true\n                    }\n                }\n            },\n            orderBy: {\n                prompts: {\n                    _count: \"desc\"\n                }\n            },\n            take: 5\n        });\n        // 获取我最受欢迎的提示词\n        const myTopPrompts = await ctx.db.prompt.findMany({\n            where: {\n                createdById: userId\n            },\n            select: {\n                id: true,\n                title: true,\n                usageCount: true,\n                category: {\n                    select: {\n                        name: true,\n                        color: true\n                    }\n                }\n            },\n            orderBy: {\n                usageCount: \"desc\"\n            },\n            take: 5\n        });\n        return {\n            myPrompts,\n            myCategories,\n            myUsages,\n            myTotalUsageCount: myTotalUsageCount._sum.usageCount || 0,\n            myTopCategories: myTopCategories.map((cat)=>({\n                    id: cat.id,\n                    name: cat.name,\n                    color: cat.color,\n                    promptCount: cat._count.prompts\n                })),\n            myTopPrompts\n        };\n    }),\n    // 获取最近使用的提示词（需要登录）\n    getRecentUsage: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.protectedProcedure.input(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        limit: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(1).max(50).default(10)\n    })).query(async ({ ctx, input })=>{\n        const recentUsages = await ctx.db.promptUsage.findMany({\n            where: {\n                userId: ctx.session.user.id\n            },\n            take: input.limit,\n            orderBy: {\n                createdAt: \"desc\"\n            },\n            include: {\n                prompt: {\n                    select: {\n                        id: true,\n                        title: true,\n                        description: true,\n                        category: {\n                            select: {\n                                name: true,\n                                color: true,\n                                icon: true\n                            }\n                        }\n                    }\n                }\n            }\n        });\n        return recentUsages.map((usage)=>({\n                id: usage.id,\n                usedAt: usage.createdAt,\n                prompt: usage.prompt\n            }));\n    }),\n    // 获取使用排行榜\n    getUsageLeaderboard: _server_api_trpc__WEBPACK_IMPORTED_MODULE_0__.publicProcedure.input(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        limit: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(1).max(50).default(10)\n    })).query(async ({ ctx, input })=>{\n        return ctx.db.prompt.findMany({\n            where: {\n                isPublic: true\n            },\n            take: input.limit,\n            orderBy: {\n                usageCount: \"desc\"\n            },\n            select: {\n                id: true,\n                title: true,\n                description: true,\n                usageCount: true,\n                category: {\n                    select: {\n                        name: true,\n                        color: true,\n                        icon: true\n                    }\n                },\n                createdBy: {\n                    select: {\n                        name: true,\n                        image: true\n                    }\n                }\n            }\n        });\n    })\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/server/api/routers/stats.ts\n");

/***/ }),

/***/ "(rsc)/./src/server/api/trpc.ts":
/*!********************************!*\
  !*** ./src/server/api/trpc.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCallerFactory: () => (/* binding */ createCallerFactory),\n/* harmony export */   createTRPCContext: () => (/* binding */ createTRPCContext),\n/* harmony export */   createTRPCRouter: () => (/* binding */ createTRPCRouter),\n/* harmony export */   protectedProcedure: () => (/* binding */ protectedProcedure),\n/* harmony export */   publicProcedure: () => (/* binding */ publicProcedure)\n/* harmony export */ });\n/* harmony import */ var _trpc_server__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @trpc/server */ \"(rsc)/./node_modules/@trpc/server/dist/initTRPC-IT_6ZYJd.mjs\");\n/* harmony import */ var _trpc_server__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @trpc/server */ \"(rsc)/./node_modules/@trpc/server/dist/tracked-gU3ttYjg.mjs\");\n/* harmony import */ var superjson__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! superjson */ \"(rsc)/./node_modules/superjson/dist/index.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/ZodError.js\");\n/* harmony import */ var _server_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ~/server/auth */ \"(rsc)/./src/server/auth/index.ts\");\n/* harmony import */ var _server_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ~/server/db */ \"(rsc)/./src/server/db.ts\");\n/**\n * YOU PROBABLY DON'T NEED TO EDIT THIS FILE, UNLESS:\n * 1. You want to modify request context (see Part 1).\n * 2. You want to create a new middleware or type of procedure (see Part 3).\n *\n * TL;DR - This is where all the tRPC server stuff is created and plugged in. The pieces you will\n * need to use are documented accordingly near the end.\n */ \n\n\n\n\n/**\n * 1. CONTEXT\n *\n * This section defines the \"contexts\" that are available in the backend API.\n *\n * These allow you to access things when processing a request, like the database, the session, etc.\n *\n * This helper generates the \"internals\" for a tRPC context. The API handler and RSC clients each\n * wrap this and provides the required context.\n *\n * @see https://trpc.io/docs/server/context\n */ const createTRPCContext = async (opts)=>{\n    const session = await (0,_server_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n    return {\n        db: _server_db__WEBPACK_IMPORTED_MODULE_2__.db,\n        session,\n        ...opts\n    };\n};\n/**\n * 2. INITIALIZATION\n *\n * This is where the tRPC API is initialized, connecting the context and transformer. We also parse\n * ZodErrors so that you get typesafety on the frontend if your procedure fails due to validation\n * errors on the backend.\n */ const t = _trpc_server__WEBPACK_IMPORTED_MODULE_3__.initTRPC.context().create({\n    transformer: superjson__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n    errorFormatter ({ shape, error }) {\n        return {\n            ...shape,\n            data: {\n                ...shape.data,\n                zodError: error.cause instanceof zod__WEBPACK_IMPORTED_MODULE_4__.ZodError ? error.cause.flatten() : null\n            }\n        };\n    }\n});\n/**\n * Create a server-side caller.\n *\n * @see https://trpc.io/docs/server/server-side-calls\n */ const createCallerFactory = t.createCallerFactory;\n/**\n * 3. ROUTER & PROCEDURE (THE IMPORTANT BIT)\n *\n * These are the pieces you use to build your tRPC API. You should import these a lot in the\n * \"/src/server/api/routers\" directory.\n */ /**\n * This is how you create new routers and sub-routers in your tRPC API.\n *\n * @see https://trpc.io/docs/router\n */ const createTRPCRouter = t.router;\n/**\n * Middleware for timing procedure execution and adding an artificial delay in development.\n *\n * You can remove this if you don't like it, but it can help catch unwanted waterfalls by simulating\n * network latency that would occur in production but not in local development.\n */ const timingMiddleware = t.middleware(async ({ next, path })=>{\n    const start = Date.now();\n    if (t._config.isDev) {\n        // artificial delay in dev\n        const waitMs = Math.floor(Math.random() * 400) + 100;\n        await new Promise((resolve)=>setTimeout(resolve, waitMs));\n    }\n    const result = await next();\n    const end = Date.now();\n    console.log(`[TRPC] ${path} took ${end - start}ms to execute`);\n    return result;\n});\n/**\n * Public (unauthenticated) procedure\n *\n * This is the base piece you use to build new queries and mutations on your tRPC API. It does not\n * guarantee that a user querying is authorized, but you can still access user session data if they\n * are logged in.\n */ const publicProcedure = t.procedure.use(timingMiddleware);\n/**\n * Protected (authenticated) procedure\n *\n * If you want a query or mutation to ONLY be accessible to logged in users, use this. It verifies\n * the session is valid and guarantees `ctx.session.user` is not null.\n *\n * @see https://trpc.io/docs/procedures\n */ const protectedProcedure = t.procedure.use(timingMiddleware).use({\n    \"use[protectedProcedure]\": ({ ctx, next })=>{\n        if (!ctx.session?.user) {\n            throw new _trpc_server__WEBPACK_IMPORTED_MODULE_5__.TRPCError({\n                code: \"UNAUTHORIZED\"\n            });\n        }\n        return next({\n            ctx: {\n                // infers the `session` as non-nullable\n                session: {\n                    ...ctx.session,\n                    user: ctx.session.user\n                }\n            }\n        });\n    }\n}[\"use[protectedProcedure]\"]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc2VydmVyL2FwaS90cnBjLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQUE7Ozs7Ozs7Q0FPQyxHQUVrRDtBQUNqQjtBQUNIO0FBRU07QUFDSjtBQUVqQzs7Ozs7Ozs7Ozs7Q0FXQyxHQUNNLE1BQU1NLG9CQUFvQixPQUFPQztJQUN0QyxNQUFNQyxVQUFVLE1BQU1KLGtEQUFJQTtJQUUxQixPQUFPO1FBQ0xDLEVBQUVBLDRDQUFBQTtRQUNGRztRQUNBLEdBQUdELElBQUk7SUFDVDtBQUNGLEVBQUU7QUFFRjs7Ozs7O0NBTUMsR0FDRCxNQUFNRSxJQUFJVCxrREFBUUEsQ0FBQ1UsT0FBTyxHQUE2QkMsTUFBTSxDQUFDO0lBQzVEQyxhQUFhVixpREFBU0E7SUFDdEJXLGdCQUFlLEVBQUVDLEtBQUssRUFBRUMsS0FBSyxFQUFFO1FBQzdCLE9BQU87WUFDTCxHQUFHRCxLQUFLO1lBQ1JFLE1BQU07Z0JBQ0osR0FBR0YsTUFBTUUsSUFBSTtnQkFDYkMsVUFDRUYsTUFBTUcsS0FBSyxZQUFZZix5Q0FBUUEsR0FBR1ksTUFBTUcsS0FBSyxDQUFDQyxPQUFPLEtBQUs7WUFDOUQ7UUFDRjtJQUNGO0FBQ0Y7QUFFQTs7OztDQUlDLEdBQ00sTUFBTUMsc0JBQXNCWCxFQUFFVyxtQkFBbUIsQ0FBQztBQUV6RDs7Ozs7Q0FLQyxHQUVEOzs7O0NBSUMsR0FDTSxNQUFNQyxtQkFBbUJaLEVBQUVhLE1BQU0sQ0FBQztBQUV6Qzs7Ozs7Q0FLQyxHQUNELE1BQU1DLG1CQUFtQmQsRUFBRWUsVUFBVSxDQUFDLE9BQU8sRUFBRUMsSUFBSSxFQUFFQyxJQUFJLEVBQUU7SUFDekQsTUFBTUMsUUFBUUMsS0FBS0MsR0FBRztJQUV0QixJQUFJcEIsRUFBRXFCLE9BQU8sQ0FBQ0MsS0FBSyxFQUFFO1FBQ25CLDBCQUEwQjtRQUMxQixNQUFNQyxTQUFTQyxLQUFLQyxLQUFLLENBQUNELEtBQUtFLE1BQU0sS0FBSyxPQUFPO1FBQ2pELE1BQU0sSUFBSUMsUUFBUSxDQUFDQyxVQUFZQyxXQUFXRCxTQUFTTDtJQUNyRDtJQUVBLE1BQU1PLFNBQVMsTUFBTWQ7SUFFckIsTUFBTWUsTUFBTVosS0FBS0MsR0FBRztJQUNwQlksUUFBUUMsR0FBRyxDQUFDLENBQUMsT0FBTyxFQUFFaEIsS0FBSyxNQUFNLEVBQUVjLE1BQU1iLE1BQU0sYUFBYSxDQUFDO0lBRTdELE9BQU9ZO0FBQ1Q7QUFFQTs7Ozs7O0NBTUMsR0FDTSxNQUFNSSxrQkFBa0JsQyxFQUFFbUMsU0FBUyxDQUFDQyxHQUFHLENBQUN0QixrQkFBa0I7QUFFakU7Ozs7Ozs7Q0FPQyxHQUNNLE1BQU11QixxQkFBcUJyQyxFQUFFbUMsU0FBUyxDQUMxQ0MsR0FBRyxDQUFDdEIsa0JBQ0pzQixHQUFHOytCQUFDLENBQUMsRUFBRUUsR0FBRyxFQUFFdEIsSUFBSSxFQUFFO1FBQ2pCLElBQUksQ0FBQ3NCLElBQUl2QyxPQUFPLEVBQUV3QyxNQUFNO1lBQ3RCLE1BQU0sSUFBSS9DLG1EQUFTQSxDQUFDO2dCQUFFZ0QsTUFBTTtZQUFlO1FBQzdDO1FBQ0EsT0FBT3hCLEtBQUs7WUFDVnNCLEtBQUs7Z0JBQ0gsdUNBQXVDO2dCQUN2Q3ZDLFNBQVM7b0JBQUUsR0FBR3VDLElBQUl2QyxPQUFPO29CQUFFd0MsTUFBTUQsSUFBSXZDLE9BQU8sQ0FBQ3dDLElBQUk7Z0JBQUM7WUFDcEQ7UUFDRjtJQUNGOzhCQUFHIiwic291cmNlcyI6WyJEOlxcQ3Vyc29yIFByb2plY3RcXHdlYnNpdGVcXEF1Z21lbnQyXFxwcm9tcHQtbWFuYWdlclxcc3JjXFxzZXJ2ZXJcXGFwaVxcdHJwYy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFlPVSBQUk9CQUJMWSBET04nVCBORUVEIFRPIEVESVQgVEhJUyBGSUxFLCBVTkxFU1M6XG4gKiAxLiBZb3Ugd2FudCB0byBtb2RpZnkgcmVxdWVzdCBjb250ZXh0IChzZWUgUGFydCAxKS5cbiAqIDIuIFlvdSB3YW50IHRvIGNyZWF0ZSBhIG5ldyBtaWRkbGV3YXJlIG9yIHR5cGUgb2YgcHJvY2VkdXJlIChzZWUgUGFydCAzKS5cbiAqXG4gKiBUTDtEUiAtIFRoaXMgaXMgd2hlcmUgYWxsIHRoZSB0UlBDIHNlcnZlciBzdHVmZiBpcyBjcmVhdGVkIGFuZCBwbHVnZ2VkIGluLiBUaGUgcGllY2VzIHlvdSB3aWxsXG4gKiBuZWVkIHRvIHVzZSBhcmUgZG9jdW1lbnRlZCBhY2NvcmRpbmdseSBuZWFyIHRoZSBlbmQuXG4gKi9cblxuaW1wb3J0IHsgaW5pdFRSUEMsIFRSUENFcnJvciB9IGZyb20gXCJAdHJwYy9zZXJ2ZXJcIjtcbmltcG9ydCBzdXBlcmpzb24gZnJvbSBcInN1cGVyanNvblwiO1xuaW1wb3J0IHsgWm9kRXJyb3IgfSBmcm9tIFwiem9kXCI7XG5cbmltcG9ydCB7IGF1dGggfSBmcm9tIFwifi9zZXJ2ZXIvYXV0aFwiO1xuaW1wb3J0IHsgZGIgfSBmcm9tIFwifi9zZXJ2ZXIvZGJcIjtcblxuLyoqXG4gKiAxLiBDT05URVhUXG4gKlxuICogVGhpcyBzZWN0aW9uIGRlZmluZXMgdGhlIFwiY29udGV4dHNcIiB0aGF0IGFyZSBhdmFpbGFibGUgaW4gdGhlIGJhY2tlbmQgQVBJLlxuICpcbiAqIFRoZXNlIGFsbG93IHlvdSB0byBhY2Nlc3MgdGhpbmdzIHdoZW4gcHJvY2Vzc2luZyBhIHJlcXVlc3QsIGxpa2UgdGhlIGRhdGFiYXNlLCB0aGUgc2Vzc2lvbiwgZXRjLlxuICpcbiAqIFRoaXMgaGVscGVyIGdlbmVyYXRlcyB0aGUgXCJpbnRlcm5hbHNcIiBmb3IgYSB0UlBDIGNvbnRleHQuIFRoZSBBUEkgaGFuZGxlciBhbmQgUlNDIGNsaWVudHMgZWFjaFxuICogd3JhcCB0aGlzIGFuZCBwcm92aWRlcyB0aGUgcmVxdWlyZWQgY29udGV4dC5cbiAqXG4gKiBAc2VlIGh0dHBzOi8vdHJwYy5pby9kb2NzL3NlcnZlci9jb250ZXh0XG4gKi9cbmV4cG9ydCBjb25zdCBjcmVhdGVUUlBDQ29udGV4dCA9IGFzeW5jIChvcHRzOiB7IGhlYWRlcnM6IEhlYWRlcnMgfSkgPT4ge1xuICBjb25zdCBzZXNzaW9uID0gYXdhaXQgYXV0aCgpO1xuXG4gIHJldHVybiB7XG4gICAgZGIsXG4gICAgc2Vzc2lvbixcbiAgICAuLi5vcHRzLFxuICB9O1xufTtcblxuLyoqXG4gKiAyLiBJTklUSUFMSVpBVElPTlxuICpcbiAqIFRoaXMgaXMgd2hlcmUgdGhlIHRSUEMgQVBJIGlzIGluaXRpYWxpemVkLCBjb25uZWN0aW5nIHRoZSBjb250ZXh0IGFuZCB0cmFuc2Zvcm1lci4gV2UgYWxzbyBwYXJzZVxuICogWm9kRXJyb3JzIHNvIHRoYXQgeW91IGdldCB0eXBlc2FmZXR5IG9uIHRoZSBmcm9udGVuZCBpZiB5b3VyIHByb2NlZHVyZSBmYWlscyBkdWUgdG8gdmFsaWRhdGlvblxuICogZXJyb3JzIG9uIHRoZSBiYWNrZW5kLlxuICovXG5jb25zdCB0ID0gaW5pdFRSUEMuY29udGV4dDx0eXBlb2YgY3JlYXRlVFJQQ0NvbnRleHQ+KCkuY3JlYXRlKHtcbiAgdHJhbnNmb3JtZXI6IHN1cGVyanNvbixcbiAgZXJyb3JGb3JtYXR0ZXIoeyBzaGFwZSwgZXJyb3IgfSkge1xuICAgIHJldHVybiB7XG4gICAgICAuLi5zaGFwZSxcbiAgICAgIGRhdGE6IHtcbiAgICAgICAgLi4uc2hhcGUuZGF0YSxcbiAgICAgICAgem9kRXJyb3I6XG4gICAgICAgICAgZXJyb3IuY2F1c2UgaW5zdGFuY2VvZiBab2RFcnJvciA/IGVycm9yLmNhdXNlLmZsYXR0ZW4oKSA6IG51bGwsXG4gICAgICB9LFxuICAgIH07XG4gIH0sXG59KTtcblxuLyoqXG4gKiBDcmVhdGUgYSBzZXJ2ZXItc2lkZSBjYWxsZXIuXG4gKlxuICogQHNlZSBodHRwczovL3RycGMuaW8vZG9jcy9zZXJ2ZXIvc2VydmVyLXNpZGUtY2FsbHNcbiAqL1xuZXhwb3J0IGNvbnN0IGNyZWF0ZUNhbGxlckZhY3RvcnkgPSB0LmNyZWF0ZUNhbGxlckZhY3Rvcnk7XG5cbi8qKlxuICogMy4gUk9VVEVSICYgUFJPQ0VEVVJFIChUSEUgSU1QT1JUQU5UIEJJVClcbiAqXG4gKiBUaGVzZSBhcmUgdGhlIHBpZWNlcyB5b3UgdXNlIHRvIGJ1aWxkIHlvdXIgdFJQQyBBUEkuIFlvdSBzaG91bGQgaW1wb3J0IHRoZXNlIGEgbG90IGluIHRoZVxuICogXCIvc3JjL3NlcnZlci9hcGkvcm91dGVyc1wiIGRpcmVjdG9yeS5cbiAqL1xuXG4vKipcbiAqIFRoaXMgaXMgaG93IHlvdSBjcmVhdGUgbmV3IHJvdXRlcnMgYW5kIHN1Yi1yb3V0ZXJzIGluIHlvdXIgdFJQQyBBUEkuXG4gKlxuICogQHNlZSBodHRwczovL3RycGMuaW8vZG9jcy9yb3V0ZXJcbiAqL1xuZXhwb3J0IGNvbnN0IGNyZWF0ZVRSUENSb3V0ZXIgPSB0LnJvdXRlcjtcblxuLyoqXG4gKiBNaWRkbGV3YXJlIGZvciB0aW1pbmcgcHJvY2VkdXJlIGV4ZWN1dGlvbiBhbmQgYWRkaW5nIGFuIGFydGlmaWNpYWwgZGVsYXkgaW4gZGV2ZWxvcG1lbnQuXG4gKlxuICogWW91IGNhbiByZW1vdmUgdGhpcyBpZiB5b3UgZG9uJ3QgbGlrZSBpdCwgYnV0IGl0IGNhbiBoZWxwIGNhdGNoIHVud2FudGVkIHdhdGVyZmFsbHMgYnkgc2ltdWxhdGluZ1xuICogbmV0d29yayBsYXRlbmN5IHRoYXQgd291bGQgb2NjdXIgaW4gcHJvZHVjdGlvbiBidXQgbm90IGluIGxvY2FsIGRldmVsb3BtZW50LlxuICovXG5jb25zdCB0aW1pbmdNaWRkbGV3YXJlID0gdC5taWRkbGV3YXJlKGFzeW5jICh7IG5leHQsIHBhdGggfSkgPT4ge1xuICBjb25zdCBzdGFydCA9IERhdGUubm93KCk7XG5cbiAgaWYgKHQuX2NvbmZpZy5pc0Rldikge1xuICAgIC8vIGFydGlmaWNpYWwgZGVsYXkgaW4gZGV2XG4gICAgY29uc3Qgd2FpdE1zID0gTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogNDAwKSArIDEwMDtcbiAgICBhd2FpdCBuZXcgUHJvbWlzZSgocmVzb2x2ZSkgPT4gc2V0VGltZW91dChyZXNvbHZlLCB3YWl0TXMpKTtcbiAgfVxuXG4gIGNvbnN0IHJlc3VsdCA9IGF3YWl0IG5leHQoKTtcblxuICBjb25zdCBlbmQgPSBEYXRlLm5vdygpO1xuICBjb25zb2xlLmxvZyhgW1RSUENdICR7cGF0aH0gdG9vayAke2VuZCAtIHN0YXJ0fW1zIHRvIGV4ZWN1dGVgKTtcblxuICByZXR1cm4gcmVzdWx0O1xufSk7XG5cbi8qKlxuICogUHVibGljICh1bmF1dGhlbnRpY2F0ZWQpIHByb2NlZHVyZVxuICpcbiAqIFRoaXMgaXMgdGhlIGJhc2UgcGllY2UgeW91IHVzZSB0byBidWlsZCBuZXcgcXVlcmllcyBhbmQgbXV0YXRpb25zIG9uIHlvdXIgdFJQQyBBUEkuIEl0IGRvZXMgbm90XG4gKiBndWFyYW50ZWUgdGhhdCBhIHVzZXIgcXVlcnlpbmcgaXMgYXV0aG9yaXplZCwgYnV0IHlvdSBjYW4gc3RpbGwgYWNjZXNzIHVzZXIgc2Vzc2lvbiBkYXRhIGlmIHRoZXlcbiAqIGFyZSBsb2dnZWQgaW4uXG4gKi9cbmV4cG9ydCBjb25zdCBwdWJsaWNQcm9jZWR1cmUgPSB0LnByb2NlZHVyZS51c2UodGltaW5nTWlkZGxld2FyZSk7XG5cbi8qKlxuICogUHJvdGVjdGVkIChhdXRoZW50aWNhdGVkKSBwcm9jZWR1cmVcbiAqXG4gKiBJZiB5b3Ugd2FudCBhIHF1ZXJ5IG9yIG11dGF0aW9uIHRvIE9OTFkgYmUgYWNjZXNzaWJsZSB0byBsb2dnZWQgaW4gdXNlcnMsIHVzZSB0aGlzLiBJdCB2ZXJpZmllc1xuICogdGhlIHNlc3Npb24gaXMgdmFsaWQgYW5kIGd1YXJhbnRlZXMgYGN0eC5zZXNzaW9uLnVzZXJgIGlzIG5vdCBudWxsLlxuICpcbiAqIEBzZWUgaHR0cHM6Ly90cnBjLmlvL2RvY3MvcHJvY2VkdXJlc1xuICovXG5leHBvcnQgY29uc3QgcHJvdGVjdGVkUHJvY2VkdXJlID0gdC5wcm9jZWR1cmVcbiAgLnVzZSh0aW1pbmdNaWRkbGV3YXJlKVxuICAudXNlKCh7IGN0eCwgbmV4dCB9KSA9PiB7XG4gICAgaWYgKCFjdHguc2Vzc2lvbj8udXNlcikge1xuICAgICAgdGhyb3cgbmV3IFRSUENFcnJvcih7IGNvZGU6IFwiVU5BVVRIT1JJWkVEXCIgfSk7XG4gICAgfVxuICAgIHJldHVybiBuZXh0KHtcbiAgICAgIGN0eDoge1xuICAgICAgICAvLyBpbmZlcnMgdGhlIGBzZXNzaW9uYCBhcyBub24tbnVsbGFibGVcbiAgICAgICAgc2Vzc2lvbjogeyAuLi5jdHguc2Vzc2lvbiwgdXNlcjogY3R4LnNlc3Npb24udXNlciB9LFxuICAgICAgfSxcbiAgICB9KTtcbiAgfSk7XG4iXSwibmFtZXMiOlsiaW5pdFRSUEMiLCJUUlBDRXJyb3IiLCJzdXBlcmpzb24iLCJab2RFcnJvciIsImF1dGgiLCJkYiIsImNyZWF0ZVRSUENDb250ZXh0Iiwib3B0cyIsInNlc3Npb24iLCJ0IiwiY29udGV4dCIsImNyZWF0ZSIsInRyYW5zZm9ybWVyIiwiZXJyb3JGb3JtYXR0ZXIiLCJzaGFwZSIsImVycm9yIiwiZGF0YSIsInpvZEVycm9yIiwiY2F1c2UiLCJmbGF0dGVuIiwiY3JlYXRlQ2FsbGVyRmFjdG9yeSIsImNyZWF0ZVRSUENSb3V0ZXIiLCJyb3V0ZXIiLCJ0aW1pbmdNaWRkbGV3YXJlIiwibWlkZGxld2FyZSIsIm5leHQiLCJwYXRoIiwic3RhcnQiLCJEYXRlIiwibm93IiwiX2NvbmZpZyIsImlzRGV2Iiwid2FpdE1zIiwiTWF0aCIsImZsb29yIiwicmFuZG9tIiwiUHJvbWlzZSIsInJlc29sdmUiLCJzZXRUaW1lb3V0IiwicmVzdWx0IiwiZW5kIiwiY29uc29sZSIsImxvZyIsInB1YmxpY1Byb2NlZHVyZSIsInByb2NlZHVyZSIsInVzZSIsInByb3RlY3RlZFByb2NlZHVyZSIsImN0eCIsInVzZXIiLCJjb2RlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/server/api/trpc.ts\n");

/***/ }),

/***/ "(rsc)/./src/server/auth/config.ts":
/*!***********************************!*\
  !*** ./src/server/auth/config.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authConfig: () => (/* binding */ authConfig)\n/* harmony export */ });\n/* harmony import */ var _auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/prisma-adapter */ \"(rsc)/./node_modules/@auth/prisma-adapter/index.js\");\n/* harmony import */ var next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/discord */ \"(rsc)/./node_modules/next-auth/providers/discord.js\");\n/* harmony import */ var _server_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ~/server/db */ \"(rsc)/./src/server/db.ts\");\n\n\n\n/**\n * Options for NextAuth.js used to configure adapters, providers, callbacks, etc.\n *\n * @see https://next-auth.js.org/configuration/options\n */ const authConfig = {\n    providers: [\n        next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1__[\"default\"]\n    ],\n    adapter: (0,_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_server_db__WEBPACK_IMPORTED_MODULE_2__.db),\n    callbacks: {\n        session: ({ session, user })=>({\n                ...session,\n                user: {\n                    ...session.user,\n                    id: user.id\n                }\n            })\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/server/auth/config.ts\n");

/***/ }),

/***/ "(rsc)/./src/server/auth/index.ts":
/*!**********************************!*\
  !*** ./src/server/auth/index.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   handlers: () => (/* binding */ handlers),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./config */ \"(rsc)/./src/server/auth/config.ts\");\n\n\n\nconst { auth: uncachedAuth, handlers, signIn, signOut } = (0,next_auth__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_config__WEBPACK_IMPORTED_MODULE_2__.authConfig);\nconst auth = (0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(uncachedAuth);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc2VydmVyL2F1dGgvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBaUM7QUFDSDtBQUVRO0FBRXRDLE1BQU0sRUFBRUcsTUFBTUMsWUFBWSxFQUFFQyxRQUFRLEVBQUVDLE1BQU0sRUFBRUMsT0FBTyxFQUFFLEdBQUdQLHFEQUFRQSxDQUFDRSwrQ0FBVUE7QUFFN0UsTUFBTUMsT0FBT0YsNENBQUtBLENBQUNHO0FBRXdCIiwic291cmNlcyI6WyJEOlxcQ3Vyc29yIFByb2plY3RcXHdlYnNpdGVcXEF1Z21lbnQyXFxwcm9tcHQtbWFuYWdlclxcc3JjXFxzZXJ2ZXJcXGF1dGhcXGluZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBOZXh0QXV0aCBmcm9tIFwibmV4dC1hdXRoXCI7XG5pbXBvcnQgeyBjYWNoZSB9IGZyb20gXCJyZWFjdFwiO1xuXG5pbXBvcnQgeyBhdXRoQ29uZmlnIH0gZnJvbSBcIi4vY29uZmlnXCI7XG5cbmNvbnN0IHsgYXV0aDogdW5jYWNoZWRBdXRoLCBoYW5kbGVycywgc2lnbkluLCBzaWduT3V0IH0gPSBOZXh0QXV0aChhdXRoQ29uZmlnKTtcblxuY29uc3QgYXV0aCA9IGNhY2hlKHVuY2FjaGVkQXV0aCk7XG5cbmV4cG9ydCB7IGF1dGgsIGhhbmRsZXJzLCBzaWduSW4sIHNpZ25PdXQgfTtcbiJdLCJuYW1lcyI6WyJOZXh0QXV0aCIsImNhY2hlIiwiYXV0aENvbmZpZyIsImF1dGgiLCJ1bmNhY2hlZEF1dGgiLCJoYW5kbGVycyIsInNpZ25JbiIsInNpZ25PdXQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/server/auth/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/server/db.ts":
/*!**************************!*\
  !*** ./src/server/db.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   db: () => (/* binding */ db)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _env__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ~/env */ \"(rsc)/./src/env.js\");\n\n\nconst createPrismaClient = ()=>new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n        log: _env__WEBPACK_IMPORTED_MODULE_1__.env.NODE_ENV === \"development\" ? [\n            \"query\",\n            \"error\",\n            \"warn\"\n        ] : [\n            \"error\"\n        ]\n    });\nconst globalForPrisma = globalThis;\nconst db = globalForPrisma.prisma ?? createPrismaClient();\nif (_env__WEBPACK_IMPORTED_MODULE_1__.env.NODE_ENV !== \"production\") globalForPrisma.prisma = db;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc2VydmVyL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBOEM7QUFFbEI7QUFFNUIsTUFBTUUscUJBQXFCLElBQ3pCLElBQUlGLHdEQUFZQSxDQUFDO1FBQ2ZHLEtBQ0VGLHFDQUFHQSxDQUFDRyxRQUFRLEtBQUssZ0JBQWdCO1lBQUM7WUFBUztZQUFTO1NBQU8sR0FBRztZQUFDO1NBQVE7SUFDM0U7QUFFRixNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLEtBQUtGLGdCQUFnQkcsTUFBTSxJQUFJTixxQkFBcUI7QUFFakUsSUFBSUQscUNBQUdBLENBQUNHLFFBQVEsS0FBSyxjQUFjQyxnQkFBZ0JHLE1BQU0sR0FBR0QiLCJzb3VyY2VzIjpbIkQ6XFxDdXJzb3IgUHJvamVjdFxcd2Vic2l0ZVxcQXVnbWVudDJcXHByb21wdC1tYW5hZ2VyXFxzcmNcXHNlcnZlclxcZGIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSBcIkBwcmlzbWEvY2xpZW50XCI7XG5cbmltcG9ydCB7IGVudiB9IGZyb20gXCJ+L2VudlwiO1xuXG5jb25zdCBjcmVhdGVQcmlzbWFDbGllbnQgPSAoKSA9PlxuICBuZXcgUHJpc21hQ2xpZW50KHtcbiAgICBsb2c6XG4gICAgICBlbnYuTk9ERV9FTlYgPT09IFwiZGV2ZWxvcG1lbnRcIiA/IFtcInF1ZXJ5XCIsIFwiZXJyb3JcIiwgXCJ3YXJuXCJdIDogW1wiZXJyb3JcIl0sXG4gIH0pO1xuXG5jb25zdCBnbG9iYWxGb3JQcmlzbWEgPSBnbG9iYWxUaGlzIGFzIHVua25vd24gYXMge1xuICBwcmlzbWE6IFJldHVyblR5cGU8dHlwZW9mIGNyZWF0ZVByaXNtYUNsaWVudD4gfCB1bmRlZmluZWQ7XG59O1xuXG5leHBvcnQgY29uc3QgZGIgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IGNyZWF0ZVByaXNtYUNsaWVudCgpO1xuXG5pZiAoZW52Lk5PREVfRU5WICE9PSBcInByb2R1Y3Rpb25cIikgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IGRiO1xuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImVudiIsImNyZWF0ZVByaXNtYUNsaWVudCIsImxvZyIsIk5PREVfRU5WIiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsImRiIiwicHJpc21hIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/server/db.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@trpc","vendor-chunks/next-auth","vendor-chunks/zod","vendor-chunks/oauth4webapi","vendor-chunks/superjson","vendor-chunks/is-what","vendor-chunks/cookie","vendor-chunks/@auth","vendor-chunks/@t3-oss","vendor-chunks/@panva","vendor-chunks/copy-anything"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute&page=%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftrpc%2F%5Btrpc%5D%2Froute.ts&appDir=D%3A%5CCursor%20Project%5Cwebsite%5CAugment2%5Cprompt-manager%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCursor%20Project%5Cwebsite%5CAugment2%5Cprompt-manager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();