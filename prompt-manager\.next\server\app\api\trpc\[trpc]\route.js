(()=>{var a={};a.id=841,a.ids=[841],a.modules={220:(a,b,c)=>{"use strict";let d;c.r(b),c.d(b,{handler:()=>aS,patchFetch:()=>aR,routeModule:()=>aN,serverHooks:()=>aQ,workAsyncStorage:()=>aO,workUnitAsyncStorage:()=>aP});var e,f,g={};c.r(g),c.d(g,{GET:()=>aM,POST:()=>aM});var h=c(6559),i=c(8088),j=c(7719),k=c(6191),l=c(1289),m=c(261),n=c(2603),o=c(9893),p=c(4823),q=c(7220),r=c(6946),s=c(7912),t=c(9786),u=c(6143),v=c(6439),w=c(3365),x=c(893),y=c(457),z=c(8761);function A(a){return"object"==typeof a&&null!==a&&"subscribe"in a}function B(a,b){let c=(function(a,b){let c=null,d=()=>{null==c||c.unsubscribe(),c=null,b.removeEventListener("abort",d)};return new ReadableStream({start(e){c=a.subscribe({next(a){e.enqueue({ok:!0,value:a})},error(a){e.enqueue({ok:!1,error:a}),e.close()},complete(){e.close()}}),b.aborted?d():b.addEventListener("abort",d,{once:!0})},cancel(){d()}})})(a,b).getReader(),d={async next(){let a=await c.read();if(a.done)return{value:void 0,done:!0};let{value:b}=a;if(!b.ok)throw b.error;return{value:b.value,done:!1}},return:async()=>(await c.cancel(),{value:void 0,done:!0})};return{[Symbol.asyncIterator]:()=>d}}var C=(0,x.f1)((0,x.jr)(),1);function D(a){let b=null,c=Symbol.for("@trpc/server/http/memo"),d=c;return{read:async()=>(d!==c||(null!=b||(b=a().catch(a=>{if(a instanceof y.gt)throw a;throw new y.gt({code:"BAD_REQUEST",message:a instanceof Error?a.message:"Invalid input",cause:a})})),d=await b,b=null),d),result:()=>d!==c?d:void 0}}let E={isMatch(a){var b;return!!(null==(b=a.headers.get("content-type"))?void 0:b.startsWith("application/json"))},async parse(a){var b;let{req:c}=a,d="1"===a.searchParams.get("batch"),e=d?a.path.split(","):[a.path],f=D(async()=>{let b;if("GET"===c.method){let c=a.searchParams.get("input");c&&(b=JSON.parse(c))}else b=await c.json();if(void 0===b)return{};if(!d)return{0:a.router._def._config.transformer.input.deserialize(b)};if(!(0,z.Gv)(b))throw new y.gt({code:"BAD_REQUEST",message:'"input" needs to be an object when doing a batch call'});let f={};for(let c of e.keys()){let d=b[c];void 0!==d&&(f[c]=a.router._def._config.transformer.input.deserialize(d))}return f}),g=await Promise.all(e.map(async(b,c)=>{let d=await (0,y.Iw)(a.router,b);return{path:b,procedure:d,getRawInput:async()=>{let b=(await f.read())[c];if((null==d?void 0:d._def.type)==="subscription"){var e,g;let c=null!=(e=null!=(g=a.headers.get("last-event-id"))?g:a.searchParams.get("lastEventId"))?e:a.searchParams.get("Last-Event-Id");c&&((0,z.Gv)(b)?b=(0,C.default)((0,C.default)({},b),{},{lastEventId:c}):null!=b||(b={lastEventId:c}))}return b},result:()=>{var a;return null==(a=f.result())?void 0:a[c]}}})),h=new Set(g.map(a=>{var b;return null==(b=a.procedure)?void 0:b._def.type}).filter(Boolean));if(h.size>1)throw new y.gt({code:"BAD_REQUEST",message:`Cannot mix procedure types in call: ${Array.from(h).join(", ")}`});let i=null!=(b=h.values().next().value)?b:"unknown",j=a.searchParams.get("connectionParams");return{isBatchCall:d,accept:c.headers.get("trpc-accept"),calls:g,type:i,connectionParams:null===j?null:function(a){let b;try{b=JSON.parse(a)}catch(a){throw new y.gt({code:"PARSE_ERROR",message:"Not JSON-parsable query params",cause:a})}var c=b;try{if(null===c)return null;if(!(0,z.Gv)(c))throw Error("Expected object");let a=Object.entries(c).filter(([a,b])=>"string"!=typeof b);if(a.length>0)throw Error(`Expected connectionParams to be string values. Got ${a.map(([a,b])=>`${a}: ${typeof b}`).join(", ")}`);return c}catch(a){throw new y.gt({code:"PARSE_ERROR",message:"Invalid connection params shape",cause:a})}}(j),signal:c.signal,url:a.url}}},F=[E,{isMatch(a){var b;return!!(null==(b=a.headers.get("content-type"))?void 0:b.startsWith("multipart/form-data"))},async parse(a){let{req:b}=a;if("POST"!==b.method)throw new y.gt({code:"METHOD_NOT_SUPPORTED",message:"Only POST requests are supported for multipart/form-data requests"});let c=D(async()=>await b.formData()),d=await (0,y.Iw)(a.router,a.path);return{accept:null,calls:[{path:a.path,getRawInput:c.read,result:c.result,procedure:d}],isBatchCall:!1,type:"mutation",connectionParams:null,signal:b.signal,url:a.url}}},{isMatch(a){var b;return!!(null==(b=a.headers.get("content-type"))?void 0:b.startsWith("application/octet-stream"))},async parse(a){let{req:b}=a;if("POST"!==b.method)throw new y.gt({code:"METHOD_NOT_SUPPORTED",message:"Only POST requests are supported for application/octet-stream requests"});let c=D(async()=>b.body);return{calls:[{path:a.path,getRawInput:c.read,result:c.result,procedure:await (0,y.Iw)(a.router,a.path)}],isBatchCall:!1,accept:null,type:"mutation",connectionParams:null,signal:b.signal,url:a.url}}}];async function G(a){let b=function(a){let b=F.find(b=>b.isMatch(a));if(b)return b;if(!b&&"GET"===a.method)return E;throw new y.gt({code:"UNSUPPORTED_MEDIA_TYPE",message:a.headers.has("content-type")?`Unsupported content-type "${a.headers.get("content-type")}`:"Missing content-type header"})}(a.req);return await b.parse(a)}function H(a="AbortError"){throw new DOMException(a,"AbortError")}var I=(0,x.f1)((0,x.Vh)(),1);let J=new WeakMap,K=()=>{};d=Symbol.toStringTag;var L=class a{constructor(a){(0,I.default)(this,"promise",void 0),(0,I.default)(this,"subscribers",[]),(0,I.default)(this,"settlement",null),(0,I.default)(this,d,"Unpromise"),"function"==typeof a?this.promise=new Promise(a):this.promise=a;let b=this.promise.then(a=>{let{subscribers:b}=this;this.subscribers=null,this.settlement={status:"fulfilled",value:a},null==b||b.forEach(({resolve:b})=>{b(a)})});"catch"in b&&b.catch(a=>{let{subscribers:b}=this;this.subscribers=null,this.settlement={status:"rejected",reason:a},null==b||b.forEach(({reject:b})=>{b(a)})})}subscribe(){let a,b,{settlement:c}=this;if(null===c){var d;let c,e;if(null===this.subscribers)throw Error("Unpromise settled but still has subscribers");let f={promise:new Promise((a,b)=>{c=a,e=b}),resolve:c,reject:e};this.subscribers=(d=this.subscribers,[...d,f]),a=f.promise,b=()=>{null!==this.subscribers&&(this.subscribers=function(a,b){let c=a.indexOf(b);if(-1!==c)return[...a.slice(0,c),...a.slice(c+1)];return a}(this.subscribers,f))}}else{let{status:d}=c;a="fulfilled"===d?Promise.resolve(c.value):Promise.reject(c.reason),b=K}return Object.assign(a,{unsubscribe:b})}then(a,b){let c=this.subscribe(),{unsubscribe:d}=c;return Object.assign(c.then(a,b),{unsubscribe:d})}catch(a){let b=this.subscribe(),{unsubscribe:c}=b;return Object.assign(b.catch(a),{unsubscribe:c})}finally(a){let b=this.subscribe(),{unsubscribe:c}=b;return Object.assign(b.finally(a),{unsubscribe:c})}static proxy(b){let c=a.getSubscribablePromise(b);return void 0!==c?c:a.createSubscribablePromise(b)}static createSubscribablePromise(b){let c=new a(b);return J.set(b,c),J.set(c,c),c}static getSubscribablePromise(a){return J.get(a)}static resolve(b){let c="object"==typeof b&&null!==b&&"then"in b&&"function"==typeof b.then?b:Promise.resolve(b);return a.proxy(c).subscribe()}static async any(b){let c=(Array.isArray(b)?b:[...b]).map(a.resolve);try{return await Promise.any(c)}finally{c.forEach(({unsubscribe:a})=>{a()})}}static async race(b){let c=(Array.isArray(b)?b:[...b]).map(a.resolve);try{return await Promise.race(c)}finally{c.forEach(({unsubscribe:a})=>{a()})}}static async raceReferences(a){let b=a.map(M);try{return await Promise.race(b)}finally{for(let a of b)a.unsubscribe()}}};function M(a){return L.proxy(a).then(()=>[a])}function N(a,b){let c=a[Symbol.asyncDispose];return a[Symbol.asyncDispose]=async()=>{await b(),await (null==c?void 0:c())},a}null!=(e=Symbol).dispose||(e.dispose=Symbol()),null!=(f=Symbol).asyncDispose||(f.asyncDispose=Symbol());let O=Symbol();function P(a){let b=null;return function(a,b){let c=a[Symbol.dispose];return a[Symbol.dispose]=()=>{b(),null==c||c()},a}({start(){if(b)throw Error("Timer already started");return new Promise(c=>{b=setTimeout(()=>c(O),a)})}},()=>{b&&clearTimeout(b)})}var Q=(0,x.P$)({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/usingCtx.js"(a,b){b.exports=function(){var a="function"==typeof SuppressedError?SuppressedError:function(a,b){var c=Error();return c.name="SuppressedError",c.error=a,c.suppressed=b,c},b={},c=[];function d(a,b){if(null!=b){if(Object(b)!==b)throw TypeError("using declarations can only be used with objects, functions, null, or undefined.");if(a)var d=b[Symbol.asyncDispose||Symbol.for("Symbol.asyncDispose")];if(void 0===d&&(d=b[Symbol.dispose||Symbol.for("Symbol.dispose")],a))var e=d;if("function"!=typeof d)throw TypeError("Object is not disposable.");e&&(d=function(){try{e.call(b)}catch(a){return Promise.reject(a)}}),c.push({v:b,d:d,a:a})}else a&&c.push({d:b,a:a});return b}return{e:b,u:d.bind(null,!1),a:d.bind(null,!0),d:function(){var d,e=this.e,f=0;function g(){for(;d=c.pop();)try{if(!d.a&&1===f)return f=0,c.push(d),Promise.resolve().then(g);if(d.d){var a=d.d.call(d.v);if(d.a)return f|=2,Promise.resolve(a).then(g,h)}else f|=1}catch(a){return h(a)}if(1===f)return e!==b?Promise.reject(e):Promise.resolve();if(e!==b)throw e}function h(c){return e=e!==b?new a(c,e):c,g()}return g()}}},b.exports.__esModule=!0,b.exports.default=b.exports}}),R=(0,x.P$)({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/OverloadYield.js"(a,b){b.exports=function(a,b){this.v=a,this.k=b},b.exports.__esModule=!0,b.exports.default=b.exports}}),S=(0,x.P$)({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/awaitAsyncGenerator.js"(a,b){var c=R();b.exports=function(a){return new c(a,0)},b.exports.__esModule=!0,b.exports.default=b.exports}}),T=(0,x.P$)({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/wrapAsyncGenerator.js"(a,b){var c=R();function d(a){var b,d;function e(b,d){try{var g=a[b](d),h=g.value,i=h instanceof c;Promise.resolve(i?h.v:h).then(function(c){if(i){var d="return"===b?"return":"next";if(!h.k||c.done)return e(d,c);c=a[d](c).value}f(g.done?"return":"normal",c)},function(a){e("throw",a)})}catch(a){f("throw",a)}}function f(a,c){switch(a){case"return":b.resolve({value:c,done:!0});break;case"throw":b.reject(c);break;default:b.resolve({value:c,done:!1})}(b=b.next)?e(b.key,b.arg):d=null}this._invoke=function(a,c){return new Promise(function(f,g){var h={key:a,arg:c,resolve:f,reject:g,next:null};d?d=d.next=h:(b=d=h,e(a,c))})},"function"!=typeof a.return&&(this.return=void 0)}d.prototype["function"==typeof Symbol&&Symbol.asyncIterator||"@@asyncIterator"]=function(){return this},d.prototype.next=function(a){return this._invoke("next",a)},d.prototype.throw=function(a){return this._invoke("throw",a)},d.prototype.return=function(a){return this._invoke("return",a)},b.exports=function(a){return function(){return new d(a.apply(this,arguments))}},b.exports.__esModule=!0,b.exports.default=b.exports}}),U=(0,x.f1)(Q(),1),V=(0,x.f1)(S(),1),W=(0,x.f1)(T(),1);function X(a){let b=a[Symbol.asyncIterator]();return b[Symbol.asyncDispose]?b:N(b,async()=>{var a;await (null==(a=b.return)?void 0:a.call(b))})}function Y(){return(Y=(0,W.default)(function*(a,b){try{let d;var c=(0,U.default)();let e=c.a(X(a)),f=c.u(P(b.maxDurationMs)).start();for(;;){if((d=yield(0,V.default)(L.race([e.next(),f])))===O&&H(),d.done)return d;yield d.value,d=null}}catch(a){c.e=a}finally{yield(0,V.default)(c.d())}})).apply(this,arguments)}function Z(){return(Z=(0,W.default)(function*(a,b){try{let d;var c=(0,U.default)();let e=c.a(X(a)),f=c.u(P(b.gracePeriodMs)),g=b.count,h=new Promise(()=>{});for(;;){if((d=yield(0,V.default)(L.race([e.next(),h])))===O&&H(),d.done)return d.value;yield d.value,0==--g&&(h=f.start()),d=null}}catch(a){c.e=a}finally{yield(0,V.default)(c.d())}})).apply(this,arguments)}function $(){let a,b;return{promise:new Promise((c,d)=>{a=c,b=d}),resolve:a,reject:b}}var _=(0,x.f1)(Q(),1),aa=(0,x.f1)(S(),1),ab=(0,x.f1)(T(),1);function ac(a){let b=a[Symbol.asyncIterator]();return new ReadableStream({async cancel(){var a;await (null==(a=b.return)?void 0:a.call(b))},async pull(a){let c=await b.next();if(c.done)return void a.close();a.enqueue(c.value)}})}var ad=(0,x.f1)(Q(),1),ae=(0,x.f1)(S(),1),af=(0,x.f1)(T(),1);let ag=Symbol("ping");function ah(a,b){return ai.apply(this,arguments)}function ai(){return(ai=(0,af.default)(function*(a,b){try{let e;var c=(0,ad.default)();let f=c.a(X(a)),g=f.next();for(;;)try{var d=(0,ad.default)();let a=d.u(P(b));if((e=yield(0,ae.default)(L.race([g,a.start()])))===O){yield ag;continue}if(e.done)return e.value;g=f.next(),yield e.value,e=null}catch(a){d.e=a}finally{d.d()}}catch(a){c.e=a}finally{yield(0,ae.default)(c.d())}})).apply(this,arguments)}var aj=(0,x.P$)({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/asyncIterator.js"(a,b){function c(a){function b(a){if(Object(a)!==a)return Promise.reject(TypeError(a+" is not an object."));var b=a.done;return Promise.resolve(a.value).then(function(a){return{value:a,done:b}})}return(c=function(a){this.s=a,this.n=a.next}).prototype={s:null,n:null,next:function(){return b(this.n.apply(this.s,arguments))},return:function(a){var c=this.s.return;return void 0===c?Promise.resolve({value:a,done:!0}):b(c.apply(this.s,arguments))},throw:function(a){var c=this.s.return;return void 0===c?Promise.reject(a):b(c.apply(this.s,arguments))}},new c(a)}b.exports=function(a){var b,d,e,f=2;for("undefined"!=typeof Symbol&&(d=Symbol.asyncIterator,e=Symbol.iterator);f--;){if(d&&null!=(b=a[d]))return b.call(a);if(e&&null!=(b=a[e]))return new c(b.call(a));d="@@asyncIterator",e="@@iterator"}throw TypeError("Object is not async iterable")},b.exports.__esModule=!0,b.exports.default=b.exports}}),ak=(0,x.f1)(S(),1),al=(0,x.f1)(T(),1),am=(0,x.f1)(Q(),1),an=(0,x.f1)(aj(),1);function ao(a){return((0,z.Gv)(a)||(0,z.Tn)(a))&&"function"==typeof(null==a?void 0:a.then)&&"function"==typeof(null==a?void 0:a.catch)}var ap=class extends Error{constructor(a){super("Max depth reached at path: "+a.join(".")),this.path=a}};function aq(){return(aq=(0,al.default)(function*(a){let{data:b}=a,c=0,d=function(){let a="idle",b=$(),c=[],d=new Set,e=[];function f(c){if("pending"!==a)return;let f=function(a,b){let c=a[Symbol.asyncIterator](),d="idle";function e(){d="done",b=()=>{}}return{pull:function(){"idle"===d&&(d="pending",c.next().then(a=>{if(a.done){d="done",b({status:"return",value:a.value}),e();return}d="idle",b({status:"yield",value:a.value})}).catch(a=>{b({status:"error",error:a}),e()}))},destroy:async()=>{var a;e(),await (null==(a=c.return)?void 0:a.call(c))}}}(c,c=>{if("pending"===a){switch(c.status){case"yield":e.push([f,c]);break;case"return":d.delete(f);break;case"error":e.push([f,c]),d.delete(f)}b.resolve()}});d.add(f),f.pull()}return{add(b){switch(a){case"idle":c.push(b);break;case"pending":f(b)}},[Symbol.asyncIterator]:()=>(0,ab.default)(function*(){try{var g=(0,_.default)();if("idle"!==a)throw Error("Cannot iterate twice");for(a="pending",g.a(N({},async()=>{a="done";let c=[];if(await Promise.all(Array.from(d.values()).map(async a=>{try{await a.destroy()}catch(a){c.push(a)}})),e.length=0,d.clear(),b.resolve(),c.length>0)throw AggregateError(c)}));c.length>0;)f(c.shift());for(;d.size>0;){for(yield(0,aa.default)(b.promise);e.length>0;){let[a,b]=e.shift();switch(b.status){case"yield":yield b.value,a.pull();break;case"error":throw b.error}}b=$()}}catch(a){g.e=a}finally{yield(0,aa.default)(g.d())}})()}}();function e(a){let b=c++,e=a(b);return d.add(e),b}function f(b){return a.maxDepth&&b.length>a.maxDepth?new ap(b):null}function g(b,c){var d,g,i;if(ao(b))return[0,(d=b,e((g=(0,al.default)(function*(b){let e=f(c);e&&(d.catch(b=>{var d;null==(d=a.onError)||d.call(a,{error:b,path:c})}),d=Promise.reject(e));try{let a=yield(0,ak.default)(d);yield[b,0,h(a,c)]}catch(d){var g,i;null==(g=a.onError)||g.call(a,{error:d,path:c}),yield[b,1,null==(i=a.formatError)?void 0:i.call(a,{error:d,path:c})]}}),function(a){return g.apply(this,arguments)})))];if((0,z.Td)(b)){if(a.maxDepth&&c.length>=a.maxDepth)throw Error("Max depth reached");return[1,e((i=(0,al.default)(function*(d){try{var e,g,i=(0,am.default)();let j=f(c);if(j)throw j;let k=i.a(X(b));try{for(;;){let a=yield(0,ak.default)(k.next());if(a.done){yield[d,0,h(a.value,c)];break}yield[d,1,h(a.value,c)]}}catch(b){null==(e=a.onError)||e.call(a,{error:b,path:c}),yield[d,2,null==(g=a.formatError)?void 0:g.call(a,{error:b,path:c})]}}catch(a){i.e=a}finally{yield(0,ak.default)(i.d())}}),function(a){return i.apply(this,arguments)}))]}return null}function h(a,b){if(void 0===a)return[[]];let c=g(a,b);if(c)return[[0],[null,...c]];if("[object Object]"!==Object.prototype.toString.call(a))return[[a]];let d={},e=[];for(let[c,f]of Object.entries(a)){let a=g(f,[...b,c]);if(!a){d[c]=f;continue}d[c]=0,e.push([c,...a])}return[[d],...e]}let i={};for(let[a,c]of Object.entries(b))i[a]=h(c,[a]);yield i;let j=d;a.pingMs&&(j=ah(d,a.pingMs));var k=!1,l=!1;try{for(var m,n,o=(0,an.default)(j);k=!(n=yield(0,ak.default)(o.next())).done;k=!1){let a=n.value;yield a}}catch(a){l=!0,m=a}finally{try{k&&null!=o.return&&(yield(0,ak.default)(o.return()))}finally{if(l)throw m}}})).apply(this,arguments)}var ar=(0,x.P$)({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/asyncGeneratorDelegate.js"(a,b){var c=R();b.exports=function(a){var b={},d=!1;function e(b,e){return d=!0,{done:!1,value:new c(e=new Promise(function(c){c(a[b](e))}),1)}}return b["undefined"!=typeof Symbol&&Symbol.iterator||"@@iterator"]=function(){return this},b.next=function(a){return d?(d=!1,a):e("next",a)},"function"==typeof a.throw&&(b.throw=function(a){if(d)throw d=!1,a;return e("throw",a)}),"function"==typeof a.return&&(b.return=function(a){return d?(d=!1,a):e("return",a)}),b},b.exports.__esModule=!0,b.exports.default=b.exports}}),as=(0,x.f1)(aj(),1),at=(0,x.f1)(S(),1),au=(0,x.f1)(T(),1),av=(0,x.f1)(ar(),1);(0,x.f1)(Q(),1);let aw={"Content-Type":"text/event-stream","Cache-Control":"no-cache, no-transform","X-Accel-Buffering":"no",Connection:"keep-alive"};var ax=(0,x.f1)(T(),1),ay=(0,x.f1)((0,x.jr)(),1);function az(a){return(0,z.eF)((0,ax.default)(function*(){throw a}))}let aA={mutation:["POST"],query:["GET"],subscription:["GET"]},aB={mutation:["POST"],query:["GET","POST"],subscription:["GET","POST"]};function aC(a){var b,c,d;let{ctx:e,info:f,responseMeta:g,untransformedJSON:h,errors:i=[],headers:j}=a,k=h?(0,x.E$)(h):200,l=!h,m=l?[]:Array.isArray(h)?h:[h],n=null!=(b=null==g?void 0:g({ctx:e,info:f,paths:null==f?void 0:f.calls.map(a=>a.path),data:m,errors:i,eagerGeneration:l,type:null!=(c=null==f||null==(d=f.calls.find(a=>{var b;return null==(b=a.procedure)?void 0:b._def.type}))||null==(d=d.procedure)?void 0:d._def.type)?c:"unknown"}))?b:{};if(n.headers)if(n.headers instanceof Headers)for(let[a,b]of n.headers.entries())j.append(a,b);else for(let[a,b]of Object.entries(n.headers))if(Array.isArray(b))for(let c of b)j.append(a,c);else"string"==typeof b&&j.set(a,b);return n.status&&(k=n.status),{status:k}}function aD(a){return!!(0,z.Gv)(a)&&(!!(0,z.Td)(a)||Object.values(a).some(ao)||Object.values(a).some(z.Td))}async function aE(a){var b,c,d,e,f,g,h;let{router:i,req:j}=a,k=new Headers([["vary","trpc-accept"]]),l=i._def._config,m=new URL(j.url);if("HEAD"===j.method)return new Response(null,{status:204});let n=null==(b=null!=(c=a.allowBatching)?c:null==(d=a.batching)?void 0:d.enabled)||b,o=null!=(e=a.allowMethodOverride)&&e&&"POST"===j.method,p=await (0,z.eF)(async()=>{try{return[void 0,await G({req:j,path:decodeURIComponent(a.path),router:i,searchParams:m.searchParams,headers:a.req.headers,url:m})]}catch(a){return[(0,y.qO)(a),void 0]}}),q=(0,z.eF)(()=>{let b;return{valueOrUndefined:()=>{if(b)return b[1]},value:()=>{let[a,c]=b;if(a)throw a;return c},create:async c=>{if(b)throw Error("This should only be called once - report a bug in tRPC");try{let d=await a.createContext({info:c});b=[void 0,d]}catch(a){b=[(0,y.qO)(a),void 0]}}}}),r=o?aB:aA,s="application/jsonl"===j.headers.get("trpc-accept"),t=null==(f=null==(g=l.sse)?void 0:g.enabled)||f;try{let[b,c]=p;if(b)throw b;if(c.isBatchCall&&!n)throw new y.gt({code:"BAD_REQUEST",message:"Batching is not enabled on the server"});if(s&&!c.isBatchCall)throw new y.gt({message:"Streaming requests must be batched (you can do a batch of 1)",code:"BAD_REQUEST"});await q.create(c);let d=c.calls.map(async b=>{let d=b.procedure;try{if(a.error)throw a.error;if(!d)throw new y.gt({code:"NOT_FOUND",message:`No procedure found on path "${b.path}"`});if(!r[d._def.type].includes(j.method))throw new y.gt({code:"METHOD_NOT_SUPPORTED",message:`Unsupported ${j.method}-request to ${d._def.type} procedure at path "${b.path}"`});if("subscription"===d._def.type&&c.isBatchCall)throw new y.gt({code:"BAD_REQUEST",message:"Cannot batch subscription calls"});let e=await d({path:b.path,getRawInput:b.getRawInput,ctx:q.value(),type:d._def.type,signal:a.req.signal});return[void 0,{data:e}]}catch(h){var e,f,g;let c=(0,y.qO)(h),d=b.result();return null==(e=a.onError)||e.call(a,{error:c,path:b.path,input:d,ctx:q.valueOrUndefined(),type:null!=(f=null==(g=b.procedure)?void 0:g._def.type)?f:"unknown",req:a.req}),[c,void 0]}});if(!c.isBatchCall){let[b]=c.calls,[e,f]=await d[0];switch(c.type){case"unknown":case"mutation":case"query":{if(k.set("content-type","application/json"),aD(null==f?void 0:f.data))throw new y.gt({code:"UNSUPPORTED_MEDIA_TYPE",message:"Cannot use stream-like response in non-streaming request - use httpBatchStreamLink"});let d=e?{error:(0,x.KQ)({config:l,ctx:q.valueOrUndefined(),error:e,input:b.result(),path:b.path,type:c.type})}:{result:{data:f.data}},g=aC({ctx:q.valueOrUndefined(),info:c,responseMeta:a.responseMeta,errors:e?[e]:[],headers:k,untransformedJSON:[d]});return new Response(JSON.stringify((0,y.t9)(l,d)),{status:g.status,headers:k})}case"subscription":{let d=(0,z.eF)(()=>e?az(e):t?A(f.data)||(0,z.Td)(f.data)?A(f.data)?B(f.data,a.req.signal):f.data:az(new y.gt({message:`Subscription ${b.path} did not return an observable or a AsyncGenerator`,code:"INTERNAL_SERVER_ERROR"})):az(new y.gt({code:"METHOD_NOT_SUPPORTED",message:'Missing experimental flag "sseSubscriptions"'}))),g=function(a){var b,c,d,e,f;let{serialize:g=z.D_}=a,h={enabled:null!=(b=null==(c=a.ping)?void 0:c.enabled)&&b,intervalMs:null!=(d=null==(e=a.ping)?void 0:e.intervalMs)?d:1e3},i=null!=(f=a.client)?f:{};if(h.enabled&&i.reconnectAfterInactivityMs&&h.intervalMs>i.reconnectAfterInactivityMs)throw Error(`Ping interval must be less than client reconnect interval to prevent unnecessary reconnection - ping.intervalMs: ${h.intervalMs} client.reconnectAfterInactivityMs: ${i.reconnectAfterInactivityMs}`);function j(){return(j=(0,au.default)(function*(){let b,c;yield{event:"connected",data:JSON.stringify(i)};let d=a.data;a.emitAndEndImmediately&&(d=function(a,b){return Z.apply(this,arguments)}(d,{count:1,gracePeriodMs:1})),a.maxDurationMs&&a.maxDurationMs>0&&a.maxDurationMs!==1/0&&(d=function(a,b){return Y.apply(this,arguments)}(d,{maxDurationMs:a.maxDurationMs})),h.enabled&&h.intervalMs!==1/0&&h.intervalMs>0&&(d=ah(d,h.intervalMs));var e=!1,f=!1;try{for(var j,k,l=(0,as.default)(d);e=!(k=yield(0,at.default)(l.next())).done;e=!1){if((b=k.value)===ag){yield{event:"ping",data:""};continue}(c=(0,y.vJ)(b)?{id:b[0],data:b[1]}:{data:b}).data=JSON.stringify(g(c.data)),yield c,b=null,c=null}}catch(a){f=!0,j=a}finally{try{e&&null!=l.return&&(yield(0,at.default)(l.return()))}finally{if(f)throw j}}})).apply(this,arguments)}function k(){return(k=(0,au.default)(function*(){try{yield*(0,av.default)((0,as.default)(function(){return j.apply(this,arguments)}())),yield{event:"return",data:""}}catch(f){var b,c;if((0,z.Gv)(f)&&"AbortError"===f.name)return;let d=(0,y.qO)(f),e=null!=(b=null==(c=a.formatError)?void 0:c.call(a,{error:d}))?b:null;yield{event:"serialized-error",data:JSON.stringify(g(e))}}})).apply(this,arguments)}return ac(function(){return k.apply(this,arguments)}()).pipeThrough(new TransformStream({transform(a,b){"event"in a&&b.enqueue(`event: ${a.event}
`),"data"in a&&b.enqueue(`data: ${a.data}
`),"id"in a&&b.enqueue(`id: ${a.id}
`),"comment"in a&&b.enqueue(`: ${a.comment}
`),b.enqueue("\n\n")}})).pipeThrough(new TextEncoderStream)}((0,ay.default)((0,ay.default)({},l.sse),{},{data:d,serialize:a=>l.transformer.output.serialize(a),formatError(c){var d,e,f;let g=(0,y.qO)(c.error),h=null==b?void 0:b.result(),i=null==b?void 0:b.path,j=null!=(d=null==b||null==(e=b.procedure)?void 0:e._def.type)?d:"unknown";return null==(f=a.onError)||f.call(a,{error:g,path:i,input:h,ctx:q.valueOrUndefined(),req:a.req,type:j}),(0,x.KQ)({config:l,ctx:q.valueOrUndefined(),error:g,input:h,path:i,type:j})}}));for(let[a,b]of Object.entries(aw))k.set(a,b);let h=aC({ctx:q.valueOrUndefined(),info:c,responseMeta:a.responseMeta,errors:[],headers:k,untransformedJSON:null});return new Response(g,{headers:k,status:h.status})}}}if("application/jsonl"===c.accept){k.set("content-type","application/json"),k.set("transfer-encoding","chunked");let b=aC({ctx:q.valueOrUndefined(),info:c,responseMeta:a.responseMeta,errors:[],headers:k,untransformedJSON:null}),e=function(a){let b=ac(function(a){return aq.apply(this,arguments)}(a)),{serialize:c}=a;return c&&(b=b.pipeThrough(new TransformStream({transform(a,b){a===ag?b.enqueue(ag):b.enqueue(c(a))}}))),b.pipeThrough(new TransformStream({transform(a,b){a===ag?b.enqueue(" "):b.enqueue(JSON.stringify(a)+"\n")}})).pipeThrough(new TextEncoderStream)}((0,ay.default)((0,ay.default)({},l.jsonl),{},{maxDepth:1/0,data:d.map(async b=>{let[d,e]=await b,f=c.calls[0];if(d){var g,h;return{error:(0,x.KQ)({config:l,ctx:q.valueOrUndefined(),error:d,input:f.result(),path:f.path,type:null!=(g=null==(h=f.procedure)?void 0:h._def.type)?g:"unknown"})}}let i=A(e.data)?B(e.data,a.req.signal):Promise.resolve(e.data);return{result:Promise.resolve({data:i})}}),serialize:l.transformer.output.serialize,onError:b=>{var d,e;null==(d=a.onError)||d.call(a,{error:(0,y.qO)(b),path:void 0,input:void 0,ctx:q.valueOrUndefined(),req:a.req,type:null!=(e=null==c?void 0:c.type)?e:"unknown"})},formatError(a){var b,d;let e=null==c?void 0:c.calls[a.path[0]],f=(0,y.qO)(a.error),g=null==e?void 0:e.result(),h=null==e?void 0:e.path,i=null!=(b=null==e||null==(d=e.procedure)?void 0:d._def.type)?b:"unknown";return(0,x.KQ)({config:l,ctx:q.valueOrUndefined(),error:f,input:g,path:h,type:i})}}));return new Response(e,{headers:k,status:b.status})}k.set("content-type","application/json");let e=(await Promise.all(d)).map(a=>{let[b,c]=a;return b?a:aD(c.data)?[new y.gt({code:"UNSUPPORTED_MEDIA_TYPE",message:"Cannot use stream-like response in non-streaming request - use httpBatchStreamLink"}),void 0]:a}),f=e.map(([a,b],d)=>{let e=c.calls[d];if(a){var f,g;return{error:(0,x.KQ)({config:l,ctx:q.valueOrUndefined(),error:a,input:e.result(),path:e.path,type:null!=(f=null==(g=e.procedure)?void 0:g._def.type)?f:"unknown"})}}return{result:{data:b.data}}}),g=e.map(([a])=>a).filter(Boolean),h=aC({ctx:q.valueOrUndefined(),info:c,responseMeta:a.responseMeta,untransformedJSON:f,errors:g,headers:k});return new Response(JSON.stringify((0,y.t9)(l,f)),{status:h.status,headers:k})}catch(i){let[b,c]=p,d=q.valueOrUndefined(),{error:e,untransformedJSON:f,body:g}=function(a,b){let{router:c,req:d,onError:e}=b.opts,f=(0,y.qO)(a);null==e||e({error:f,path:b.path,input:b.input,ctx:b.ctx,type:b.type,req:d});let g={error:(0,x.KQ)({config:c._def._config,error:f,type:b.type,path:b.path,input:b.input,ctx:b.ctx})},h=JSON.stringify((0,y.t9)(c._def._config,g));return{error:f,untransformedJSON:g,body:h}}(i,{opts:a,ctx:q.valueOrUndefined(),type:null!=(h=null==c?void 0:c.type)?h:"unknown"});return new Response(g,{status:aC({ctx:d,info:c,responseMeta:a.responseMeta,untransformedJSON:f,errors:[e],headers:k}).status,headers:k})}}var aF=(0,x.f1)((0,x.jr)(),1);let aG=a=>a=(a=a.startsWith("/")?a.slice(1):a).endsWith("/")?a.slice(0,-1):a;async function aH(a){let b=new Headers,c=async c=>{var d;return null==(d=a.createContext)?void 0:d.call(a,(0,aF.default)({req:a.req,resHeaders:b},c))},d=aG(new URL(a.req.url).pathname),e=aG(a.endpoint),f=aG(d.slice(e.length));return await aE((0,aF.default)((0,aF.default)({},a),{},{req:a.req,createContext:c,path:f,error:null,onError(b){var c;null==a||null==(c=a.onError)||c.call(a,(0,aF.default)((0,aF.default)({},b),{},{req:a.req}))},responseMeta(c){var d;let e=null==(d=a.responseMeta)?void 0:d.call(a,c);if(null==e?void 0:e.headers)if(e.headers instanceof Headers)for(let[a,c]of e.headers.entries())b.append(a,c);else for(let[a,c]of Object.entries(e.headers))if(Array.isArray(c))for(let d of c)b.append(a,d);else"string"==typeof c&&b.set(a,c);return{headers:b,status:null==e?void 0:e.status}}}))}var aI=c(8516),aJ=c(4903),aK=c(7645);let aL=async a=>(0,aK.dW)({headers:a.headers}),aM=a=>aH({endpoint:"/api/trpc",req:a,router:aJ.P,createContext:()=>aL(a),onError:"development"===aI._.NODE_ENV?({path:a,error:b})=>{console.error(`❌ tRPC failed on ${a??"<no-path>"}: ${b.message}`)}:void 0}),aN=new h.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/trpc/[trpc]/route",pathname:"/api/trpc/[trpc]",filename:"route",bundlePath:"app/api/trpc/[trpc]/route"},distDir:".next",projectDir:"",resolvedPagePath:"D:\\Cursor Project\\website\\Augment2\\prompt-manager\\src\\app\\api\\trpc\\[trpc]\\route.ts",nextConfigOutput:"",userland:g}),{workAsyncStorage:aO,workUnitAsyncStorage:aP,serverHooks:aQ}=aN;function aR(){return(0,j.patchFetch)({workAsyncStorage:aO,workUnitAsyncStorage:aP})}async function aS(a,b,c){var d;let e="/api/trpc/[trpc]/route";"/index"===e&&(e="/");let f=await aN.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!f)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:g,params:h,nextConfig:j,isDraftMode:x,prerenderManifest:y,routerServerContext:z,isOnDemandRevalidate:A,revalidateOnlyGenerated:B,resolvedPathname:C}=f,D=(0,m.normalizeAppPath)(e),E=!!(y.dynamicRoutes[D]||y.routes[C]);if(E&&!x){let a=!!y.routes[C],b=y.dynamicRoutes[D];if(b&&!1===b.fallback&&!a)throw new v.NoFallbackError}let F=null;!E||aN.isDev||x||(F="/index"===(F=C)?"/":F);let G=!0===aN.isDev||!E,H=E&&!G,I=a.method||"GET",J=(0,l.getTracer)(),K=J.getActiveScopeSpan(),L={params:h,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!j.experimental.dynamicIO,authInterrupts:!!j.experimental.authInterrupts},supportsDynamicResponse:G,incrementalCache:(0,k.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=j.experimental)?void 0:d.cacheLife,isRevalidate:H,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>aN.onRequestError(a,b,d,z)},sharedContext:{buildId:g}},M=new n.NodeNextRequest(a),N=new n.NodeNextResponse(b),O=o.NextRequestAdapter.fromNodeNextRequest(M,(0,o.signalFromNodeResponse)(b));try{let d=async c=>aN.handle(O,L).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=J.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==p.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${I} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${I} ${a.url}`)}),f=async f=>{var g,h;let l=async({previousCacheEntry:g})=>{try{if(!(0,k.getRequestMeta)(a,"minimalMode")&&A&&B&&!g)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(f);a.fetchMetrics=L.renderOpts.fetchMetrics;let h=L.renderOpts.pendingWaitUntil;h&&c.waitUntil&&(c.waitUntil(h),h=void 0);let i=L.renderOpts.collectedTags;if(!E)return await (0,r.I)(M,N,e,L.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,s.toNodeOutgoingHttpHeaders)(e.headers);i&&(b[u.NEXT_CACHE_TAGS_HEADER]=i),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==L.renderOpts.collectedRevalidate&&!(L.renderOpts.collectedRevalidate>=u.INFINITE_CACHE)&&L.renderOpts.collectedRevalidate,d=void 0===L.renderOpts.collectedExpire||L.renderOpts.collectedExpire>=u.INFINITE_CACHE?void 0:L.renderOpts.collectedExpire;return{value:{kind:w.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==g?void 0:g.isStale)&&await aN.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,q.c)({isRevalidate:H,isOnDemandRevalidate:A})},z),b}},m=await aN.handleResponse({req:a,nextConfig:j,cacheKey:F,routeKind:i.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:A,revalidateOnlyGenerated:B,responseGenerator:l,waitUntil:c.waitUntil});if(!E)return null;if((null==m||null==(g=m.value)?void 0:g.kind)!==w.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==m||null==(h=m.value)?void 0:h.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,k.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",A?"REVALIDATED":m.isMiss?"MISS":m.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let n=(0,s.fromNodeOutgoingHttpHeaders)(m.value.headers);return(0,k.getRequestMeta)(a,"minimalMode")&&E||n.delete(u.NEXT_CACHE_TAGS_HEADER),!m.cacheControl||b.getHeader("Cache-Control")||n.get("Cache-Control")||n.set("Cache-Control",(0,t.getCacheControlHeader)(m.cacheControl)),await (0,r.I)(M,N,new Response(m.value.body,{headers:n,status:m.value.status||200})),null};K?await f(K):await J.withPropagatedContext(a.headers,()=>J.trace(p.BaseServerSpan.handleRequest,{spanName:`${I} ${a.url}`,kind:l.SpanKind.SERVER,attributes:{"http.method":I,"http.target":a.url}},f))}catch(b){if(K||await aN.onRequestError(a,b,{routerKind:"App Router",routePath:D,routeType:"route",revalidateReason:(0,q.c)({isRevalidate:H,isOnDemandRevalidate:A})}),E)throw b;return await (0,r.I)(M,N,new Response(null,{status:500})),null}}},261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:a=>{"use strict";a.exports=require("node:buffer")},4870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:a=>{"use strict";a.exports=require("crypto")},6330:a=>{"use strict";a.exports=require("@prisma/client")},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6487:()=>{},6559:(a,b,c)=>{"use strict";a.exports=c(4870)},6946:(a,b,c)=>{"use strict";Object.defineProperty(b,"I",{enumerable:!0,get:function(){return g}});let d=c(898),e=c(2471),f=c(7912);async function g(a,b,c,g){if((0,d.isNodeNextResponse)(b)){var h;b.statusCode=c.status,b.statusMessage=c.statusText;let d=["set-cookie","www-authenticate","proxy-authenticate","vary"];null==(h=c.headers)||h.forEach((a,c)=>{if("x-middleware-set-cookie"!==c.toLowerCase())if("set-cookie"===c.toLowerCase())for(let d of(0,f.splitCookiesString)(a))b.appendHeader(c,d);else{let e=void 0!==b.getHeader(c);(d.includes(c.toLowerCase())||!e)&&b.appendHeader(c,a)}});let{originalResponse:i}=b;c.body&&"HEAD"!==a.method?await (0,e.pipeToNodeResponse)(c.body,i,g):i.end()}}},7598:a=>{"use strict";a.exports=require("node:crypto")},7975:a=>{"use strict";a.exports=require("node:util")},8335:()=>{},9121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[778,631,880,903],()=>b(b.s=220));module.exports=c})();