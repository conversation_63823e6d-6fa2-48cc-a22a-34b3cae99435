{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\n/**\n * 合并 Tailwind CSS 类名\n */\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n/**\n * 复制文本到剪贴板\n */\nexport async function copyToClipboard(text: string): Promise<boolean> {\n  try {\n    if (navigator.clipboard && window.isSecureContext) {\n      await navigator.clipboard.writeText(text)\n      return true\n    } else {\n      // 降级方案\n      const textArea = document.createElement('textarea')\n      textArea.value = text\n      textArea.style.position = 'fixed'\n      textArea.style.left = '-999999px'\n      textArea.style.top = '-999999px'\n      document.body.appendChild(textArea)\n      textArea.focus()\n      textArea.select()\n      \n      const success = document.execCommand('copy')\n      document.body.removeChild(textArea)\n      return success\n    }\n  } catch (error) {\n    console.error('复制失败:', error)\n    return false\n  }\n}\n\n/**\n * 格式化日期\n */\nexport function formatDate(date: Date | string, options?: Intl.DateTimeFormatOptions): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date\n  \n  const defaultOptions: Intl.DateTimeFormatOptions = {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }\n  \n  return dateObj.toLocaleDateString('zh-CN', { ...defaultOptions, ...options })\n}\n\n/**\n * 格式化相对时间\n */\nexport function formatRelativeTime(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date\n  const now = new Date()\n  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000)\n  \n  if (diffInSeconds < 60) {\n    return '刚刚'\n  }\n  \n  const diffInMinutes = Math.floor(diffInSeconds / 60)\n  if (diffInMinutes < 60) {\n    return `${diffInMinutes}分钟前`\n  }\n  \n  const diffInHours = Math.floor(diffInMinutes / 60)\n  if (diffInHours < 24) {\n    return `${diffInHours}小时前`\n  }\n  \n  const diffInDays = Math.floor(diffInHours / 24)\n  if (diffInDays < 7) {\n    return `${diffInDays}天前`\n  }\n  \n  const diffInWeeks = Math.floor(diffInDays / 7)\n  if (diffInWeeks < 4) {\n    return `${diffInWeeks}周前`\n  }\n  \n  const diffInMonths = Math.floor(diffInDays / 30)\n  if (diffInMonths < 12) {\n    return `${diffInMonths}个月前`\n  }\n  \n  const diffInYears = Math.floor(diffInDays / 365)\n  return `${diffInYears}年前`\n}\n\n/**\n * 截断文本\n */\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) {\n    return text\n  }\n  return text.slice(0, maxLength) + '...'\n}\n\n/**\n * 高亮搜索关键词\n */\nexport function highlightText(text: string, query: string): string {\n  if (!query.trim()) {\n    return text\n  }\n  \n  const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&')})`, 'gi')\n  return text.replace(regex, '<mark class=\"bg-yellow-200 dark:bg-yellow-800\">$1</mark>')\n}\n\n/**\n * 生成随机颜色\n */\nexport function generateRandomColor(): string {\n  const colors = [\n    '#EF4444', // red-500\n    '#F97316', // orange-500\n    '#F59E0B', // amber-500\n    '#EAB308', // yellow-500\n    '#84CC16', // lime-500\n    '#22C55E', // green-500\n    '#10B981', // emerald-500\n    '#14B8A6', // teal-500\n    '#06B6D4', // cyan-500\n    '#0EA5E9', // sky-500\n    '#3B82F6', // blue-500\n    '#6366F1', // indigo-500\n    '#8B5CF6', // violet-500\n    '#A855F7', // purple-500\n    '#D946EF', // fuchsia-500\n    '#EC4899', // pink-500\n    '#F43F5E', // rose-500\n  ]\n  \n  return colors[Math.floor(Math.random() * colors.length)]!\n}\n\n/**\n * 防抖函数\n */\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout | null = null\n  \n  return (...args: Parameters<T>) => {\n    if (timeout) {\n      clearTimeout(timeout)\n    }\n    \n    timeout = setTimeout(() => {\n      func(...args)\n    }, wait)\n  }\n}\n\n/**\n * 节流函数\n */\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let inThrottle = false\n  \n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args)\n      inThrottle = true\n      setTimeout(() => {\n        inThrottle = false\n      }, wait)\n    }\n  }\n}\n\n/**\n * 格式化文件大小\n */\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n  \n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\n/**\n * 验证邮箱格式\n */\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\n/**\n * 生成唯一ID\n */\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36)\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;;;AAKO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAKO,eAAe,gBAAgB,IAAY;IAChD,IAAI;QACF,IAAI,UAAU,SAAS,IAAI,OAAO,eAAe,EAAE;YACjD,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,OAAO;QACT,OAAO;YACL,OAAO;YACP,MAAM,WAAW,SAAS,aAAa,CAAC;YACxC,SAAS,KAAK,GAAG;YACjB,SAAS,KAAK,CAAC,QAAQ,GAAG;YAC1B,SAAS,KAAK,CAAC,IAAI,GAAG;YACtB,SAAS,KAAK,CAAC,GAAG,GAAG;YACrB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,SAAS,KAAK;YACd,SAAS,MAAM;YAEf,MAAM,UAAU,SAAS,WAAW,CAAC;YACrC,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,SAAS;QACvB,OAAO;IACT;AACF;AAKO,SAAS,WAAW,IAAmB,EAAE,OAAoC;IAClF,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAE5D,MAAM,iBAA6C;QACjD,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;IAEA,OAAO,QAAQ,kBAAkB,CAAC,SAAS;QAAE,GAAG,cAAc;QAAE,GAAG,OAAO;IAAC;AAC7E;AAKO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,QAAQ,OAAO,EAAE,IAAI;IAEvE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,gBAAgB;IACjD,IAAI,gBAAgB,IAAI;QACtB,OAAO,GAAG,cAAc,GAAG,CAAC;IAC9B;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI;QACpB,OAAO,GAAG,YAAY,GAAG,CAAC;IAC5B;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,GAAG;QAClB,OAAO,GAAG,WAAW,EAAE,CAAC;IAC1B;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,IAAI,cAAc,GAAG;QACnB,OAAO,GAAG,YAAY,EAAE,CAAC;IAC3B;IAEA,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa;IAC7C,IAAI,eAAe,IAAI;QACrB,OAAO,GAAG,aAAa,GAAG,CAAC;IAC7B;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,OAAO,GAAG,YAAY,EAAE,CAAC;AAC3B;AAKO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW;QAC5B,OAAO;IACT;IACA,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAKO,SAAS,cAAc,IAAY,EAAE,KAAa;IACvD,IAAI,CAAC,MAAM,IAAI,IAAI;QACjB,OAAO;IACT;IAEA,MAAM,QAAQ,IAAI,OAAO,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,uBAAuB,QAAQ,CAAC,CAAC,EAAE;IAC9E,OAAO,KAAK,OAAO,CAAC,OAAO;AAC7B;AAKO,SAAS;IACd,MAAM,SAAS;QACb;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;AAC1D;AAKO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI,UAAiC;IAErC,OAAO,CAAC,GAAG;QACT,IAAI,SAAS;YACX,aAAa;QACf;QAEA,UAAU,WAAW;YACnB,QAAQ;QACV,GAAG;IACL;AACF;AAKO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI,aAAa;IAEjB,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW;gBACT,aAAa;YACf,GAAG;QACL;IACF;AACF;AAKO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAKO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAKO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE", "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/components/ui/Card.tsx"], "sourcesContent": ["import { forwardRef, type HTMLAttributes } from 'react'\nimport { cn } from '~/lib/utils'\n\nconst Card = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-xl border bg-card text-card-foreground shadow',\n        className\n      )}\n      {...props}\n    />\n  )\n)\nCard.displayName = 'Card'\n\nconst CardHeader = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex flex-col space-y-1.5 p-6', className)}\n      {...props}\n    />\n  )\n)\nCardHeader.displayName = 'CardHeader'\n\nconst CardTitle = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLHeadingElement>>(\n  ({ className, ...props }, ref) => (\n    <h3\n      ref={ref}\n      className={cn('font-semibold leading-none tracking-tight', className)}\n      {...props}\n    />\n  )\n)\nCardTitle.displayName = 'CardTitle'\n\nconst CardDescription = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLParagraphElement>>(\n  ({ className, ...props }, ref) => (\n    <p\n      ref={ref}\n      className={cn('text-sm text-muted-foreground', className)}\n      {...props}\n    />\n  )\n)\nCardDescription.displayName = 'CardDescription'\n\nconst CardContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n  )\n)\nCardContent.displayName = 'CardContent'\n\nconst CardFooter = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex items-center p-6 pt-0', className)}\n      {...props}\n    />\n  )\n)\nCardFooter.displayName = 'CardFooter'\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACpB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAIf,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACzB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAIf,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAGlE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAIf,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/components/ui/Badge.tsx"], "sourcesContent": ["import { type VariantProps, cva } from 'class-variance-authority'\nimport { forwardRef, type HTMLAttributes } from 'react'\nimport { cn } from '~/lib/utils'\n\nconst badgeVariants = cva(\n  'inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',\n  {\n    variants: {\n      variant: {\n        default:\n          'border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80',\n        secondary:\n          'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80',\n        destructive:\n          'border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80',\n        outline: 'text-foreground',\n        success:\n          'border-transparent bg-green-500 text-white shadow hover:bg-green-500/80',\n        warning:\n          'border-transparent bg-yellow-500 text-white shadow hover:bg-yellow-500/80',\n        info:\n          'border-transparent bg-blue-500 text-white shadow hover:bg-blue-500/80',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nconst Badge = forwardRef<HTMLDivElement, BadgeProps>(\n  ({ className, variant, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(badgeVariants({ variant }), className)}\n        {...props}\n      />\n    )\n  }\n)\nBadge.displayName = 'Badge'\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,wKACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,SACE;YACF,SACE;YACF,MACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE;IACjC,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/components/ui/Button.tsx"], "sourcesContent": ["import { forwardRef, type ButtonHTMLAttributes } from 'react'\nimport { cn } from '~/lib/utils'\n\nexport interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'\n  size?: 'default' | 'sm' | 'lg' | 'icon'\n  loading?: boolean\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'default', size = 'default', loading = false, disabled, children, ...props }, ref) => {\n    return (\n      <button\n        className={cn(\n          // 基础样式\n          'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50',\n          \n          // 变体样式\n          {\n            // 默认样式\n            'bg-primary text-primary-foreground shadow hover:bg-primary/90': variant === 'default',\n            \n            // 危险操作样式\n            'bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90': variant === 'destructive',\n            \n            // 轮廓样式\n            'border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground': variant === 'outline',\n            \n            // 次要样式\n            'bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80': variant === 'secondary',\n            \n            // 幽灵样式\n            'hover:bg-accent hover:text-accent-foreground': variant === 'ghost',\n            \n            // 链接样式\n            'text-primary underline-offset-4 hover:underline': variant === 'link',\n          },\n          \n          // 尺寸样式\n          {\n            'h-9 px-4 py-2': size === 'default',\n            'h-8 rounded-md px-3 text-xs': size === 'sm',\n            'h-10 rounded-md px-8': size === 'lg',\n            'h-9 w-9': size === 'icon',\n          },\n          \n          className\n        )}\n        disabled={disabled || loading}\n        ref={ref}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"mr-2 h-4 w-4 animate-spin\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\n\nButton.displayName = 'Button'\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACtB,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACpG,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OAAO;QACP,uOAEA,OAAO;QACP;YACE,OAAO;YACP,iEAAiE,YAAY;YAE7E,SAAS;YACT,gFAAgF,YAAY;YAE5F,OAAO;YACP,4FAA4F,YAAY;YAExG,OAAO;YACP,0EAA0E,YAAY;YAEtF,OAAO;YACP,gDAAgD,YAAY;YAE5D,OAAO;YACP,mDAAmD,YAAY;QACjE,GAEA,OAAO;QACP;YACE,iBAAiB,SAAS;YAC1B,+BAA+B,SAAS;YACxC,wBAAwB,SAAS;YACjC,WAAW,SAAS;QACtB,GAEA;QAEF,UAAU,YAAY;QACtB,KAAK;QACJ,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 385, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/store/index.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { devtools, persist } from 'zustand/middleware'\n\n// 定义类型\nexport interface Category {\n  id: string\n  name: string\n  description?: string\n  color: string\n  icon?: string\n  createdAt: Date\n  updatedAt: Date\n  createdById: string\n  _count?: {\n    prompts: number\n  }\n}\n\nexport interface Prompt {\n  id: string\n  title: string\n  content: string\n  description?: string\n  tags: string[]\n  usageCount: number\n  isPublic: boolean\n  createdAt: Date\n  updatedAt: Date\n  categoryId?: string\n  createdById: string\n  category?: {\n    id: string\n    name: string\n    color: string\n    icon?: string\n  }\n  createdBy?: {\n    id: string\n    name?: string\n    image?: string\n  }\n}\n\n// UI状态接口\ninterface UIState {\n  // 侧边栏状态\n  sidebarOpen: boolean\n  setSidebarOpen: (open: boolean) => void\n  \n  // 搜索状态\n  searchQuery: string\n  setSearchQuery: (query: string) => void\n  \n  // 当前选中的分类\n  selectedCategoryId?: string\n  setSelectedCategoryId: (categoryId?: string) => void\n  \n  // 视图模式\n  viewMode: 'grid' | 'list'\n  setViewMode: (mode: 'grid' | 'list') => void\n  \n  // 排序方式\n  sortBy: 'createdAt' | 'updatedAt' | 'usageCount' | 'title'\n  setSortBy: (sortBy: 'createdAt' | 'updatedAt' | 'usageCount' | 'title') => void\n  \n  sortOrder: 'asc' | 'desc'\n  setSortOrder: (order: 'asc' | 'desc') => void\n  \n  // 模态框状态\n  modals: {\n    createPrompt: boolean\n    editPrompt: boolean\n    createCategory: boolean\n    editCategory: boolean\n    promptDetail: boolean\n  }\n  setModal: (modal: keyof UIState['modals'], open: boolean) => void\n  \n  // 当前编辑的项目\n  currentEditingPrompt?: Prompt\n  setCurrentEditingPrompt: (prompt?: Prompt) => void\n  \n  currentEditingCategory?: Category\n  setCurrentEditingCategory: (category?: Category) => void\n  \n  currentViewingPrompt?: Prompt\n  setCurrentViewingPrompt: (prompt?: Prompt) => void\n}\n\n// 搜索历史状态接口\ninterface SearchHistoryState {\n  searchHistory: string[]\n  addSearchHistory: (query: string) => void\n  clearSearchHistory: () => void\n  removeSearchHistory: (query: string) => void\n}\n\n// 用户偏好设置接口\ninterface UserPreferencesState {\n  // 主题设置\n  theme: 'light' | 'dark' | 'system'\n  setTheme: (theme: 'light' | 'dark' | 'system') => void\n  \n  // 语言设置\n  language: 'zh-CN' | 'en-US'\n  setLanguage: (language: 'zh-CN' | 'en-US') => void\n  \n  // 每页显示数量\n  pageSize: number\n  setPageSize: (size: number) => void\n  \n  // 是否显示描述\n  showDescription: boolean\n  setShowDescription: (show: boolean) => void\n  \n  // 是否显示标签\n  showTags: boolean\n  setShowTags: (show: boolean) => void\n  \n  // 是否显示使用次数\n  showUsageCount: boolean\n  setShowUsageCount: (show: boolean) => void\n}\n\n// 创建UI状态store\nexport const useUIStore = create<UIState>()(\n  devtools(\n    persist(\n      (set) => ({\n        sidebarOpen: true,\n        setSidebarOpen: (open) => set({ sidebarOpen: open }),\n        \n        searchQuery: '',\n        setSearchQuery: (query) => set({ searchQuery: query }),\n        \n        selectedCategoryId: undefined,\n        setSelectedCategoryId: (categoryId) => set({ selectedCategoryId: categoryId }),\n        \n        viewMode: 'grid',\n        setViewMode: (mode) => set({ viewMode: mode }),\n        \n        sortBy: 'createdAt',\n        setSortBy: (sortBy) => set({ sortBy }),\n        \n        sortOrder: 'desc',\n        setSortOrder: (order) => set({ sortOrder: order }),\n        \n        modals: {\n          createPrompt: false,\n          editPrompt: false,\n          createCategory: false,\n          editCategory: false,\n          promptDetail: false,\n        },\n        setModal: (modal, open) => \n          set((state) => ({\n            modals: { ...state.modals, [modal]: open }\n          })),\n        \n        currentEditingPrompt: undefined,\n        setCurrentEditingPrompt: (prompt) => set({ currentEditingPrompt: prompt }),\n        \n        currentEditingCategory: undefined,\n        setCurrentEditingCategory: (category) => set({ currentEditingCategory: category }),\n        \n        currentViewingPrompt: undefined,\n        setCurrentViewingPrompt: (prompt) => set({ currentViewingPrompt: prompt }),\n      }),\n      {\n        name: 'ui-store',\n        partialize: (state) => ({\n          sidebarOpen: state.sidebarOpen,\n          viewMode: state.viewMode,\n          sortBy: state.sortBy,\n          sortOrder: state.sortOrder,\n        }),\n      }\n    ),\n    { name: 'ui-store' }\n  )\n)\n\n// 创建搜索历史store\nexport const useSearchHistoryStore = create<SearchHistoryState>()(\n  devtools(\n    persist(\n      (set, get) => ({\n        searchHistory: [],\n        addSearchHistory: (query) => {\n          if (!query.trim()) return\n          \n          const { searchHistory } = get()\n          const newHistory = [query, ...searchHistory.filter(h => h !== query)].slice(0, 10)\n          set({ searchHistory: newHistory })\n        },\n        clearSearchHistory: () => set({ searchHistory: [] }),\n        removeSearchHistory: (query) => \n          set((state) => ({\n            searchHistory: state.searchHistory.filter(h => h !== query)\n          })),\n      }),\n      {\n        name: 'search-history-store',\n      }\n    ),\n    { name: 'search-history-store' }\n  )\n)\n\n// 创建用户偏好设置store\nexport const useUserPreferencesStore = create<UserPreferencesState>()(\n  devtools(\n    persist(\n      (set) => ({\n        theme: 'system',\n        setTheme: (theme) => set({ theme }),\n        \n        language: 'zh-CN',\n        setLanguage: (language) => set({ language }),\n        \n        pageSize: 20,\n        setPageSize: (size) => set({ pageSize: size }),\n        \n        showDescription: true,\n        setShowDescription: (show) => set({ showDescription: show }),\n        \n        showTags: true,\n        setShowTags: (show) => set({ showTags: show }),\n        \n        showUsageCount: true,\n        setShowUsageCount: (show) => set({ showUsageCount: show }),\n      }),\n      {\n        name: 'user-preferences-store',\n      }\n    ),\n    { name: 'user-preferences-store' }\n  )\n)\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AA4HO,MAAM,aAAa,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC7B,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,MAAQ,CAAC;QACR,aAAa;QACb,gBAAgB,CAAC,OAAS,IAAI;gBAAE,aAAa;YAAK;QAElD,aAAa;QACb,gBAAgB,CAAC,QAAU,IAAI;gBAAE,aAAa;YAAM;QAEpD,oBAAoB;QACpB,uBAAuB,CAAC,aAAe,IAAI;gBAAE,oBAAoB;YAAW;QAE5E,UAAU;QACV,aAAa,CAAC,OAAS,IAAI;gBAAE,UAAU;YAAK;QAE5C,QAAQ;QACR,WAAW,CAAC,SAAW,IAAI;gBAAE;YAAO;QAEpC,WAAW;QACX,cAAc,CAAC,QAAU,IAAI;gBAAE,WAAW;YAAM;QAEhD,QAAQ;YACN,cAAc;YACd,YAAY;YACZ,gBAAgB;YAChB,cAAc;YACd,cAAc;QAChB;QACA,UAAU,CAAC,OAAO,OAChB,IAAI,CAAC,QAAU,CAAC;oBACd,QAAQ;wBAAE,GAAG,MAAM,MAAM;wBAAE,CAAC,MAAM,EAAE;oBAAK;gBAC3C,CAAC;QAEH,sBAAsB;QACtB,yBAAyB,CAAC,SAAW,IAAI;gBAAE,sBAAsB;YAAO;QAExE,wBAAwB;QACxB,2BAA2B,CAAC,WAAa,IAAI;gBAAE,wBAAwB;YAAS;QAEhF,sBAAsB;QACtB,yBAAyB,CAAC,SAAW,IAAI;gBAAE,sBAAsB;YAAO;IAC1E,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,aAAa,MAAM,WAAW;YAC9B,UAAU,MAAM,QAAQ;YACxB,QAAQ,MAAM,MAAM;YACpB,WAAW,MAAM,SAAS;QAC5B,CAAC;AACH,IAEF;IAAE,MAAM;AAAW;AAKhB,MAAM,wBAAwB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IACxC,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,eAAe,EAAE;QACjB,kBAAkB,CAAC;YACjB,IAAI,CAAC,MAAM,IAAI,IAAI;YAEnB,MAAM,EAAE,aAAa,EAAE,GAAG;YAC1B,MAAM,aAAa;gBAAC;mBAAU,cAAc,MAAM,CAAC,CAAA,IAAK,MAAM;aAAO,CAAC,KAAK,CAAC,GAAG;YAC/E,IAAI;gBAAE,eAAe;YAAW;QAClC;QACA,oBAAoB,IAAM,IAAI;gBAAE,eAAe,EAAE;YAAC;QAClD,qBAAqB,CAAC,QACpB,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe,MAAM,aAAa,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;gBACvD,CAAC;IACL,CAAC,GACD;IACE,MAAM;AACR,IAEF;IAAE,MAAM;AAAuB;AAK5B,MAAM,0BAA0B,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC1C,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,MAAQ,CAAC;QACR,OAAO;QACP,UAAU,CAAC,QAAU,IAAI;gBAAE;YAAM;QAEjC,UAAU;QACV,aAAa,CAAC,WAAa,IAAI;gBAAE;YAAS;QAE1C,UAAU;QACV,aAAa,CAAC,OAAS,IAAI;gBAAE,UAAU;YAAK;QAE5C,iBAAiB;QACjB,oBAAoB,CAAC,OAAS,IAAI;gBAAE,iBAAiB;YAAK;QAE1D,UAAU;QACV,aAAa,CAAC,OAAS,IAAI;gBAAE,UAAU;YAAK;QAE5C,gBAAgB;QAChB,mBAAmB,CAAC,OAAS,IAAI;gBAAE,gBAAgB;YAAK;IAC1D,CAAC,GACD;IACE,MAAM;AACR,IAEF;IAAE,MAAM;AAAyB", "debugId": null}}, {"offset": {"line": 513, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/hooks/useStore.ts"], "sourcesContent": ["import { useCallback } from 'react'\nimport { \n  useUIStore, \n  useSearchHistoryStore, \n  useUserPreferencesStore,\n  type Prompt,\n  type Category \n} from '~/store'\n\n// UI相关的hooks\nexport const useUI = () => {\n  const store = useUIStore()\n  \n  return {\n    // 侧边栏\n    sidebarOpen: store.sidebarOpen,\n    toggleSidebar: useCallback(() => store.setSidebarOpen(!store.sidebarOpen), [store]),\n    openSidebar: useCallback(() => store.setSidebarOpen(true), [store]),\n    closeSidebar: useCallback(() => store.setSidebarOpen(false), [store]),\n    \n    // 搜索\n    searchQuery: store.searchQuery,\n    setSearchQuery: store.setSearchQuery,\n    clearSearch: useCallback(() => store.setSearchQuery(''), [store]),\n    \n    // 分类筛选\n    selectedCategoryId: store.selectedCategoryId,\n    setSelectedCategoryId: store.setSelectedCategoryId,\n    clearCategoryFilter: useCallback(() => store.setSelectedCategoryId(undefined), [store]),\n    \n    // 视图模式\n    viewMode: store.viewMode,\n    setViewMode: store.setViewMode,\n    toggleViewMode: useCallback(() => {\n      store.setViewMode(store.viewMode === 'grid' ? 'list' : 'grid')\n    }, [store]),\n    \n    // 排序\n    sortBy: store.sortBy,\n    setSortBy: store.setSortBy,\n    sortOrder: store.sortOrder,\n    setSortOrder: store.setSortOrder,\n    toggleSortOrder: useCallback(() => {\n      store.setSortOrder(store.sortOrder === 'asc' ? 'desc' : 'asc')\n    }, [store]),\n  }\n}\n\n// 模态框相关的hooks\nexport const useModals = () => {\n  const store = useUIStore()\n  \n  return {\n    modals: store.modals,\n    \n    // 提示词相关模态框\n    openCreatePrompt: useCallback(() => store.setModal('createPrompt', true), [store]),\n    closeCreatePrompt: useCallback(() => store.setModal('createPrompt', false), [store]),\n    \n    openEditPrompt: useCallback((prompt: Prompt) => {\n      store.setCurrentEditingPrompt(prompt)\n      store.setModal('editPrompt', true)\n    }, [store]),\n    closeEditPrompt: useCallback(() => {\n      store.setCurrentEditingPrompt(undefined)\n      store.setModal('editPrompt', false)\n    }, [store]),\n    \n    openPromptDetail: useCallback((prompt: Prompt) => {\n      store.setCurrentViewingPrompt(prompt)\n      store.setModal('promptDetail', true)\n    }, [store]),\n    closePromptDetail: useCallback(() => {\n      store.setCurrentViewingPrompt(undefined)\n      store.setModal('promptDetail', false)\n    }, [store]),\n    \n    // 分类相关模态框\n    openCreateCategory: useCallback(() => store.setModal('createCategory', true), [store]),\n    closeCreateCategory: useCallback(() => store.setModal('createCategory', false), [store]),\n    \n    openEditCategory: useCallback((category: Category) => {\n      store.setCurrentEditingCategory(category)\n      store.setModal('editCategory', true)\n    }, [store]),\n    closeEditCategory: useCallback(() => {\n      store.setCurrentEditingCategory(undefined)\n      store.setModal('editCategory', false)\n    }, [store]),\n    \n    // 当前编辑的项目\n    currentEditingPrompt: store.currentEditingPrompt,\n    currentEditingCategory: store.currentEditingCategory,\n    currentViewingPrompt: store.currentViewingPrompt,\n  }\n}\n\n// 搜索历史相关的hooks\nexport const useSearchHistory = () => {\n  const store = useSearchHistoryStore()\n  \n  return {\n    searchHistory: store.searchHistory,\n    addSearchHistory: store.addSearchHistory,\n    clearSearchHistory: store.clearSearchHistory,\n    removeSearchHistory: store.removeSearchHistory,\n    \n    // 便捷方法\n    hasHistory: store.searchHistory.length > 0,\n    recentSearches: store.searchHistory.slice(0, 5), // 最近5个搜索\n  }\n}\n\n// 用户偏好设置相关的hooks\nexport const useUserPreferences = () => {\n  const store = useUserPreferencesStore()\n  \n  return {\n    // 主题\n    theme: store.theme,\n    setTheme: store.setTheme,\n    isDarkMode: store.theme === 'dark',\n    isLightMode: store.theme === 'light',\n    isSystemMode: store.theme === 'system',\n    \n    // 语言\n    language: store.language,\n    setLanguage: store.setLanguage,\n    isChineseMode: store.language === 'zh-CN',\n    isEnglishMode: store.language === 'en-US',\n    \n    // 显示设置\n    pageSize: store.pageSize,\n    setPageSize: store.setPageSize,\n    showDescription: store.showDescription,\n    setShowDescription: store.setShowDescription,\n    showTags: store.showTags,\n    setShowTags: store.setShowTags,\n    showUsageCount: store.showUsageCount,\n    setShowUsageCount: store.setShowUsageCount,\n    \n    // 便捷方法\n    toggleDescription: useCallback(() => store.setShowDescription(!store.showDescription), [store]),\n    toggleTags: useCallback(() => store.setShowTags(!store.showTags), [store]),\n    toggleUsageCount: useCallback(() => store.setShowUsageCount(!store.showUsageCount), [store]),\n  }\n}\n\n// 组合hook，提供常用的状态和操作\nexport const useAppState = () => {\n  const ui = useUI()\n  const modals = useModals()\n  const searchHistory = useSearchHistory()\n  const preferences = useUserPreferences()\n  \n  return {\n    ui,\n    modals,\n    searchHistory,\n    preferences,\n    \n    // 全局重置方法\n    resetFilters: useCallback(() => {\n      ui.clearSearch()\n      ui.clearCategoryFilter()\n      ui.setSortBy('createdAt')\n      ui.setSortOrder('desc')\n    }, [ui]),\n    \n    // 搜索相关的组合操作\n    performSearch: useCallback((query: string) => {\n      if (query.trim()) {\n        searchHistory.addSearchHistory(query.trim())\n        ui.setSearchQuery(query.trim())\n      }\n    }, [ui, searchHistory]),\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AASO,MAAM,QAAQ;IACnB,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,aAAU,AAAD;IAEvB,OAAO;QACL,MAAM;QACN,aAAa,MAAM,WAAW;QAC9B,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAM,MAAM,cAAc,CAAC,CAAC,MAAM,WAAW,GAAG;YAAC;SAAM;QAClF,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAM,MAAM,cAAc,CAAC,OAAO;YAAC;SAAM;QAClE,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAM,MAAM,cAAc,CAAC,QAAQ;YAAC;SAAM;QAEpE,KAAK;QACL,aAAa,MAAM,WAAW;QAC9B,gBAAgB,MAAM,cAAc;QACpC,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAM,MAAM,cAAc,CAAC,KAAK;YAAC;SAAM;QAEhE,OAAO;QACP,oBAAoB,MAAM,kBAAkB;QAC5C,uBAAuB,MAAM,qBAAqB;QAClD,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAM,MAAM,qBAAqB,CAAC,YAAY;YAAC;SAAM;QAEtF,OAAO;QACP,UAAU,MAAM,QAAQ;QACxB,aAAa,MAAM,WAAW;QAC9B,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;YAC1B,MAAM,WAAW,CAAC,MAAM,QAAQ,KAAK,SAAS,SAAS;QACzD,GAAG;YAAC;SAAM;QAEV,KAAK;QACL,QAAQ,MAAM,MAAM;QACpB,WAAW,MAAM,SAAS;QAC1B,WAAW,MAAM,SAAS;QAC1B,cAAc,MAAM,YAAY;QAChC,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;YAC3B,MAAM,YAAY,CAAC,MAAM,SAAS,KAAK,QAAQ,SAAS;QAC1D,GAAG;YAAC;SAAM;IACZ;AACF;AAGO,MAAM,YAAY;IACvB,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,aAAU,AAAD;IAEvB,OAAO;QACL,QAAQ,MAAM,MAAM;QAEpB,WAAW;QACX,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAM,MAAM,QAAQ,CAAC,gBAAgB,OAAO;YAAC;SAAM;QACjF,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAM,MAAM,QAAQ,CAAC,gBAAgB,QAAQ;YAAC;SAAM;QAEnF,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;YAC3B,MAAM,uBAAuB,CAAC;YAC9B,MAAM,QAAQ,CAAC,cAAc;QAC/B,GAAG;YAAC;SAAM;QACV,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;YAC3B,MAAM,uBAAuB,CAAC;YAC9B,MAAM,QAAQ,CAAC,cAAc;QAC/B,GAAG;YAAC;SAAM;QAEV,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;YAC7B,MAAM,uBAAuB,CAAC;YAC9B,MAAM,QAAQ,CAAC,gBAAgB;QACjC,GAAG;YAAC;SAAM;QACV,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;YAC7B,MAAM,uBAAuB,CAAC;YAC9B,MAAM,QAAQ,CAAC,gBAAgB;QACjC,GAAG;YAAC;SAAM;QAEV,UAAU;QACV,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAM,MAAM,QAAQ,CAAC,kBAAkB,OAAO;YAAC;SAAM;QACrF,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAM,MAAM,QAAQ,CAAC,kBAAkB,QAAQ;YAAC;SAAM;QAEvF,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;YAC7B,MAAM,yBAAyB,CAAC;YAChC,MAAM,QAAQ,CAAC,gBAAgB;QACjC,GAAG;YAAC;SAAM;QACV,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;YAC7B,MAAM,yBAAyB,CAAC;YAChC,MAAM,QAAQ,CAAC,gBAAgB;QACjC,GAAG;YAAC;SAAM;QAEV,UAAU;QACV,sBAAsB,MAAM,oBAAoB;QAChD,wBAAwB,MAAM,sBAAsB;QACpD,sBAAsB,MAAM,oBAAoB;IAClD;AACF;AAGO,MAAM,mBAAmB;IAC9B,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD;IAElC,OAAO;QACL,eAAe,MAAM,aAAa;QAClC,kBAAkB,MAAM,gBAAgB;QACxC,oBAAoB,MAAM,kBAAkB;QAC5C,qBAAqB,MAAM,mBAAmB;QAE9C,OAAO;QACP,YAAY,MAAM,aAAa,CAAC,MAAM,GAAG;QACzC,gBAAgB,MAAM,aAAa,CAAC,KAAK,CAAC,GAAG;IAC/C;AACF;AAGO,MAAM,qBAAqB;IAChC,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,0BAAuB,AAAD;IAEpC,OAAO;QACL,KAAK;QACL,OAAO,MAAM,KAAK;QAClB,UAAU,MAAM,QAAQ;QACxB,YAAY,MAAM,KAAK,KAAK;QAC5B,aAAa,MAAM,KAAK,KAAK;QAC7B,cAAc,MAAM,KAAK,KAAK;QAE9B,KAAK;QACL,UAAU,MAAM,QAAQ;QACxB,aAAa,MAAM,WAAW;QAC9B,eAAe,MAAM,QAAQ,KAAK;QAClC,eAAe,MAAM,QAAQ,KAAK;QAElC,OAAO;QACP,UAAU,MAAM,QAAQ;QACxB,aAAa,MAAM,WAAW;QAC9B,iBAAiB,MAAM,eAAe;QACtC,oBAAoB,MAAM,kBAAkB;QAC5C,UAAU,MAAM,QAAQ;QACxB,aAAa,MAAM,WAAW;QAC9B,gBAAgB,MAAM,cAAc;QACpC,mBAAmB,MAAM,iBAAiB;QAE1C,OAAO;QACP,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAM,MAAM,kBAAkB,CAAC,CAAC,MAAM,eAAe,GAAG;YAAC;SAAM;QAC9F,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAM,MAAM,WAAW,CAAC,CAAC,MAAM,QAAQ,GAAG;YAAC;SAAM;QACzE,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAM,MAAM,iBAAiB,CAAC,CAAC,MAAM,cAAc,GAAG;YAAC;SAAM;IAC7F;AACF;AAGO,MAAM,cAAc;IACzB,MAAM,KAAK;IACX,MAAM,SAAS;IACf,MAAM,gBAAgB;IACtB,MAAM,cAAc;IAEpB,OAAO;QACL;QACA;QACA;QACA;QAEA,SAAS;QACT,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;YACxB,GAAG,WAAW;YACd,GAAG,mBAAmB;YACtB,GAAG,SAAS,CAAC;YACb,GAAG,YAAY,CAAC;QAClB,GAAG;YAAC;SAAG;QAEP,YAAY;QACZ,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;YAC1B,IAAI,MAAM,IAAI,IAAI;gBAChB,cAAc,gBAAgB,CAAC,MAAM,IAAI;gBACzC,GAAG,cAAc,CAAC,MAAM,IAAI;YAC9B;QACF,GAAG;YAAC;YAAI;SAAc;IACxB;AACF", "debugId": null}}, {"offset": {"line": 712, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/components/PromptCard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion } from 'framer-motion'\nimport { toast } from 'react-hot-toast'\nimport { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from '~/components/ui/Card'\nimport { Badge } from '~/components/ui/Badge'\nimport { Button } from '~/components/ui/Button'\nimport { useModals } from '~/hooks/useStore'\nimport { copyToClipboard, formatRelativeTime, truncateText } from '~/lib/utils'\nimport type { Prompt } from '~/store'\n\ninterface PromptCardProps {\n  prompt: Prompt\n  onCopy?: (promptId: string) => void\n  showActions?: boolean\n  className?: string\n}\n\nexport function PromptCard({ \n  prompt, \n  onCopy, \n  showActions = true,\n  className \n}: PromptCardProps) {\n  const [isHovered, setIsHovered] = useState(false)\n  const { openPromptDetail, openEditPrompt } = useModals()\n\n  const handleCopy = async (e: React.MouseEvent) => {\n    e.stopPropagation()\n    \n    const success = await copyToClipboard(prompt.content)\n    if (success) {\n      toast.success('提示词已复制到剪贴板')\n      onCopy?.(prompt.id)\n    } else {\n      toast.error('复制失败，请重试')\n    }\n  }\n\n  const handleEdit = (e: React.MouseEvent) => {\n    e.stopPropagation()\n    openEditPrompt(prompt)\n  }\n\n  const handleCardClick = () => {\n    openPromptDetail(prompt)\n  }\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      exit={{ opacity: 0, y: -20 }}\n      whileHover={{ y: -4 }}\n      transition={{ duration: 0.2 }}\n      className={className}\n    >\n      <Card \n        className=\"h-full cursor-pointer transition-all duration-200 hover:shadow-lg\"\n        onMouseEnter={() => setIsHovered(true)}\n        onMouseLeave={() => setIsHovered(false)}\n        onClick={handleCardClick}\n      >\n        <CardHeader className=\"pb-3\">\n          <div className=\"flex items-start justify-between\">\n            <CardTitle className=\"text-lg font-semibold line-clamp-2\">\n              {prompt.title}\n            </CardTitle>\n            \n            {/* 使用次数徽章 */}\n            <Badge variant=\"secondary\" className=\"ml-2 shrink-0\">\n              {prompt.usageCount}次\n            </Badge>\n          </div>\n          \n          {/* 分类标签 */}\n          {prompt.category && (\n            <div className=\"flex items-center gap-2 mt-2\">\n              <div \n                className=\"w-3 h-3 rounded-full\"\n                style={{ backgroundColor: prompt.category.color }}\n              />\n              <span className=\"text-sm text-muted-foreground\">\n                {prompt.category.name}\n              </span>\n            </div>\n          )}\n        </CardHeader>\n\n        <CardContent className=\"pb-3\">\n          {/* 描述 */}\n          {prompt.description && (\n            <p className=\"text-sm text-muted-foreground mb-3 line-clamp-2\">\n              {prompt.description}\n            </p>\n          )}\n          \n          {/* 内容预览 */}\n          <div className=\"text-sm bg-muted/50 rounded-md p-3 mb-3\">\n            <p className=\"line-clamp-3\">\n              {truncateText(prompt.content, 150)}\n            </p>\n          </div>\n          \n          {/* 标签 */}\n          {prompt.tags.length > 0 && (\n            <div className=\"flex flex-wrap gap-1 mb-3\">\n              {prompt.tags.slice(0, 3).map((tag, index) => (\n                <Badge key={index} variant=\"outline\" className=\"text-xs\">\n                  {tag}\n                </Badge>\n              ))}\n              {prompt.tags.length > 3 && (\n                <Badge variant=\"outline\" className=\"text-xs\">\n                  +{prompt.tags.length - 3}\n                </Badge>\n              )}\n            </div>\n          )}\n        </CardContent>\n\n        <CardFooter className=\"pt-0\">\n          <div className=\"flex items-center justify-between w-full\">\n            {/* 创建时间和作者 */}\n            <div className=\"flex items-center gap-2 text-xs text-muted-foreground\">\n              <span>{formatRelativeTime(prompt.createdAt)}</span>\n              {prompt.createdBy?.name && (\n                <>\n                  <span>•</span>\n                  <span>{prompt.createdBy.name}</span>\n                </>\n              )}\n            </div>\n            \n            {/* 操作按钮 */}\n            {showActions && (\n              <motion.div \n                className=\"flex items-center gap-1\"\n                initial={{ opacity: 0 }}\n                animate={{ opacity: isHovered ? 1 : 0 }}\n                transition={{ duration: 0.2 }}\n              >\n                <Button\n                  size=\"sm\"\n                  variant=\"ghost\"\n                  onClick={handleCopy}\n                  className=\"h-8 px-2\"\n                >\n                  <svg\n                    className=\"w-4 h-4\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      strokeWidth={2}\n                      d=\"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\"\n                    />\n                  </svg>\n                </Button>\n                \n                <Button\n                  size=\"sm\"\n                  variant=\"ghost\"\n                  onClick={handleEdit}\n                  className=\"h-8 px-2\"\n                >\n                  <svg\n                    className=\"w-4 h-4\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      strokeWidth={2}\n                      d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                    />\n                  </svg>\n                </Button>\n              </motion.div>\n            )}\n          </div>\n        </CardFooter>\n      </Card>\n    </motion.div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAmBO,SAAS,WAAW,EACzB,MAAM,EACN,MAAM,EACN,cAAc,IAAI,EAClB,SAAS,EACO;IAChB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,gBAAgB,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,YAAS,AAAD;IAErD,MAAM,aAAa,OAAO;QACxB,EAAE,eAAe;QAEjB,MAAM,UAAU,MAAM,CAAA,GAAA,mHAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,OAAO;QACpD,IAAI,SAAS;YACX,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,SAAS,OAAO,EAAE;QACpB,OAAO;YACL,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,eAAe;QACjB,eAAe;IACjB;IAEA,MAAM,kBAAkB;QACtB,iBAAiB;IACnB;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,MAAM;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC3B,YAAY;YAAE,GAAG,CAAC;QAAE;QACpB,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW;kBAEX,cAAA,8OAAC,gIAAA,CAAA,OAAI;YACH,WAAU;YACV,cAAc,IAAM,aAAa;YACjC,cAAc,IAAM,aAAa;YACjC,SAAS;;8BAET,8OAAC,gIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAClB,OAAO,KAAK;;;;;;8CAIf,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;;wCAClC,OAAO,UAAU;wCAAC;;;;;;;;;;;;;wBAKtB,OAAO,QAAQ,kBACd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,iBAAiB,OAAO,QAAQ,CAAC,KAAK;oCAAC;;;;;;8CAElD,8OAAC;oCAAK,WAAU;8CACb,OAAO,QAAQ,CAAC,IAAI;;;;;;;;;;;;;;;;;;8BAM7B,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;wBAEpB,OAAO,WAAW,kBACjB,8OAAC;4BAAE,WAAU;sCACV,OAAO,WAAW;;;;;;sCAKvB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CACV,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,OAAO,OAAO,EAAE;;;;;;;;;;;wBAKjC,OAAO,IAAI,CAAC,MAAM,GAAG,mBACpB,8OAAC;4BAAI,WAAU;;gCACZ,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBACjC,8OAAC,iIAAA,CAAA,QAAK;wCAAa,SAAQ;wCAAU,WAAU;kDAC5C;uCADS;;;;;gCAIb,OAAO,IAAI,CAAC,MAAM,GAAG,mBACpB,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;;wCAAU;wCACzC,OAAO,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;8BAOjC,8OAAC,gIAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAM,CAAA,GAAA,mHAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO,SAAS;;;;;;oCACzC,OAAO,SAAS,EAAE,sBACjB;;0DACE,8OAAC;0DAAK;;;;;;0DACN,8OAAC;0DAAM,OAAO,SAAS,CAAC,IAAI;;;;;;;;;;;;;;4BAMjC,6BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS,YAAY,IAAI;gCAAE;gCACtC,YAAY;oCAAE,UAAU;gCAAI;;kDAE5B,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC;4CACC,WAAU;4CACV,MAAK;4CACL,QAAO;4CACP,SAAQ;sDAER,cAAA,8OAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAa;gDACb,GAAE;;;;;;;;;;;;;;;;kDAKR,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC;4CACC,WAAU;4CACV,MAAK;4CACL,QAAO;4CACP,SAAQ;sDAER,cAAA,8OAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAa;gDACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWxB", "debugId": null}}, {"offset": {"line": 1051, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/components/ui/Input.tsx"], "sourcesContent": ["import { forwardRef, type InputHTMLAttributes } from 'react'\nimport { cn } from '~/lib/utils'\n\nexport interface InputProps extends InputHTMLAttributes<HTMLInputElement> {\n  error?: string\n}\n\nconst Input = forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, error, ...props }, ref) => {\n    return (\n      <div className=\"w-full\">\n        <input\n          type={type}\n          className={cn(\n            'flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50',\n            error && 'border-destructive focus-visible:ring-destructive',\n            className\n          )}\n          ref={ref}\n          {...props}\n        />\n        {error && (\n          <p className=\"mt-1 text-sm text-destructive\">{error}</p>\n        )}\n      </div>\n    )\n  }\n)\nInput.displayName = 'Input'\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAMA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrB,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE;IACrC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,MAAM;gBACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yUACA,SAAS,qDACT;gBAEF,KAAK;gBACJ,GAAG,KAAK;;;;;;YAEV,uBACC,8OAAC;gBAAE,WAAU;0BAAiC;;;;;;;;;;;;AAItD;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1095, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/website/Augment2/prompt-manager/src/app/_components/HomePage.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion } from 'framer-motion'\nimport { api } from '~/trpc/react'\nimport { PromptCard } from '~/components/PromptCard'\nimport { Button } from '~/components/ui/Button'\nimport { Input } from '~/components/ui/Input'\nimport { Badge } from '~/components/ui/Badge'\nimport { useUI, useModals } from '~/hooks/useStore'\n\nexport function HomePage() {\n  const [searchQuery, setSearchQuery] = useState('')\n  const { openCreatePrompt } = useModals()\n  \n  // 获取数据\n  const { data: latestPrompts, isLoading: isLoadingPrompts } = api.prompt.getLatest.useQuery({ limit: 8 })\n  const { data: popularPrompts } = api.prompt.getPopular.useQuery({ limit: 6 })\n  const { data: categories } = api.category.getAll.useQuery()\n  const { data: stats } = api.stats.getOverview.useQuery()\n  \n  // 复制提示词的mutation\n  const copyPromptMutation = api.prompt.copy.useMutation()\n  \n  const handleCopyPrompt = async (promptId: string) => {\n    try {\n      await copyPromptMutation.mutateAsync({ id: promptId })\n    } catch (error) {\n      console.error('更新使用次数失败:', error)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800\">\n      {/* 头部区域 */}\n      <header className=\"bg-white/80 backdrop-blur-sm border-b border-slate-200 dark:bg-slate-900/80 dark:border-slate-700 sticky top-0 z-40\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-4\">\n              <h1 className=\"text-2xl font-bold text-slate-900 dark:text-white\">\n                提示词管理工具\n              </h1>\n              {stats && (\n                <div className=\"hidden md:flex items-center gap-4 text-sm text-slate-600 dark:text-slate-400\">\n                  <span>{stats.totalPrompts} 个提示词</span>\n                  <span>•</span>\n                  <span>{stats.totalCategories} 个分类</span>\n                  <span>•</span>\n                  <span>{stats.totalUsages} 次使用</span>\n                </div>\n              )}\n            </div>\n            \n            <div className=\"flex items-center gap-4\">\n              <div className=\"relative\">\n                <Input\n                  placeholder=\"搜索提示词...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"w-64\"\n                />\n                <svg\n                  className=\"absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                  />\n                </svg>\n              </div>\n              \n              <Button onClick={openCreatePrompt}>\n                <svg\n                  className=\"w-4 h-4 mr-2\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M12 4v16m8-8H4\"\n                  />\n                </svg>\n                新建提示词\n              </Button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <main className=\"container mx-auto px-4 py-8\">\n        {/* 分类快速导航 */}\n        {categories && categories.length > 0 && (\n          <section className=\"mb-8\">\n            <h2 className=\"text-lg font-semibold text-slate-900 dark:text-white mb-4\">\n              分类导航\n            </h2>\n            <div className=\"flex flex-wrap gap-2\">\n              {categories.map((category) => (\n                <Badge\n                  key={category.id}\n                  variant=\"outline\"\n                  className=\"cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors\"\n                  style={{ borderColor: category.color }}\n                >\n                  <div \n                    className=\"w-2 h-2 rounded-full mr-2\"\n                    style={{ backgroundColor: category.color }}\n                  />\n                  {category.name}\n                  {category._count && (\n                    <span className=\"ml-1 text-xs text-slate-500\">\n                      ({category._count.prompts})\n                    </span>\n                  )}\n                </Badge>\n              ))}\n            </div>\n          </section>\n        )}\n\n        {/* 热门提示词 */}\n        {popularPrompts && popularPrompts.length > 0 && (\n          <section className=\"mb-8\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h2 className=\"text-lg font-semibold text-slate-900 dark:text-white\">\n                🔥 热门提示词\n              </h2>\n              <Button variant=\"ghost\" size=\"sm\">\n                查看更多\n              </Button>\n            </div>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {popularPrompts.map((prompt, index) => (\n                <motion.div\n                  key={prompt.id}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: index * 0.1 }}\n                >\n                  <PromptCard\n                    prompt={prompt}\n                    onCopy={handleCopyPrompt}\n                  />\n                </motion.div>\n              ))}\n            </div>\n          </section>\n        )}\n\n        {/* 最新提示词 */}\n        <section>\n          <div className=\"flex items-center justify-between mb-4\">\n            <h2 className=\"text-lg font-semibold text-slate-900 dark:text-white\">\n              ✨ 最新提示词\n            </h2>\n            <Button variant=\"ghost\" size=\"sm\">\n              查看全部\n            </Button>\n          </div>\n          \n          {isLoadingPrompts ? (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4\">\n              {Array.from({ length: 8 }).map((_, index) => (\n                <div\n                  key={index}\n                  className=\"h-64 bg-slate-200 dark:bg-slate-700 rounded-lg animate-pulse\"\n                />\n              ))}\n            </div>\n          ) : latestPrompts && latestPrompts.length > 0 ? (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4\">\n              {latestPrompts.map((prompt, index) => (\n                <motion.div\n                  key={prompt.id}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: index * 0.1 }}\n                >\n                  <PromptCard\n                    prompt={prompt}\n                    onCopy={handleCopyPrompt}\n                  />\n                </motion.div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-12\">\n              <div className=\"text-slate-400 dark:text-slate-600 mb-4\">\n                <svg\n                  className=\"w-16 h-16 mx-auto mb-4\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={1}\n                    d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                  />\n                </svg>\n              </div>\n              <h3 className=\"text-lg font-medium text-slate-900 dark:text-white mb-2\">\n                还没有提示词\n              </h3>\n              <p className=\"text-slate-600 dark:text-slate-400 mb-4\">\n                创建您的第一个提示词，开始管理您的AI助手对话模板\n              </p>\n              <Button onClick={openCreatePrompt}>\n                创建第一个提示词\n              </Button>\n            </div>\n          )}\n        </section>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWO,SAAS;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,YAAS,AAAD;IAErC,OAAO;IACP,MAAM,EAAE,MAAM,aAAa,EAAE,WAAW,gBAAgB,EAAE,GAAG,qHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;QAAE,OAAO;IAAE;IACtG,MAAM,EAAE,MAAM,cAAc,EAAE,GAAG,qHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;QAAE,OAAO;IAAE;IAC3E,MAAM,EAAE,MAAM,UAAU,EAAE,GAAG,qHAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ;IACzD,MAAM,EAAE,MAAM,KAAK,EAAE,GAAG,qHAAA,CAAA,MAAG,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ;IAEtD,iBAAiB;IACjB,MAAM,qBAAqB,qHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW;IAEtD,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,mBAAmB,WAAW,CAAC;gBAAE,IAAI;YAAS;QACtD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;oCAGjE,uBACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;oDAAM,MAAM,YAAY;oDAAC;;;;;;;0DAC1B,8OAAC;0DAAK;;;;;;0DACN,8OAAC;;oDAAM,MAAM,eAAe;oDAAC;;;;;;;0DAC7B,8OAAC;0DAAK;;;;;;0DACN,8OAAC;;oDAAM,MAAM,WAAW;oDAAC;;;;;;;;;;;;;;;;;;;0CAK/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;0DAEZ,8OAAC;gDACC,WAAU;gDACV,MAAK;gDACL,QAAO;gDACP,SAAQ;0DAER,cAAA,8OAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,aAAa;oDACb,GAAE;;;;;;;;;;;;;;;;;kDAKR,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAS;;0DACf,8OAAC;gDACC,WAAU;gDACV,MAAK;gDACL,QAAO;gDACP,SAAQ;0DAER,cAAA,8OAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,aAAa;oDACb,GAAE;;;;;;;;;;;4CAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhB,8OAAC;gBAAK,WAAU;;oBAEb,cAAc,WAAW,MAAM,GAAG,mBACjC,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAG1E,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,iIAAA,CAAA,QAAK;wCAEJ,SAAQ;wCACR,WAAU;wCACV,OAAO;4CAAE,aAAa,SAAS,KAAK;wCAAC;;0DAErC,8OAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,iBAAiB,SAAS,KAAK;gDAAC;;;;;;4CAE1C,SAAS,IAAI;4CACb,SAAS,MAAM,kBACd,8OAAC;gDAAK,WAAU;;oDAA8B;oDAC1C,SAAS,MAAM,CAAC,OAAO;oDAAC;;;;;;;;uCAZzB,SAAS,EAAE;;;;;;;;;;;;;;;;oBAsBzB,kBAAkB,eAAe,MAAM,GAAG,mBACzC,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAuD;;;;;;kDAGrE,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;kDAAK;;;;;;;;;;;;0CAKpC,8OAAC;gCAAI,WAAU;0CACZ,eAAe,GAAG,CAAC,CAAC,QAAQ,sBAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO,QAAQ;wCAAI;kDAEjC,cAAA,8OAAC,gIAAA,CAAA,aAAU;4CACT,QAAQ;4CACR,QAAQ;;;;;;uCAPL,OAAO,EAAE;;;;;;;;;;;;;;;;kCAgBxB,8OAAC;;0CACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAuD;;;;;;kDAGrE,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;kDAAK;;;;;;;;;;;;4BAKnC,iCACC,8OAAC;gCAAI,WAAU;0CACZ,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;wCAEC,WAAU;uCADL;;;;;;;;;uCAKT,iBAAiB,cAAc,MAAM,GAAG,kBAC1C,8OAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,QAAQ,sBAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO,QAAQ;wCAAI;kDAEjC,cAAA,8OAAC,gIAAA,CAAA,aAAU;4CACT,QAAQ;4CACR,QAAQ;;;;;;uCAPL,OAAO,EAAE;;;;;;;;;qDAapB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,WAAU;4CACV,MAAK;4CACL,QAAO;4CACP,SAAQ;sDAER,cAAA,8OAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAa;gDACb,GAAE;;;;;;;;;;;;;;;;kDAIR,8OAAC;wCAAG,WAAU;kDAA0D;;;;;;kDAGxE,8OAAC;wCAAE,WAAU;kDAA0C;;;;;;kDAGvD,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAS;kDAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjD", "debugId": null}}]}