'use client'

import { useState } from 'react'
import { toast } from 'react-hot-toast'
import { But<PERSON> } from '~/components/ui/Button'
import { Input } from '~/components/ui/Input'
import { Textarea } from '~/components/ui/Textarea'
import { generateRandomColor } from '~/lib/utils'
import type { Category } from '~/store'

interface CategoryFormProps {
  category?: Category
  onSubmit?: (data: CategoryFormData) => void
  onCancel?: () => void
  isLoading?: boolean
}

export interface CategoryFormData {
  name: string
  description?: string
  color: string
  icon?: string
}

const PRESET_COLORS = [
  '#EF4444', // red-500
  '#F97316', // orange-500
  '#F59E0B', // amber-500
  '#EAB308', // yellow-500
  '#84CC16', // lime-500
  '#22C55E', // green-500
  '#10B981', // emerald-500
  '#14B8A6', // teal-500
  '#06B6D4', // cyan-500
  '#0EA5E9', // sky-500
  '#3B82F6', // blue-500
  '#6366F1', // indigo-500
  '#8B5CF6', // violet-500
  '#A855F7', // purple-500
  '#D946EF', // fuchsia-500
  '#EC4899', // pink-500
]

const PRESET_ICONS = [
  'code', 'pen', 'book', 'star', 'heart', 'lightbulb', 'tag', 'folder',
  'document', 'chat', 'puzzle', 'rocket', 'shield', 'globe', 'music', 'camera'
]

export function CategoryForm({ category, onSubmit, onCancel, isLoading = false }: CategoryFormProps) {
  const [formData, setFormData] = useState<CategoryFormData>({
    name: category?.name || '',
    description: category?.description || '',
    color: category?.color || '#3B82F6',
    icon: category?.icon || 'folder',
  })
  
  const [errors, setErrors] = useState<Record<string, string>>({})
  
  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}
    
    if (!formData.name.trim()) {
      newErrors.name = '分类名称不能为空'
    } else if (formData.name.length > 50) {
      newErrors.name = '分类名称不能超过50个字符'
    }
    
    if (formData.description && formData.description.length > 200) {
      newErrors.description = '描述不能超过200个字符'
    }
    
    if (!formData.color.match(/^#[0-9A-F]{6}$/i)) {
      newErrors.color = '颜色格式不正确'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }
  
  // 处理表单提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      toast.error('请检查表单输入')
      return
    }
    
    onSubmit?.(formData)
  }
  
  // 处理输入变化
  const handleInputChange = (field: keyof CategoryFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }
  
  // 随机生成颜色
  const handleRandomColor = () => {
    const randomColor = generateRandomColor()
    handleInputChange('color', randomColor)
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* 分类名称 */}
      <div>
        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
          分类名称 <span className="text-red-500">*</span>
        </label>
        <Input
          value={formData.name}
          onChange={(e) => handleInputChange('name', e.target.value)}
          placeholder="输入分类名称..."
          error={errors.name}
          maxLength={50}
        />
        <div className="text-xs text-slate-500 mt-1">
          {formData.name.length}/50
        </div>
      </div>
      
      {/* 描述 */}
      <div>
        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
          描述
        </label>
        <Textarea
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          placeholder="简短描述这个分类..."
          rows={3}
          error={errors.description}
          maxLength={200}
        />
        <div className="text-xs text-slate-500 mt-1">
          {(formData.description || '').length}/200
        </div>
      </div>
      
      {/* 颜色选择 */}
      <div>
        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
          分类颜色
        </label>
        <div className="space-y-3">
          {/* 预设颜色 */}
          <div className="grid grid-cols-8 gap-2">
            {PRESET_COLORS.map((color) => (
              <button
                key={color}
                type="button"
                className={`w-8 h-8 rounded-full border-2 transition-all ${
                  formData.color === color 
                    ? 'border-slate-900 dark:border-slate-100 scale-110' 
                    : 'border-slate-300 dark:border-slate-600 hover:scale-105'
                }`}
                style={{ backgroundColor: color }}
                onClick={() => handleInputChange('color', color)}
              />
            ))}
          </div>
          
          {/* 自定义颜色 */}
          <div className="flex items-center gap-2">
            <input
              type="color"
              value={formData.color}
              onChange={(e) => handleInputChange('color', e.target.value)}
              className="w-8 h-8 rounded border border-input"
            />
            <Input
              value={formData.color}
              onChange={(e) => handleInputChange('color', e.target.value)}
              placeholder="#3B82F6"
              className="flex-1"
              error={errors.color}
            />
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleRandomColor}
            >
              随机
            </Button>
          </div>
        </div>
      </div>
      
      {/* 图标选择 */}
      <div>
        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
          图标
        </label>
        <div className="grid grid-cols-8 gap-2">
          {PRESET_ICONS.map((icon) => (
            <button
              key={icon}
              type="button"
              className={`p-2 rounded border text-sm transition-all ${
                formData.icon === icon
                  ? 'border-primary bg-primary text-primary-foreground'
                  : 'border-input hover:bg-accent'
              }`}
              onClick={() => handleInputChange('icon', icon)}
            >
              {icon}
            </button>
          ))}
        </div>
      </div>
      
      {/* 预览 */}
      <div>
        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
          预览
        </label>
        <div className="flex items-center gap-2 p-3 border border-input rounded-md">
          <div 
            className="w-4 h-4 rounded-full"
            style={{ backgroundColor: formData.color }}
          />
          <span className="font-medium">{formData.name || '分类名称'}</span>
          <span className="text-xs text-muted-foreground">({formData.icon})</span>
        </div>
      </div>
      
      {/* 操作按钮 */}
      <div className="flex items-center gap-3 pt-4">
        <Button
          type="submit"
          loading={isLoading}
          className="flex-1 sm:flex-none"
        >
          {category ? '更新分类' : '创建分类'}
        </Button>
        
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
          className="flex-1 sm:flex-none"
        >
          取消
        </Button>
      </div>
    </form>
  )
}
