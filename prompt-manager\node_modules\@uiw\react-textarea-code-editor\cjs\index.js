"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault")["default"];
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard")["default"];
Object.defineProperty(exports, "__esModule", {
  value: true
});
var _exportNames = {};
exports["default"] = void 0;
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _react = _interopRequireDefault(require("react"));
var _Editor = _interopRequireWildcard(require("./Editor"));
Object.keys(_Editor).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _Editor[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function get() {
      return _Editor[key];
    }
  });
});
var _rehypePrismPlus = _interopRequireDefault(require("rehype-prism-plus"));
var _jsxRuntime = require("react/jsx-runtime");
var _excluded = ["rehypePlugins"];
var _default = exports["default"] = /*#__PURE__*/_react["default"].forwardRef(function (props, ref) {
  var _props$rehypePlugins = props.rehypePlugins,
    rehypePlugins = _props$rehypePlugins === void 0 ? [[_rehypePrismPlus["default"], {
      ignoreMissing: true
    }]] : _props$rehypePlugins,
    reset = (0, _objectWithoutProperties2["default"])(props, _excluded);
  return /*#__PURE__*/(0, _jsxRuntime.jsx)(_Editor["default"], (0, _objectSpread2["default"])((0, _objectSpread2["default"])({}, reset), {}, {
    rehypePlugins: rehypePlugins,
    ref: ref
  }));
});