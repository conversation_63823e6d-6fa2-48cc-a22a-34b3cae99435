'use client'

import { motion, MotionProps } from 'framer-motion'
import { forwardRef, ReactNode } from 'react'
import { Button, ButtonProps } from '~/components/ui/Button'
import { cn } from '~/lib/utils'

interface AnimatedButtonProps extends ButtonProps {
  children: ReactNode
  animationType?: 'bounce' | 'scale' | 'shake' | 'pulse' | 'rotate'
  className?: string
}

const animations = {
  bounce: {
    whileHover: { y: -2 },
    whileTap: { y: 0 },
    transition: { type: 'spring', stiffness: 400, damping: 10 }
  },
  scale: {
    whileHover: { scale: 1.05 },
    whileTap: { scale: 0.95 },
    transition: { type: 'spring', stiffness: 400, damping: 10 }
  },
  shake: {
    whileHover: { x: [-1, 1, -1, 1, 0] },
    transition: { duration: 0.4 }
  },
  pulse: {
    whileHover: { scale: [1, 1.05, 1] },
    transition: { duration: 0.3, repeat: Infinity }
  },
  rotate: {
    whileHover: { rotate: [0, -5, 5, 0] },
    transition: { duration: 0.3 }
  }
}

export const AnimatedButton = forwardRef<HTMLButtonElement, AnimatedButtonProps>(
  ({ children, animationType = 'scale', className, ...props }, ref) => {
    const animation = animations[animationType]

    return (
      <motion.button
        ref={ref}
        className={cn('cursor-pointer inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2', className)}
        {...animation}
        {...props}
      >
        {children}
      </motion.button>
    )
  }
)

AnimatedButton.displayName = 'AnimatedButton'

// 浮动动作按钮
export function FloatingActionButton({ 
  children, 
  onClick, 
  className 
}: { 
  children: ReactNode
  onClick?: () => void
  className?: string 
}) {
  return (
    <motion.button
      className={cn(
        'fixed bottom-6 right-6 w-14 h-14 bg-blue-500 hover:bg-blue-600 text-white rounded-full shadow-lg flex items-center justify-center z-50',
        className
      )}
      onClick={onClick}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ type: 'spring', stiffness: 260, damping: 20 }}
    >
      {children}
    </motion.button>
  )
}

// 成功动画按钮
export function SuccessButton({ 
  children, 
  isSuccess, 
  onClick, 
  className,
  ...props 
}: ButtonProps & { 
  isSuccess?: boolean
}) {
  return (
    <motion.div
      animate={isSuccess ? { scale: [1, 1.2, 1] } : { scale: 1 }}
      transition={{ duration: 0.3 }}
    >
      <Button
        className={cn(
          'relative overflow-hidden',
          isSuccess && 'bg-green-500 hover:bg-green-600',
          className
        )}
        onClick={onClick}
        {...props}
      >
        <motion.div
          initial={{ x: '-100%' }}
          animate={isSuccess ? { x: '0%' } : { x: '-100%' }}
          transition={{ duration: 0.3 }}
          className="absolute inset-0 bg-green-400"
        />
        <span className="relative z-10">
          {isSuccess ? '成功!' : children}
        </span>
      </Button>
    </motion.div>
  )
}

// 加载按钮
export function LoadingButton({ 
  children, 
  isLoading, 
  onClick, 
  className,
  ...props 
}: ButtonProps & { 
  isLoading?: boolean
}) {
  return (
    <Button
      className={cn('relative', className)}
      onClick={onClick}
      disabled={isLoading}
      {...props}
    >
      <motion.span
        animate={{ opacity: isLoading ? 0 : 1 }}
        transition={{ duration: 0.2 }}
      >
        {children}
      </motion.span>
      
      {isLoading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.2 }}
          className="absolute inset-0 flex items-center justify-center"
        >
          <motion.div
            className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
          />
        </motion.div>
      )}
    </Button>
  )
}

// 点击波纹效果按钮
export function RippleButton({ 
  children, 
  onClick, 
  className,
  ...props 
}: ButtonProps) {
  return (
    <motion.div className="relative overflow-hidden rounded-md">
      <Button
        className={cn('relative z-10', className)}
        onClick={onClick}
        {...props}
      >
        {children}
      </Button>
      
      <motion.div
        className="absolute inset-0 bg-white opacity-20 rounded-full"
        initial={{ scale: 0, opacity: 0.5 }}
        whileTap={{ scale: 4, opacity: 0 }}
        transition={{ duration: 0.4 }}
      />
    </motion.div>
  )
}
