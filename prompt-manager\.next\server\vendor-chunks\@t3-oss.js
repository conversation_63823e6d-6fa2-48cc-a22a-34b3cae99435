"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@t3-oss";
exports.ids = ["vendor-chunks/@t3-oss"];
exports.modules = {

/***/ "(rsc)/./node_modules/@t3-oss/env-core/dist/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/@t3-oss/env-core/dist/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createEnv: () => (/* binding */ createEnv)\n/* harmony export */ });\n/** The Standard Schema interface. */ function parseWithDictionary(dictionary, value) {\n    const result = {};\n    const issues = [];\n    for(const key in dictionary){\n        const schema = dictionary[key];\n        const prop = value[key];\n        const propResult = schema[\"~standard\"].validate(prop);\n        if (propResult instanceof Promise) {\n            throw new Error(`Validation must be synchronous, but ${key} returned a Promise.`);\n        }\n        if (propResult.issues) {\n            issues.push(...propResult.issues.map((issue)=>({\n                    ...issue,\n                    path: [\n                        key,\n                        ...issue.path ?? []\n                    ]\n                })));\n            continue;\n        }\n        result[key] = propResult.value;\n    }\n    if (issues.length) {\n        return {\n            issues\n        };\n    }\n    return {\n        value: result\n    };\n}\n\nfunction createEnv(opts) {\n    const runtimeEnv = opts.runtimeEnvStrict ?? opts.runtimeEnv ?? process.env;\n    const emptyStringAsUndefined = opts.emptyStringAsUndefined ?? false;\n    if (emptyStringAsUndefined) {\n        for (const [key, value] of Object.entries(runtimeEnv)){\n            if (value === \"\") {\n                delete runtimeEnv[key];\n            }\n        }\n    }\n    const skip = !!opts.skipValidation;\n    // biome-ignore lint/suspicious/noExplicitAny: <explanation>\n    if (skip) return runtimeEnv;\n    const _client = typeof opts.client === \"object\" ? opts.client : {};\n    const _server = typeof opts.server === \"object\" ? opts.server : {};\n    const _shared = typeof opts.shared === \"object\" ? opts.shared : {};\n    const isServer = opts.isServer ?? (typeof window === \"undefined\" || \"Deno\" in window);\n    const finalSchema = isServer ? {\n        ..._server,\n        ..._shared,\n        ..._client\n    } : {\n        ..._client,\n        ..._shared\n    };\n    const parsed = parseWithDictionary(finalSchema, runtimeEnv);\n    const onValidationError = opts.onValidationError ?? ((issues)=>{\n        console.error(\"❌ Invalid environment variables:\", issues);\n        throw new Error(\"Invalid environment variables\");\n    });\n    const onInvalidAccess = opts.onInvalidAccess ?? (()=>{\n        throw new Error(\"❌ Attempted to access a server-side environment variable on the client\");\n    });\n    if (parsed.issues) {\n        return onValidationError(parsed.issues);\n    }\n    const isServerAccess = (prop)=>{\n        if (!opts.clientPrefix) return true;\n        return !prop.startsWith(opts.clientPrefix) && !(prop in _shared);\n    };\n    const isValidServerAccess = (prop)=>{\n        return isServer || !isServerAccess(prop);\n    };\n    const ignoreProp = (prop)=>{\n        return prop === \"__esModule\" || prop === \"$$typeof\";\n    };\n    const extendedObj = (opts.extends ?? []).reduce((acc, curr)=>{\n        return Object.assign(acc, curr);\n    }, {});\n    const fullObj = Object.assign(parsed.value, extendedObj);\n    const env = new Proxy(fullObj, {\n        get (target, prop) {\n            if (typeof prop !== \"string\") return undefined;\n            if (ignoreProp(prop)) return undefined;\n            if (!isValidServerAccess(prop)) return onInvalidAccess(prop);\n            return Reflect.get(target, prop);\n        }\n    });\n    // biome-ignore lint/suspicious/noExplicitAny: <explanation>\n    return env;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@t3-oss/env-core/dist/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@t3-oss/env-nextjs/dist/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/@t3-oss/env-nextjs/dist/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createEnv: () => (/* binding */ createEnv)\n/* harmony export */ });\n/* harmony import */ var _t3_oss_env_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @t3-oss/env-core */ \"(rsc)/./node_modules/@t3-oss/env-core/dist/index.js\");\n\n\nconst CLIENT_PREFIX = \"NEXT_PUBLIC_\";\nfunction createEnv(opts) {\n    const client = typeof opts.client === \"object\" ? opts.client : {};\n    const server = typeof opts.server === \"object\" ? opts.server : {};\n    const shared = opts.shared;\n    const runtimeEnv = opts.runtimeEnv ? opts.runtimeEnv : {\n        ...process.env,\n        ...opts.experimental__runtimeEnv\n    };\n    return (0,_t3_oss_env_core__WEBPACK_IMPORTED_MODULE_0__.createEnv)({\n        ...opts,\n        shared,\n        client,\n        server,\n        clientPrefix: CLIENT_PREFIX,\n        runtimeEnv\n    });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHQzLW9zcy9lbnYtbmV4dGpzL2Rpc3QvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBNEQ7O0FBRTVEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsMkRBQVc7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMOztBQUVxQiIsInNvdXJjZXMiOlsiRDpcXEN1cnNvciBQcm9qZWN0XFx3ZWJzaXRlXFxBdWdtZW50MlxccHJvbXB0LW1hbmFnZXJcXG5vZGVfbW9kdWxlc1xcQHQzLW9zc1xcZW52LW5leHRqc1xcZGlzdFxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlRW52IGFzIGNyZWF0ZUVudiQxIH0gZnJvbSAnQHQzLW9zcy9lbnYtY29yZSc7XG5cbmNvbnN0IENMSUVOVF9QUkVGSVggPSBcIk5FWFRfUFVCTElDX1wiO1xuZnVuY3Rpb24gY3JlYXRlRW52KG9wdHMpIHtcbiAgICBjb25zdCBjbGllbnQgPSB0eXBlb2Ygb3B0cy5jbGllbnQgPT09IFwib2JqZWN0XCIgPyBvcHRzLmNsaWVudCA6IHt9O1xuICAgIGNvbnN0IHNlcnZlciA9IHR5cGVvZiBvcHRzLnNlcnZlciA9PT0gXCJvYmplY3RcIiA/IG9wdHMuc2VydmVyIDoge307XG4gICAgY29uc3Qgc2hhcmVkID0gb3B0cy5zaGFyZWQ7XG4gICAgY29uc3QgcnVudGltZUVudiA9IG9wdHMucnVudGltZUVudiA/IG9wdHMucnVudGltZUVudiA6IHtcbiAgICAgICAgLi4ucHJvY2Vzcy5lbnYsXG4gICAgICAgIC4uLm9wdHMuZXhwZXJpbWVudGFsX19ydW50aW1lRW52XG4gICAgfTtcbiAgICByZXR1cm4gY3JlYXRlRW52JDEoe1xuICAgICAgICAuLi5vcHRzLFxuICAgICAgICBzaGFyZWQsXG4gICAgICAgIGNsaWVudCxcbiAgICAgICAgc2VydmVyLFxuICAgICAgICBjbGllbnRQcmVmaXg6IENMSUVOVF9QUkVGSVgsXG4gICAgICAgIHJ1bnRpbWVFbnZcbiAgICB9KTtcbn1cblxuZXhwb3J0IHsgY3JlYXRlRW52IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@t3-oss/env-nextjs/dist/index.js\n");

/***/ })

};
;