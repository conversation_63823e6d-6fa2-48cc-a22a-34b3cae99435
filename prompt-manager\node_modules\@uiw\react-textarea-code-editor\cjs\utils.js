"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault")["default"];
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.htmlEncode = htmlEncode;
exports.processHtml = void 0;
exports.stopPropagation = stopPropagation;
var _toConsumableArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));
var _rehype = require("rehype");
var processHtml = exports.processHtml = function processHtml(html) {
  var plugins = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];
  return (0, _rehype.rehype)().data('settings', {
    fragment: true
  }).use((0, _toConsumableArray2["default"])(plugins)).processSync("".concat(html)).toString();
};
function htmlEncode(sHtml) {
  return sHtml.replace(/```(tsx?|jsx?|html|xml)(.*)\s+([\s\S]*?)(\s.+)?```/g, function (str) {
    return str.replace(/[<&"]/g, function (c) {
      return {
        '<': '&lt;',
        '>': '&gt;',
        '&': '&amp;',
        '"': '&quot;'
      }[c];
    });
  }).replace(/[<&"]/g, function (c) {
    return {
      '<': '&lt;',
      '>': '&gt;',
      '&': '&amp;',
      '"': '&quot;'
    }[c];
  });
}
function stopPropagation(e) {
  e.stopPropagation();
  e.preventDefault();
}