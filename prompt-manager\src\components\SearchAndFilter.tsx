'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Input } from '~/components/ui/Input'
import { Button } from '~/components/ui/Button'
import { Badge } from '~/components/ui/Badge'
import { useUI, useSearchHistory, useModals } from '~/hooks/useStore'
import { debounce } from '~/lib/utils'
import { api } from '~/trpc/react'

interface SearchAndFilterProps {
  onSearch?: (query: string) => void
  onSortChange?: (sortBy: string, sortOrder: 'asc' | 'desc') => void
  hasActiveFilters?: boolean
  onClearFilters?: () => void
}

export function SearchAndFilter({
  onSearch,
  onSortChange,
  hasActiveFilters = false,
  onClearFilters,
}: SearchAndFilterProps) {
  const {
    searchQuery,
    setSearchQuery,
    sortBy,
    setSortBy,
    sortOrder,
    setSortOrder,
  } = useUI()
  
  const {
    searchHistory,
    addSearchHistory,
    removeSearchHistory,
    recentSearches,
  } = useSearchHistory()

  const [showSearchHistory, setShowSearchHistory] = useState(false)
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery)
  
  // 防抖搜索
  const debouncedSearch = debounce((query: string) => {
    setSearchQuery(query)
    onSearch?.(query)
    if (query.trim()) {
      addSearchHistory(query.trim())
    }
  }, 300)
  
  // 处理搜索输入
  const handleSearchChange = (value: string) => {
    setLocalSearchQuery(value)
    debouncedSearch(value)
  }
  
  // 处理搜索历史点击
  const handleHistoryClick = (query: string) => {
    setLocalSearchQuery(query)
    setSearchQuery(query)
    onSearch?.(query)
    setShowSearchHistory(false)
  }
  

  
  // 处理排序
  const handleSortChange = (newSortBy: string) => {
    if (newSortBy === sortBy) {
      // 如果是同一个字段，切换排序方向
      const newSortOrder = sortOrder === 'asc' ? 'desc' : 'asc'
      setSortOrder(newSortOrder)
      onSortChange?.(sortBy, newSortOrder)
    } else {
      // 如果是不同字段，使用默认排序方向
      setSortBy(newSortBy as any)
      setSortOrder('desc')
      onSortChange?.(newSortBy, 'desc')
    }
  }
  
  // 清除所有筛选
  const handleClearFilters = () => {
    setLocalSearchQuery('')
    setSearchQuery('')
    setSortBy('createdAt')
    setSortOrder('desc')
    onSearch?.('')
    onSortChange?.('createdAt', 'desc')
    onClearFilters?.()
  }

  return (
    <div className="space-y-6">
      {/* 搜索框 */}
      <div className="relative">
        <div className="form-control">
          <div className="input-group">
            <input
              type="text"
              placeholder="搜索提示词标题、内容或标签..."
              value={localSearchQuery}
              onChange={(e) => handleSearchChange(e.target.value)}
              onFocus={() => setShowSearchHistory(true)}
              onBlur={() => setTimeout(() => setShowSearchHistory(false), 200)}
              className="input input-bordered w-full"
            />
            <button className="btn btn-square">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </button>
          </div>
        </div>
        
        {/* 搜索历史下拉 */}
        {showSearchHistory && recentSearches.length > 0 && (
          <div className="dropdown dropdown-open w-full">
            <div className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-full mt-1">
              <div className="menu-title">
                <span>最近搜索</span>
              </div>
              {recentSearches.map((query, index) => (
                <li key={index}>
                  <div className="flex items-center justify-between">
                    <button
                      onClick={() => handleHistoryClick(query)}
                      className="flex-1 text-left"
                    >
                      {query}
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        removeSearchHistory(query)
                      }}
                      className="btn btn-ghost btn-xs"
                    >
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                </li>
              ))}
            </div>
          </div>
        )}
      </div>
      
      {/* 排序 */}
      <div className="flex flex-wrap items-center gap-4">
        <span className="text-sm font-medium">排序：</span>
        <div className="join">
          {[
            { key: 'createdAt', label: '创建时间' },
            { key: 'updatedAt', label: '更新时间' },
            { key: 'usageCount', label: '使用次数' },
            { key: 'title', label: '标题' },
          ].map((sort) => (
            <button
              key={sort.key}
              onClick={() => handleSortChange(sort.key)}
              className={`btn join-item btn-sm ${
                sortBy === sort.key ? 'btn-active' : 'btn-outline'
              }`}
            >
              {sort.label}
              {sortBy === sort.key && (
                <svg
                  className={`w-3 h-3 ml-1 transition-transform ${
                    sortOrder === 'asc' ? 'rotate-180' : ''
                  }`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              )}
            </button>
          ))}
        </div>
        
        {/* 清除筛选 */}
        {hasActiveFilters && (
          <button
            onClick={handleClearFilters}
            className="btn btn-outline btn-error btn-sm"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            清除筛选
          </button>
        )}
      </div>
    </div>
  )
}
