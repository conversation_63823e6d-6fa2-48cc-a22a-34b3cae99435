'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Input } from '~/components/ui/Input'
import { Button } from '~/components/ui/Button'
import { Badge } from '~/components/ui/Badge'
import { useUI, useSearchHistory, useModals } from '~/hooks/useStore'
import { debounce } from '~/lib/utils'
import { api } from '~/trpc/react'

interface SearchAndFilterProps {
  onSearch?: (query: string) => void
  onSortChange?: (sortBy: string, sortOrder: 'asc' | 'desc') => void
  hasActiveFilters?: boolean
  onClearFilters?: () => void
}

export function SearchAndFilter({
  onSearch,
  onSortChange,
  hasActiveFilters = false,
  onClearFilters,
}: SearchAndFilterProps) {
  const {
    searchQuery,
    setSearchQuery,
    sortBy,
    setSortBy,
    sortOrder,
    setSortOrder,
  } = useUI()
  
  const {
    searchHistory,
    addSearchHistory,
    removeSearchHistory,
    recentSearches,
  } = useSearchHistory()

  const [showSearchHistory, setShowSearchHistory] = useState(false)
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery)
  
  // 防抖搜索
  const debouncedSearch = debounce((query: string) => {
    setSearchQuery(query)
    onSearch?.(query)
    if (query.trim()) {
      addSearchHistory(query.trim())
    }
  }, 300)
  
  // 处理搜索输入
  const handleSearchChange = (value: string) => {
    setLocalSearchQuery(value)
    debouncedSearch(value)
  }
  
  // 处理搜索历史点击
  const handleHistoryClick = (query: string) => {
    setLocalSearchQuery(query)
    setSearchQuery(query)
    onSearch?.(query)
    setShowSearchHistory(false)
  }
  

  
  // 处理排序
  const handleSortChange = (newSortBy: string) => {
    if (newSortBy === sortBy) {
      // 如果是同一个字段，切换排序方向
      const newSortOrder = sortOrder === 'asc' ? 'desc' : 'asc'
      setSortOrder(newSortOrder)
      onSortChange?.(sortBy, newSortOrder)
    } else {
      // 如果是不同字段，使用默认排序方向
      setSortBy(newSortBy as any)
      setSortOrder('desc')
      onSortChange?.(newSortBy, 'desc')
    }
  }
  
  // 清除所有筛选
  const handleClearFilters = () => {
    setLocalSearchQuery('')
    setSearchQuery('')
    setSortBy('createdAt')
    setSortOrder('desc')
    onSearch?.('')
    onSortChange?.('createdAt', 'desc')
    onClearFilters?.()
  }

  return (
    <div className="space-y-6">
      {/* 搜索框 */}
      <div className="relative">
        <div className="relative group">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <Input
            placeholder="🔍 搜索提示词标题、内容或标签..."
            value={localSearchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
            onFocus={() => setShowSearchHistory(true)}
            onBlur={() => setTimeout(() => setShowSearchHistory(false), 200)}
            className="pl-12 pr-4 h-12 text-base bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500/50 relative z-10"
          />
          <div className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400 group-hover:text-blue-500 transition-colors duration-200">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          {localSearchQuery && (
            <button
              onClick={() => handleSearchChange('')}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 transition-colors duration-200"
            >
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>
        
        {/* 搜索历史下拉 */}
        <AnimatePresence>
          {showSearchHistory && recentSearches.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              transition={{ duration: 0.2, ease: "easeOut" }}
              className="absolute top-full left-0 right-0 mt-2 bg-white/90 dark:bg-slate-800/90 backdrop-blur-xl border border-slate-200/50 dark:border-slate-700/50 rounded-xl shadow-2xl z-50 overflow-hidden"
            >
              <div className="p-4">
                <div className="flex items-center gap-2 text-xs font-medium text-slate-500 dark:text-slate-400 mb-3">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  最近搜索
                </div>
                <div className="space-y-1">
                  {recentSearches.map((query, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.05 }}
                      className="flex items-center justify-between p-3 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 dark:hover:from-slate-700/50 dark:hover:to-slate-600/50 rounded-lg cursor-pointer group transition-all duration-200"
                      onClick={() => handleHistoryClick(query)}
                    >
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full opacity-60 group-hover:opacity-100 transition-opacity duration-200"></div>
                        <span className="text-sm font-medium text-slate-700 dark:text-slate-300">{query}</span>
                      </div>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          removeSearchHistory(query)
                        }}
                        className="text-slate-400 hover:text-red-500 dark:hover:text-red-400 opacity-0 group-hover:opacity-100 transition-all duration-200 p-1 rounded-md hover:bg-red-50 dark:hover:bg-red-900/20"
                      >
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
      
      {/* 排序 */}
      <div className="flex flex-wrap items-center gap-6">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
              <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12" />
              </svg>
            </div>
            <span className="text-sm font-medium text-slate-700 dark:text-slate-300">排序：</span>
          </div>
          <div className="flex gap-2 bg-white/60 dark:bg-slate-700/60 backdrop-blur-sm rounded-xl p-1 border border-white/20">
            {[
              { key: 'createdAt', label: '创建时间', icon: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z' },
              { key: 'updatedAt', label: '更新时间', icon: 'M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15' },
              { key: 'usageCount', label: '使用次数', icon: 'M13 10V3L4 14h7v7l9-11h-7z' },
              { key: 'title', label: '标题', icon: 'M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H5a1 1 0 01-1-1V4h2z' },
            ].map((sort) => (
              <Button
                key={sort.key}
                variant="ghost"
                size="sm"
                onClick={() => handleSortChange(sort.key)}
                className={`text-xs font-medium transition-all duration-200 relative ${
                  sortBy === sort.key
                    ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg hover:shadow-xl'
                    : 'text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-100 hover:bg-white/50 dark:hover:bg-slate-600/50'
                }`}
              >
                <div className="flex items-center gap-2">
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={sort.icon} />
                  </svg>
                  {sort.label}
                  {sortBy === sort.key && (
                    <svg
                      className={`w-3 h-3 transition-transform duration-200 ${
                        sortOrder === 'asc' ? 'rotate-180' : ''
                      }`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  )}
                </div>
              </Button>
            ))}
          </div>
        </div>
        
        {/* 清除筛选 */}
        {hasActiveFilters && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.2 }}
          >
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClearFilters}
              className="text-xs font-medium bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/40 border border-red-200/50 dark:border-red-700/50 rounded-lg transition-all duration-200 hover:shadow-md"
            >
              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              清除筛选
            </Button>
          </motion.div>
        )}
      </div>
    </div>
  )
}
