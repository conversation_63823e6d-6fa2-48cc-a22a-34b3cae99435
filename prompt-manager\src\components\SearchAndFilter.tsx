'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Input } from '~/components/ui/Input'
import { Button } from '~/components/ui/Button'
import { Badge } from '~/components/ui/Badge'
import { useUI, useSearchHistory, useModals } from '~/hooks/useStore'
import { debounce } from '~/lib/utils'
import { api } from '~/trpc/react'

interface SearchAndFilterProps {
  onSearch?: (query: string) => void
  onSortChange?: (sortBy: string, sortOrder: 'asc' | 'desc') => void
  hasActiveFilters?: boolean
  onClearFilters?: () => void
}

export function SearchAndFilter({
  onSearch,
  onSortChange,
  hasActiveFilters = false,
  onClearFilters,
}: SearchAndFilterProps) {
  const {
    searchQuery,
    setSearchQuery,
    sortBy,
    setSortBy,
    sortOrder,
    setSortOrder,
  } = useUI()
  
  const {
    searchHistory,
    addSearchHistory,
    removeSearchHistory,
    recentSearches,
  } = useSearchHistory()

  const [showSearchHistory, setShowSearchHistory] = useState(false)
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery)
  
  // 防抖搜索
  const debouncedSearch = debounce((query: string) => {
    setSearchQuery(query)
    onSearch?.(query)
    if (query.trim()) {
      addSearchHistory(query.trim())
    }
  }, 300)
  
  // 处理搜索输入
  const handleSearchChange = (value: string) => {
    setLocalSearchQuery(value)
    debouncedSearch(value)
  }
  
  // 处理搜索历史点击
  const handleHistoryClick = (query: string) => {
    setLocalSearchQuery(query)
    setSearchQuery(query)
    onSearch?.(query)
    setShowSearchHistory(false)
  }
  

  
  // 处理排序
  const handleSortChange = (newSortBy: string) => {
    if (newSortBy === sortBy) {
      // 如果是同一个字段，切换排序方向
      const newSortOrder = sortOrder === 'asc' ? 'desc' : 'asc'
      setSortOrder(newSortOrder)
      onSortChange?.(sortBy, newSortOrder)
    } else {
      // 如果是不同字段，使用默认排序方向
      setSortBy(newSortBy as any)
      setSortOrder('desc')
      onSortChange?.(newSortBy, 'desc')
    }
  }
  
  // 清除所有筛选
  const handleClearFilters = () => {
    setLocalSearchQuery('')
    setSearchQuery('')
    setSortBy('createdAt')
    setSortOrder('desc')
    onSearch?.('')
    onSortChange?.('createdAt', 'desc')
    onClearFilters?.()
  }

  return (
    <div className="space-y-4">
      {/* 搜索框 */}
      <div className="relative">
        <div className="relative">
          <Input
            placeholder="搜索提示词标题、内容或标签..."
            value={localSearchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
            onFocus={() => setShowSearchHistory(true)}
            onBlur={() => setTimeout(() => setShowSearchHistory(false), 200)}
            className="pl-10 pr-4"
          />
          <svg
            className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
        
        {/* 搜索历史下拉 */}
        <AnimatePresence>
          {showSearchHistory && recentSearches.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-md shadow-lg z-50"
            >
              <div className="p-2">
                <div className="text-xs text-slate-500 dark:text-slate-400 mb-2">
                  最近搜索
                </div>
                {recentSearches.map((query, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-2 hover:bg-slate-100 dark:hover:bg-slate-700 rounded cursor-pointer"
                    onClick={() => handleHistoryClick(query)}
                  >
                    <span className="text-sm">{query}</span>
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        removeSearchHistory(query)
                      }}
                      className="text-slate-400 hover:text-slate-600 dark:hover:text-slate-300"
                    >
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
      
      {/* 排序 */}
      <div className="flex flex-wrap items-center gap-4">{/* 排序 */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-slate-600 dark:text-slate-400">排序：</span>
          <div className="flex gap-1">
            {[
              { key: 'createdAt', label: '创建时间' },
              { key: 'updatedAt', label: '更新时间' },
              { key: 'usageCount', label: '使用次数' },
              { key: 'title', label: '标题' },
            ].map((sort) => (
              <Button
                key={sort.key}
                variant={sortBy === sort.key ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleSortChange(sort.key)}
                className="text-xs"
              >
                {sort.label}
                {sortBy === sort.key && (
                  <svg
                    className={`w-3 h-3 ml-1 transition-transform ${
                      sortOrder === 'asc' ? 'rotate-180' : ''
                    }`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                )}
              </Button>
            ))}
          </div>
        </div>
        
        {/* 清除筛选 */}
        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClearFilters}
            className="text-xs"
          >
            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
            清除筛选
          </Button>
        )}
      </div>
    </div>
  )
}
