'use client'

import { motion, AnimatePresence } from 'framer-motion'
import { PromptCard } from '~/components/PromptCard'
import { But<PERSON> } from '~/components/ui/Button'
import { CardSkeleton } from '~/components/LoadingSpinner'
import { useUI } from '~/hooks/useStore'
import { api } from '~/trpc/react'
import type { Prompt } from '~/store'

interface PromptListProps {
  prompts?: Prompt[]
  isLoading?: boolean
  onCopy?: (promptId: string) => void
  showPagination?: boolean
  currentPage?: number
  totalPages?: number
  onPageChange?: (page: number) => void
}

export function PromptList({
  prompts = [],
  isLoading = false,
  onCopy,
  showPagination = false,
  currentPage = 1,
  totalPages = 1,
  onPageChange,
}: PromptListProps) {
  const { viewMode, toggleViewMode } = useUI()

  // 加载状态
  if (isLoading) {
    return (
      <div className="space-y-4">
        {/* 视图切换按钮骨架 */}
        <div className="flex items-center justify-between">
          <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-32 animate-pulse" />
          <div className="h-9 bg-slate-200 dark:bg-slate-700 rounded w-24 animate-pulse" />
        </div>
        
        {/* 内容骨架 */}
        <div className={
          viewMode === 'grid'
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4'
            : 'space-y-4'
        }>
          {Array.from({ length: 8 }).map((_, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <CardSkeleton />
            </motion.div>
          ))}
        </div>
      </div>
    )
  }

  // 空状态
  if (prompts.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="hero">
          <div className="hero-content text-center">
            <div className="max-w-md">
              <div className="w-24 h-24 mx-auto mb-6 bg-base-300 rounded-full flex items-center justify-center">
                <svg
                  className="w-12 h-12 opacity-50"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <h1 className="text-3xl font-bold">没有找到提示词</h1>
              <p className="py-6">
                尝试调整搜索条件或创建新的提示词
              </p>
              <div className="flex justify-center gap-4">
                <button className="btn btn-primary">
                  创建提示词
                </button>
                <button className="btn btn-outline">
                  清除筛选
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* 工具栏 */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-slate-600 dark:text-slate-400">
          共 {prompts.length} 个提示词
        </div>
        
        <div className="flex items-center gap-2">
          {/* 视图切换 */}
          <Button
            variant="outline"
            size="sm"
            onClick={toggleViewMode}
            className="flex items-center gap-2"
          >
            {viewMode === 'grid' ? (
              <>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                </svg>
                列表视图
              </>
            ) : (
              <>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                </svg>
                网格视图
              </>
            )}
          </Button>
        </div>
      </div>

      {/* 提示词列表 */}
      <AnimatePresence mode="wait">
        <motion.div
          key={viewMode}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
          className={
            viewMode === 'grid'
              ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
              : 'space-y-6'
          }
        >
          {prompts.map((prompt, index) => (
            <motion.div
              key={prompt.id}
              initial={{ opacity: 0, y: 30, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{
                delay: index * 0.08,
                duration: 0.4,
                ease: "easeOut"
              }}
              className={viewMode === 'list' ? 'w-full' : ''}
            >
              <PromptCard
                prompt={prompt}
                onCopy={onCopy}
                className={viewMode === 'list' ? 'w-full' : ''}
              />
            </motion.div>
          ))}
        </motion.div>
      </AnimatePresence>

      {/* 分页 */}
      {showPagination && totalPages > 1 && (
        <div className="flex justify-center mt-8">
          <div className="join">
            <button
              onClick={() => onPageChange?.(currentPage - 1)}
              disabled={currentPage <= 1}
              className="join-item btn btn-outline"
            >
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
              上一页
            </button>

            {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
              let page;
              if (totalPages <= 5) {
                page = i + 1;
              } else if (currentPage <= 3) {
                page = i + 1;
              } else if (currentPage >= totalPages - 2) {
                page = totalPages - 4 + i;
              } else {
                page = currentPage - 2 + i;
              }

              return (
                <button
                  key={page}
                  onClick={() => onPageChange?.(page)}
                  className={`join-item btn ${
                    currentPage === page ? 'btn-active' : 'btn-outline'
                  }`}
                >
                  {page}
                </button>
              );
            })}

            <button
              onClick={() => onPageChange?.(currentPage + 1)}
              disabled={currentPage >= totalPages}
              className="join-item btn btn-outline"
            >
              下一页
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
