'use client'

import { motion, AnimatePresence } from 'framer-motion'
import { PromptCard } from '~/components/PromptCard'
import { But<PERSON> } from '~/components/ui/Button'
import { CardSkeleton } from '~/components/LoadingSpinner'
import { useUI } from '~/hooks/useStore'
import { api } from '~/trpc/react'
import type { Prompt } from '~/store'

interface PromptListProps {
  prompts?: Prompt[]
  isLoading?: boolean
  onCopy?: (promptId: string) => void
  showPagination?: boolean
  currentPage?: number
  totalPages?: number
  onPageChange?: (page: number) => void
}

export function PromptList({
  prompts = [],
  isLoading = false,
  onCopy,
  showPagination = false,
  currentPage = 1,
  totalPages = 1,
  onPageChange,
}: PromptListProps) {
  const { viewMode, toggleViewMode } = useUI()

  // 加载状态
  if (isLoading) {
    return (
      <div className="space-y-4">
        {/* 视图切换按钮骨架 */}
        <div className="flex items-center justify-between">
          <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-32 animate-pulse" />
          <div className="h-9 bg-slate-200 dark:bg-slate-700 rounded w-24 animate-pulse" />
        </div>
        
        {/* 内容骨架 */}
        <div className={
          viewMode === 'grid'
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4'
            : 'space-y-4'
        }>
          {Array.from({ length: 8 }).map((_, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <CardSkeleton />
            </motion.div>
          ))}
        </div>
      </div>
    )
  }

  // 空状态
  if (prompts.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-full blur-3xl"></div>
          <div className="relative bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-2xl p-12 border border-white/20 dark:border-slate-700/20 shadow-lg">
            <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center shadow-lg">
              <svg
                className="w-10 h-10 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
            <h3 className="text-2xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 dark:from-slate-100 dark:to-slate-300 bg-clip-text text-transparent mb-3">
              没有找到提示词
            </h3>
            <p className="text-slate-600 dark:text-slate-400 text-lg mb-6">
              尝试调整搜索条件或创建新的提示词
            </p>
            <div className="flex justify-center gap-4">
              <button className="bg-gradient-to-r from-blue-500 to-purple-500 text-white px-6 py-3 rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105">
                创建提示词
              </button>
              <button className="bg-white/60 dark:bg-slate-700/60 text-slate-700 dark:text-slate-300 px-6 py-3 rounded-xl font-medium border border-white/20 hover:bg-white/80 dark:hover:bg-slate-600/80 transition-all duration-200">
                清除筛选
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* 工具栏 */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-slate-600 dark:text-slate-400">
          共 {prompts.length} 个提示词
        </div>
        
        <div className="flex items-center gap-2">
          {/* 视图切换 */}
          <Button
            variant="outline"
            size="sm"
            onClick={toggleViewMode}
            className="flex items-center gap-2"
          >
            {viewMode === 'grid' ? (
              <>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                </svg>
                列表视图
              </>
            ) : (
              <>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                </svg>
                网格视图
              </>
            )}
          </Button>
        </div>
      </div>

      {/* 提示词列表 */}
      <AnimatePresence mode="wait">
        <motion.div
          key={viewMode}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
          className={
            viewMode === 'grid'
              ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
              : 'space-y-6'
          }
        >
          {prompts.map((prompt, index) => (
            <motion.div
              key={prompt.id}
              initial={{ opacity: 0, y: 30, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{
                delay: index * 0.08,
                duration: 0.4,
                ease: "easeOut"
              }}
              className={viewMode === 'list' ? 'w-full' : ''}
            >
              <PromptCard
                prompt={prompt}
                onCopy={onCopy}
                className={viewMode === 'list' ? 'w-full' : ''}
              />
            </motion.div>
          ))}
        </motion.div>
      </AnimatePresence>

      {/* 分页 */}
      {showPagination && totalPages > 1 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="flex items-center justify-center gap-3 mt-12"
        >
          <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-xl p-2 shadow-lg border border-white/20 dark:border-slate-700/20">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onPageChange?.(currentPage - 1)}
                disabled={currentPage <= 1}
                className="bg-white/60 dark:bg-slate-700/60 hover:bg-gradient-to-r hover:from-blue-500 hover:to-purple-500 hover:text-white transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            上一页
          </Button>
          
          <div className="flex items-center gap-1">
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              let pageNum: number
              if (totalPages <= 5) {
                pageNum = i + 1
              } else if (currentPage <= 3) {
                pageNum = i + 1
              } else if (currentPage >= totalPages - 2) {
                pageNum = totalPages - 4 + i
              } else {
                pageNum = currentPage - 2 + i
              }
              
              return (
                <Button
                  key={pageNum}
                  variant="ghost"
                  size="sm"
                  onClick={() => onPageChange?.(pageNum)}
                  className={`w-10 h-10 p-0 transition-all duration-200 ${
                    currentPage === pageNum
                      ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg'
                      : 'bg-white/60 dark:bg-slate-700/60 hover:bg-gradient-to-r hover:from-blue-500 hover:to-purple-500 hover:text-white'
                  }`}
                >
                  {pageNum}
                </Button>
              )
            })}
          </div>
          
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onPageChange?.(currentPage + 1)}
                disabled={currentPage >= totalPages}
                className="bg-white/60 dark:bg-slate-700/60 hover:bg-gradient-to-r hover:from-blue-500 hover:to-purple-500 hover:text-white transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
            下一页
            <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </Button>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  )
}
